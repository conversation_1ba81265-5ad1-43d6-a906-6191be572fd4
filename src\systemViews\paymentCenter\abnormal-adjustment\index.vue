<!-- 异常提醒 -->
<template>
  <div class="app-container">
    <el-card>
      <dialog-search @getList="getList" formRowNumber="4" :columns="tabelForm.columns" :isShowRightBtn="$checkPermi(['pay:adjust:list'])">
        <template v-slot:formList>


          <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="80px">

            

            <el-form-item label="模糊搜索" prop="staffName">
                  <el-input v-model="queryParams.staffName" placeholder="请输入人员姓名、账号进行搜索" clearable></el-input>
                </el-form-item>
            <el-form-item label="操作时间" prop="selectCreateTime">
              <el-date-picker v-model="selectCreateTime" type="datetimerange" value-format="YYYY-MM-DD"
                value="YYYY-MM-DD" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                @change="handleDateChange">
              </el-date-picker>
            </el-form-item>

            <el-form-item label="调整类型" prop="adjustType">
              <el-select v-model="queryParams.adjustType" placeholder="请选择调整类型" clearable>
                <el-option v-for="dict in adjust_type" :key="dict.id" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="调整状态" prop="status">
              <el-select v-model="queryParams.status" placeholder="请选择调整状态" clearable>
                <el-option v-for="dict in adjust_status" :key="dict.id" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-form>
        </template>
        <template v-slot:searchList>
          <el-button v-hasPermi="['pay:adjust:list']" type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button v-hasPermi="['pay:adjust:list']" icon="Refresh" @click="resetQuery">重置</el-button>
        </template>

        <template v-slot:searchBtnList>
          <el-button v-hasPermi="['pay:adjust:add']" type="primary" icon="Plus" @click="handleSupplementary">补扣
          </el-button>
          <el-button v-hasPermi="['pay:adjust:add']" icon="Plus" @click="handleRefund">退款
          </el-button>
          <el-button v-hasPermi="['pay:adjust:add']" icon="Upload" @click="handleBatchSupplementary">批量导入补扣
          </el-button>
          <el-button v-hasPermi="['pay:adjust:add']" icon="Upload" @click="handleBatchRefund">批量导入退款
          </el-button>

          <el-button icon="Download" @click="handleExport"  v-hasPermi="['pay:adjust:export']">导出
          </el-button>

        
        </template>


      </dialog-search>

      <public-table ref="publictable" :rowKey="tabelForm.tableKey" :tableData="userList" :columns="tabelForm.columns"
        :configFlag="tabelForm.tableConfig" :pageValue="queryParams" :total="total" :getList="getList"  @clickTextDetail="clickTextDetail">
        <template #operation="{ scope }">
          <!-- <el-button v-hasPermi="['pay:adjust:add']" link icon="Delete" type="primary" title="删除" @click="handleDelete(scope.row)">
          </el-button> -->
        </template>
      </public-table>

      <DialogBox :visible="diaWindow.open1" :dialogWidth="diaWindow.dialogWidth"
        :dialogFooterBtn="diaWindow.dialogFooterBtn" @save="save" @cancellation="cancellation" @close="close"
        :dialogTitle="diaWindow.headerTitle" :dialogTop="diaWindow.dialogTop"
        :SaveSubmitText="diaWindow.SaveSubmitText">
        <template #content>

          <addRefund ref="addRef" v-if="diaWindow.popupType == 'addRefund'" :popupType="diaWindow.popupType"
            :rowData="diaWindow.rowData" :dialogFooterBtn="diaWindow.dialogFooterBtn" @closeBtn="cancellationRefresh">
          </addRefund>

          <addRefund ref="addRef" v-if="diaWindow.popupType == 'addSupplementary'" :popupType="diaWindow.popupType"
            :rowData="diaWindow.rowData" :dialogFooterBtn="diaWindow.dialogFooterBtn" @closeBtn="cancellationRefresh">
          </addRefund>

          <div class="flex" style="margin-bottom: 10px" v-if="diaWindow.popupType == 'batchRefund'">
            <el-form style="margin-right: 20px" ref="formRef" :model="extraUploadFileData" label-width="80px"
              :rules="rules">
              <el-row>
                <el-col :span="24">
                  <el-form-item label="支付场景" prop="payScene">
                    <el-select v-model="extraUploadFileData.payScene" placeholder="请选择支付场景" clearable filterable>
                      <el-option v-for="item in paySceneOptions" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="支付类型" prop="payType">
                    <el-select v-model="extraUploadFileData.payType" placeholder="请选择支付类型" clearable filterable>
                      <el-option v-for="item in payTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="导入模版:" prop="type">
                    <div class="flex a-link" @click="importTemplateRefund">
                      <el-icon>
                        <UploadFilled />
                      </el-icon>
                      批量退款导入模版
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="导入文件:" prop="type">

                    <UploadFile ref="uploadRef" :isDrag="false" :uploadData="extraUploadFileData" :action="upload.url"
                      :limit="1" :loading="upload.isUploading" :accept="'.xlsx, .xls'" :disabled="upload.isUploading"
                      :auto-upload="false" @update:file-list="handleFileListUpdateRefund"
                      tip='提示：仅允许导入"xls"或"xlsx"格式文件！<br>' @file-success="handleFileSuccessRefund"
                      @file-error="handleFileErrorRefund" />

                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>

          <div class="flex" style="margin-bottom: 10px" v-if="diaWindow.popupType == 'batchSupplementary'">
            <el-form style="margin-right: 20px" ref="formRefDeduction" :model="extraUploadFileData" label-width="80px"
              :rules="rules">
              <el-row>
                <el-col :span="24">
                  <el-form-item label="支付场景" prop="payScene">
                    <el-select v-model="extraUploadFileData.payScene" placeholder="请选择支付场景" clearable filterable>
                      <el-option v-for="item in paySceneOptions" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="支付类型" prop="payType">
                    <el-select v-model="extraUploadFileData.payType" placeholder="请选择支付类型" clearable filterable>
                      <el-option v-for="item in payTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="导入模版:" prop="type">
                    <div class="flex a-link" @click="importTemplateDeduction">
                      <el-icon>
                        <UploadFilled />
                      </el-icon>
                      批量补扣导入模版
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="导入文件:" prop="type">

                    <UploadFile ref="uploadRefDeduction" :isDrag="false" :uploadData="extraUploadFileData" :action="uploadDeduction.url"
                      :limit="1" :loading="uploadDeduction.isUploading" :accept="'.xlsx, .xls'" :disabled="uploadDeduction.isUploading"
                      :auto-upload="false" @update:file-list="handleFileListUpdateDeduction"
                      tip='提示：仅允许导入"xls"或"xlsx"格式文件！<br>' @file-success="handleFileSuccessDeduction"
                      @file-error="handleFileErrorDeduction" />

                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>
          
          <uploadDetail v-if="diaWindow.popupType == 'uploadSuccessdetailList'" ref="uploadDetailRef"
            :uoloadStatus="diaWindow.uoloadStatus" :redisLock="redisLock" :adjustType="diaWindow.adjustType" @closeBtn="cancellationRefsh"></uploadDetail>
  <faceInfo v-if="diaWindow.popupType == 'faceInfo'"   :rowData="diaWindow.rowData" ></faceInfo>
        </template>
      </DialogBox>
    </el-card>
  </div>
</template>

<script setup>
import { screenIndex } from "@/api/paymentCenter/abnormal-adjustment/index";
import { ref, reactive, onMounted, onUnmounted, watch } from "vue";
import { ElMessageBox, ElMessage } from "element-plus";
import { apiUrl } from "@/utils/config";
import { formatMinuteTime } from "@/utils";
import addRefund from "./components/addRefund";
import UploadFile from "@/components/UploadFile/index";
import uploadDetail from "./components/uploadDetail.vue";
const userList = ref([]);
const publictable = ref(null);
const addRef = ref(null);

// 弹窗
const diaWindow = reactive({
  open1: false,
  headerTitle: "",
  SaveSubmitText: '保存',
  popupType: "",
  rowData: "",
  dialogWidth: "50%",
  dialogFooterBtn: false,
  adjustType: "", // 添加调整类型字段，用于区分是补扣还是退款
});
const selectCreateTime = ref([])
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  startDate: '',
  endDate: '',
});
const total = ref(0);

// 用户导入参数
const uploadDetailRef = ref(null);
const redisLock = ref(""); // 定义 redisLock 变量
const fileListRefund = ref([])
const fileListDeduction = ref([]) // 新增补扣文件列表
const uploadRef = ref(null);
const uploadRefDeduction = ref(null); // 新增补扣上传引用
const upload = reactive({
  title: "",
  isUploading: false,
  updateSupport: 0,
  url: `${import.meta.env.VITE_APP_BASE_API
    }/pay${apiUrl}/pay/adjust/batchImportRefund`,
});
// 新增补扣上传参数
const uploadDeduction = reactive({
  title: "",
  isUploading: false,
  updateSupport: 0,
  url: `${import.meta.env.VITE_APP_BASE_API
    }/pay${apiUrl}/pay/adjust/batchImportDeduction`,
});
// 批量导入额外参数
const formRef = ref(null);
const formRefDeduction = ref(null); // 新增补扣表单引用
const extraUploadFileData = ref({
  payScene: '',
  payType: ''
});
const paySceneOptions = ref([]);
const payTypeOptions = ref([]);

// 表单验证规则
const rules = {
  payScene: [
    { required: true, message: '请选择支付场景', trigger: 'change' }
  ],
  payType: [
    { required: true, message: '请选择支付类型', trigger: 'change' }
  ]
};
// 添加轮询状态控制变量
const isPollingActive = ref(true);
// 字典
const { proxy } = getCurrentInstance();
const { adjust_type, adjust_status } = proxy.useDict("adjust_type", "adjust_status");

// 处理日期变化
const handleDateChange = (val) => {
  if (val) {
    queryParams.value.startDate = val[0];
    queryParams.value.endDate = val[1];
  } else {
    queryParams.value.startDate = "";
    queryParams.value.endDate = "";
  }
};

const tabelForm = reactive({
  tableKey: "1", //表格key值
  isShowRightToolbar: true, //是否显示右侧显示和隐藏
  showSearch: true,
  // 表格表格数据
  columns: [
    {
      fieldIndex: "loginName", // 对应列内容的字段名
      label: "登录账号", // 显示的标题
      visible: true, // 展示与隐藏
      minWidth: "120px", //最小宽度%
      width: "", //宽度
      align: "center", //表格对齐方式
    },
    {
      fieldIndex: "staffName", // 对应列内容的字段名
      label: "员工姓名", // 显示的标题
      visible: true, // 展示与隐藏
      minWidth: "120px", //最小宽度%
      width: "", //宽度
       type:'clickText'
    },
    {
      fieldIndex: "adjustType", // 对应列内容的字段名
      label: "调整类型", // 显示的标题
      visible: true, // 展示与隐藏
      minWidth: "120px", //最小宽度%
      align: "center", //表格对齐方式
      type: "dict",
      dictList: adjust_type,
    },
    {
      fieldIndex: "amount", // 对应列内容的字段名
      label: "金额(元)", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "120", //最小宽度%
      width: "", //宽度
      type: "dollor",
    },

    {
      fieldIndex: "providerName", // 对应列内容的字段名
      label: "供应商", // 显示的标题
      visible: true, // 展示与隐藏
      minWidth: "140px", //最小宽度%
      width: "", //宽度
      align: "left", //表格对齐方式
    },
    {
      fieldIndex: "paySceneName",
      label: "支付场景",
      show: true,
      sortable: true,
      visible: true, // 展示与隐藏
      minWidth: "120px",
    },
    {
      fieldIndex: "payTypeName",
      label: "支付类型",
      show: true,
      sortable: true,
      visible: true, // 展示与隐藏
      minWidth: "120px",
    },
    {
      fieldIndex: "accountName",
      label: "账户名称",
      show: true,
      sortable: true,
      visible: true, // 展示与隐藏
      minWidth: "120px",
    },
    {
      fieldIndex: "status", // 对应列内容的字段名
      label: "状态", // 显示的标题
      visible: true, // 展示与隐藏
      minWidth: "120px", //最小宽度%
      align: "center", //表格对齐方式
      type: "dict",
      dictList: adjust_status,
    },
    {
      fieldIndex: "updateDate", // 对应列内容的字段名
      label: "操作时间", // 显示的标题
      visible: true, // 展示与隐藏
      minWidth: "150px", //最小宽度%
      align: "center", //表格对齐方式
    },
  ],
  // 表格基础配置项，看个人需求添加
  tableConfig: {
    needPage: true, // 是否需要分页
    index: true, // 是否需要序号
    selection: false, // 是否需要多选
    reserveSelection: false, // 是否需要支持跨页选择
    indexFixed: false, // 序号列固定 left true right
    selectionFixed: false, //多选列固定left true right
    indexWidth: "50", //序号列宽度
    loading: false, //表格loading
    showSummary: false, //是否开启合计
    height: null, //表格固定高度 比如：300px
  },
});

/** 查询用户列表 */
const getList = () => {
  tabelForm.tableConfig.loading = true;
  screenIndex.getAdjustList(queryParams.value).then((response) => {
    userList.value = response.data.records;
    total.value = response.data.total;
    tabelForm.tableConfig.loading = false;
  });
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};
/** 重置按钮操作 */
const resetQuery = () => {
  queryParams.value = {};
  proxy.resetForm("queryForm");
  handleQuery();
};

/** 提交保存 */
const save = () => {
  if (diaWindow.popupType == "batchRefund") {
    formRef.value.validate((valid) => {
      if (valid) {
        uploadRef.value.submitFileForm();
      } else {
        return false;
      }
    });
  }

  if (diaWindow.popupType == "batchSupplementary") {
    formRefDeduction.value.validate((valid) => {
      if (valid) {
        uploadRefDeduction.value.submitFileForm();
      } else {
        return false;
      }
    });
  }

  if (diaWindow.popupType == "uploadSuccessdetailList") {
    uploadDetailRef.value.saveBtn(redisLock.value);
  }

  if (diaWindow.popupType == "addRefund") {
    addRef.value.saveForm();
  }


  if (diaWindow.popupType == "addSupplementary") {
    addRef.value.saveForm();
  }
};

/** 点击取消保存并刷新 */
const cancellationRefresh = (val) => {
  // 停止轮询
  isPollingActive.value = false;
  close(false);
  getList();
};

/** 点击取消保存 */
const cancellation = (val) => {
  // 取消操作时停止轮询
  isPollingActive.value = false;
  close(false);
};

/** 关闭弹窗后刷新 */
const cancellationRefsh = () => {
  // 停止轮询
  isPollingActive.value = false;
  close(false);
  getList();
};

/** 关闭弹窗 */
const close = (val) => {
  diaWindow.open1 = val;
  // 如果关闭弹窗，停止轮询
  if (!val) {
    isPollingActive.value = false;
  }
};

const handleDelete = (row, callback) => {
  ElMessageBox.confirm("确认删除吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      delDeviceById(row.id).then((res) => {
        if (res.code == "1") {
          ElMessage.success("删除成功");
          getList();
          callback(true);
        }
      });
    })
    .catch(() => { });
};
// 批量导入退款 文件上传成功处理
const handleFileSuccessRefund = (response) => {
  upload.isUploading = false;
  if (response.response.code == "1") {
    setTimeout(() => {
      upload.isUploading = false;
      // 确保在开始新的导入时，轮询状态是激活的
      isPollingActive.value = true;
      getImportResultRefund(response.response.data);
    }, 800);
  } else {
    ElMessage.error(response.response.message);
  }
};

// 批量导入退款 导入校验 getImportResultRefund
const getImportResultRefund = async (data) => {
  // 如果轮询已停止，则不继续请求
  if (!isPollingActive.value) return;

  const res = await screenIndex.getImportResult({ redisLock: data });
  redisLock.value = data;
  if (res.code === "1") {
    // 0:导入执行中、1:导入失败、2:导入成功
    switch (res.data) {
      case "0":
        // 导入中，轮询检查状态，但仅在轮询激活时继续
        setTimeout(() => {
          if (isPollingActive.value) {
            getImportResultRefund(data);
          }
        }, 2000);
        break;
      case "1":
        ElMessage.error(`导入失败`);
        // 清空导入文件和状态
        setTimeout(() => {
          upload.isUploading = false;
          uploadRef.value.clearFiles();
          uploadFaildetailListFun2();
          getList(); // 刷新列表
        }, 1000);
        break;
      case "2":
        // 导入成功，获取成功数据
        const successData = await screenIndex.importDataList({
          redisLock: data,
        });
        ElMessage.success(`导入成功，成功${successData.data.total}条`);
        // 清空导入文件和状态
        setTimeout(() => {
          // 存储下data
          redisLock.value = data;
          upload.isUploading = false;
          uploadRef.value.clearFiles();
          uploadSuccessdetailListFun2();
          getList(); // 刷新列表
        }, 1000);

        break;
    }
  }
  return res;
};
const uploadFaildetailListFun2 = () => {
  diaWindow.headerTitle = "批量导入退款失败列表";
  diaWindow.popupType = "uploadSuccessdetailList";
  diaWindow.uoloadStatus = "failed";
  diaWindow.dialogFooterBtn = false;
  diaWindow.dialogWidth = "75%";
  diaWindow.dialogTop = '15vh'
  diaWindow.adjustType = 'refund'; // 添加类型标识：退款
  diaWindow.open1 = true;
};
const uploadSuccessdetailListFun2 = () => {
  diaWindow.headerTitle = "批量导入退款成功列表";
  diaWindow.popupType = "uploadSuccessdetailList";
  diaWindow.dialogFooterBtn = true;
  diaWindow.uoloadStatus = "successed";
  diaWindow.SaveSubmitText = '确认'
  diaWindow.dialogWidth = "75%";
  diaWindow.dialogTop = '15vh'
  diaWindow.adjustType = 'refund'; // 添加类型标识：退款
  diaWindow.open1 = true;
};

// 人员信息
const clickTextDetail = (row) => {
  diaWindow.headerTitle = "人员信息";
  diaWindow.popupType = "faceInfo";
  diaWindow.rowData = row; // 如果需要传递数据到弹窗
  diaWindow.dialogFooterBtn = false;
  diaWindow.dialogWidth = "530px";
  diaWindow.dialogTop = '18vh'
  
  diaWindow.open1 = true;
};
//   批量导入退款文件上传失败处理
const handleFileErrorRefund = () => {
  upload.isUploading = false;
  ElMessage.error("导入失败");
};

//  批量导入退款
const handleFileListUpdateRefund = (files) => {
  fileListRefund.value = files; // 存储文件列表
};

// 批量导入退款模版
const importTemplateRefund = () => {
  proxy.download(
    `pay${apiUrl}/pay/adjust/refundTemplate`,
    {},
    `批量导入退款模版_${formatMinuteTime(new Date())}.xlsx`
  );
};
// 批量导入补扣 文件上传成功处理
const handleFileSuccessDeduction = (response) => {
  uploadDeduction.isUploading = false;
  if (response.response.code == "1") {
    setTimeout(() => {
      uploadDeduction.isUploading = false;
      // 确保在开始新的导入时，轮询状态是激活的
      isPollingActive.value = true;
      getImportResultDeduction(response.response.data);
    }, 800);
  } else {
    ElMessage.error(response.response.message);
  }
};

// 批量导入补扣 导入校验 getImportResultDeduction
const getImportResultDeduction = async (data) => {
  // 如果轮询已停止，则不继续请求
  if (!isPollingActive.value) return;

  const res = await screenIndex.getImportResult({ redisLock: data });
  redisLock.value = data;
  if (res.code === "1") {
    // 0:导入执行中、1:导入失败、2:导入成功
    switch (res.data) {
      case "0":
        // 导入中，轮询检查状态，但仅在轮询激活时继续
        setTimeout(() => {
          if (isPollingActive.value) {
            getImportResultDeduction(data);
          }
        }, 2000);
        break;
      case "1":
        ElMessage.error(`导入失败`);
        // 清空导入文件和状态
        setTimeout(() => {
          uploadDeduction.isUploading = false;
          uploadRefDeduction.value.clearFiles();
          uploadFaildetailListDeduction();
          getList(); // 刷新列表
        }, 1000);
        break;
      case "2":
        // 导入成功，获取成功数据
        const successData = await screenIndex.importDataList({
          redisLock: data,
        });
        ElMessage.success(`导入成功，成功${successData.data.total}条`);
        // 清空导入文件和状态
        setTimeout(() => {
          // 存储下data
          redisLock.value = data;
          uploadDeduction.isUploading = false;
          uploadRefDeduction.value.clearFiles();
          uploadSuccessdetailListDeduction();
          getList(); // 刷新列表
        }, 1000);

        break;
    }
  }
  return res;
};

// 补扣失败列表
const uploadFaildetailListDeduction = () => {
  diaWindow.headerTitle = "批量导入补扣失败列表";
  diaWindow.popupType = "uploadSuccessdetailList";
  diaWindow.uoloadStatus = "failed";
  diaWindow.dialogFooterBtn = false;
  diaWindow.dialogWidth = "75%";
  diaWindow.dialogTop = '15vh'
  diaWindow.adjustType = 'supplementary'; // 添加类型标识：补扣
  diaWindow.open1 = true;
};

// 补扣成功列表
const uploadSuccessdetailListDeduction = () => {
  diaWindow.headerTitle = "批量导入补扣成功列表";
  diaWindow.popupType = "uploadSuccessdetailList";
  diaWindow.dialogFooterBtn = true;
  diaWindow.uoloadStatus = "successed";
  diaWindow.SaveSubmitText = '确认'
  diaWindow.dialogWidth = "75%";
  diaWindow.dialogTop = '15vh'
  diaWindow.adjustType = 'supplementary'; // 添加类型标识：补扣
  diaWindow.open1 = true;
};

//   批量导入补扣文件上传失败处理
const handleFileErrorDeduction = () => {
  uploadDeduction.isUploading = false;
  ElMessage.error("导入失败");
};

//  批量导入补扣
const handleFileListUpdateDeduction = (files) => {
  fileListDeduction.value = files; // 存储文件列表到补扣文件列表
};

// 批量导入补扣模版
const importTemplateDeduction = () => {
  proxy.download(
    `pay${apiUrl}/pay/adjust/deductionTemplate`,
    {},
    `批量导入补扣模版_${formatMinuteTime(new Date())}.xlsx`
  );
};
// 获取支付场景树
const getPaySceneTreeData = () => {
  screenIndex.getPaySceneTree({}).then(res => {
    if (res.code === "1" && res.data) {
      paySceneOptions.value = res.data;
      // 如果只有一个选项，自动选中
      if (paySceneOptions.value.length === 1) {
        extraUploadFileData.value.payScene = paySceneOptions.value[0].value;
        // 自动加载支付类型
        getPayTypeTreeData(paySceneOptions.value[0].id);
      }
    }
  });
};

// 获取支付类型树
const getPayTypeTreeData = (payScene) => {
  if (!payScene) {
    payTypeOptions.value = [];
    return;
  }
  const params = { id: payScene };
  screenIndex.getPayTypeTree(params).then(res => {
    if (res.code === "1" && res.data) {
      payTypeOptions.value = res.data;
      // 如果只有一个选项，自动选中
      if (payTypeOptions.value.length === 1) {
        extraUploadFileData.value.payType = payTypeOptions.value[0].value;
      }
    } else {
      payTypeOptions.value = [];
    }
  });
};

  // 监听支付场景变化，更新支付类型选项
  watch(
    () => extraUploadFileData.value.payScene,
    (newVal) => {
      extraUploadFileData.value.payType = ''; // 清空支付类型
      // 找到选中的支付场景对象，以获取正确的id值
      if (newVal) {
        const selectedScene = paySceneOptions.value.find(item => item.value === newVal);
        const sceneId = selectedScene ? selectedScene.id || selectedScene.value : newVal;
        getPayTypeTreeData(sceneId);
      } else {
        getPayTypeTreeData('');
      }
    }
  );

// 批量导入退款
const handleBatchRefund = () => {
  diaWindow.headerTitle = "批量导入退款";
  diaWindow.popupType = "batchRefund";
  diaWindow.rowData = {}; // 如果需要传递数据到弹窗
  diaWindow.dialogFooterBtn = true;
  diaWindow.dialogWidth = "30%";
  diaWindow.SaveSubmitText = '保存'
  diaWindow.dialogTop = '12vh'
  // 开启新的批量导入前，重置轮询状态
  isPollingActive.value = true;
  // 获取支付场景数据
  getPaySceneTreeData();
  // 初始化清空支付类型数据
  payTypeOptions.value = [];
  extraUploadFileData.value = {
    payScene: '',
    payType: ''
  };
  diaWindow.open1 = true;
};

// 批量导入 补扣

const handleBatchSupplementary = () => {
  diaWindow.headerTitle = "批量导入补扣";
  diaWindow.popupType = "batchSupplementary";
  diaWindow.rowData = {}; // 如果需要传递数据到弹窗
  diaWindow.dialogFooterBtn = true;
  diaWindow.dialogWidth = "30%";
  diaWindow.SaveSubmitText = '保存'
  diaWindow.dialogTop = '12vh'
  // 开启新的批量导入前，重置轮询状态
  isPollingActive.value = true;
  // 获取支付场景数据
  getPaySceneTreeData();
  // 初始化清空支付类型数据
  payTypeOptions.value = [];
  extraUploadFileData.value = {
    payScene: '',
    payType: ''
  };
  diaWindow.open1 = true;
};

const handleRefund = () => {
  diaWindow.dialogWidth = "70%";
  diaWindow.headerTitle = "新增退款";
  diaWindow.rowData = {};
  diaWindow.SaveSubmitText = '保存'
  diaWindow.dialogFooterBtn = true;
  diaWindow.popupType = "addRefund";
  diaWindow.dialogTop = '12vh'
  diaWindow.open1 = true;
};

const handleSupplementary = () => {
  diaWindow.dialogWidth = "70%";
  diaWindow.headerTitle = "新增补扣";
  diaWindow.rowData = {};
  diaWindow.SaveSubmitText = '保存'
  diaWindow.dialogFooterBtn = true;
  diaWindow.popupType = "addSupplementary";
  diaWindow.dialogTop = '12vh'
  diaWindow.open1 = true;
};



const handleExport = () => {
  proxy.download(
    'wisdompay' + apiUrl + '/pay/adjust/export',
    {
      ...queryParams.value
    },
    `异常调整表_${formatMinuteTime(new Date())}.xlsx`
  );
};


onMounted(() => {
  getList();
});

// 组件卸载时停止所有轮询
onUnmounted(() => {
  isPollingActive.value = false;
});
</script>

<style lang="scss" scoped>
.el-card {
  min-height: calc(100vh - 110px);
}

.icon-btn {
  font-size: 16px;
  margin-right: 5px;
}

.a-link {
  color: #c20000;
  align-items: center;
  cursor: pointer;

  :deep(.el-icon) {
    margin-right: 8px;
  }
}
</style>
