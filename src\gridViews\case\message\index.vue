<!--消息提醒-->
<template>
  <el-row>
    <el-col :span="24">
      <div class="padding-box-4">
        <div class="big-common-box-one"
        >
          <div class="common-header">
            <div class="left-header-text">消息提醒</div>
            <div class="flex-1"/>
            <div class="header-right-btn">
              更多
            </div>
          </div>
          <div class="big-common-content">
            <div class="news-content">
              <el-scrollbar style="height: 100%">

                <el-empty v-if="tableData.length == 0" :image-size="100"/>
                <div v-else-if="tableData.length > 0" class="messages-lists">
                  <el-scrollbar style="height: 100%">
                    <div style="padding-right: 12px">
                      <div
                          v-for="(item, index) of tableData"
                          :key="index"
                          class="messages-lists-one"
                      >
                        <div v-if="item.isRead" class="right-status-icon">
                          <img src="@/assets/images/home/<USER>" />
                          <div class="right-status-icon-text">
                            {{ "已读" }}
                          </div>
                        </div>

                        <div v-if="!item.isRead" class="right-status-icon">
                          <img src="@/assets/images/home/<USER>" />
                          <div class="right-status-icon-text">
                            {{ "未读" }}
                          </div>
                        </div>
                        <div class="tixingIcons">
                          <svg-icon icon-class="tixingtodo" />
                        </div>

                        <div class="left-infos">
                          <div class="messages-top" :title="item.messageContent">
                            {{ item.messageContent }}
                          </div>

                          <div class="messages-bottom" :title="item.createTime">
                            <span>{{ item.sendUserName }}</span>
                            <span class="messages-line">|</span>
                            <span>{{ item.createTime }}</span>
                            <span class="messages-line">|</span>

                            <span>{{  item.messageType  }}</span>
                          </div>
                        </div>

                        <div
                            class="right-icon-btn"
                            @click.stop="
                          deleteOwnMessageRecord(item.id, item.messageTitle)
                        "
                        >
                          <span class="el-icon-delete" />
                        </div>
                      </div>
                    </div>
                  </el-scrollbar>
                </div>
              </el-scrollbar>
            </div>
          </div>
        </div>
      </div>
    </el-col>
  </el-row>
</template>


<script setup>
import {ref} from "vue";

const tableData = ref([]);
const params = reactive({
  pageNum: 1,
  pageSize: 10,
  noticeTitle: undefined,
  createBy: undefined,
  status: undefined,
});

const tableDatas = async () => {
  try {
    const response = {
      "total": 1473,
      "rows": [
        {
          "id": "aaf7abb02fea996c304b866be27a9fe6",
          "messageTitle": "积分消费交易提醒",
          "messageContent": "消费成功！交易金额：19.90元，交易商家：线上超市:设备支付",
          "sendUserName": "邢东玉",
          "sendDeptName": "审计",
          "receiveUserName": "邢东玉",
          "receiveDeptName": "审计",
          "messageType": "6",
          "messageItem": null,
          "receiveUserId": 14756,
          "isRead": 1,
          "createTime": "2024-04-29 15:45:11",
          "pageNum": null,
          "pageSize": null
        },
        {
          "id": "6fef5c10cc9350f748bfd9ed54483bbb",
          "messageTitle": "报修申请完成提醒",
          "messageContent": "【联通(山东)产业互联网有限公司管理员20240202关于办公大楼3楼水的报修申请】报修申请已完成！",
          "sendUserName": "李庆尧",
          "sendDeptName": "审计",
          "receiveUserName": "李庆尧",
          "receiveDeptName": "审计",
          "messageType": "1",
          "messageItem": null,
          "receiveUserId": 14748,
          "isRead": 1,
          "createTime": "2024-02-02 15:36:32",
          "pageNum": null,
          "pageSize": null
        },
        {
          "id": "df5a47ac3d1b0eb0938cb48661b35cde",
          "messageTitle": "报修申请完成提醒",
          "messageContent": "【审计李佳峰20240131关于办公大楼3楼办公的报修申请】报修申请已完成！",
          "sendUserName": "孙建忠",
          "sendDeptName": "审计",
          "receiveUserName": "孙建忠",
          "receiveDeptName": "审计",
          "messageType": "1",
          "messageItem": null,
          "receiveUserId": 14435,
          "isRead": 1,
          "createTime": "2024-01-31 16:48:56",
          "pageNum": null,
          "pageSize": null
        },
        {
          "id": "8b77b8ecc128bbaa63e60408e35d29f1",
          "messageTitle": "报修申请完成提醒",
          "messageContent": "【审计李佳峰20240131关于办公大楼3楼木工的报修申请】报修申请已完成！",
          "sendUserName": "孙建忠",
          "sendDeptName": "审计",
          "receiveUserName": "孙建忠",
          "receiveDeptName": "审计",
          "messageType": "1",
          "messageItem": null,
          "receiveUserId": 14435,
          "isRead": 1,
          "createTime": "2024-01-31 16:37:34",
          "pageNum": null,
          "pageSize": null
        },
        {
          "id": "aaf7abb02fea996c304b866be27a9fe6",
          "messageTitle": "积分消费交易提醒",
          "messageContent": "消费成功！交易金额：19.90元，交易商家：线上超市:设备支付",
          "sendUserName": "邢东玉",
          "sendDeptName": "审计",
          "receiveUserName": "邢东玉",
          "receiveDeptName": "审计",
          "messageType": "6",
          "messageItem": null,
          "receiveUserId": 14756,
          "isRead": 1,
          "createTime": "2024-04-29 15:45:11",
          "pageNum": null,
          "pageSize": null
        },
        {
          "id": "6fef5c10cc9350f748bfd9ed54483bbb",
          "messageTitle": "报修申请完成提醒",
          "messageContent": "【联通(山东)产业互联网有限公司管理员20240202关于办公大楼3楼水的报修申请】报修申请已完成！",
          "sendUserName": "李庆尧",
          "sendDeptName": "审计",
          "receiveUserName": "李庆尧",
          "receiveDeptName": "审计",
          "messageType": "1",
          "messageItem": null,
          "receiveUserId": 14748,
          "isRead": 1,
          "createTime": "2024-02-02 15:36:32",
          "pageNum": null,
          "pageSize": null
        },
        {
          "id": "df5a47ac3d1b0eb0938cb48661b35cde",
          "messageTitle": "报修申请完成提醒",
          "messageContent": "【审计李佳峰20240131关于办公大楼3楼办公的报修申请】报修申请已完成！",
          "sendUserName": "孙建忠",
          "sendDeptName": "审计",
          "receiveUserName": "孙建忠",
          "receiveDeptName": "审计",
          "messageType": "1",
          "messageItem": null,
          "receiveUserId": 14435,
          "isRead": 1,
          "createTime": "2024-01-31 16:48:56",
          "pageNum": null,
          "pageSize": null
        }
      ],
      "code": 200,
      "msg": "查询成功"
    }
    tableData.value = response.rows;
  } catch (error) {
    console.error(error);
  }
};


// 组件挂载时加载数据
onMounted(() => {
  tableDatas();
});
</script>

<style scoped lang="scss">
.big-common-box-one {
  background: #ffffff;
  border-radius: 4px;
  width: 100%;
  padding:0 10px 10px;
  height: 574px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); // 添加阴影效果
  transition: box-shadow 0.3s ease;
  box-sizing: border-box;

  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2); // 悬停时加深阴影
  }
}

.common-header {
  height: 45px;
  line-height: 45px;
  border-bottom: 1px solid #e8eaeb;
  box-sizing: border-box;
  padding-bottom: 0px;

  .left-header-text {
    font-size: 15px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #333333;
  }

  .header-right-btn {
    font-size: 13px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #9ea4aa;
    cursor: pointer;
  }
}

.common-content {
  height: calc(100% - 45px);
}

.messages-lists {
  height: 100%;

  .messages-lists-one {
    position: relative;
    height: 63px;
    border-radius: 4px;
    border: 1px solid #e4e4e4;
    box-sizing: border-box;
    padding: 5px 14px;
    display: flex;
    align-items: center;
    margin-top: 10px;
    cursor: pointer;
    .tixingIcons {
      width: 32px;
      height: 32px;
      background: rgba(194, 0, 0, 0.09);
      border-radius: 2px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      margin-right: 10px;
      .svg-icon {
        color: #c20000;
        font-size: 18px;
      }
    }

    .left-infos {
      width: 80%;
      margin-right: 8px;

      .messages-top {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #333333;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-bottom: 5px;
        padding-right: 5px;
      }

      .messages-bottom {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: 13px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #9d9fa1;

        .messages-line {
          padding: 0px 5px;
        }

        .messages-text-one {
          display: flex;
          align-items: center;

          .messages-text {
            margin-right: 5px;
          }
        }
      }
    }

    .right-status-icon {
      // background: #6ec11b;
      position: absolute;
      right: 0px;
      top: 0px;

      img {
        width: 42px;
      }

      .right-status-icon-text {
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        position: absolute;
        left: 50%;
        top: 40%;
        transform: translate(-50%, -50%);
        white-space: nowrap;
      }
    }

    .right-icon-btn {
      cursor: pointer;
      position: relative;
      top: 5px;
    }
  }
}

.news-top-infos-text {
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #333333;
  padding-right: 6px;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 0;
  display: block;
  width: 50px;
}
.new-times {
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #9d9fa1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
