<template>
  <el-card :class="bgColor === true ? 'hotSearch-card' : 'unshow-hotSearch-card'" v-loading="loading" shadow="never">
    <template #header v-if="showTitle">
      <span class="card-title">
        <span> <el-icon class="icon iconfont"></el-icon>热搜 </span>
      </span>
    </template>
    <div class="hot home-card-body-news">
      <div v-for="(item, index) in newList" :key="index">
        <a :href="'https://www.toutiao.com/amos_land_page/?category_name=topic_innerflow&event_type=hot_board&topic_id=' +item.id"
           target=" _blank"
           style="display: flex; margin-bottom: 10px"
        >
          <div
              :class="
              index == 0
                ? 'eIndex'
                : '' || index == 1
                ? 'e2Index'
                : '' || index == 2
                ? 'e3Index'
                : ''
            "
              style="margin-right: 20px"
          >
            {{ index + 1 }}
          </div>
          <div>{{ item.word }}</div>
          <div class="hotFont" v-if="item.words_type">热</div>
        </a>
      </div>
    </div>
  </el-card>
</template>

<script setup name="UseHotSearch">
import {useRouter} from "vue-router";
import {getDate} from "@/api/grid/useHotSearch";

const {proxy} = getCurrentInstance()
const router = useRouter()
const props = defineProps({
  showTitle: Boolean,
  bgColor: Boolean
})

const loading = ref(false)
const list = ref([])
const newList = ref([])

function getDates() {
  getDate().then((res) => {
    list.value = res.data.data[0].words;
    newList.value = list.value.slice(0, 10);
  });
}

getDates();
</script>

<style scoped lang="scss">
.hotSearch-card {
  overflow:auto;
  height: 100%;
  background: #fff;
}
.unshow-hotSearch-card {
  height: 100%;
  background: #fff;
  border: 0px;
}
.eIndex {
  color: red;
}
.e2Index {
  color: blue;
}
.e3Index {
  color: burlywood;
}
.home-card-body {
  margin-top: -20px;
}
.hotFont {
  width: 20px;
  height: 20px;
  background: red;
  margin: 2px 0 0 5px;
  text-align: center;
  line-height: 20px;
  color: white;
  border-radius: 5px;
  font-size: 14px;
}

</style>
