<template>
  <div class="container-table1">
    <el-card>
      <dialog-search
        @getList="getList"
        formRowNumber="4"
        :columns="tabelForm.columns"
         :isShowRightBtn="$checkPermi(['parking:parkChargingRules:list'])"
      >
        <template v-slot:formList>
          <el-form
            :model="queryParams"
            ref="queryForm"
            :inline="true"
            label-width="88px"
          >
            <el-form-item label="规则名称">
              <el-input
                v-model="queryParams.ruleName"
                placeholder="请输入规则名称"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item label="停车场名称">
              <el-input
                v-model="queryParams.parkingName"
                placeholder="请输入停车场名称"
                clearable
              ></el-input>
            </el-form-item>
          </el-form>
        </template>
        <template v-slot:searchList>
          <el-button type="primary" icon="Search" @click="handleQuery"  v-hasPermi="['parking:parkChargingRules:list']"
            >搜索
          </el-button>
          <el-button icon="Refresh" @click="resetQuery"  v-hasPermi="['parking:parkChargingRules:list']">重置</el-button>
        </template>

        <template v-slot:searchBtnList>
          <el-button type="primary" icon="Plus" @click="handleAdd" v-hasPermi="['parking:parkChargingRules:add']"
            >新增
          </el-button>
        </template>
      </dialog-search>

      <public-table
        ref="publictable"
        :rowKey="tabelForm.tableKey"
        :tableData="list"
        :columns="tabelForm.columns"
        :configFlag="tabelForm.tableConfig"
        :pageValue="pageParams"
        :total="total"
        :getList="getList"
      >
        <template #operation_0="{ scope }">
          <el-radio-group
            v-model="scope.row.chargeStatus"
            @change="handleStatusChange(scope.row)"
          >
            <el-radio-button
              :label="item.value"
              v-for="(item, index) in park_charge_status"
              :key="index"
              >{{ item.label }}</el-radio-button
            >
          </el-radio-group>
        </template>

        <template #operation="{ scope }">
          <el-button
           v-hasPermi="['parking:parkChargingRules:list']"
            size="mini"
            type="text"
            title="查看"
            icon="View"
            @click="handleView(scope.row)"
          >
          </el-button>
          <el-button
           v-hasPermi="['parking:parkChargingRules:edit']"
            size="mini"
            type="text"
            title="修改"
            icon="Edit"
            @click="handleUpate(scope.row)"
          >
          </el-button>

          <el-button
             v-hasPermi="['parking:parkChargingRules:remove']"
            size="mini"
            type="text"
            title="删除"
            icon="Delete"
            @click="handleDelete(scope.row)"
          >
          </el-button>
        </template>
      </public-table>
    </el-card>
    <DialogBox
      :visible="open1"
      @save="save"
      :dialogWidth="dialogWidth"
      :dialogFooterBtn="dialogFooterBtn"
      @cancellation="cancellation"
      @close="close"
      CloseSubmitText="取消"
      SaveSubmitText="确定"
      :dialogTitle="dialogTitle"
    >
      <template #content>
        <edit
          ref="editRef"
          :popupType="popupType"
          :rowData="rowData"
          @closeBtn="cancellationRefresh"
        ></edit>
      </template>
    </DialogBox>
  </div>
</template>

<script setup name="IncomeRules">
import edit from "./components/add.vue";
import { getCurrentInstance, reactive, ref } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { screenIndex } from "@/api/majorParking/Income-rules";

const publictable = ref(null);
const queryParams = ref({
  rulesType:'1'
});
// 表格数据
const list = ref([{}]);
// 表格分页参数
const pageParams = ref({
  pageNum: 1,
  pageSize: 10,
});

// 表格总数
const total = ref(0);
// 是否显示弹窗

// 停车场列表
const parkingList = ref([]);

const open1 = ref(false);
const dialogTitle = ref("");
const popupType = ref("");
const editRef = ref(null);
const rowData = ref({});
const dialogWidth = ref();
const dialogFooterBtn = ref(false);
// 字典
const { proxy } = getCurrentInstance();
const { park_charge_people_car_charge, car_type, park_charge_status } =
  proxy.useDict(
    "park_charge_people_car_charge",
    "car_type",
    "park_charge_status"
  );
// 表格配置
const tabelForm = reactive({
  tableKey: "1", //表格key值
  isShowRightToolbar: true, //是否显示右侧显示和隐藏
  showSearch: true,
  // 表格表格数据
  columns: [
    {
      fieldIndex: "ruleName", // 对应列内容的字段名
      label: "规则名称", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      minWidth: "120px", //最小宽度%
      width: "", //宽度
      align: "left",
    },
    {
      fieldIndex: "parkingName", // 对应列内容的字段名
      label: "停车场名称", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      minWidth: "120px", //最小宽度%
      width: "", //宽度
      align: "left",
    },
    {
      fieldIndex: "peopleOrCarCharge", // 对应列内容的字段名
      label: "收费模式", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      minWidth: "100px", //最小宽度%
      width: "", //宽度
      type: "dict",
      dictList: park_charge_people_car_charge,
    },
    {
      fieldIndex: "freeDuration", // 对应列内容的字段名
      label: "免费时长", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      minWidth: "120px", //最小宽度%
      width: "110px", //宽度
      align: "",
    },
    {
      fieldIndex: "isHolidaysCharge", // 对应列内容的字段名
      label: "公休日是否收费", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      minWidth: "110px", //最小宽度%
      width: "", //宽度
      type: "dict",
      dictList: [
        {
          label: "否",
          value: "1",
        },
        {
          label: "是",
          value: "0",
        },
      ],
    },

    {
      fieldIndex: "carTypeSplit", // 对应列内容的字段名
      label: "车辆类型", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      minWidth: "180px", //最小宽度%
      width: "", //宽度
      type: "dicts",
      dictList: car_type,
      align: "left",
    },
{
    fieldIndex: "chargeStatus", // 对应列内容的字段名
      label: "是否启用",
      width: "110",
      type: "dict",
      dictList: park_charge_status,
      visible: true,
    },
    {
      label: "操作",
      slotname: "operation",
      width: "150",
      fixed: "right", //固定
      visible: true,
    },
  ],
  // 表格基础配置项，看个人需求添加
  tableConfig: {
    needPage: true, // 是否需要分页
    index: true, // 是否需要序号
    selection: false, // 是否需要多选
    reserveSelection: false, // 是否需要支持跨页选择
    indexFixed: false, // 序号列固定 left true right
    selectionFixed: false, //多选列固定left true right
    indexWidth: "50", //序号列宽度
    loading: false, //表格loading
    showSummary: false, //是否开启合计
    height: null, //表格固定高度 比如：300px
  },
});

// 获取停车场数据
const selectPark = async () => {
  try {
    const res = await screenIndex.findparkinglot({}, { status: "0" });
    parkingList.value = res.data.records || [];
  } catch (error) {
    console.error("获取停车场列表失败:", error);
  }
};
/** 查询规则列表 */
const getList = () => {
  tabelForm.tableConfig.loading = true;
  screenIndex
    .findParkingRule(queryParams.value, pageParams.value)
    .then((response) => {
      list.value = response.data.records;
      total.value = response.data.total;
      tabelForm.tableConfig.loading = false;
    });
};

/** 搜索按钮操作 */
const handleQuery = () => {
  pageParams.value.pageNum = 1;
  getList();
};
/** 重置按钮操作 */
const resetQuery = () => {
  queryParams.value = {
    rulesType:'1'
  };
  proxy.resetForm("queryForm");
  handleQuery();
};

/** 提交保存 */
const save = (val) => {
  editRef.value.saveForm();
};

/** 新增 */
const handleAdd = () => {
  dialogTitle.value = "新增停车场收费规则";
  popupType.value = "add";
  dialogFooterBtn.value = true;
  rowData.value = {};
  dialogWidth.value = "60%";
  open1.value = true;
};

/** 修改 */
const handleUpate = (row) => {
  dialogTitle.value = "修改停车场收费规则";
  popupType.value = "edit";
  dialogFooterBtn.value = true;
  rowData.value = row;
  dialogWidth.value = "60%";
  open1.value = true;
};

/** 查看 */
const handleView = (row) => {
  dialogTitle.value = "查看停车场收费规则";
  popupType.value = "view";
  dialogWidth.value = "60%";
  dialogFooterBtn.value = false;
  rowData.value = row;
  open1.value = true;
};
// 状态变更处理
const handleStatusChange = async (row) => {
  let text  = ''
  if(row.chargeStatus == '1'){
      text = `确定要禁用规则名称【 ${row.ruleName} 】的状态吗？`
  }else{
       text = `确定要启用规则名称【 ${row.ruleName} 】的状态吗？`
  }
 
  try {
    // 添加确认对话框
    await ElMessageBox.confirm(
      text,
      "操作确认",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }
    );
    const res = await screenIndex.updateParkingRule(row);
    if (res.code == "1") {
      ElMessage.success("状态更新成功");
      emit("closeBtn");
    }
  } catch (error) {
    getList();
  }
};

// 删除规则
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm("确认要删除吗？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });
    const res = await screenIndex.deleteParkingRule(row);
    if (res.code == "1") {
      ElMessage.success("删除成功");
      await getList();
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    console.error("删除失败:", error);
  }
};
/** 点击取消保存 */
const cancellation = (val) => {
  close(false);
};

const cancellationRefresh = () => {
  close(false);
  getList();
};
/** 关闭弹窗 */
const close = (val) => {
  open1.value = val;
};

// 生命周期：组件挂载时调用
onMounted(() => {
  getList();
});
</script>

<style lang="scss" scoped>

</style>
