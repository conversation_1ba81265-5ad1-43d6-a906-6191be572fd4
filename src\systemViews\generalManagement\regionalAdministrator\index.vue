<template>
    <div class="app-container">
      <Splitpanes class="default-theme">
        <Pane :size="18" :min-size="10">
          <el-card class="dep-card" style="height: 100%">
            <div class="flex">
              <el-input
                v-model="searchValue"
                placeholder="请输入区域名称"
                clearable
                  @clear="initTreeselect"
              />
              <el-button
                type="primary"
                icon="Search"
                @click="handleTree"
                style="margin-left: 8px"
                >搜索</el-button
              >
            </div>
            <!-- 组织树 -->
            <treeLoad
              ref="myTree"
              :isShowSearch="false"
              :defaultProps="defaultProps"
              :treeData="treeData"
              :treeBtnEdit="false"
              @loadFirstNodeFather="loadRootNodes"
              @loadChildNodeFather="loadChildNodes"
              :treeBtnDelete="false"
             :tree-name="isLazy ? 'name' : 'label'"
              :defaultExpandedKeys="defaultExpandedKeys"
              :isLazy="isLazy"
              :treeindex="treeindex"
              @checkedKeys="handleNodeClick"
              @editNodes="editNodesEdit"
            />
          </el-card>
        </Pane>
        <Pane :size="82" :min-size="62">
          <el-card class="dep-card" shadow="never" v-loading="treeLoading">
            <div
              class="areaPath"
              style="font-weight: bold; padding-bottom: 5px; color: #606266"
            >
              <span>{{ areaPath }}</span>
            </div>
            <dialog-search
              @getList="getList"
             formRowNumber="3"
              :columns="tabelForm.columns"
            >
            <template #formList>
            <el-form ref="queryForm" :inline="true" :model="pageParams" label-width="75px">
                            <el-form-item label="人员姓名" prop="staffName">
                                <el-input v-model="pageParams.staffName" placeholder="请输入人员姓名" clearable 
                                    @keyup.enter.native="handleQuery" />
                            </el-form-item>
                            <el-form-item label="手机号码" prop="cellphone">
                                <el-input v-model="pageParams.cellphone" placeholder="请输入手机号码" clearable 
                                    @keyup.enter.native="handleQuery" />
                            </el-form-item>
                        </el-form>
                        </template>
                        <template v-slot:searchList>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </template>

            <template v-slot:searchBtnList>
              <el-button type="primary"  icon="Plus" @click="handleAdd">新增
              </el-button>

              <el-button  icon="Download"
                @click="handleExport">导出
              </el-button>
            </template>
            </dialog-search>
  
            <public-table
              ref="publictable"
              :rowKey="tabelForm.tableKey"
              :tableData="userData"
              :columns="tabelForm.columns"
              :configFlag="tabelForm.tableConfig"
              :pageValue="pageParams"
              :total="total"
              :getList="getList"
            >
            <template #cellphone="{ scope }">
                        <div>{{ scope.row.cellphone ? scope.row.cellphone.replace(/(\d{3})(\d{4})(\d{4})/,
                            "$1****$3") :
                            "-" }}</div>
                    </template>

              <template #operation="{ scope }">
                <div>
                
                  <el-button
                    link
                    icon="Delete"
                    type="primary"
                    @click="handleDelete(scope.row)"
                  >
                  </el-button>
                </div>
              </template>
            </public-table>
          </el-card>
        </Pane>
      </Splitpanes>
  
      <DialogBox
        :visible="diaWindow.open1"
        :dialogWidth="diaWindow.dialogWidth"
        :dialogFooterBtn="diaWindow.dialogFooterBtn"
        @save="save"
        @cancellation="cancellation"
        @close="close"
        :dialogTitle="diaWindow.headerTitle"
      >
        <template #content>
          <add ref="addRef" :popupType="diaWindow.popupType" :rowData="diaWindow.rowData" @dialogResult="cancellationRefresh"></add>
        </template>
      </DialogBox>
  
    </div>
  </template>
  
  <script setup name="RegionalAdministrator">
import {
  areaTreeSelect,
  getAllAreaTree,
  getAreaPath,
} from "@/api/area/area";
import {
  getAdminList,
  saveAreaAdmin,
  deleteAreaAdminById,
} from "@/api/regionalAdministrator/index";
import { ElMessageBox, ElMessage } from "element-plus";
import add from "./components/add";
import treeLoad from "@/components/Tree/treeLoad";
import { formatMinuteTime } from '@/utils';
import { ref, reactive, getCurrentInstance } from "vue";
import { apiUrl } from '@/utils/config'; 
const { proxy } = getCurrentInstance();
const { area_type, area_status } = proxy.useDict("area_type", "area_status");
const searchValue = ref("");
const treeData = ref([]);
const title = ref("新建区域信息");
const myTree = ref(null)
const userData = ref([{}]);
const publictable = ref(null);
const total = ref(0);

const addRef = ref(null);
const treeindex = ref(0);
const defaultExpandedKeys = ref([]);
const isLazy = ref(true);
// 定义事件发射器
const emit = defineEmits(["editNodes", "delNodes"]);
const pageParams = ref({
  pageNum: 1,
  pageSize: 10,
  staffName: "",
  cellphone:''
});
// 弹窗
const diaWindow = reactive({
  open1: false,
  headerTitle: "",
  popupType: "",
  rowData: "",
  dialogWidth: "50%",
  dialogFooterBtn: false,
});
// 区域名称
const areaPath = ref("");

// 树结构数据默认
const tabelForm = reactive({
  tableKey: "1", //表格key值
  isShowRightToolbar: true, //是否显示右侧显示和隐藏
  showSearch: true,
  // 表格数据
  columns: [
    {
      fieldIndex: "areaName", // 对应列内容的字段名
      label: "区域名称", // 显示的标题
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      minWidth: "120px", //最小宽度%
      width: "", //宽度
      align: "left",
    },
   
    {
      fieldIndex: "areaType", // 对应列内容的字段名
      label: "区域类型", // 显示的标题
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      minWidth: "120px", //最小宽度%
      width: "", //宽度
      align: "left",
      type: "dict",
      dictList: area_type,
    },
    {
      fieldIndex: "staffName", // 对应列内容的字段名
      label: "人员姓名", // 显示的标题
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      minWidth: "120px", //最小宽度%
      width: "", //宽度
    },
    {
      fieldIndex: "cellphone", // 对应列内容的字段名
      label: "手机号码", // 显示的标题
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      minWidth: "120px", //最小宽度%
      width: "", //宽度
      slotname:'cellphone'
    },
    {
      fieldIndex: "createDate", // 对应列内容的字段名
      label: "添加时间", // 显示的标题
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      minWidth: "120px", //最小宽度%
      width: "", //宽度
    },
    {
      label: "操作",
      slotname: "operation",
      width: "150",
      fixed: "right", //固定
      visible: true,
    },
  ],
  // 表格基础配置项，看个人需求添加
  tableConfig: {
    needPage: true, // 是否需要分页
    index: true, // 是否需要序号
    selection: false, // 是否需要多选
    reserveSelection: false, // 是否需要支持跨页选择
    indexFixed: false, // 序号列固定 left true right
    selectionFixed: false, //多选列固定left true right
    indexWidth: "50", //序号列宽度
    loading: false, //表格loading
    showSummary: false, //是否开启合计
    height: null, //表格固定高度 比如：300px
  },
});

/** 查询公告列表 */
function getList() {
  tabelForm.tableConfig.loading = true;
  getAdminList({ cellphone: pageParams.value.cellphone,staffName: pageParams.value.staffName,areaTableId:pageParams.value.id },{pageNum:pageParams.value.pageNum,pageSize:pageParams.value.pageSize} ).then((res) => {
    userData.value = res.data.records;
    console.log(userData.value, "userData.value---------------");
    total.value = res.data.total;
    tabelForm.tableConfig.loading = false;
  });
}

/** 搜索按钮操作 */
const handleQuery = () => {
  pageParams.value.pageNum = 1;
  getList();
};

/** 树结构数据默认 */
const defaultProps = reactive({
  children: "children",
  label: "name",
});

/** 树结构点击 */
const handleNodeClick = (data) => {
  pageParams.value.id = data.id;
  getList();
  updateAreaPath();
};

function handleDelete(row, callback) {
  ElMessageBox.confirm(
    "确认删除吗？",
    "提示",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }
  )
    .then(() => {
        deleteAreaAdminById(row.id).then((res) => {
        if (res.code == "1") {
          ElMessage.success("删除成功");
          getList();
          callback(true);
        }
      });
    })
    .catch(() => {});
}

// 懒加载根节点（当 isLazy=true 时）
const loadRootNodes = (resolve) => {
  // 这里模拟异步获取数据
  setTimeout(() => {
    areaTreeSelect({ areaName: searchValue.value }).then((response) => {
      resolve(response.data);
    });
  }, 500);
};

// 懒加载子节点
const loadChildNodes = (node, resolve) => {
  // 模拟异步请求
  setTimeout(() => {
    areaTreeSelect({ ...node.data, ...{ areaName: searchValue.value } }).then(
      (response) => {
        resolve(response.data);
      }
    );
  }, 500);
};

// 懒加载树搜索
const handleTree = () => {
  if (searchValue.value.length < 2) {
    ElMessage.warning("请至少输入两个字的查询条件！");
    return false;
  } else {
    treeindex.value++;
    isLazy.value = false;
    defaultExpandedKeys.value = [];
    getAllAreaTree({ areaName: searchValue.value }).then((res) => {
      treeData.value = res.data;
      expandTree(treeData.value);
    });
  }
};

const expandTree = (data) => {
  data.forEach((item) => {
    if (item.label.includes(searchValue.value)) {
      defaultExpandedKeys.value.push(item.id);
    }
    if (item.children) {
      expandTree(item.children);
    }
  });
};

const initTreeselect = () => {
  searchValue.value = "";
  treeindex.value = 0;
  treeData.value = [];
  isLazy.value = true;
};
const updateAreaPath = async () => {
  const res = await getAreaPath({ areaId: pageParams.value.id });
  areaPath.value = res.data?.map((item) => item.areaName).join(" > ") || "";
};

const handleAdd = () => {
  diaWindow.dialogWidth = "60%";
  diaWindow.headerTitle = "新增区域管理员";
  diaWindow.rowData = {};
  diaWindow.dialogFooterBtn = true;
  diaWindow.popupType = "add";
  diaWindow.open1 = true;
};

const handleUpdate = (row) => {
  diaWindow.dialogWidth = "30%";
  diaWindow.headerTitle = "修改区域管理员";
  diaWindow.rowData = row;
  diaWindow.dialogFooterBtn = true;
  diaWindow.popupType = "edit";
  diaWindow.open1 = true;
};
const editNodesEdit = (node, data) => {
  handleUpdate({ parentId: node.parent.data.id, id: data.id });
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy.download(
     'user' +apiUrl + '/areaAdmin/export',
    {
        cellphone: pageParams.value.cellphone,staffName: pageParams.value.staffName,areaTableId:pageParams.value.id 
    },
    `区域管理员_${formatMinuteTime(new Date())}.xlsx`
  );
};
/** 提交保存 */
const save = (val) => {
  addRef.value.saveBtn();
};

/** 点击取消保存并刷新 */
const cancellationRefresh = (val) => {
  close(false);
  getList();
  treeindex.value++;
};

/** 点击取消保存 */
const cancellation = (val) => {
  close(false);
};

/** 关闭弹窗 */
const close = (val) => {
  diaWindow.open1 = val;
};


  
  // 重置
  const resetQuery = () => {
    defaultExpandedKeys.value = []
    myTree.value.setCurrentKeyFun()
    pageParams.value.id = ''
    proxy.resetForm("queryForm");
    handleQuery()
  };
onMounted(() => {
  getList();
});
</script>
  
  <style scoped>
.dep-card {
  min-height: calc(100vh - 110px);
}


</style>
  