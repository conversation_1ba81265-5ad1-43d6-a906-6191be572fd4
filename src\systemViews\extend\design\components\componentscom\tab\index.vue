<template>
  <div
    class="tab"
    :style="{ backgroundImage: 'url(' + datas.config.bgImg + ')' }"
  >
    <uni-section title="选项卡" type="line">
      <div class="uni-padding-wrap">
        <uni-segmented-control :current="current" :values="datas.config.tabList" :style-type="datas.config.tabType"
                               :active-color="datas.config.color" @clickItem="onClickItem" />
      </div>
      <div class="content">
        <div><span class="content-text">选项卡{{ current + 1 }}的内容</span></div>
      </div>
    </uni-section>
    <!-- 选项卡 -->
    <!-- 删除组件 -->
    <slot name="deles" />
  </div>
</template>

<script>
import uniSection from "@/components/UniUI/uni-section"
import uniSegmentedControl from "@/components/UniUI/uni-segmented-control"
export default {
  name: 'tab',
  components: {
    uniSegmentedControl,
    uniSection,
  },
  props: {
    datas: Object,
  },
  created(){
    console.log(this.datas,'--------tab')
  },
  data() {
    return {
      current: 0,
    }
  },
  methods: {
    onClickItem(e) {
      if (this.current !== e.currentIndex) {
        this.current = e.currentIndex
      }
    },
  }
}
</script>

<style scoped lang="less">
.tab {
  position: relative;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  /* 默认导航 */
  .defaultNavigation {
    // overflow-x: scroll;
    justify-content: space-evenly;
    &::-webkit-scrollbar {
      height: 1px;
    }
    &::-webkit-scrollbar-thumb {
      background-color: #155bd4;
    }
    :deep(.el-collapse-item__header .el-collapse-item__wrap) {
      border-bottom: 0 !important;
    }
    /* 导航 */
    .navigationList {
      display: flex;
      flex-direction: column;
      align-items: center;
      img {
        margin-top: 5px;
        width: 45px;
        height: 45px;
      }
      p {
        font-size: 12px;
        margin-top: 5px;
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        text-align: center;
        box-sizing: border-box;
      }
    }
  }
}
.uni-padding-wrap {
  padding: 0 30px;
}
.content {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 70px;
  text-align: center;
}

.content-text {
  font-size: 14px;
  color: #666;
}
</style>
