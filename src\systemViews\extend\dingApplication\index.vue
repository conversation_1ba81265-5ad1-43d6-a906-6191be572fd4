<template>
  <div class="app-container">
    <el-card shadow="never">
      <!--    查询-->
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
        <el-form-item label="应用名称" prop="applicationName">
          <el-input v-model="queryParams.applicationName" placeholder="请输入应用名称" clearable style="width: 180px"
                    @keyup.enter="handleQuery"/>
        </el-form-item>


        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <!--  刷新按钮-->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="Plus" @click="handleAdd"
                     v-hasPermi="['sys:extend:tb-application:add']">新增
          </el-button>
        </el-col>
        <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>
      <!--  展示-->
      <el-table v-loading="loading" :data="dataList">
        <el-table-column prop="applicationId" align="left" label="应用主键" :show-overflow-tooltip="true" ></el-table-column>
        <el-table-column prop="applicationName" align="left"  label="应用名称" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column prop="cropId" align="left"  label="钉钉cropId" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column prop="agentId" align="left"  label="钉钉agentId" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column prop="appKey" align="left"  label="钉钉appKey" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column prop="appSecret" align="left"  label="钉钉appSecret" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column prop="appStatus" align="left"  label="是否启用" :show-overflow-tooltip="true">
          <template #default="scope">
            <el-switch
                v-model="scope.row.appStatus"
                active-value="1"
                inactive-value="2"
                @change="switchStatus(scope.row)"
            >
            </el-switch>

          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                       :loading=" reloadId === scope.row.id && reloadType === 'edit'"
                       v-hasPermi="['sys:extend:tb-application:edit']">修改
            </el-button>
            <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
                       :loading="reloadId === scope.row.id && reloadType === 'remove'"
                       v-hasPermi="['sys:extend:tb-application:remove']">删除
            </el-button>

          </template>
        </el-table-column>
      </el-table>

      <!--    分页器-->
      <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
      />
      <!-- 修改参数配置对话框 -->
      <el-dialog
          :title="title"
          v-model="open"
          width="1000px"
          append-to-body
          @close="cancel"
          :close-on-press-escape="false"
      >
        <el-form ref="formRef" :model="form" :rules="rules" label-width="150px">
          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="应用主键" prop="applicationId">
                <el-input
                    v-model="form.applicationId"
                    placeholder="请输入应用主键"
                    maxlength="64"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="应用名称" prop="applicationName">
                <el-input
                    v-model="form.applicationName"
                    placeholder="请输入应用名称"
                    maxlength="64"
                    :disabled="reloadType === 'edit'"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="钉钉cropId" prop="cropId">
                <el-input
                    v-model="form.cropId"
                    placeholder="请输入钉钉cropId"
                    maxlength="64"
                    :disabled="reloadType === 'edit'"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="钉钉agentId" prop="agentId">
                <el-input
                    v-model="form.agentId"
                    placeholder="请输入钉钉agentId"
                    maxlength="64"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="钉钉appKey" prop="appKey">
                <el-input
                    v-model="form.appKey"
                    placeholder="请输入钉钉appKey"
                    maxlength="64"
                    :disabled="reloadType === 'edit'"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="钉钉appSecret" prop="appSecret">
                <el-input
                    v-model="form.appSecret"
                    placeholder="请输入钉钉appSecret"
                    maxlength="64"
                    :disabled="reloadType === 'edit'"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="url" prop="url">
                <el-input
                    v-model="form.url"
                    placeholder="请输入url"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="消息模板id" prop="templateId">
                <el-input
                    v-model="form.templateId"
                    placeholder="请输入消息模板id"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="是否启用">
                <el-switch
                    v-model="form.appStatus"
                    active-value="1"
                    inactive-value="2"
                >
                </el-switch>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForm" :loading="saveLoading">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </div>
        </template>

      </el-dialog>
    </el-card>
  </div>
</template>

<script setup name="DingApplication">
//TODO: 修改时判断应用主键已存在
import {
  page,
  getById,
  del,
  add,
  updateById,
  checkApplication
} from "@/api/extend/dingApplication";
import {ElMessage, ElMessageBox} from "element-plus";
const {proxy} = getCurrentInstance();
//查询相关
const total = ref(0)
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  applicationName: undefined,
})
const showSearch = ref(true);
const dataList = ref([])
const loading = ref(true)
/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList();
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}
/** 查询列表 */
function getList() {
  loading.value=true
  page({
    ...queryParams.value,
  }).then(response => {
    if (response.success) {
      total.value = response.data.total
      dataList.value = response.data.records
    }
    loading.value = false;
  }).catch(() => {
    loading.value = false;
  })
}
getList()

//新增修改相关
const title=ref("")
const open=ref(false)
const formRef = ref();
const form = ref({});
const validateApplicationId=(rule, value, callback)=>{
  if (value === undefined||value === '') {
    callback(new Error("应用主键不能为空"));
  } else {

    checkApplication(form.value).then((response) => {
      if (response.data) {
        callback()
      } else {
        callback(new Error(response.message));
      }
    });
  }
}
const rules = ref({
  applicationId: [
    // { required: true, message: "应用主键不能为空", trigger: "blur" },
    { required: true, validator: validateApplicationId, trigger: "blur" },
  ],
  applicationName: [
    { required: true, message: "应用名称不能为空", trigger: "blur" },
  ],
  cropId: [
    { required: true, message: "钉钉cropId不能为空", trigger: "blur" },
  ],
  agentId: [
    { required: true, message: "钉钉agentId不能为空", trigger: "blur" },
  ],
  appKey: [
    { required: true, message: "钉钉appKey不能为空", trigger: "blur" },
  ],
  appSecret: [
    { required: true, message: "钉钉appSecret不能为空", trigger: "blur" },
  ]
})
const saveLoading = ref(false)
const reloadId = ref()
const reloadType = ref()
function reset(){
  form.value={
    applicationId: undefined,
    applicationName: undefined,
    cropId: undefined,
    agentId: undefined,
    appKey: undefined,
    appSecret: undefined,
    url: undefined,
    templateId: undefined,
  }
  proxy.resetForm("formRef")
  proxy.resetForm("queryRef")
}

//新增
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加应用"
  reloadType.value = "add"
}
//修改
function handleUpdate(row) {
  reset();
  let id = row.id;
  reloadId.value = id;
  reloadType.value = "edit"
  getById(id).then((response) => {
    if (response.data) {
      form.value = response.data;
      open.value = true;
      title.value = "修改租户"
    } else {
      ElMessage.error("数据异常")
    }
  })
}
//钉钉应用状态开关
function switchStatus(row){
  let data = row;
  updateById(data).then((response) => {
    if (response.data) {
    } else {
      ElMessage.error(response.message)
    }
  });
}
//提交
function submitForm() {
  formRef.value.validate((valid) => {
    if (valid) {
      saveLoading.value = true;
      if (form.value.id !== undefined) {
        update(form.value).then((response) => {
          if (!response.success) {
            ElMessage.error(response.message);
            saveLoading.value = false;
          } else {
            ElMessage.success("修改成功")
            open.value = false;
            saveLoading.value = false;
            getList();
          }
        })
      } else {
        add(form.value).then((response) => {
          if (!response.success) {
            ElMessage.error(response.message);
            saveLoading.value = false;
          } else {
            ElMessage.success("新增成功")
            open.value = false;
            saveLoading.value = false;
            getList();
          }
        })
      }
    }
  })
}

function handleDelete(row) {
  let id = row.id;
  reloadType.value = "remove"
  reloadId.value = id;
  ElMessageBox.confirm(
      '是否确认删除应用名称为"' + row.applicationName + '"的数据项?',
      "警告",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }
  ).then(()=>del(id)).then(() => {
    reloadId.value = undefined;
    reloadType.value = undefined;
    getList();
    ElMessage.success("删除成功")
  }).catch(() => {
    reloadId.value = undefined;
    reloadType.value = undefined;
  })
}
// 取消/关闭按钮
function cancel() {
  open.value = false;
  reloadId.value = undefined;
  reloadType.value = undefined;
  reset();
}
</script>

<style scoped>

</style>
