import request from '@/utils/request'
import { apiUrl } from '@/utils/config'; 


//获取区域管理员列表
export function getAdminList(data,params) {
  return request({
    url: `user${apiUrl}/areaAdmin/queryAdminList`,
    method: 'post',
    data: {...data,...params},
    params
  })
}

//保存区域管理员
export function saveAreaAdmin(data) {
  return request({
    url: `user${apiUrl}/areaAdmin/saveAreaAdmin`,
    method: 'post',
    data
  })
}

//删除区域管理员
export function deleteAreaAdminById(id) {
  return request({
    url: `user${apiUrl}/areaAdmin/delAdminById/` + id,
    method: 'get'
  })
}



export function selectApplyUserList(data) {
    return request({
      url: `user${apiUrl}/majorParking/parkingLot/selectUserList`,  
      method: "post",
      data: data,
    })
  }



  export function selectUserList(data) {
    return request({
      url: `user${apiUrl}/area/selectUserList`,  
      method: "post",
      data: data,
    })
  }

  
// 查询后端构建区域树


  // 查询后端构建区域树
export function getAllAreaTree(data) {
    return request({
      url: `user${apiUrl}/area/getAllAreaTree`,
      method: "post",
      data: data,
    });
  }