<template>
  <div class="app-container">
    <el-card style="height: 100%" shadow="never">
      <el-table v-loading="loading" :data="onlineList">
        <el-table-column label="序号" type="index" width="50" align="center"/>
        <el-table-column label="租户" prop="customParam.tenantName" :show-overflow-tooltip="true" align="center"/>
        <el-table-column label="用户ID" prop="userid" :show-overflow-tooltip="true" align="center" width="200px"/>
        <el-table-column label="所在组织" prop="currentOrgName" :show-overflow-tooltip="true" align="center"/>
        <el-table-column label="令牌" prop="token" :show-overflow-tooltip="true" align="center" width="180">
          <template #default="scope">
            <a  @click="copyToClipboard(scope.row.token)">{{ scope.row.token }}</a>
          </template>
        </el-table-column>
        <el-table-column label="用户姓名" prop="staffName" :show-overflow-tooltip="true" align="center" width="180"/>
        <el-table-column label="过期时间" prop="expiration" :show-overflow-tooltip="true" align="center" width="180"/>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right" width="100">
          <template #default="scope">
            <el-button link type="primary" icon="Delete" @click="forceLoginout(scope.row)"
                       v-hasPermi="['sys:operation:online:quit']">强退
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup name="Online">
import { onlineUsers, forceLogout } from "@/api/operation/online";
const { proxy } = getCurrentInstance();

/** 表格数据显示 */
const onlineList = ref([]);
const loading = ref(true);
function getList() {
  loading.value = true;
  onlineUsers().then(res => {
    if (res.data) {
      onlineList.value = res.data;
    }
    loading.value = false;
  }).catch(() => {
    loading.value = false;
  })
}

/** 强退操作 */
const forceLoginout = (row) => {
  proxy.$modal.confirm('此操作将退出（'+row.staffName+'），是否继续？').then(() => {
    forceLogout(row.token).then(res => {
      proxy.$modal.msgSuccess("强退成功！")
      getList();
    })
  }).catch(() =>  {
    proxy.$modal.msg("操作已取消")
  })
}

/** 复制令牌 */
const copyToClipboard = (context) => {
  navigator.clipboard.writeText(context).then(() => {
    proxy.$modal.msgSuccess("复制成功！")
  }).catch((error) => {
    proxy.$modal.msgError("复制失败！")
  });
}
getList();
</script>

