<template>
  <template v-for="(item, index) in tagsViewStore.iframeViews">
    <div class="container" v-show="route.path === item.path">
      <iframe :id="'iframe' + index" style="width: 100%; height: 100%" :src="item.meta && item.meta.link" frameborder="no">
      </iframe>
    </div>
  </template>
</template>

<script setup>
import useTagsViewStore from "@/store/modules/tagsView";
const route = useRoute();
const tagsViewStore = useTagsViewStore();
</script>
<style lang="scss" scoped>
.container {
  height: calc(100vh - 84px);
  overflow: hidden;
}
</style>