<template>
  <div style="width: 100%; height: 100%">
    <el-card :class="bgColor === true ?'iframe-card' :'unshow-iframe-card'" v-loading="loading" shadow="never">
      <template #header v-if="showTitle">
        <span class="card-title">
          <div class="title">
            <span>URL嵌入 （必须要设置访问地址）</span>
          </div>
        </span>
      </template>
      <iframe
          :src="props.iframeUrl"
          class="ele-iframe"
          v-if="props.iframeUrl"
      ></iframe>
      <el-empty
          style="padding: 20px 0"
          :image-size="50"
          description="暂未设置Iframe地址，功能不可用！"
      ></el-empty>
    </el-card>
  </div>
</template>

<script setup name="UseIframe">

const loading = ref(false)
const props = defineProps({
  showTitle: <PERSON>olean,
  bgColor: <PERSON><PERSON><PERSON>,
  iframeUrl: String
})

</script>

<style scoped lang="scss">
.iframe-card {
  background: #fff;
  height: 100%;
  .card-title {
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .icon {
      font-size: 18px;
      color: #419eee;
      padding: 0px 5px;
      -webkit-transition: font-size 0.25s linear, width 0.25s linear;
      -moz-transition: font-size 0.25s linear, width 0.25s linear;
      transition: font-size 0.25s linear, width 0.25s linear;
    }
    .exec {
      padding: 3px 0;
    }
  }
  :deep(.el-card__body) {
    padding: 0px;
    width: 100%;
    height: 100%;
  }
}
.unshow-iframe-card {
  border: 0px;
  height: 100%;
  background: #fff;
  .card-title {
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .icon {
      font-size: 18px;
      color: #419eee;
      padding: 0px 5px;
      -webkit-transition: font-size 0.25s linear, width 0.25s linear;
      -moz-transition: font-size 0.25s linear, width 0.25s linear;
      transition: font-size 0.25s linear, width 0.25s linear;
    }
    .exec {
      padding: 3px 0;
    }
  }
  :deep(.el-card__body) {
    padding: 0px;
    width: 100%;
    height: 100%;
  }
}

.ele-iframe {
  width: 100%;
  height: 100%;
}
</style>
