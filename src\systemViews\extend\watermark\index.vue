<template>
  <div class="app-container">
    <el-card shadow="never">
      <el-row :gutter="20" type="flex" justify="center">
        <el-col :span="12">
          <el-form
              :model="watermark"
              :rules="rules"
              ref="watermarkRef"
              label-width="100px"
          >
            <el-form-item label="是否开启水印" prop="status">
              <el-switch v-model="watermark.status"></el-switch>
            </el-form-item>
            <el-form-item label="水印内容" prop="content">
              <el-input v-model="watermark.content"></el-input>
            </el-form-item>
            <el-form-item label="水印密度" prop="density">
              <el-slider v-model="watermark.density"></el-slider>
            </el-form-item>
            <el-form-item label="水印偏移度" prop="rotate">
              <el-slider v-model="watermark.rotate"></el-slider>
            </el-form-item>
            <el-form-item label="字体大小" prop="fontSize">
              <el-slider v-model="watermark.fontSize"></el-slider>
            </el-form-item>
            <el-form-item>
              <el-button
                  type="primary"
                  :loading="saveLoading"
                  @click="submitForm()"
              >更新</el-button
              >
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup name="Watermark">
import useUserStore from "@/store/modules/user";
const userStore = useUserStore()
import { getPersonalConfig, updateConfig } from "@/api/system/config";
import {ElMessage} from "element-plus";
import dayjs from "dayjs";
const saveLoading=ref(false)
const loading=ref(true)
const watermarkRef=ref()
const watermark=ref({
  status: undefined,
  content: undefined,
  density: undefined,
  rotate: undefined,
  fontSize: undefined,
  color: undefined,
  transparent: undefined
})
const obj=ref({})
const rules=ref({ name: [
    { required: true, message: "请输入活动名称", trigger: "blur" },
    { min: 3, max: 5, message: "长度在 3 到 5 个字符", trigger: "blur" },
  ],
  region: [
    { required: true, message: "请选择活动区域", trigger: "change" },
  ],
  date1: [
    {
      type: "date",
      required: true,
      message: "请选择日期",
      trigger: "change",
    }],
      date2: [
        {
          type: "date",
          required: true,
          message: "请选择时间",
          trigger: "change",
        },
      ],
      type: [
        {
          type: "array",
          required: true,
          message: "请至少选择一个活动性质",
          trigger: "change",
        },
      ],
      resource: [
        { required: true, message: "请选择活动资源", trigger: "change" },
      ],
      desc: [{ required: true, message: "请填写活动形式", trigger: "blur" }],
})
function getConfig() {
  loading.value=true;
  getPersonalConfig({ configCode: "Watermark",}).then((r) => {
    loading.value = false;
    if (r.success) {
      watermark.value = JSON.parse(r.data.attra);
      watermark.value.content =`${userStore.userInfo.orgName}@${userStore.userInfo.staffName}@${setDefaultDateRange()}`
      obj.value.configId = r.data.configId
    } else {
      ElMessage.error(r.message);
    }
  });

}
function submitForm(){
  watermarkRef.value.validate((valid) => {
    if (valid) {
      saveLoading.value = true;
      updateConfig({
        configId: obj.value.configId,
        attra: JSON.stringify(watermark.value),
      }).then((r) => {
        saveLoading.value = false;
        if (r.success) {
          ElMessage.success("修改成功，请刷新浏览器查看效果");
        } else {
          ElMessage.error(r.message);
        }
      });
    }
  });
}

// 初始化默认值
const setDefaultDateRange = () => {
  const now = dayjs(); // 当前时间
  const startDate = now.format("YYYY-MM-DD"); // 当前日期


return startDate



};
onMounted(() => {
  setTimeout(() => {getConfig()}, 1000)
})

</script>

<style scoped>

</style>
