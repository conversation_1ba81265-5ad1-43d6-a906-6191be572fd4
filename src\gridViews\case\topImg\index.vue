<template>
  <el-row>
    <el-col :span="6">
        <div class="big-common-box-one" style="display: flex; align-items: center; justify-content: center;  height: 206px;">
            <div class="users-info">
                  <div class="left-user-img">
                    <!-- <img :src="userImg" v-if="userImg" /> -->
                    <img
                      src="@/assets/images/touxiang.png"
                      style="width: 80%"
                    />
            </div>

            <div class="right-user-info">
                    <div class="right-user-info-top">
                      hi {{ userObj.staffName }}
                    </div>
                    <div class="right-user-info-bottom">
                      欢迎来到智慧后勤一体化平台！
                    </div>
             </div>
        </div>
        </div>
    </el-col>
    <el-col :span="18" style="padding-left:12px;">
      <div class="big-common-box-one" style="  height: 206px;">
        <el-carousel indicator-position="outside">
          <el-carousel-item  v-for="(item,index) of list.imgList" :key="index" >
            <el-image :src="item.coverUrl"/>
          </el-carousel-item>
        </el-carousel>
      </div>
    </el-col>
  </el-row>
</template>

  <script setup>
import { ref } from "vue";
import {indexData} from "@/api/release/poster";
const loading = ref(false)
const list = ref([])
const form = ref({
  queryNum: 3,
})
const props = defineProps({
  showTitle: Boolean,
  bgColor: Boolean
})


import useUserStore from "@/store/modules/user";
import {getUser} from "@/api/system/user";

const userStore = useUserStore();
const user = ref({})
const userObj = ref({})
const userId = userStore.userInfo.staffId;


function getUserProfile() {
  getUser(userId).then(res => {
    userObj.value =res.data;
    user.value = {
      staffId: res.data.staffId,
      staffName: res.data.staffName,
      cellphone: res.data.cellphone,
      email: res.data.email
    }
  })
}

function getList() {
  indexData(form.value).then((res) => {
    var news = res.data
    news.forEach(item=>{
      console.log(item.advertisingType,'carousel',item.advertisingType === "carousel")
      if(item.advertisingType === "carousel") {
        list.value = item
      }
    })

    console.log('imgList',list.value)
  });
}

// 组件挂载时加载数据
onMounted(() => {
  getUserProfile();
  getList();
});

</script>

  <style scoped lang="scss">
.big-common-box-one {
  background: #ffffff;
  border-radius: 4px;
  width: 100%;
  padding: 10px;

  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); // 添加阴影效果
  transition: box-shadow 0.3s ease;
  box-sizing: border-box;

  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2); // 悬停时加深阴影
  }

  .el-carousel {
    height: 100%;
    border-radius: 4px; // 轮播图圆角
    overflow: hidden; // 防止图片溢出
  }

  .el-image {
    width: 100%;
    height:100%; // 根据实际需求调整高度
    object-fit: cover; // 图片填充方式
  }
}
:deep(.el-carousel__container) {
  height: 100%;
}

.users-info {
  display: flex;
  align-items: center;

  .left-user-img {
    width: 90px;
    height: 90px;
    border-radius: 50%;
    background: #c20000;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      width: 100%;
      border-radius: 50%;
    }
  }

  .right-user-info {
    margin-left: 24px;
    flex: 1;

    .right-user-info-top {
      font-size: 20px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #333333;
      padding-bottom: 12px;
      border-bottom: 1px solid #e8eaeb;
    }

    .right-user-info-bottom {
      font-size: 15px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #8c9398;
      padding-top: 12px;
    }
  }
}
::v-deep .el-carousel__indicators--outside{
  position: absolute !important;
  bottom: 0px;
  width: 100%;
  left: 0;
}
</style>
