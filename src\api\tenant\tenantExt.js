import request from "@/utils/request";

// 查询分页
export function page(params) {
  return request({
    url: "/user/tenantExtend",
    method: "get",
    params: params
  });
}

// 根据ID查询详细
export function getById(id) {
  return request({
    url: "/user/tenantExtend/" + id,
    method: "get"
  });
}

// 修改用户
// export function update(data) {
//   return request({
//     url: "/user/tenantExtend/",
//     method: "put",
//     data: data
//   });
// }


// 修改用户
export function update(data) {
  return request({
    url: "/user/tenantExtend/update",
    method: "post",
    data: data
  });
}
// // 删除
// export function del(id) {
//   return request({
//     url: "/user/tenantExtend/" + id,
//     method: "delete"
//   });
// }

// 删除
export function del(id) {
  return request({
    url: "/user/tenantExtend/delete/" + id,
    method: "post"
  });
}
