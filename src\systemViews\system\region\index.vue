<template>
  <div class="app-container">
    <Splitpanes class="default-theme">
      <Pane :size="15" :min-size="15">
        <el-card class="region-card" shadow="never" v-loading="treeLoading">
          <template #header>
            <span>行政区划</span>
            <el-button style="float: right; padding: 3px 0" link icon="Refresh" @click="reloadTree">刷新
            </el-button>
          </template>
          <el-tree
              :props="lazyTreeProps" @node-click="handleNodeClick"
              :load="loadNode" lazy :expand-on-click-node="false" ref="asyncTree"
              :default-expanded-keys="[defaultRegionId]" node-key="id" highlight-current>
          </el-tree>
        </el-card>
      </Pane>
      <Pane :size="85" :min-size="65">
        <el-card class="region-card">
          <template #header>
            <span>{{ selectNodeName }}</span>
            <el-button style="float: right; padding: 3px 0" link icon="Refresh" @click="allRegion">全部行政区划
            </el-button>
          </template>

          <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="100px">
            <el-form-item label="行政区划名称" prop="regionName">
              <el-input v-model="queryParams.regionName" placeholder="请输入行政区划名称" clearable style="width: 240px"
                        @keyup.enter.native="handleQuery"/>
            </el-form-item>
            <el-form-item label="行政区划代码" prop="regionCode">
              <el-input v-model="queryParams.regionCode" placeholder="请输入行政区划代码" clearable style="width: 240px"
                        @keyup.enter.native="handleQuery"/>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>

          <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
              <el-button type="primary" plain icon="Plus" v-if="selectNode" @click="handleAdd">新增</el-button>
            </el-col>

            <right-toolbar v-model:showSearch="showSearch" @queryTable="getSysRegionList"></right-toolbar>
          </el-row>

          <el-table v-loading="loading" :data="sysRegionList" stripe>
            <el-table-column label="序号" type="index"/>
            <el-table-column prop="regionName" label="行政区划名称"/>
            <el-table-column prop="regionFullName" label="行政区划全称"/>
            <el-table-column prop="regionCode" label="行政区划代码"/>
            <el-table-column prop="regionLevel" label="行政区划级别">
              <!--              <template #default="scope">-->
              <!--              </template>-->
            </el-table-column>
            <el-table-column prop="regionSort" label="排序"/>
            <el-table-column label="操作">
              <template #default="scope">
                <el-button @click="updateDialog(scope.row)" icon="Edit" link type="primary">修改</el-button>
                <el-button @click="deleteOne(scope.row)" icon="Delete" link type="primary">删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination
              v-show="total > 0"
              :total="total"
              v-model:page="queryParams.pageNum"
              v-model:limit="queryParams.pageSize"
              @pagination="getSysRegionList"
          />
        </el-card>
      </Pane>
    </Splitpanes>
    <!-- 添加或修改参数配置对话框 -->
    <el-dialog :title="dialogTitle" v-model="dialogFormVisible" ref="dialog" width="600px">
      <el-form ref="sysRegionFormRef" :model="sysRegionForm" :rules="sysRegionFormRules" label-width="110px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="上级行政区划" prop="parentName">
              <el-input v-model="parentName" placeholder="请选择上级行政区划" disabled>
                <template #append>
                  <el-link @click="proxy.$refs.regionSelect.open()" :disabled="isTopNode">选择</el-link>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item v-show="false" prop="parentId">
              <el-input v-model="sysRegionForm.parentId"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="行政区划代码" prop="regionCode">
              <el-input v-model="sysRegionForm.regionCode"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="行政区划名称" prop="regionName">
              <el-input v-model="sysRegionForm.regionName"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="行政区划全称" prop="regionFullName">
              <el-input v-model="sysRegionForm.regionFullName"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="行政区划名称" prop="regionLevel">
              <el-select v-model="sysRegionForm.regionLevel">
                <el-option
                    v-for="item in administrative_level"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="排序" prop="regionSort">
              <el-input-number v-model="sysRegionForm.regionSort" :min="0" :max="999999"
                               label="请输入序号"></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="addOrUpdate('sysRegionFormRef')">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <region-select ref="regionSelect" name="" value="" tenant-id="" @selectedRegion="selectedRegion"/>
  </div>
</template>

<script setup name="Region">
import {add, deleteSysRegion, findPage, findOne, update, loadTree} from '@/api/system/region';
import RegionSelect from "@/systemViews/system/region/components/RegionSelect";
import {ref} from "vue";

const {proxy} = getCurrentInstance();
const {administrative_level} = proxy.useDict("administrative_level");
// 树结构加载
const treeLoading = ref(false);
// 选中节点
const selectNode = ref(undefined);
// 默认展开的行政区划
const defaultRegionId = ref('1');
const selectNodeName = ref("行政区划");
// 默认展示搜索条件
const showSearch = ref(true);
// 表格加载
const loading = ref(true);
// 总计
const total = ref(0);
// 是否为顶级节点
const isTopNode = ref(false);
// 行政区划列表
const sysRegionList = ref([]);
// 对话框显隐
const dialogFormVisible = ref(false);
// 对话框标题
const dialogTitle = ref('新增');
// 新增修改表单
const sysRegionForm = ref({});
// 父级名称
const parentName = ref('');
const lazyTreeProps = ref({
  children: "children",
  label: "regionName",
  isLeaf: "leaf",
});
// 表单校验规则
const sysRegionFormRules = {
  regionCode: [
    {required: true, message: '请输入行政区划代码', trigger: 'blur'}
  ],
  regionName: [
    {required: true, message: '请输入行政区划名称', trigger: 'blur'}
  ],
  regionFullName: [
    {required: true, message: '请输入行政区划全称', trigger: 'blur'}
  ],
  regionLevel: [
    {required: true, message: '请输入行政区划级别', trigger: 'blur'}
  ],
}
// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  regionName: '',
  regionCode: '',
  id: ''
})

function getSysRegionList() {
  loading.value = true;
  findPage(queryParams.value).then(res => {
    sysRegionList.value = res.data.records;
    total.value = res.data.total;
    loading.value = false;
  })
}

function handleAdd() {
  dialogTitle.value = '新增'
  resetRegionForm();
  if (selectNode.value) {
    parentName.value = selectNodeName.value;
    sysRegionForm.value.parentId = selectNode.value.id;
  }
  dialogFormVisible.value = true;
}

function addOrUpdate(formName) {
  proxy.$refs[formName].validate((valid) => {
    if (valid) {
      if (dialogTitle.value === '新增') {
        add(sysRegionForm.value).then(res => {
          if (res.code == "1") {
            proxy.$modal.msgSuccess('添加成功！');
            dialogFormVisible.value = false;
            getSysRegionList();
          } else {
            proxy.$modal.msgError(res.message);
          }
        })
      } else {
        update(sysRegionForm.value).then(res => {
          if (res.code == "1") {
            proxy.$modal.msgSuccess('修改成功！');
            dialogFormVisible.value = false;
            getSysRegionList();
          } else {
            proxy.$modal.msgError(res.message);
          }
        })
      }
    } else {
      proxy.$modal.msgWarning('请完成表单填写');
      return false
    }
  })
}

function updateDialog(row) {
  resetRegionForm();
  dialogTitle.value = '修改';
  let region = findOne(row.id).then(res => {
    sysRegionForm.value = res.data;
    if (res.data.parentId !== '') {
      return findOne(res.data.parentId);
      dialogFormVisible.value = true;
    } else {
      parentName.value = "已经是最顶层区划";
      dialogFormVisible.value = true;
      isTopNode.value = true;
    }
  });
  region.then(res => {
    if (res) {
      parentName.value = res.data.regionName;
      dialogFormVisible.value = true;
    }
  })
}

function deleteOne(row) {
  proxy.$modal.confirm('此操作将永久删除(' + row.regionName + '), 是否继续?').then(() => {
    loading.value = true;
    deleteSysRegion(row.id).then(res => {
      proxy.$modal.msgSuccess('删除成功');
      getSysRegionList();
    })
  }).catch(() => {
    proxy.$modal.msg('已取消删除');
  })
}

// 重置新增修改表单
function resetRegionForm() {
  isTopNode.value = false;
  proxy.resetForm("sysRegionFormRef")
  sysRegionForm.value = {
    regionSort: 1
  }
}

// 取消新增修改表单
function cancel() {
  dialogFormVisible.value = false;
}

//懒加载树形结构的组织
function loadNode(node, resolve) {
  treeLoading.value = true;
  if (node.level === 0) {
    loadTree({
      id: defaultRegionId.value,
      queryType: "current",
    }).then((response) => {
      resolve(response.data);
      treeLoading.value = false;
    });
  } else {
    loadTree({
      id: node.data.id,
      queryType: "down",
    }).then((response) => {
      resolve(response.data);
      treeLoading.value = false;
    });
  }
}

// 树形节点单击事件
function handleNodeClick(data) {
  queryParams.value.id = data.id;
  selectNode.value = data;
  selectNodeName.value = data.regionName;
  getSysRegionList();
}

// 重新加载树形结构的行政区划
function reloadTree() {
  const cnode = proxy.$refs.asyncTree.getCurrentNode();
  if (cnode) {
    const node = proxy.$refs.asyncTree.getNode(cnode);
    node.childNodes = [];
    node.loaded = false;
    node.expand();
  } else {
    proxy.$refs.selectAsyncTree.root.loaded = false;
    proxy.$refs.selectAsyncTree.root.expand();
  }
}

// 刷新全部行政区划
function allRegion() {
  const node = proxy.$refs.asyncTree.root;
  node.loaded = false;
  node.expand();
  queryParams.value.id = defaultRegionId.value;
  selectNodeName.value = "行政区划";
  selectNode.value = undefined;
  getSysRegionList();
}

// 查询搜索
function handleQuery() {
  queryParams.value.page = 1;
  getSysRegionList();
}

// 重置搜索
function resetQuery() {
  proxy.resetForm("queryForm");
  handleQuery();
}

// 区划选择的回调
function selectedRegion(data) {
  sysRegionForm.value = {
    ...sysRegionForm.value,
    parentId: data.id,
  };
  parentName.value = data.regionName;
  proxy.$refs.regionSelect.close();
}

getSysRegionList()
</script>

<style scoped>
.region-card {
  min-height: calc(100vh - 120px);
}
</style>
