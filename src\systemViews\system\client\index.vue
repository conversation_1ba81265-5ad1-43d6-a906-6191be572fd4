<template>
  <div class="app-container">
    <el-card>
      <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          v-show="showSearch"
          @submit.native.prevent
      >
        <el-form-item v-if="userType === 'admin'" label="租户">
          <el-select v-model="queryParams.tenantId" style="width: 200px" remote :remote-method="getTenantList"
                     :loading="getTenantLoading"
                     @change="tenantChange(queryParams.tenantId)">
            <el-option
                v-for="item in tenantList"
                :key="item.tenantId"
                :label="item.tenantName"
                :value="item.tenantId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="客户端类型" prop="clientType">
          <el-select v-model="queryParams.clientType" style="width: 200px" clearable>
            <el-option
                v-for="item in client_type"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="配置名称" prop="configName">
          <el-input v-model="queryParams.configName" placeholder="请输入配置名称" clearable
                    style="width: 240px" @keyup.enter.native="handleQuery"/>
        </el-form-item>

        <el-form-item label="配置编码" prop="configCode">
          <el-input v-model="queryParams.configCode" placeholder="请输入配置编码" clearable style="width: 240px"
                    @keyup.enter.native="handleQuery"/>
        </el-form-item>

        <el-form-item label="配置类型" prop="configType">
          <el-select v-model="queryParams.configType" style="width: 200px" clearable>
            <el-option
                v-for="item in client_config_type"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="Plus" v-hasPermi="['sys:base:client:add']" @click="handleAdd">新增
          </el-button>
        </el-col>
        <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="configList" stripe>
        <el-table-column label="序号" type="index" width="50"/>
        <el-table-column label="配置名称" align="left" prop="configName" :show-overflow-tooltip="true"/>
        <el-table-column label="配置编码" align="left" prop="configCode" :show-overflow-tooltip="true">
          <template #default="scope">
            <a @click="copyToClipboard(scope.row.configCode)">{{ scope.row.configCode }}</a>
          </template>
        </el-table-column>
        <el-table-column label="配置类型" align="left" prop="configType">
          <template #default="scope">
            <dict-tag :options="client_config_type" :value="scope.row.configType" />
          </template>
        </el-table-column>
        <el-table-column label="配置值" align="left" prop="configValue" style="width: 35px; height: 35px"
                         :show-overflow-tooltip="true">
          <template #default="scope">
            <span v-if="scope.row.configType === '1'">
              <a @click="copyToClipboard(scope.row.configValue)">{{ scope.row.configValue }}</a>
            </span>
            <el-link v-if="scope.row.configType === '2'" type="primary" @click="handleLookEditorValueDailog(scope.row)">
              查看详情
            </el-link>
            <img v-if="scope.row.configType === '3'" :src="scope.row.configValue"
                 style="width: 35px; height: 35px; border: none"/>
            <el-switch v-if="scope.row.configType === '4'" v-model="scope.row.configValue" disabled active-value="1"
                       inactive-value="0">
            </el-switch>
          </template>
        </el-table-column>
        <el-table-column label="客户端类型" align="left" prop="clientType">
          <template #default="scope">
            <dict-tag :options="client_type" :value="scope.row.clientType" />
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button link icon="Edit" type="primary" v-hasPermi="['sys:base:client:edit']"
                       @click="handleUpdate(scope.row)" :loading="
                reloadId === scope.row.configId && reloadType === 'edit'">修改
            </el-button>
            <el-button link icon="Delete" type="primary" v-hasPermi="['sys:base:client:remove']" @click="handleDelete(scope.row)"
                       :loading="reloadId === scope.row.configId && reloadType === 'remove'">删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
      />
    </el-card>

    <!-- 添加或修改参数配置对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body @close="dialogReset">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="配置名称" prop="configName">
          <el-input v-model="form.configName" placeholder="请输入配置名称"/>
        </el-form-item>
        <el-form-item label="配置编码" prop="configCode">
          <el-input v-model="form.configCode" placeholder="请输入配置编码"/>
        </el-form-item>
        <el-form-item label="配置描述" prop="configDescribe">
          <el-input v-model="form.configDescribe" placeholder="请输入配置描述"/>
        </el-form-item>
        <el-form-item label="客户端类型" prop="clientType">
          <el-select v-model="form.clientType">
            <el-option
                v-for="item in client_type"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="配置类型" prop="configType">
          <el-select v-model="form.configType">
            <el-option
                v-for="item in client_config_type"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="配置值" prop="configInput" v-show="form.configType === '1'">
          <el-input v-model="form.configInput" @input="$forceUpdate()"/>
        </el-form-item>
        <el-form-item label="内容" prop="configEditor" v-show="form.configType === '2'">
          <editor v-model="form.configEditor" :min-height="192" :read-only.sync="formDisabled"/>
        </el-form-item>
        <el-form-item label="配置值" prop="configImage" v-show="form.configType === '3'">
          <el-upload ref="coverUpload" :http-request="uploadImage"
                     :on-preview="handlePreview"
                     :on-remove="handleRemove"
                     :before-remove="beforeRemove"
                     :on-exceed="handleExceed"
                     :limit="1"
                     :file-list="imageList"
                     list-type="picture-card"
          >
            <el-icon><Plus /></el-icon>
          </el-upload>
          <el-dialog v-model="previewDialogVisible" append-to-body>
            <img width="100%" :src="previewImageUrl"/>
          </el-dialog>
        </el-form-item>
        <el-form-item label="配置值" prop="configSwitch" v-show="form.configType === '4'">
          <el-switch
              active-text="开启"
              inactive-text="关闭"
              active-value="1"
              inactive-value="0"
              @change="$forceUpdate()"
              v-model="form.configSwitch">
          </el-switch>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm" :loading="saveLoading">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看富文本详情 -->
    <el-dialog title="详情" v-model="lookEditorValueDailog" append-to-body>
      <editor v-model="configEditor" :min-height="192" :read-only.sync="formDisabledLook"/>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="lookEditorValueDailog = false">关 闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Client">
import {
  getClinet,
  delClient,
  addClient,
  updateClient,
  getClientPage,
} from "@/api/system/client";
import {uploadSingle} from "@/api/tool/upload";
import Editor from "@/components/Editor";
import {getTenants} from "@/api/tenant/tenant";
import {ref} from "vue";
import useUserStore from "@/store/modules/user";
import {copyToClipboard} from "@/utils";

const {proxy} = getCurrentInstance();
const {client_config_type, client_type} = proxy.useDict("client_config_type", "client_type");
const userStore = useUserStore();
const userType = userStore.userInfo.customParam.userType;

// 遮罩层
const loading = ref(true);
const getTenantLoading = ref(false);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 参数表格数据
const configList = ref([]);
// 弹出层标题
const title = ref("");
// 是否显示弹出层
const open = ref(false);
// 类型数据字典
const typeOptions = ref([]);
const clienOptions = ref([]);
// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  configCode: undefined,
  configCodes: undefined,
  configName: undefined,
  configType: undefined,
  clientType: undefined,
  tenantId: userStore.userInfo.customParam.tenantId
});
const tenantList = ref([]);
// 表单参数
const form = ref({
  configId: undefined,
  configType: undefined,
  configContent: undefined,
  configName: undefined,
  configCode: undefined,
  configDescribe: undefined,
  clientType: undefined,
  configValue: undefined,
  configInput: "",
  configEditor: undefined,
  configImage: undefined,
  configSwitch: "1",
});
// 表单校验
const rules = {
  clientType: [
    {required: true, message: "客户端类型不能为空", trigger: "blur"},
  ],
  configName: [
    {required: true, message: "配置名称不能为空", trigger: "blur"},
  ],
  configCode: [
    {required: true, message: "配置编码不能为空", trigger: "blur"},
  ],
  configType: [
    {required: true, message: "配置类型不能为空", trigger: "blur"},
  ],
  configValue: [
    {required: true, message: "配置值不能为空", trigger: "blur"},
  ]
}
const reloadId = ref(undefined);
const reloadType = ref(undefined);
const saveLoading = ref(false);
/* 图片管理 */
const previewDialogVisible = ref(false);
const previewImageUrl = ref("");
const imageList = ref([]);
const formDisabled = ref(false);
const lookEditorValueDailog = ref(false);
const configEditor = ref(undefined);
const formDisabledLook = ref(false);

/** 查询参数列表 */
function getList() {
  loading.value = true;
  const param = {...queryParams.value};
  delete param.tenantName;
  getClientPage(param).then((response) => {
    configList.value = response.data.records;
    total.value = response.data.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {};
  proxy.resetForm("formRef");
  proxy.$refs.coverUpload && proxy.$refs.coverUpload.clearFiles();
  imageList.value = [];
  form.value.configId = undefined;
}

/** 搜索按钮操作 */
function handleQuery() {
  if (queryParams.value.configCode) {
    var newCode = queryParams.value.configCode.split(/,|，|\s+/);
    queryParams.value.configCodes = newCode;
  } else {
    queryParams.value.configCodes = queryParams.value.configCode;
  }
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryForm");
  handleQuery();
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加参数";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const configId = row.configId;
  reloadId.value = configId;
  reloadType.value = "edit";
  getClinet(configId).then((response) => {
    reloadId.value = undefined;
    reloadType.value = undefined;
    if (response.data) {
      form.value = response.data;
      if (response.data.configType) {
        switch (response.data.configType) {
          case "1":
            form.value.configInput = response.data.configValue;
            break;
          case "2":
            form.value.configEditor = response.data.configValue;
            break;
          case "3":
            imageList.value.push({
              url: response.data.configValue,
            });
            break;
          case "4":
            form.value.configSwitch = response.data.configValue;
            break;
          default:
            proxy.$modal.msgError("当前配置类型不支持!");
            break;
        }
      }
      open.value = true;
      title.value = "修改参数";
    } else {
      proxy.$modal.msgError("数据异常！");
    }
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      saveLoading.value = true;
      if (form.value.configId != undefined) {
        if (form.value.configType) {
          switch (form.value.configType) {
            case "1":
              form.value.configValue = form.value.configInput;
              break;
            case "2":
              form.value.configValue = form.value.configEditor;
              break;
            case "3":
              let imgUrls = "";
              imageList.value.forEach((item) => {
                imgUrls += item.url + ",";
              });
              if (imgUrls !== "")
                imgUrls = imgUrls.substring(0, imgUrls.length - 1);
              form.value.configValue = imgUrls;
              break;
            case "4":
              form.value.configValue = form.value.configSwitch;
              break;
            default:
              proxy.$modal.msgError("当前配置类型不支持!");
              break;
          }
        }
        updateClient(form.value).then((response) => {
          if (!response.success) {
            proxy.$modal.msgError(response.message);
            saveLoading.value = false;
            reset();
          } else {
            proxy.$modal.msgSuccess("修改成功");
            open.value = false;
            getList();
            reset();
            saveLoading.value = false;
          }
        });
      } else {
        // 新增
        switch (form.value.configType) {
          case "1":
            form.value.configValue = form.value.configInput;
            break;
          case "2":
            form.value.configValue = form.value.configEditor;
            break;
          case "3":
            let imgUrls = "";
            imageList.value.forEach((item) => {
              imgUrls += item.url + ",";
            });
            if (imgUrls !== "")
              imgUrls = imgUrls.substring(0, imgUrls.length - 1);
            form.value.configValue = imgUrls;
            break;
          case "4":
            form.value.configValue = form.value.configSwitch;
            break;
          default:
            proxy.$modal.msgError("当前配置类型不支持!");
            break;
        }
        addClient(form.value).then((response) => {
          if (!response.success) {
            proxy.$modal.msgError(response.message);
            saveLoading.value = false;
            reset();
          } else {
            proxy.$modal.msgSuccess("新增成功");
            open.value = false;
            getList();
            reset();
            saveLoading.value = false;
          }
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const configId = row.configId;
  reloadId.value = configId;
  reloadType.value = "remove";
  proxy.$modal.confirm('是否确认删除配置名称为"' + row.configName + '"的数据项?').then(function () {
    return delClient(configId);
  }).then(() => {
    reloadId.value = undefined;
    reloadType.value = undefined;
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {
    reloadId.value = undefined;
    reloadType.value = undefined;
  });
}

/* 图片上传 */
const uploadImage = (content)=> {
  let formData = new FormData();
  //content.file 	文件file对象
  formData.append("multipartFile", content.file);
  formData.append("appCode", "file");
  return uploadSingle(formData).then((res) => {
    imageList.value.push({
      url: res.data.url,
    });
  });
}

const handleRemove = (file, fileList) => {
  imageList.value = [];
}

const handlePreview = (file) => {
  console.log(file)
  previewImageUrl.value = file.url;
  console.log()
  previewDialogVisible.value = true;
}

const handleExceed = (files, fileList) => {
  proxy.$modal.msgWarning(`只能上传一张图片,请先移除前一张`);
}

const beforeRemove = (file, fileList) => {
  return proxy.$modal.confirm(`确定移除？`);
}

// 点击查看富文本框内容
function handleLookEditorValueDailog(data) {
  lookEditorValueDailog.value = true;
  configEditor.value = data.configValue;
}

// 监听对话框重置
function dialogReset() {
  reset();
}

function getTenantList(tenantName) {
  getTenantLoading.value = true;
  let query = {}
  if (tenantName !== undefined && tenantName !== '') {
    query.tenantName = tenantName
    query.tenantId = undefined
  } else {
    query.tenantId = queryParams.value.tenantId
  }
  getTenants(query).then((response) => {
    tenantList.value = response.data;
  }).finally(() => {
    getTenantLoading.value = false;
  });
}

function tenantChange(tenantId) {
  if (tenantId !== "") {
    queryParams.value.tenantName = tenantList.value.find(
        (item) => item.tenantId === tenantId
    ).tenantName;
  }
  handleQuery();
}

getTenantList();
getList();
</script>

<style scoped>

</style>
