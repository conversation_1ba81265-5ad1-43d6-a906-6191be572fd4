import request from '@/utils/request'

import { apiUrl } from '@/utils/config';

// 停车记录
export function pageList(query, params) {
  return request({
    url: '/parking/record/parkingRecordList',
    method: 'post',
    data: {...query,...params},
    params: params

  })
}
// 停车缴费记录
export function pagePayList(query, params) {
  return request({
    url: '/parking/record/parkingConsumptionRecordList',
    method: 'post',
    data:  {...query,...params},
    params: params

  })
}
// 宇视停车记录
export function getParkingRecordList(query, params) {
  return request({
    url: '/parking/record/getParkingRecordList',
    method: 'post',
    data:  {...query,...params},
    params: params

  })
}

// 停车场记录
export function parkRecordsPage(query, params) {
  return request({
    url: `park${apiUrl}/majorParking/parkRecords/page`,
    method: 'post',
    data: {...query,...params},
    params: params

  })
}


//停车场查询
export function findparkinglot(param,data) {
  return request({
    url: `park${apiUrl}/majorParking/parkingLot/findparkinglot`,
    method: 'post',
    data: {...param,...data},
    params: param
  })
}

export function getFileInfoById(id) {
  return request({
      url: `park${apiUrl}/majorParking/parkRecords/getFileInfoById/${id}`,
      method: 'get',
  })
}



export function findparkinglots(data) {
  return request({
      url: `park${apiUrl}/majorParking/parkRecords/findparkinglot`,
      method: 'post',
      data:data
  })
}
export function updateParkRecords(data) {
  return request({
      url: `park${apiUrl}/majorParking/parkRecords/updateParkRecords`,
      method: 'post',
      data:data
  })
}
export function gateControlByRecords(data) {
  return request({
      url: `park${apiUrl}/majorParking/parkRecords/gateControlByRecords`,
      method: 'post',
      data:data
  })
}


export function findparkingTree(data) {
  return request({
      url: `park${apiUrl}/majorParking/parkingLot/findparkingTree`,
      method: 'post',
      data:{}
  })
}
