<!-- 部门授权 -->
<template>
    <div class="container-table1">
        <Splitpanes class="default-theme">
            <Pane :size="18" :min-size="10">
                <el-card class="dep-card" style="height: 100%">
                    <div class="flex" style="margin-bottom: 12px">
                        <el-input v-model="searchValue" placeholder="请输入区域名称" clearable @clear="initTreeselect" />
                        <el-button type="primary" icon="Search" @click="handleTree"
                            style="margin-left: 8px">搜索</el-button>
                    </div>
                    <!-- 组织树 -->
                    <treeLoad ref="myTree" :isShowSearch="false" :defaultProps="defaultProps" :treeData="treeData"
                        :treeBtnEdit="false" @loadFirstNodeFather="loadRootNodes" @loadChildNodeFather="loadChildNodes"
                        :treeBtnDelete="false" :tree-name="isLazy ? 'name' : 'label'"
                        :defaultExpandedKeys="defaultExpandedKeys" :isLazy="isLazy" :treeindex="treeindex"
                        @checkedKeys="handleNodeClick" />
                </el-card>
            </Pane>
            <Pane :size="82" :min-size="62">
                <el-card>
                    <div class="area-path-style" v-if="areaPath">
                        <i class="icon iconfont icon-quyupaixu"></i>
                        <span>{{ areaPath }}</span>
                    </div>
                    <el-row :gutter="24">
                        <el-col :span="24" :xs="24">
                            <dialog-search @getList="getList" formRowNumber="3" :columns="tabelForm.columns"
                                :isShowRightBtn="true">
                                <template #formList>
                                    <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="80px">
                                       
                                        <el-form-item label="人员姓名">
              <el-input v-model="queryParams.nickName" placeholder="请输入人员姓名" clearable></el-input>
            </el-form-item>
            <el-form-item label="人员账号">
              <el-input v-model="queryParams.userName" placeholder="请输入人员账号" clearable></el-input>
            </el-form-item>
            <el-form-item label="人员部门">
              <el-input v-model="queryParams.deptName" placeholder="请输入部门名称" clearable></el-input>
            </el-form-item>

                                        <el-form-item label="过期状态" prop="accreditType">
                                            <el-select v-model="queryParams.accreditStatus" placeholder="请选择过期状态"
                                                clearable>
                                                <el-option v-for="(item, index) of accredit_status" :key="index"
                                                    :label="item.label" :value="item.value" />
                                            </el-select>
                                        </el-form-item>
                                        <el-form-item label="过期日期">
                                            <el-date-picker v-model="queryParams.dateRange" type="daterange"
                                                range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                                                :value-format='YYYY - MM - DD'>
                                            </el-date-picker>
                                        </el-form-item>
                                    </el-form>
                                </template>
                                <template #searchList>
                                    <el-button type="primary" icon="Search" @click="handleQuery">
                                        搜索
                                    </el-button>
                                    <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                                </template>
                                <template #searchBtnList>
                                    <el-button type="primary" icon="Plus" @click="handleAdd">
                                        新增人员授权
                                    </el-button>
                                    <el-button icon="Download" @click="handleExport">
                                        导出
                                    </el-button>
                                </template>
                            </dialog-search>

                            <PublicTable ref="publictable" :rowKey="tabelForm.tableKey" :tableData="deviceList"
                                :columns="tabelForm.columns" :configFlag="tabelForm.tableConfig" :pageValue="pageParams"
                                :total="total" :getList="getList">

                                <!-- 操作列 -->
                                <template #operation="{ scope }">
                                    <el-button link icon="View" type="primary" title="查看授权"
                                        @click="handleView(scope.row)">
                                    </el-button>

                                    <el-button link  type="primary" title="修改日期"
                                        @click="handleEdit(scope.row)">
                                        <i class="icon iconfont icon-xiugairiqi"></i>
                                    </el-button>
                                    <el-button link  type="primary" title="取消授权"
                                        @click="handleDelete(scope.row)">
                                        <i class="icon iconfont icon-quxiaoshouquan2"></i>
                                    </el-button>


                                </template>
                            </PublicTable>

                            <DialogBox :visible="open" :dialogWidth="dialogWidth" @save="submits"
                                @cancellation="cancellation" @close="close" :dialogFooterBtn="dialogFooterBtn"
                                CloseSubmitText="取消" SaveSubmitText="确定" :dialogTitle="headerTitle" :dialogTop="dialogTop">
                                <template #content>
                                    <peopleAddAccredit v-if="popupType == 'add'" ref="peopleAddAccreditRef"
                                        @submitClose="submitClose" :row-data="rowData" :popup-type="popupType" />
                                    <peopleView v-if="popupType == 'view'" ref="peopleAccreditViewRef"
                                        @submitClose="submitClose" :row-data="rowData" :popup-type="popupType">
                                    </peopleView>

                                    <peopleDate v-if="popupType == 'edit'" ref="peopleAccreditEditDateRef"
                                        @submitClose="submitClose" :row-data="rowData" :popup-type="popupType">
                                    </peopleDate>
                                </template>
                            </DialogBox>
                        </el-col>
                    </el-row>
                </el-card>
            </Pane>
        </Splitpanes>
    </div>
</template>

<script setup>
import { ref, reactive } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import peopleAddAccredit from "./components/peopleAddAccredit.vue";
import peopleView from "./components/peopleView.vue";
import peopleDate from "./components/peopleDate.vue";
import { screenIndex } from "@/api/admittance/accredit";
import { formatMinuteTime } from "@/utils";
import { formatDateStringPrev } from "@/utils/common";
import treeLoad from "@/components/Tree/treeLoad";
const { proxy } = getCurrentInstance();
const { accredit_type, accredit_status } =
    proxy.useDict(
        "accredit_type",
        "accredit_status"
    );


// 表格配置
const tabelForm = ref({
    tableKey: "1",
    columns: [
        {
            fieldIndex: 'nickName',
            label: '人员姓名',
            resizable: true,
            visible: true,
            sortable: true,
            minWidth: '120px'
        },
        {
            fieldIndex: 'userName',
            label: '人员账号',
            resizable: true,
            visible: true,
            sortable: true,
            minWidth: '120px'
        },
        {
            fieldIndex: 'deptName',
            label: '人员部门',
            resizable: true,
            visible: true,
            sortable: true,
            minWidth: '120px'
        },
        {
            fieldIndex: 'accreditType',
            label: '授权类型',
            resizable: true,
            visible: true,
            sortable: true,
            minWidth: '120px',
            type: 'dict',
            dictList: accredit_type
        },
        {
            fieldIndex: 'areaName',
            label: '区域名称',
            resizable: true,
            visible: true,
            sortable: true,
            minWidth: '150px',
            align: 'left'
        },
        {
            fieldIndex: 'createTime',
            label: '创建时间',
            resizable: true,
            visible: true,
            sortable: true,
            minWidth: '150px',
        },
        {
            fieldIndex: 'expirationTime',
            label: '过期日期',
            resizable: true,
            visible: true,
            sortable: true,
            minWidth: '150px',
            defaultLabel: '长期有效'
        },
        {
            fieldIndex: 'accreditStatus',
            label: '过期状态',
            resizable: true,
            visible: true,
            sortable: true,
            minWidth: '120px',
            type: 'dict',
            dictList: accredit_status
        },
        {
            fieldIndex: 'updateBy',
            label: '操作账号',
            resizable: true,
            visible: true,
            sortable: true,
            minWidth: '120px'
        },
        {
            label: "操作",
            slotname: "operation",
            width: "120",
            fixed: "right",
            visible: true,
        }
    ],
    tableConfig: {
        needPage: true,
        index: true,
        selection: false,
        reserveSelection: false,
        indexFixed: false,
        selectionFixed: false,
        indexWidth: "50",
        loading: false,
        showSummary: false,
        height: null,
    },
});

// 查询参数
const pageParams = ref({
    pageNum: 1,
    pageSize: 10,
});

// 表格数据
const deviceList = ref([{}]);
const total = ref(0);
// 弹窗参数
const dialogTop = ref('')
const dialogFooterBtn = ref(true);
const headerTitle = ref("");
const rowData = ref({});
const open = ref(false);
const dialogWidth = ref("50%");
const popupType = ref("");
const queryParams = ref({
    areaId: '0'
});
const peopleAddAccreditRef = ref(null);
const peopleAccreditViewRef = ref(null);
const peopleAccreditEditDateRef = ref(null)
// 树参数
const searchValue = ref("");
const defaultProps = reactive({
    children: "children",
    label: "name",
});
const treeData = ref([]);
const defaultExpandedKeys = ref([]);
const isLazy = ref(true);
const treeindex = ref(0);
const areaPath = ref("");
const myTree = ref(null);
// 懒加载根节点（当 isLazy=true 时）
const loadRootNodes = (resolve) => {
    // 这里模拟异步获取数据
    setTimeout(() => {
        screenIndex
            .areaTreeSelect({ name: searchValue.value, status: "1" })
            .then((response) => {
                resolve(response.data);
            });
    }, 500);
};

// 懒加载子节点
const loadChildNodes = (node, resolve) => {
    // 模拟异步请求
    setTimeout(() => {
        screenIndex
            .areaTreeSelect({
                ...node.data,
                ...{ name: searchValue.value, status: "1" },
            })
            .then((response) => {
                resolve(response.data);
            });
    }, 500);
};
/** 树结构点击 */
const handleNodeClick = (data) => {
    queryParams.value.areaId = data.id;
    getList();
    updateAreaPath();
};
const updateAreaPath = async () => {
    const res = await screenIndex.getAreaPath({
        areaId: queryParams.value.areaId,
    });
    areaPath.value = res.data?.map((item) => item.areaName).join(" > ") || "";
};
// 懒加载树搜索
const handleTree = () => {
    if (searchValue.value.length < 2) {
        ElMessage.warning("请至少输入两个字的查询条件！");
        return false;
    } else {
        treeindex.value++;
        isLazy.value = false;
        defaultExpandedKeys.value = [];
        screenIndex.getAllAreaTree({ areaName: searchValue.value }).then((res) => {
            treeData.value = res.data;
            expandTree(treeData.value);
        });
    }
};

const expandTree = (data) => {
    data.forEach((item) => {
        if (item.label.includes(searchValue.value)) {
            defaultExpandedKeys.value.push(item.id);
        }
        if (item.children) {
            expandTree(item.children);
        }
    });
};

const initTreeselect = () => {
    searchValue.value = "";
    treeindex.value = 0;
    treeData.value = [];
    isLazy.value = true;
};
// 获取列表
const getList = () => {
    tabelForm.value.tableConfig.loading = true;
    screenIndex.userAccreditList(pageParams.value, queryParams.value).then((res) => {
             // 格式化时间
             res.data.records.forEach(row => {
      if (row.expirationTime != null && row.expirationTime !== '') {
        row.expirationTime = formatDateStringPrev(row.expirationTime);
      }
    })
        deviceList.value = res.data.records;
        total.value = res.data.total;
        tabelForm.value.tableConfig.loading = false;
    });
};

// 查询
const handleQuery = () => {
    pageParams.value.pageNum = 1;
    getList();
};

// 重置
const resetQuery = () => {
    queryParams.value = {
        queryParams: '0'
    };
    areaPath.value = "";

    defaultExpandedKeys.value = [];
    myTree.value.setCurrentKeyFun();

    pageParams.value.pageNum = 1;

    getList();
};

// 新增
const handleAdd = () => {

    headerTitle.value = "新增授权";
    dialogWidth.value = "50%";
    popupType.value = "add";
    dialogFooterBtn.value = true;
    rowData.value = {};
    open.value = true;
    dialogTop.value = ''
};

// 修改日期
const handleEdit  = (row) => {
    headerTitle.value = "修改日期";
    popupType.value = "edit";
    dialogWidth.value = "25%";
    dialogFooterBtn.value = true;
    rowData.value = row;
    open.value = true;
     dialogTop.value = '15vh'
};

// 查看
const handleView = (row) => {
    headerTitle.value = "查看人员权限";
    popupType.value = "view";
    dialogWidth.value = "30%";
    dialogFooterBtn.value = false;
    rowData.value = row;
    open.value = true;
     dialogTop.value = ''
};

// 删除
const handleDelete = (row) => {
    ElMessageBox.confirm("确认取消授权吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
    }).then(() => {
        // 调用解绑API
        screenIndex.deleteDeptAreaById(row.id).then((res) => {
            ElMessage.success("取消授权成功");
            getList();
        })
      
    });
};

// 导出
const handleExport = () => {
    proxy.download(
        '/admittance/accredit/exportDept',
        { ...queryParams.value },
        `部门授权数据_${formatMinuteTime(new Date())}.xlsx`
    );
};

// 提交
const submits = () => {
    if (popupType.value === 'add') {
        peopleAddAccreditRef.value.saveBtn()
    } else {
        // 其他类型可根据需要扩展
        // 例如：if (props.popupType === 'edit') { ... }
    }
}

// 取消
const cancellation = () => {
    close(false);
};

// 关闭弹窗
const close = (val) => {
    open.value = val;
};

// 提交关闭
const submitClose = () => {
    open.value = false;
    getList();
};

onMounted(() => {
    // getList();
});
</script>

<style scoped>
/* 自定义样式 */
</style>