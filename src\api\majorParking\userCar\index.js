import request from '@/utils/request'
import { apiUrl } from '@/utils/config'; 
export class screenIndex {
  static stUserCar(params,data) {
    return request({
      url: `park${apiUrl}/majorParking/userCar/stUserCar`,
      method: "post",
      data: {...data,...params},
      params: params
    })
  }
  static deleteUserCarById(id) {
    return request({
      url: `park${apiUrl}/majorParking/userCar/deleteUserCarById/` + id,
      method: "get",
    })
  }
  static getUUId() {
    return request({
      url: `park${apiUrl}/majorParking/userCar/getUUid`,
      method: 'get'
    })
  }

  static addUserCar(data) {
    return request({
      url: `park${apiUrl}/majorParking/userCar/addUserCar`,
      method: "post",
      data: data
    })
  }
  static updataUserCar(data) {
    return request({
      url: `park${apiUrl}/majorParking/userCar/updataUserCar`,
      method: "post",
      data: data
    })
  }
  static removeImg(id) {
    return request({
      url: '/majorParking/userCar/removeImage/' + id,
      method: 'post'
    })
  }
  //查询申请人列表
  static selectApplyUserList(data) {
    return request({
      url: "user/staffs/selectUserList",  //########################后期更改#################
      method: "post",
      data: data,
    })
  }

// 临时车辆查询

  static stTemporaryCar(params,data) {
    return request({
      url: `park${apiUrl}/majorParking/userCar/stTemporaryCar`,
      method: "post",
      data:  {...data,...params},
      params: params
    })
  }

//临时车辆表新增
  static addTemporaryCar(data) {
    return request({
      url: `park${apiUrl}/majorParking/userCar/addTemporaryCar`,
      method: "post",
      data: data
    })
  }

  static getFileInfoById(id) {
    return request({
        url: `park${apiUrl}/majorParking/userCar/getFileInfoById/${id}`,
        method: 'get',
    })
  }
  // 员工车辆历史记录
  static stPastUserCar(params,data) {
    return request({
      url: `park${apiUrl}/majorParking/userCar/pastUserCarList`,
      method: "post",
      data: {...data,...params},
      params: params
    })
  }
}




// 附件查询




