<template>
  <div
    class="dialog-box"
    :class="popupType === 'view' ? 'dialog-box-view' : 'dialog-box-edit'"
  >
    <el-row>
      <el-col :span="12" >
        <el-form
          ref="ruleform"
          :model="formData"
          :rules="rules"
          inline="true"
          label-width="70px"
        >
          <el-form-item label="有效日期" prop="expirationTime">
            <el-date-picker
              v-model="formData.expirationTime"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD 23:59:59"
              type="date"
               :disabled-date="disabledDate"
              placeholder="默认长期有效"
            />
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <Splitpanes class="default-theme">
      <!-- 有效日期选择 -->

      <Pane :size="50" :min-size="20">
        <el-card class="dep-card dep-card1">
          <el-form
            ref="ruleform"
            :model="formData"
            :rules="rules"
            inline="true"
            label-width="60px"
          >
            <el-form-item label="停车场">
              <el-tree
                style="margin: 20px auto"
                :data="parkingOptions"
                default-expand-all
                node-key="id"
                ref="deptTree"
                highlight-current
                show-checkbox
                @check-change="handleCheckChange"
                :props="parkingProps"
              />
            </el-form-item>
          </el-form>
        </el-card>
      </Pane>
      <Pane :size="50" :min-size="20">
        <el-card class="dep-card dep-card2">
          <el-form
            ref="ruleform"
            :model="formData"
            :rules="rules"
            inline="true"
            label-width="70px"
          >
            <el-form-item label="车辆编组">
              <el-tree
                style="margin: 20px auto"
                :check-strictly="true"
                :data="typeOptions"
                show-checkbox
                default-expand-all
                node-key="value"
                ref="areaTree"
                highlight-current
                :props="typeProps"
              />
            </el-form-item>
          </el-form>
        </el-card>
      </Pane>
    </Splitpanes>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  findparkinglot,
  addTypeAccredit,
} from "@/api/majorParking/accredit/accredit";

// 定义组件的属性
const props = defineProps({
  closeBtn: {
    type: Function,
    default: null,
  },
  popupType: {
    type: String,
    default: "",
  },
  rowData: {
    type: Object,
    default: () => ({}),
  },
});

const { proxy } = getCurrentInstance();
const { parking_rule_vehicle_type } = proxy.useDict(
  "parking_rule_vehicle_type"
);

// 定义组件的事件
const emit = defineEmits(["close"]);

// 表单数据
const formData = reactive({
  parkingIds: [],
  typeIds: [],
  expirationTime: null,
});

// 时间选择器的禁用日期配置

const disabledDate = (time) => {
  const today = new Date();
    today.setHours(0, 0, 0, 0);
    return time.getTime() < today.getTime();
};

// 停车场选项
const parkingOptions = ref([]);

// 车辆类型选项
const typeOptions = ref([]);

// 停车场树形组件的属性配置
const parkingProps = reactive({
  multiple: true,
  emitPath: false,
  value: "id",
  children: "children",
  label: "label",
});

// 车辆类型树形组件的属性配置
const typeProps = reactive({
  multiple: true,
  emitPath: false,
  value: "value",
  children: "children",
  label: "label",
});

// 表单校验规则
const rules = reactive({
  parkingIds: [{ required: true, message: "请选择停车场", trigger: "blur" }],
  typeIds: [{ required: true, message: "请选择车辆类型", trigger: "blur" }],
});

// 树形组件的引用
const deptTree = ref(null);
const areaTree = ref(null);
// 方法
const handleCheckChange = () => {
  // 处理选中/取消选中事件
};

// 获取停车场数据
const getParking = async () => {
  try {
    const res = await findparkinglot({ status: "0" });
    parkingOptions.value = res.data;
  } catch (error) {
    console.error("获取停车场数据失败:", error);
  }
};

// 获取车辆类型数据
const getType = () => {
  typeOptions.value = parking_rule_vehicle_type;
};

// // 保存按钮点击事件
// const saveBtn = async () => {
//   // 获取选中的停车场和车辆类型
//   const parkingIds = deptTree.value ? deptTree.value.getCheckedKeys() : [];
//   const typeIds = areaTree.value ? areaTree.value.getCheckedKeys() : [];

//   // 赋值到表单数据
//   formData.parkingIds = parkingIds;
//   formData.typeIds = typeIds;

//   // 验证表单数据
//   if (formData.parkingIds.length < 1) {
//     ElMessage.warning("请选择停车场");
//     return;
//   }
//   if (formData.typeIds.length < 1) {
//     ElMessage.warning("请选择车辆类型");
//     return;
//   }

//   // 提交表单数据
//   try {
//     const res = await addTypeAccredit(formData);
//     if (res.code == '1') {
//       ElMessage.success(res.message);
//       emit("close");
//     }
//   } catch (error) {
//     console.error("保存失败:", error);
//   }
// };
// 递归获取父级节点
const getParentKeys = (treeData, key, path = []) => {
  for (const node of treeData) {
    if (node.id === key) {
      return path;
    }
    if (node.children) {
      const result = getParentKeys(node.children, key, [...path, node.id]);
      if (result) return result;
    }
  }
  return null;
};
const saveBtn = async () => {
  // 获取选中的停车场节点
  const parkingCheckedKeys = deptTree.value ? deptTree.value.getCheckedKeys() : [];
  const parkingParentKeys = [];
  parkingCheckedKeys.forEach((key) => {
    const parents = getParentKeys(parkingOptions.value, key); // deptTreeData 是停车场树形数据
    if (parents) {
      parkingParentKeys.push(...parents);
    }
  });

  // 获取选中的车辆类型节点
  const typeCheckedKeys = areaTree.value ? areaTree.value.getCheckedKeys() : [];
  const typeParentKeys = [];
  typeCheckedKeys.forEach((key) => {
    const parents = getParentKeys(parkingOptions.value, key); // areaTreeData 是车辆类型树形数据
    if (parents) {
      typeParentKeys.push(...parents);
    }
  });

  // 合并选中节点和父级节点
  formData.parkingIds = [...new Set([...parkingCheckedKeys, ...parkingParentKeys])]; // 去重
  formData.typeIds = [...new Set([...typeCheckedKeys, ...typeParentKeys])]; // 去重

  // 验证表单数据
  if (formData.parkingIds.length < 1) {
    ElMessage.warning("请选择停车场");
    return;
  }
  if (formData.typeIds.length < 1) {
    ElMessage.warning("请选择车辆类型");
    return;
  }

  // 提交表单数据
  try {
    const res = await addTypeAccredit(formData);
    if (res.code == '1') {
      ElMessage.success(res.message);
      emit("close");
    }
  } catch (error) {
    console.error("保存失败:", error);
  }
};

// 生命周期：组件挂载时调用
onMounted(() => {
  getParking();
  getType();
});
defineExpose({
  saveBtn,
});
</script>

<style scoped lang="scss">
:deep(.el-checkbox .el-checkbox__input) {
  margin-right: 6px;
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-date-editor.el-input) {
  width: 100%;
}
.dep-card1{
  height: calc(100vh - 400px);
  overflow-y: auto;
}
.dep-card2{
  height: calc(100vh - 400px);
  overflow-y: auto;
}
</style>