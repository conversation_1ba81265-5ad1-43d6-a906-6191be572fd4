<template>
    <el-input
      type="text"
      :model-value="modelValue"
      @input="handleInput"
      @blur="handleBlur"
      :placeholder="customPlaceholder || placeholderText"
      clearable
    />
  </template>
  
  <script setup>
  import { defineProps, defineEmits, computed } from 'vue'
  
  const props = defineProps({
    modelValue: [String, Number],
    inputType: {
      type: String,
      default: 'integer',
      validator: v => ['integer', 'decimal'].includes(v)
    },
    customPlaceholder: {
      type: String,
      default: ''
    }
  })
  
  const emit = defineEmits(['update:modelValue'])
  
  const placeholderText = computed(() => 
    props.inputType === 'integer' ? '请输入整数' : '请输入小数并保留两位'
  )
  
  const handleInput = (value) => {
    let processed = value
    
    if (props.inputType === 'integer') {
      // 整数模式：只允许数字
      processed = processed.replace(/\D/g, '')
    } else {
      // 小数模式：允许数字和小数点
      processed = processed
        .replace(/[^\d.]/g, '')           // 移除非数字和小数点
        .replace(/(\..*)\./g, '$1')       // 禁止多个小数点
        .replace(/^\./g, '')              // 禁止小数点开头
        .replace(/^0+(\d)/, '$1')         // 禁止前导零
        .replace(/(\.\d{2}).*/g, '$1')    // 限制两位小数
    }
  
    emit('update:modelValue', processed)
  }
  
  const handleBlur = () => {
    if (props.inputType === 'decimal' && props.modelValue) {
      let value = props.modelValue.toString()
      const [intPart = '', decPart = ''] = value.split('.')
      
      // 补全两位小数
      const formatted = `${intPart}.${decPart.padEnd(2, '0').slice(0, 2)}`
      emit('update:modelValue', value.includes('.') ? formatted : `${value}.00`)
    }
  }
  </script>