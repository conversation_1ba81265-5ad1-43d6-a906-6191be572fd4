import request from '@/utils/request'
import { apiUrl } from '@/utils/config';
export class screenIndex {
  // 查询 取餐人员
  static findParkingRule(data, params) {
    return request({
      url: `park${apiUrl}/major/parkChargingRules/findChargingRules`,
      method: "post",
      data: {...data,...params},
      params: params
    })
  }

  // 根据 id 查询
  static findParkingRuleById(data) {
    return request({
      url:  `park${apiUrl}/major/parkChargingRules/findRulesById`,
      method: "post",
      data: data
    })
  }


  // 保存停车规则
  static saveParkingRule(data) {
    return request({
      url: `park${apiUrl}/major/parkChargingRules/addChargingRules`,
      method: "post",
      data: data
    })
  }

  // 修改停车规则
  static updateParkingRule(data) {
    return request({
      url:  `park${apiUrl}/major/parkChargingRules/updateChargingRules`,
      method: "post",
      data: data
    })
  }


  // 删除停车规则
  static deleteParkingRule(data) {
    return request({
      url: `park${apiUrl}/major/parkChargingRules/deleteChargingRules`,
      method: 'post',
      data: data
    })
  }



  //查询车牌
  static findPlateForRule(data) {
    return request({
      url: `park${apiUrl}/majorParking/parkingRule/stUserCar`,
      method: 'post',
      data:data
    })
  }

  //获取停车规则id
  static getParkingRuleId() {
    return request({
      url:`park${apiUrl}/majorParking/parkingRule/getParkingRuleId`,
      method: 'post'
    })
  }


  static findparkinglot(param,data) {
    return request({
      url: `park${apiUrl}/major/parkChargingRules/findparkinglot`,
      method: 'post',
      data:{...data,...param},
      params: param
    })
  }
  static findparkinglotTree(data) {
    return request({
      url: `park${apiUrl}/majorParking/parkingLot/findparkinglotTree`,
      method: 'post',
      data:{},
    })
  }

  
static selectUserList(data) {
    return request({
      url: `user${apiUrl}/area/selectUserList`,  
      method: "post",
      data: data,
    })
  }


}
