<template>
    <div  class="dialog-box"
    :class="popupType === 'view' ? 'dialog-box-view' : 'dialog-box-edit'">
        <!-- 使用组合式API重构的表单 -->
        <el-form
          ref="ruleformRef"
          :model="formData"
          label-width="80px"
        >
          <!-- 查看模式专用字段 -->
            <el-row>
              <el-col :span="24">
                <el-form-item label="人员姓名" prop="nickName">
                  <div>{{ rowData.nickName || "" }}</div>
                </el-form-item>
              </el-col>
            </el-row>
          <el-row>
              <el-col :span="24">
                <el-form-item   label="人员部门" prop="deptName">
                  <div >{{ rowData.deptName||"" }}</div>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="24">
                <el-form-item   label="手机号码" prop="telephone">
                  <div >{{ rowData.telephone||"" }}</div>
                </el-form-item>
              </el-col>
            </el-row>
        
        
        </el-form>
      </div> 
  
  
  </template>
  
  <script setup>
  import { ref, computed, onMounted } from 'vue'

  
  // 属性定义
  const props = defineProps({
    closeBtn: Function,
    popupType: {
      type: String,
      default: 'view'
    },
    rowData: {
      type: Object,
      default: () => ({})
    }
  })

  


  // 生命周期钩子
  onMounted(() => {

  })
  
  
  </script>
  
  <style scoped lang="scss">
  
  </style>