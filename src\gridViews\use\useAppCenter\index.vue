<template>
  <el-card :class="bgColor === true ?'app-card':'unshow-app-card'" shadow="never" style="width:100%;height:100%">
    <template #header v-if="showTitle">
      <span class="card-title">
        <div class="title">应用中心</div>
      </span>
    </template>
    <div class="app-container">
      <div class="screen">
        <span class="select-prop">筛选系统</span>
        <el-select v-model="form.categoryName" clearable placeholder="请选择">
          <el-option v-for="item in titleList" :key="item.value" :label="item.value" :value="item.value"/>
        </el-select>
      </div>
      <el-empty v-if="list == null && loading" :image-size="200" style="margin-top: 50px" />

      <template v-for="item in list">
        <el-card
            v-if="list!==null && (!form.categoryName)?true:(form.categoryName===item.categoryName)"
            v-loading="loading"
            element-loading-text="加载中"
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.8)"
            shadow="never"
            class="app-card"
        >
          <template #header>
          <span class="card-title">
            <span><el-icon class="icon iconfont icon-shuxian"></el-icon>{{ item.categoryName }}</span>
          </span>
          </template>
          <div class="apps">
            <div v-for="citem in item.apps" :key="citem.id" class="app-item" @click="toApp(citem)">
              <img class="app-item-logoTemp" :src="citem.logoTemp" />
              <span>{{ citem.name }}</span>
            </div>
          </div>
        </el-card>
      </template>
    </div>
  </el-card>
</template>

<script setup name="UseAppCenter">
import {selectRoleApps} from "@/api/extend/application";
import {getInfo} from "@/api/login";
import {getToken} from "@/utils/auth";
import ssoCrypto from "@/utils/ssoCrypto";
import {useRoute} from "vue-router";

const {proxy} = getCurrentInstance()
const route = useRoute()
const props = defineProps({
  showTitle: Boolean,
  bgColor: Boolean
})

const loading = ref(true)
const list = ref([])
const titleList = ref([])
const form = ref({
  categoryName: ""
})

function getList() {
  loading.value = true;
  selectRoleApps("2").then((res) => {
    if (res.code === "1") {
      list.value = res.data;
      list.value.forEach((item) => {
        titleList.value.push({value: item.categoryName});
      });
      loading.value = false;
    }
  });
}

const toApp = (item) => {
  let url = item.url;
  // 判断是否单点
  if(item.urlType === 0){
    window.open(url)
  }else{
    getInfo({ token: getToken() }).then((res) => {
          const usrs = url.split("?");
          console.log(`未加密：${res.loginName}:${res.tenantId}`);
          console.log(ssoCrypto(`${res.loginName}:${res.tenantId}`));
          window.open(
              `${usrs[0]}?${usrs.length > 1 ? usrs[1] + "&" : ""}mmy=${ssoCrypto(
                  `${res.loginName}:${res.tenantId}`
              )}`
          );
        })
        .catch((error) => {
          proxy.$modal.msg({
            message: "居中的文字",
            center: true,
          });
        });
  }
}

getList();
</script>

<style scoped lang="scss">
.app-card {
  margin: 0px 0px 10px 0px;
  background-color: #fff;
  overflow: auto;
  .app-container {
    max-width: 1443px;
    margin: 0 auto;
  }
  :deep(.screen) {
    padding: 10px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    span {
      margin-right: 10px;
      font-size: 14px;
      width: 60px;
    }
    .el-select {
      width: 240px
    }
  }

  .card-title {
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .title {
      font-size: 24px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #646464;
    }
    .tab {
      display: flex;
      align-items: center;
      .tab-items {
        height: 32px;
        font-size: 14px;
        line-height: 32px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #646464;
        text-align: center;
        cursor: pointer;
        border: 0px;
        margin-left: 20px;
        color: #ffffff;
        background: #4d85e2;
      }
      .tab-item {
        height: 32px;
        font-size: 14px;
        line-height: 32px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #646464;
        text-align: center;
        cursor: pointer;
        background: #fff;
        border: 0px;
        margin-left: 20px;
      }
      .tab-item:focus {
        height: 32px;
        font-size: 14px;
        line-height: 32px;
        text-align: center;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #ffffff;
        background: #4d85e2;
      }
    }
  }
  .apps {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;

    .app-item {
      padding: 40px;
      flex-basis: 30%;
      background-color: #ffffff;
      margin: 15px;
      border-radius: 10px;
      display: flex;
      flex-direction: row;
      align-items: center;

      > span {
        font-weight: bold;
        margin-left: 20px;
      }

      .app-item-logoTemp {
        width: 50px;
        height: 50px;
      }

      &:hover {
        box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
      }

      cursor: pointer;
    }
  }
}
.unshow-app-card {
  margin: 0px 0px 10px 0px;
  background-color: #fff;
  border: 0px;

  .app-container {
    max-width: 1443px;
    margin: 0 auto;
  }
  :deep(.screen) {
    padding: 10px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    span {
      margin-right: 10px;
      font-size: 14px;
      width: 60px;
    }
    .el-select {
      width: 240px;
    }
  }

  .card-title {
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .title {
      font-size: 24px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #646464;
    }
    .tab {
      display: flex;
      align-items: center;
      .tab-items {
        height: 32px;
        font-size: 14px;
        line-height: 32px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #646464;
        text-align: center;
        cursor: pointer;
        border: 0px;
        margin-left: 20px;
        color: #ffffff;
        background: #4d85e2;
      }
      .tab-item {
        height: 32px;
        font-size: 14px;
        line-height: 32px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #646464;
        text-align: center;
        cursor: pointer;
        background: #fff;
        border: 0px;
        margin-left: 20px;
      }
      .tab-item:focus {
        height: 32px;
        font-size: 14px;
        line-height: 32px;
        text-align: center;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #ffffff;
        background: #4d85e2;
      }
    }
  }
  .apps {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;

    .app-item {
      padding: 40px;
      flex-basis: 30%;
      background-color: #ffffff;
      margin: 15px;
      border-radius: 10px;
      display: flex;
      flex-direction: row;
      align-items: center;

      > span {
        font-weight: bold;
        margin-left: 20px;
      }

      .app-item-logoTemp {
        width: 50px;
        height: 50px;
      }

      &:hover {
        box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
      }

      cursor: pointer;
    }
  }
}
</style>
