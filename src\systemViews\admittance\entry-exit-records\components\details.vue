<!--出入记录内部员工详情 -->

<template>
    <el-form ref="ruleform" :model="formData">
      <div class="user_info">
        <div class="user_info_content">
          <div class="user_name">
            <div class="user_name_left">{{ formData.nickName }}</div>
            <div class="user_ht" v-if="formData.typePage=='internal'">
              {{ $formatDictLabel(formData.staffType, staff_type) }}
            </div>
            <div class="user_ht" v-if="formData.typePage=='visitor'">
              {{ '访客' }}
            </div>
          </div>
          <div class="user_fg">
            <img src="@/assets/images/user_fg.png" alt="" />
          </div>
          <div class="user_p_d">
            <div class="user_dept" v-if="formData.typePage=='internal'">{{ formData.deptName }}</div>
            <div class="user_dept" v-if="formData.typePage=='visitor'">{{ formData.visitorDeptName }}</div>
            <div class="user_phone">通行时间：{{ formData.createTime }}</div>
          </div>
          <el-row>
            <el-col :span="12">
              <div class="user_one_info">
                <div class="user_one_info_left">区域名称：</div>
                <div class="user_one_info_right">{{ formData.areaName }}</div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="user_one_info">
                <div class="user_one_info_left">设备名称：</div>
                <div class="user_one_info_right">
                  {{ formData.equipmentName }}
                </div>
              </div>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <div class="user_one_info">
                <div class="user_one_info_left">动作类型：</div>
                <div class="user_one_info_right">
                  {{ $formatDictLabel(formData.actionType, action_type) }}
                </div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="user_one_info">
                <div class="user_one_info_left">验证方式：</div>
                <div class="user_one_info_right">
                  {{ $formatDictLabel(formData.verification, verification) }}
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
      <div class="user_info_img">
        <PreviewImage
              :photo-id="formData.attachmentId"
              v-if="formData.attachmentId"
            ></PreviewImage>
      </div>
    </el-form>
</template>

<script setup>
import { ref, computed, watch ,getCurrentInstance} from 'vue';
import { apiUrl } from "@/utils/config";
const props = defineProps({

  rowData: {
    type: Object,
    default: () => ({}),
  },
});
const formData = ref({ ...props.rowData });

// Watch for prop changes
watch(
  () => props.rowData,
  (val) => {
    formData.value = { ...val };
  },
  { immediate: true, deep: true }
);

const { proxy } = getCurrentInstance();
const { staff_type,action_type,verification } = proxy.useDict(
    "staff_type", "action_type","verification"
);




</script>

<style scoped lang="scss">

.user_info_img {
  margin: 10px auto;
  width: 300px;
  height: 450px;
  background: url("@/assets/images/user_xk.png") no-repeat center center;
  background-size: 100% 100%;
  padding: 10px;
  box-sizing: border-box;
  .el-image {
    width: 100%;
    height: 100%;
  }
}
.user_info {
  display: flex;
  .user_info_content {
    padding-left: 30px;
    flex: 1;
    .user_fg {
      margin-top: 15px;
    }
    .user_name {
      display: flex;
      align-items: center;
      .user_name_left {
        font-size: 20px;
        font-weight: 600;
        color: #333;
        margin-right: 25px;
      }
      .user_ht {
        padding: 0px 15px;
        height: 25px;
        line-height: 25px;
        background: #ffa349;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 20px;
      }
    }
    .user_p_d {
      display: flex;
      align-items: center;
      margin-top: 15px;
      .user_dept {
        min-height: 28px;
        line-height: 28px;
        background: #238ff0;
        padding: 0px 15px;
        color: #fff;
        font-size: 14px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
      }
      .user_phone {
        min-height: 28px;
        line-height: 28px;
        background: #238ff0;
        padding: 0px 15px;
        color: #fff;
        font-size: 14px;
        display: flex;
        align-items: center;
        min-width: 150px;
      }
    }
    .user_one_info {
      margin-top: 15px;
      display: flex;
      align-items: center;
      .user_one_info_left {
        color: #000;
        font-size: 15px;
      }
      .user_one_info_right {
        color: #000;
        font-size: 15px;
        flex: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}

</style>
