<!-- 临时车辆 -->
<template>
  <div class="container-table-box">
      <el-card>
    <el-row :gutter="24">
      <el-col :span="24" :xs="24">
        <dialog-search
          @getList="getList"
          formRowNumber="4"
          :columns="tabelForm.columns"
          :isShowRightBtn="$checkPermi(['parking:temporary:list'])"
        >
          <template #formList>
            <el-form
              :model="queryParams"
              ref="queryForm"
              :inline="true"
              label-width="80px"
            >
              <el-form-item label="车牌号码" prop="plateNo">
                <el-input
                  v-model="queryParams.plateNo"
                  placeholder="请输入车牌号码"
                  clearable
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item label="人员姓名" prop="staffName">
                <el-input
                  v-model="queryParams.staffName"
                  placeholder="请输入人员姓名"
                  clearable
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item label="车辆状态" prop="carStatus">
                <el-select
                  @change="getList"
                  v-model="queryParams.carStatus"
                  placeholder="请选择车辆状态"
                  clearable
                >
                  <el-option
                    v-for="(item, index) of car_status"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="车辆编组" prop="vehicleType">
                <el-select @change="getList" v-model="queryParams.vehicleType" placeholder="请选择车辆编组" clearable>
                  <el-option v-for="(item, index) of parkingRuleVehicleTypeList" :key="index" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
              <el-form-item label="车辆类型" prop="carType">
                <el-select @change="getList" v-model="queryParams.carType" placeholder="请选择车辆类型" clearable>
                  <el-option v-for="(item, index) of car_type" :key="index" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
              <el-form-item label="车牌颜色" prop="plateColor">
                <el-select @change="getList" v-model="queryParams.plateColor" placeholder="请选择车牌颜色" clearable>
                  <el-option v-for="(item, index) of plate_color" :key="index" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
              <el-form-item label="车辆规格" prop="vehicleSpecification">
                <el-select @change="getList" v-model="queryParams.vehicleSpecification" placeholder="请选择车辆规格" clearable>
                  <el-option v-for="(item, index) of vehicle_specification" :key="index" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-form>
          </template>
          <template #searchList>
            <el-button type="primary" icon="Search" @click="handleQuery" v-hasPermi="['parking:temporary:list']">
              搜索
            </el-button>
            <el-button icon="Refresh" @click="resetQuery" v-hasPermi="['parking:temporary:list']"> 重置 </el-button>
          </template>
          <template #searchBtnList>
            <el-button type="primary" icon="Plus" @click="handleAdd" v-hasPermi="['parking:temporary:add']">
              新增
            </el-button>
            <el-button icon="Download" @click="handleExport" v-hasPermi="['parking:temporary:export']">
              导出
            </el-button>
            <el-button icon="Upload" @click="handleImport" v-hasPermi="['parking:temporary:import']">
              导入
            </el-button>

            <el-button icon="Download" @click="importTemplate" v-hasPermi="['parking:temporary:import']"> 
              模板导出
            </el-button>
          </template>
        </dialog-search>

        <PublicTable
          ref="publictable"
          :rowKey="tabelForm.tableKey"
          :tableData="userList"
          :columns="tabelForm.columns"
          :configFlag="tabelForm.tableConfig"
          :pageValue="pageParams"
          :total="total"
          :getList="getList"
        >
          <!-- 有效期限 -->
          <template #validDate="{ scope }">
            {{ scope.row.validDate || "长期有效" }}
          </template>

          <!-- 手机号码 -->
          <template #telephone="{ scope }">
            <div>
              {{
                scope.row.cellphone
                  ? scope.row.cellphone.replace(
                      /(\d{3})(\d{4})(\d{4})/,
                      "$1****$3"
                    )
                  : "-"
              }}
            </div>
          </template>

          <!-- 车辆类型 -->
          <template #vehicleoperation="{ scope }">
            <div>
              {{
                fromatComonDict(
                  scope.row.vehicleType,
                  parkingRuleVehicleTypeList
                )
              }}
            </div>
          </template>

          <!-- 操作列 -->
          <template #operation="{ scope }">
            <el-button
              link
              icon="View"
              type="primary"
              title="查看"
              v-hasPermi="['parking:temporary:list']"
              @click="handleView(scope.row)"
            >
            </el-button>
            <el-button
              link
              icon="Edit"
              type="primary"
              title="修改"
              v-hasPermi="['parking:temporary:edit']"
              @click="handleUpdate(scope.row)"
            >
            </el-button>
            <el-button
              link
              icon="Delete"
              type="primary"
              title="删除"
               v-hasPermi="['parking:temporary:remove']"
              @click="handleDelete(scope.row)"
            >
            </el-button>
          </template>
        </PublicTable>

        <DialogBox
          :visible="open"
          :dialogWidth="dialogWidth"
          @save="submits"
          @cancellation="cancellation"
          @close="close"
          :dialogFooterBtn="dialogFooterBtn"
          CloseSubmitText="取消"
          SaveSubmitText="确定"
          :dialogTitle="headerTitle"
        >
          <template #content>
            <div
              class="dialog-box dialog-box-edit"
              v-if="popupType == 'upload'"
            >
              <el-form
                :model="queryParams"
                ref="queryForm"
                :inline="true"
                label-width="68px"
              >
                <el-form-item label="租户" prop="tenantId" style="width: 100%">
                  <TenantSelect
                    v-model="extraUploadFileData.tenantId"
                  ></TenantSelect>
                </el-form-item>

                <el-form-item
                  label="截止日期"
                  prop="validDate"
                  style="width: 100%"
                >
                  <el-date-picker
                    v-model="extraUploadFileData.validDate"
                    value-format="YYYY-MM-DD"
                    type="date"
                    placeholder="选择截止日期"
                  />
                </el-form-item>
              </el-form>
            </div>
            <UploadFile
              v-if="popupType == 'upload'"
              ref="uploadRef"
              :uploadData="extraUploadFileData"
              :action="upload.url"
              :limit="1"
              :accept="'.xlsx, .xls'"
              :disabled="upload.isUploading"
              :auto-upload="false"
              tip="提示：仅允许导入“xls”或“xlsx”格式文件！<br>上传数据必须包含车牌号码、车牌颜色、人员姓名、手机号码！"
              @file-success="handleFileSuccess"
              @file-error="handleFileError"
            />

            <userCarEdit
              ref="userCarEditRef"
              v-if="
                popupType == 'add' || popupType == 'edit' || popupType == 'view'
              "
              @submitClose="submitClose"
              :row-data="rowData"
              :list="parkingRuleVehicleTypeList"
              :popup-type="popupType"
            />
          </template>
        </DialogBox>

        <DialogBox :visible="open2" dialogWidth="70%"   @cancellation="cancellation2"
          @close="close2" :dialogFooterBtn="false" CloseSubmitText="取消" SaveSubmitText="确定"
          dialogTitle="查看导入记录">
          <template #content>
            <views :rowData="rowData2"></views>
           
          </template>
        </DialogBox>
      </el-col>
    </el-row>
    </el-card>
  </div>
</template>

  <script setup name="TemporaryCar">
import UploadFile from "@/components/UploadFile/index";
import views from "./components/view";
import { reactive, ref, onMounted, getCurrentInstance } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import userCarEdit from "./components/userCarEdit.vue";
import { screenIndex } from "@/api/majorParking/userCar";
import { getDictInfo } from "@/api/system/dict/data";
import useUserStore from "@/store/modules/user";
import { apiUrl } from "@/utils/config";
import { formatMinuteTime } from "@/utils";
const { proxy } = getCurrentInstance();
// 定义组件属性和事件
defineProps({
  // 定义组件属性
});
defineEmits(["updateRow"]);
// 车辆编组列表
const parkingRuleVehicleTypeList = ref([]);
// 车辆类型列表
const carType = ref([]);
// 字典数据
const { car_status, vehicle_specification,plate_color ,car_type} = proxy.useDict(
  "car_status",
  "vehicle_specification",
  "plate_color",
    "car_type"
);
// 表格配置
const tabelForm = ref({
  tableKey: "1",
  isShowRightToolbar: true,
  showSearch: true,
  columns: [
    {
      fieldIndex: "plateNo",
      label: "车牌号码",
      resizable: true,
      visible: true,
      sortable: true,
      minWidth: "120px", //最小宽度%
    },
    {
      fieldIndex: "vehicleType",
      label: "车辆编组",
      resizable: true,
      visible: true,
      sortable: true,
      minWidth: "160px", //最小宽度%
      type: "dict",
      dictList: [],
    },
    {
      fieldIndex: "carType",
      label: "车辆类型",
      resizable: true,
      visible: true,
      sortable: true,
      minWidth: "160px", //最小宽度%
      type: "dict",
      dictList: car_type,
    },
    {
      fieldIndex: "staffName",
      label: "人员姓名",
      resizable: true,
      minWidth: "120px", //最小宽度%
      visible: true,
      sortable: true,
    },
    {
      fieldIndex: "orgName",
      label: "人员部门",
      resizable: true,
      minWidth: "120px", //最小宽度%
      visible: true,
      sortable: true,
    },
    {
      fieldIndex: "cellphone",
      label: "手机号码",
      resizable: true,
      visible: true,
      minWidth: "120px", //最小宽度%
      sortable: true,
      slotname: "telephone",
    },
    {
      fieldIndex: "carStatus",
      label: "车辆状态",
      resizable: true,
      visible: true,
      sortable: true,
      minWidth: "120px", //最小宽度%
      type: "dict",
      dictList: car_status,
    },
    {
      fieldIndex: "validDate",
      slotname: "validDate",
      label: "有效期限",
      resizable: true,
      minWidth: "120px", //最小宽度%
      visible: true,
      sortable: true,
    },
    {
      fieldIndex: "carBrand",
      label: "车辆品牌",
      resizable: true,
      minWidth: "120px", //最小宽度%
      visible: true,
      sortable: true,
    },
    {
      fieldIndex: "carModel",
      label: "品牌型号",
      resizable: true,
      visible: true,
      minWidth: "120px", //最小宽度%
      sortable: true,
    },
    {
      fieldIndex: "vehicleSpecification",
      label: "车辆规格",
      resizable: true,
      visible: true,
      sortable: true,
      minWidth: "120px", //最小宽度%
      type: "dict",
      dictList: vehicle_specification,
    },
    {
      fieldIndex: "carColor",
      label: "车辆颜色",
      resizable: true,
      minWidth: "120px", //最小宽度%
      visible: true,
      sortable: true,
    },
    {
      fieldIndex: "plateColor",
      label: "车牌颜色",
      resizable: true,
      minWidth: "110px", //最小宽度%
      visible: true,
      sortable: true,
      type: "dict",
      dictList: plate_color,
    },
    {
      fieldIndex: "remark",
      label: "车辆备注",
      resizable: true,
      minWidth: "120px", //最小宽度%
      visible: true,
      sortable: true,
      align: "left",
    },
    {
      label: "操作",
      slotname: "operation",
      width: "120",
      fixed: "right",
      visible: true,
    },
  ],
  tableConfig: {
    needPage: true,
    index: true,
    selection: false,
    reserveSelection: false,
    indexFixed: false,
    selectionFixed: false,
    indexWidth: "50",
    loading: false,
    showSummary: false,
    height: null,
  },
});

// 查询参数
const pageParams = ref({
  pageNum: 1,
  pageSize: 10,
});

// 表格数据
const userList = ref([]);

// 日期范围
const dateRange = ref([]);
const userStore = useUserStore();
// 表格配置
// 用户导入参数
const upload = reactive({
  title: "",
  isUploading: false,
  updateSupport: 0,
  url: `${
    import.meta.env.VITE_APP_BASE_API
  }/park${apiUrl}/majorParking/userCar/importDataForForTemporary`,
});

const extraUploadFileData = ref({
  validDate: "",
  tenantId: userStore.userInfo.tenantId,
});

// 表格总数
const total = ref(0);
// 是否显示弹窗底部按钮
const dialogFooterBtn = ref(true);
// 弹窗标题
const headerTitle = ref("");

// 点击行信息
const rowData = ref({});
const rowData2 = ref([]);
// 是否显示弹窗
const open = ref(false);
const open2 = ref(false);

// 弹窗宽度
const dialogWidth = ref("");

// 弹窗类型
const popupType = ref("");

// form
const queryParams = ref({});

// upload ref

const uploadRef = ref(null);

// 修改 ref

const userCarEditRef = ref(null);
// 主要方法
// 获取车辆类型
const getVehicleType = async () => {
  const res = await getDictInfo("parking_rule_vehicle_type");
  parkingRuleVehicleTypeList.value = res.data
  parkingRuleVehicleTypeList.value.forEach((item) => {
    item.value = item.dictValue;
    item.label = item.dictLabel;
  });
  tabelForm.value.columns[1].dictList = parkingRuleVehicleTypeList.value;

  getList();
};
// 获取车辆类型
const getCarType = async () => {
  const res = await getDictInfo("car_type");
  carType.value = res.data
  carType.value.forEach((item) => {
    item.value = item.dictValue
    item.label = item.dictLabel
  })
  getList();
};

// 列表请求
const getList = () => {
  tabelForm.value.tableConfig.loading = true;
  screenIndex
    .stTemporaryCar(pageParams.value, queryParams.value)
    .then((res) => {
      userList.value = res.data.records;
      total.value = res.data.total;
      tabelForm.value.tableConfig.loading = false;
    });
};

// 查询
const handleQuery = () => {
  pageParams.value.pageNum = 1;
  getList();
};

// 重置
const resetQuery = () => {
  dateRange.value = [];
  queryParams.value = {};
  pageParams.value.pageNum = 1;
  pageParams.value.pageSize = 10;
  getList();
};

// 新增
const handleAdd = () => {
  headerTitle.value = "新增临时车辆";
  popupType.value = "add";
   dialogWidth.value = '50%'
  dialogFooterBtn.value = true;
  open.value = true;
};

// 修改
const handleUpdate = (row) => {
  headerTitle.value = "修改临时车辆";
  popupType.value = "edit";
  dialogFooterBtn.value = true;
   dialogWidth.value = '50%'
  rowData.value = row;
  open.value = true;
};

// 查看详情
const handleView = (row) => {
  const formattedRow = {
    ...row,
    telephone: row.telephone
      ? row.telephone.replace(/(\d{3})(\d{4})(\d{4})/, "$1****$3")
      : "-",
  };
  dialogFooterBtn.value = false;
  headerTitle.value = "查看临时车辆";
  rowData.value = formattedRow;
   dialogWidth.value = '50%'
  popupType.value = "view";
  open.value = true;
};

// 删除
const handleDelete = (row) => {
  ElMessageBox.confirm("确认要删除该条数据吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      screenIndex.deleteUserCarById(row.id).then((res) => {
        if (res.code == "1") {
          ElMessage.success("删除成功");
          getList();
        } else {
          ElMessage.error(res.msg);
        }
      });
    })
    .catch(() => {
      // 用户取消删除
    });
};

// 文件上传成功处理
const handleFileSuccess = (response) => {
  if (response.response.code == '1') {
    ElMessage.success("导入成功");
    setTimeout(() => {
      upload.isUploading = false;
      open.value = false;
      open2.value = true
      rowData2.value = response.response.data

      getList();
    }, 800);
  } else {
    ElMessage.error(response.response.message);
  }
};

// 文件上传失败处理
const handleFileError = () => {
  ElMessage.error("导入失败");
};

// 导入
const handleImport = () => {
  popupType.value = "upload";
  headerTitle.value = "用户导入";
  dialogWidth.value = "400px";
  open.value = true;
};

// 导出
const handleExport = () => {
  proxy.download(
   `park${apiUrl}/majorParking/userCar/exportForTemporary`,
    {
      ...queryParams.value,
    },
    `临时车辆表_${formatMinuteTime(new Date())}.xlsx`
  );
};

// 模板导出
const importTemplate = () => {
  proxy.download(
   `park${apiUrl}/majorParking/userCar/importTemplate`,
    {
      ...queryParams.value,
    },
    `临时车辆导入模版_${formatMinuteTime(new Date())}.xlsx`
  );
};

// 提交关闭
const submitClose = () => {
  open.value = false;
  setTimeout(() => {
    getList();
  }, 100);
};

// 车辆类型格式化方法
const fromatComonDict = (value, list) => {
  const item = list.find((item) => item.value === value);
  return item ? item.label : "";
};
/** 点击提交 */
const submits = () => {
  if (popupType.value == "upload") {
    uploadRef.value.submitFileForm();
  }

  if (popupType.value == "add" || popupType.value == "edit") {
    userCarEditRef.value.saveBtn();
  }
};
/** 点击取消保存 */
const cancellation = (val) => {
  close(false);
};
/** 关闭弹窗 */
const close = (val) => {
  open.value = val;
};

/** 点击取消保存 */
const cancellation2 = (val) => {
  close2(false);
};
/** 关闭弹窗 */
const close2 = (val) => {
  open2.value = val;
};

// 生命周期钩子：组件挂载时调用
onMounted(() => {
  getVehicleType();
  getCarType();
});
</script>

  <style scoped>
/* 添加自定义样式 */
</style>
