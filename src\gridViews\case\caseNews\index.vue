<template>
  <div class="big-common-box-one">
  <el-card class="notice-card" v-loading="caseNews" shadow="never">
    <template #header>
      <div class="common-header">
        <div class="left-header-text">新闻资讯</div>
        <div class="flex-1"/>
        <div class="header-right-btn"  @click="moreExt('business_news')">
          更多
        </div>
      </div>
    </template>
    <div class="home-card-body">
      <div class="left-content">
        <div class="top">
          <img v-if="listNO1.coverUrl" :src="listNO1.coverUrl" alt="图片加载中..." />
        </div>

        <div  v-if="listNO1">
          <router-link :to="portalIndex+`/detail/${listNO1.programaType}/${listNO1.noticeId}`" class="bottom" target="_blank">
          <div class="time" v-if="listNO1.createDate">
            <div class="data">{{ parseTime(listNO1.createDate, "{d}") }}</div>
            <div class="year">{{ listNO1.createDate.substring(0, 7) }}</div>
          </div>
          <div class="bottom-content">
            <div class="titles" :title="listNO1.noticeTitle">{{ listNO1.noticeTitle }}</div>
            <div class="contents" :title="listNO1.noticeContentText">{{ listNO1.noticeContentText }}</div>
          </div>
          </router-link>
        </div>
        <div v-else style="width:100%;height:100%;text-align:center;line-height:180px;color:white">暂无数据</div>
      </div>
      <div class="right-content">
        <div class="right-top" v-if="listNO1">
          <router-link :to="portalIndex+`/detail/${listNO1.programaType}/${listNO1.noticeId}`" class="right-top" target="_blank">
            <div class="bottom-content">
              <div class="titles" :title="listNO1.noticeTitle">{{ listNO1.noticeTitle }}</div>
              <div class="contents" :title="listNO1.noticeContentText">{{ listNO1.noticeContentText }}</div>
            </div>
            <div class="time" v-if="listNO1.createDate">
              <div class="data">{{ listNO1.createDate.substring(8, 10) }}</div>
              <div class="year">{{ listNO1.createDate.substring(0, 7) }}</div>
            </div>
          </router-link>
        </div>
        <div v-else style="width:100%;height:180px;text-align:center;line-height:180px;">暂无数据</div>

        <div class="right-bottom" v-if="list.length > 0">
          <div class="moreButton">
            <el-button class="exec" link type="primary" style="font-size: 12px" @click="moreExt(listNO1.programaType)">MORE</el-button>
          </div>
          <router-link class="mainContent" v-for="item in list" :key="item.noticeId"
                       :to="portalIndex+`/detail/${item.programaType}/${item.noticeId}`" target="_blank">
            <div class="main-title">
              「
              <span style="color: red">{{ item.noticeTypeName }}</span>
              」
              >
              <div class="titles" :title="item.noticeTitle">{{ item.noticeTitle }}</div>
            </div>
            <div class="main-time">{{ parseTime(item.createDate, "{y}-{m}-{d}") }}</div>
          </router-link>
        </div>
        <el-empty v-else :image-size="30" description="暂无数据" style="height:100%">
          <el-link icon="Refresh" @click="getNotice" :underline="false" type="primary">刷新</el-link>
        </el-empty>
      </div>
    </div>
  </el-card>
</div>
</template>

<script setup name="CaseNews">
import {ref} from "vue";
import useUserStore from "@/store/modules/user";
import {findList} from "@/api/release/programType";
import {selectTopPage} from "@/api/release/notice";
import {useRoute} from "vue-router";

const { proxy } = getCurrentInstance();
const userStore = useUserStore();
const router = useRouter();
const portalIndex = ref(proxy.portalIndex);
const caseNews = ref(false);
const queryParams = ref({
  tenantId: userStore.userInfo.customParam.tenantId,
  programaTypeCode: undefined,
  programaTypeName: undefined,
})

const list = ref([])
const listNO1 = ref({})
const programaList = ref([])

function getProgramaTypeList() {
  findList(queryParams.value).then((response) => {
    programaList.value = response.slice(0, 2);
  });
}

const getNotice = (item) => {
  caseNews.value = true;
  selectTopPage({
    programaType: "business_news",
    queryNum:5
  }).then((res) => {
    if (res.success) {
      list.value = res.data.records;
      if (res.data.records[0]) {
        listNO1.value = res.data.records[0];
        listNO1.value.createDate = proxy.parseTime(
            listNO1.value.createDate,
            "{y}-{m}-{d}"
        );
        caseNews.value = false;
        console.log('listNO1',listNO1.value)
      } else {
        caseNews.value = false;
      }
    } else {
      console.log(res.error);
    }
  });
}

const moreExt = (type) => {
  router.push(portalIndex.value+`/list/${type}`);
}

getNotice("system_notice");
getProgramaTypeList();
</script>

<style scoped lang="scss">
.big-common-box-one {
  background: #ffffff;
  border-radius: 4px;
  width: 100%;
  padding:0 10px 10px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); // 添加阴影效果
  transition: box-shadow 0.3s ease;
  box-sizing: border-box;

  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2); // 悬停时加深阴影
  }

  .el-carousel {
    height: 100%;
    border-radius: 4px; // 轮播图圆角
    overflow: hidden; // 防止图片溢出
  }

  .el-image {
    width: 100%;
    height: 280px; // 根据实际需求调整高度
    object-fit: cover; // 图片填充方式
  }
}
.notice-card {
  border: 0px;
  height: 100%!important;
  min-height:100%!important;
  background: #fff;
  :deep(.el-card__header) {
    padding: 0;
    height: 20px;
    border: 0px;
    margin: 0 0 10px 0px;
  }
  :deep(.el-card__body) {
    padding: 0;
  }
  .card-title {
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .title {
      font-size: 24px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #646464;
    }
    .tab {
      display: flex;
      align-items: center;
      .tab-items {
        height: 32px;
        font-size: 14px;
        line-height: 32px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #646464;
        text-align: center;
        cursor: pointer;
        border: 0px;
        margin-left: 20px;
        color: #ffffff;
        background: #4d85e2;
      }
      .tab-item {
        height: 32px;
        font-size: 14px;
        line-height: 32px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #646464;
        text-align: center;
        cursor: pointer;
        background: #fff;
        border: 0px;
        margin-left: 20px;
      }
      .tab-item:focus {
        height: 32px;
        font-size: 14px;
        line-height: 32px;
        text-align: center;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #ffffff;
        background: #c20000;
      }
    }
  }
  .home-card-body {
    width: 100%;
    height: 480px;
    display: flex;
    .left-content {
      width: 48%;
      height: 100%;
      background: rgba(194, 0, 0, 0.6);
      .top {
        width: 100%;
        height: 252px;
        img {
          width: calc(100% - 20px);
          margin-top: 10px;
          margin-left: 10px;
          height: 100%;
        }
      }
      .bottom {
        padding: 43px;
        height: 180px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .time {
          width: 20%;
          text-align: center;
          .data {
            font-size: 46px;
            font-family: Microsoft YaHei;
            font-weight: bold;
            color: #f7fafe;
            line-height: 28px;
          }
          .year {
            margin-top: 21px;
            font-size: 18px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #f7fafe;
            line-height: 28px;
          }
        }
        .bottom-content {
          width: 75%;
          .titles {
            width: 100%;
            word-break: keep-all;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-size: 18px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #f7fafe;
            border-bottom: 1px solid #fff;
            padding-bottom: 15px;
            margin-bottom: 15px;
          }
          .contents {
            text-overflow: ellipsis;

            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;

            display: -moz-box;
            -moz-line-clamp: 2;
            -moz-box-orient: vertical;

            overflow-wrap: break-word;
            word-break: break-all;
            white-space: normal;
            overflow: hidden;

            font-size: 14px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #f7fafe;
            line-height: 28px;
          }
        }
      }
    }
    .right-content {
      width: 52%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .right-top {
        width: 100%;
        height: 124px;
        display: flex;
        align-items: center;
        .bottom-content {
          width: 70%;
          margin-left: 70px;
          .titles {
            width: 100%;
            word-break: keep-all;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            border-bottom: 1px solid #ececec;
            padding-bottom: 15px;
            margin-bottom: 15px;
            font-size: 16px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #797979;
          }
          .contents {
            text-overflow: ellipsis;

            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;

            display: -moz-box;
            -moz-line-clamp: 2;
            -moz-box-orient: vertical;

            overflow-wrap: break-word;
            word-break: break-all;
            white-space: normal;
            overflow: hidden;

            font-size: 14px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #979797;
            line-height: 28px;
            opacity: 0.8;
          }
        }
        .time {
          width: 15%;
          margin-left: 10px;
          text-align: center;
          .data {
            font-size: 46px;
            font-family: Microsoft YaHei;
            font-weight: bold;
            color: #a6a6a6;
            line-height: 28px;
          }
          .year {
            margin-top: 21px;
            font-size: 18px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #a6a6a6;
            line-height: 28px;
          }
        }
      }
      .right-bottom {
        width: 100%;
        height: calc(100% - 106px);

        .moreButton {
          display: block;
          text-align: right;
        }
        .mainContent {
          display: flex;
          justify-content: space-between;
          flex-direction: row;
          margin-left: 70px;
          padding: 15px 0px;
          font-size: 16px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          color: #777777;
          border-bottom: 1px solid #ebebeb;
          .main-title {
            overflow: hidden;
            text-overflow: ellipsis;
            word-break: break-all;
            white-space: nowrap;
            flex-basis: 70%;
            display: flex;
            .titles {
              width: 100%;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
          .main-time {
            flex-basis: 20%;
            text-align: center;
          }
        }
      }
    }
  }
  .card-title {
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .icon {
      font-size: 18px;
      color: #419eee;
      padding: 0px 5px;
      -webkit-transition: font-size 0.25s linear, width 0.25s linear;
      -moz-transition: font-size 0.25s linear, width 0.25s linear;
      transition: font-size 0.25s linear, width 0.25s linear;
    }
    .exec {
      padding: 3px 0;
    }
  }
  .notice {
    display: flex;
    justify-content: space-between;
    flex-direction: row;
    padding: 4px 0;
    font-size: 14px;
    .notice-title {
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-all;
      white-space: nowrap;
      flex-basis: 70%;
    }
    .notice-time {
      flex-basis: 20%;
      text-align: right;
    }
  }
}
.header-right-btn {
  font-size: 13px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #9ea4aa;
  cursor: pointer;
}
</style>
