import request from '@/utils/request'

export function findPage(query) {
    return request({
        url: '/generator/datasource/findPage',
        method: 'post',
        data: query
    })
}

export function findOne(id) {
    return request({
        url: '/generator/datasource/findOne',
        method: 'get',
        params: {
            id
        }
    })
}

export function findList() {
    return request({
        url: '/generator/datasource/findList',
        method: 'get'
    })
}

export function testConn(data) {
    return request({
        url: '/generator/datasource/testConnection',
        method: 'post',
        data
    })
}

export function addDatasource(data) {
    return request({
        url: '/generator/datasource/add',
        method: 'post',
        data
    })
}

export function updateDatasource(data) {
    return request({
        url: '/generator/datasource/edit',
        method: 'post',
        data
    })
}

export function delDatasource(id) {
    return request({
        url: '/generator/datasource/delete',
        method: 'get',
        params: {
            id
        }
    })
}
