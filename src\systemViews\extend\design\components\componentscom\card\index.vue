<template>
  <div
    class="card"
    :style="{ backgroundImage: 'url(' + datas.config.bgImg + ')' }"
  >
    <uni-section title="卡片" type="line" style="padding-bottom: 1px">
      <uni-card title="基础卡片" sub-title="副标题" extra="额外信息" :thumbnail="logo" v-if="datas.config.picture">
        <span class="uni-body">这是一个带头像和双标题的基础卡片，此示例展示了一个完整的卡片。</span>
      </uni-card>
      <uni-card title="基础卡片" extra="额外信息" v-else>
        <span class="uni-body">这是一个基础卡片示例，此示例展示了一个标题加标题额外信息的标准卡片。</span>
      </uni-card>
    </uni-section>
    <!-- 删除组件 -->
    <slot name="deles" />
  </div>
</template>

<script>
import uniSection from "@/components/UniUI/uni-section"
import uniCard from "@/components/UniUI/uni-card";
import logo from "@/assets/logo/logo.png"
export default {
  name: 'card',
  components: {
    uniCard,
    uniSection
  },
  props: {
    datas: Object,
  },
  created(){
    console.log(this.datas,'--------card')
  },
  data() {
    return {
      extraIcon: {
        color: '#4cd964',
        size: '22',
        type: 'gear-filled'
      },
      logo: logo
    }
  }
}
</script>

<style scoped lang="less">
.card {
  position: relative;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  /* 默认导航 */
  .defaultNavigation {
    // overflow-x: scroll;
    justify-content: space-evenly;
    &::-webkit-scrollbar {
      height: 1px;
    }
    &::-webkit-scrollbar-thumb {
      background-color: #155bd4;
    }
    :deep(.el-collapse-item__header .el-collapse-item__wrap) {
      border-bottom: 0 !important;
    }
    /* 导航 */
    .navigationList {
      display: flex;
      flex-direction: column;
      align-items: center;
      img {
        margin-top: 5px;
        width: 45px;
        height: 45px;
      }
      p {
        font-size: 12px;
        margin-top: 5px;
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        text-align: center;
        box-sizing: border-box;
      }
    }
  }
}
.uni-padding-wrap {
  padding: 0 30px;
}
.content {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 70px;
  text-align: center;
}

.content-text {
  font-size: 14px;
  color: #666;
}
</style>
