<!-- 支付中心-账号管理 -->
 

<template>
    <div>
        <TabContainers :tabList="tabList" :activeName="activeName"></TabContainers>
    </div> 

</template>

<script setup name="accountManagement">
// tab切换
import TabContainers from '@/components/TabContainer/index';
import accountSettings from '@/systemViews/paymentCenter/account-management/account-settings'
import accountType from '@/systemViews/paymentCenter/account-management/account-type'
import accountMerging from '@/systemViews/paymentCenter/account-management/account-merging'
import paymentType from '@/systemViews/paymentCenter/account-management/payment-type'
import { ref } from "vue";
// 定义 tab 列表
const tabList = ref([
  {
    label: '账户设置',
    value: '01',
    component: accountSettings
  },
  {
    label: '账户类型',
    value: '02',
    component: accountType
  },
  {
    label: '账户合并设置',
    value: '03',
    component: accountMerging
  },
  // {
  //   label: '支付类型',
  //   value: '04',
  //   component: paymentType
  // }
])
// 默认激活的 tab
const activeName = ref('01')
</script>

<style scoped lang="scss">
:deep(.el-tabs .el-tabs__content .content) {
    height: calc(100vh - 250px);
}
</style>