import request from "@/utils/request";

// 查询分页
export function page(params) {
  return request({
    url: "/user/tenantApply/findPage",
    method: "get",
    params: params
  });
}

// 根据ID查询详细
export function getById(id) {
  return request({
    url: "/user/tenantApply/findOne",
    method: "get",
    params:{
      id
    }
  });
}

// 根据租户申请单号查询详情
export function getByTenantApplyNum(tenantApplyNum) {
  return request({
    url: "/user/tenantApply/findSysTenantApplyInfo/" + tenantApplyNum,
    method: "get"
  });
}

// 新增租户申请
export function add(data) {
  return request({
    url: "/user/tenantApply/add",
    method: "post",
    data
  });
}

// 修改用户
export function update(data) {
  return request({
    url: "/user/tenantApply/update",
    method: "post",
    data
  });
}

// 删除
export function del(id) {
  return request({
    url: "/user/tenantApply/" + id,
    method: "delete"
  });
}

// 审核通过
export function auditOk(data) {
  return request({
    url: "/user/tenantApply/auditOk",
    method: "post",
    data
  });
}

// 审核拒绝
export function auditReject(data) {
  return request({
    url: "/user/tenantApply/auditReject",
    method: "post",
    data
  });
}
