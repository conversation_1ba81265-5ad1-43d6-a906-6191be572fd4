<template>
  <div class="app-container" style="width:1443px;margin:0 auto">
    <el-breadcrumb separator="/" style="margin:20px 0">
      <el-breadcrumb-item :to="{ path: portalIndex }" replace
      >智慧园区平台</el-breadcrumb-item
      >
      <el-breadcrumb-item
      >
      <router-link :to="portalIndex+`/list/${$route.params.type}`">
        {{title}}

      </router-link>

      </el-breadcrumb-item
      >
      <el-breadcrumb-item><a>详情</a></el-breadcrumb-item>
    </el-breadcrumb>
    <el-card class="d-card" shadow="never" v-loading="loading">
      <div slot="header">
        <h2 class="d-title">{{ data.noticeTitle||data.title}}</h2>
      </div>
      <p class="d-desc">
        <span class="d-type">类型：{{ data.noticeTypeName }}</span>
        <span>时间：{{ parseTime(data.createDate, "{y}-{m}-{d}") }}</span>
      </p>
      <div class="ql-container">
        <div class="ql-snow ql-editor" v-html="data.noticeContent" />
      </div>
    </el-card>
  </div>
</template>

<script setup name="noticeDetail">
import {
  selectNoticeByNoticeId,
  termById
} from "@/api/release/notice";
import {findList} from "@/api/release/programType";

import "quill/dist/quill.core.css"  ;
import "quill/dist/quill.snow.css";
import "quill/dist/quill.bubble.css";

const loading=ref(false)
const noticeId=ref(undefined)
const data=ref({})
const title=ref('')
const { proxy } = getCurrentInstance()
const portalIndex = ref(proxy.portalIndex);

function getOne() {
  loading.value=true;
  selectNoticeByNoticeId({
    noticeId:noticeId.value
  }).then((r)=>{
    loading.value=false;
    data.value=r.data
  })
}
function getEsOne() {
  loading.value=true
  termById({
    id:noticeId.value
  }).then((r)=>{
    loading.value=false;
    data.value=r.data.nameValuePairs;
  })
}
import useUserStore from "@/store/modules/user";
const userStore = useUserStore();

async function origin(){
  let programaList = []
  await findList({tenantId:userStore.userInfo.customParam.tenantId}).then((response)=>{
    programaList =response
  })
  noticeId.value = proxy.$route.params.id;
  let type = proxy.$route.params.type;
  if (programaList.filter(item => item.programaTypeCode === type)){
    title.value = programaList.find(item => item.programaTypeCode === type).programaTypeName
    getOne()
  }else {
    title.value=type
    getEsOne()
  }
}
origin()
</script>

<style scoped lang="scss">
.d-card {
  border: 0px;
  min-height: calc(100vh - 150px);
  padding: 0px 50px;
  .d-title {
    text-align: center;
  }
  .d-desc {
    text-align: right;
    margin: 5px 10px;
    .d-type {
      padding: 0px 20px;
    }
  }
}
</style>
