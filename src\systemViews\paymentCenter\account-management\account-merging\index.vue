<!-- 账户合并 -->

<template>
  <div class="app-container-other">
    <Splitpanes class="default-theme">
      <Pane :size="100" :min-size="65">
        <el-card class="dep-card">
          <dialog-search
            @getList="getList"
            formRowNumber="4"
            :columns="tabelForm.columns"
            :isShowRightBtn="$checkPermi(['pay:relationship:list'])"
          >
            <template v-slot:formList>
              <el-form
                :model="queryParams"
                ref="queryForm"
                :inline="true"
                label-width="75px"
              >
                <el-form-item label="账户名称">
                  <el-input
                    v-model="queryParams.accountName"
                    placeholder="请输入账户名称"
                    clearable
                  ></el-input>
                  
                </el-form-item>
                <el-form-item label="账户编码">
                  <el-input
                    v-model="queryParams.accountCode"
                    placeholder="请输入账户编码"
                    clearable
                  ></el-input>
                </el-form-item>
               
              </el-form>
            </template>
            <template v-slot:searchList>
              <el-button type="primary" icon="Search" @click="handleQuery"  v-hasPermi="['pay:relationship:list']"
                >搜索</el-button
              >
              <el-button icon="Refresh" @click="resetQuery"  v-hasPermi="['pay:relationship:list']">重置</el-button>
            </template>

            <template v-slot:searchBtnList>
              <el-button
                type="primary"
                icon="Plus"
                size="mini"
                @click="handleAdd"
                 v-hasPermi="['pay:relationship:add']"
                >新增合并关系</el-button
              >
              <el-button icon="Download" @click="handleExport"      v-hasPermi="['pay:relationship:export']">导出 </el-button>
            </template>
          </dialog-search>

          <public-table
            ref="publictable"
            :rowKey="tabelForm.tableKey"
            :tableData="list"
            :columns="tabelForm.columns"
            :configFlag="tabelForm.tableConfig"
            :pageValue="pageParams"
            :total="total"
            :getList="getList"
          >
            <template #operation="{ scope }">
              <el-button
                size="mini"
                type="text"
                 v-hasPermi="['pay:relationship:edit']"
                title="修改"
                icon="Edit"
                @click="handleEdit(scope.row)"
              >
              </el-button>

              <el-button
                size="mini"
                type="text"
                  v-hasPermi="['pay:relationship:remove']"
                title="删除"
                icon="Delete"
                @click="handleDelete(scope.row)"
              >
              </el-button>
            </template>
          </public-table>
        </el-card>
      </Pane>
    </Splitpanes>

    <DialogBox
      :visible="open1"
      :dialogWidth="diaWindow.dialogWidth"
      :dialogFooterBtn="diaWindow.dialogFooterBtn"
      @save="save"
      @cancellation="cancellation"
      @close="close"
      :dialogTitle="diaWindow.headerTitle"
    >
      <template #content>
        <accountAdd
          ref="accountAddRef"
          :rowData="diaWindow.rowData"
          :popupType="diaWindow.popupType"
          @closeBtn="cancellationRefsh"
        ></accountAdd>
      </template>
    </DialogBox>
  </div>
</template>
  
  <script setup name="User">
import { ElMessage, ElMessageBox } from "element-plus";
import { ref, reactive, getCurrentInstance } from "vue";
import {
 screenIndex
} from "@/api/paymentCenter/account-management/account-merging/index";
import { apiUrl } from "@/utils/config";
import { formatMinuteTime } from "@/utils";
import accountAdd from "./components/addMerrage.vue";
const { proxy } = getCurrentInstance();
const { type_status,account_type } = proxy.useDict(
  "type_status",
  "account_type"
);
const accountTypeList = ref([])
const accountAddRef = ref(null);
const open1 = ref(false);
const diaWindow = reactive({
  headerTitle: "",
  popupType: "",
  rowData: "",
  dialogWidth: "30%",
  dialogFooterBtn: false,
});

const pageParams = ref({
  pageNum: 1,
  pageSize: 10,
});
const queryParams = ref({
 
});
const list = ref([]);
const total = ref(0);
const tabelForm = reactive({
  tableKey: "1", //表格key值
  isShowRightToolbar: true, //是否显示右侧显示和隐藏
  showSearch: true,
  // 表格表格数据
  columns: [
    {
      fieldIndex: "accountName", // 对应列内容的字段名
      label: "账户名称", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "120", //最小宽度%
      width: "", //宽度
      align: "left", //表格对齐方式
    },
    {
      fieldIndex: "accountCode", // 对应列内容的字段名
      label: "账户编码", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "120", //最小宽度%
      width: "", //宽度
      align: "", //表格对齐方式
    },
    {
      fieldIndex: "typeName", // 对应列内容的字段名
      label: "账户类型", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "120", //最小宽度%
      width: "", //宽度
      align: "", //表格对齐方式
    },
    {
      fieldIndex: "typeCode", // 对应列内容的字段名
      label: "类型编码", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "120", //最小宽度%
      width: "", //宽度
      align: "", //表格对齐方式
    },
    {
      fieldIndex: "mergeAccounts", // 对应列内容的字段名
      label: "可合并账户", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "160", //最小宽度%
      width: "", //宽度
      align: "left", //表格对齐方式
    },


   
    {
      fieldIndex: "createDate", // 对应列内容的字段名
      label: "创建时间", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "150", //最小宽度%
      width: "", //宽度
    },

    {
      fieldIndex: "operator", // 对应列内容的字段名
      label: "操作人", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "120", //最小宽度%
      width: "", //宽度
    },

    {
      label: "操作",
      slotname: "operation",
      width: "100",
      fixed: "right", //固定
      visible: true,
    },
  ],
  // 表格基础配置项，看个人需求添加
  tableConfig: {
    needPage: true, // 是否需要分页
    index: true, // 是否需要序号
    selection: false, // 是否需要多选
    reserveSelection: false, // 是否需要支持跨页选择
    indexFixed: false, // 序号列固定 left true right
    selectionFixed: false, //多选列固定left true right
    indexWidth: "50", //序号列宽度
    loading: false, //表格loading
    showSummary: false, //是否开启合计
    height: null, //表格固定高度 比如：300px
  },
});
// 账户类型
const payAccountTypePageList = async () => {
  const res = await screenIndex.payAccountTypePageList({  });

  accountTypeList.value = res.data.records.map(item => ({
      value: item.typeCode,    // 对应 typeCode
      label: item.typeName,    // 对应 typeName
      // 保留原始数据（根据需要可选）
      ...item
    }));
    tabelForm.columns[2].dictList =  accountTypeList.value

};
/** 查询列表 */
const getList = () => {
  tabelForm.tableConfig.loading = true;
  screenIndex.payAccountConsolidationList(queryParams.value, pageParams.value).then((response) => {
    // 处理返回的数据，将mergeAccounts字段的英文内容替换为中文
    const records = response.data.records.map(item => {
      if (item.mergeAccounts) {
        item.mergeAccounts = item.mergeAccounts.replace(/,/g, '，');
      }
      return item;
    });
    
    list.value = records;
    total.value = response.data.total;
    tabelForm.tableConfig.loading = false;
  });
};

/** 搜索按钮操作 */
const handleQuery = () => {
  pageParams.value.pageNum = 1;
  getList();
};
/** 重置按钮操作 */
const resetQuery = () => {
  proxy.resetForm("queryForm");
  queryParams.value = {
   
  };
  handleQuery();
};

/** 导出按钮操作 */

const handleExport = () => {
  proxy.download(
    `pay${apiUrl}/pay/payAccountConsolidation/export`,
    {
      ...queryParams.value,
    },
    `账户合并设置列表_${formatMinuteTime(new Date())}.xlsx`
  );
};
/** 删除 */
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm("确认要删除吗？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });

    const res = await screenIndex.delete({accountManageId:row.id});
    if (res.code == "1") {
      ElMessage.success("删除成功");
      await getList();
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    console.error("删除失败:", error);
    // 用户取消删除或其他错误
  }
};


// 新增
const handleAdd = () => {
  diaWindow.headerTitle = "新增合并关系";
  diaWindow.popupType = "add";
  diaWindow.rowData = {}; // 如果需要传递数据到弹窗
  diaWindow.dialogFooterBtn = true;
  diaWindow.dialogWidth = "70%";
  open1.value = true;
};

// 修改
const handleEdit = (row) => {
  diaWindow.headerTitle = "修改合并关系";
  diaWindow.popupType = "edit";
  diaWindow.rowData = row; // 如果需要传递数据到弹窗
  diaWindow.dialogFooterBtn = true;
  diaWindow.dialogWidth = "70%";
  open1.value = true;
};
/** 点击确定保存 */
const save = () => {
  if (diaWindow.popupType == "add") {
    accountAddRef.value.saveForm();
  }

  if (diaWindow.popupType == "edit") {
    accountAddRef.value.saveForm();
  }
};
/** 点击确定后刷新 */
const cancellationRefsh = () => {
  close(false);
  getList();
};
/** 点击取消保存 */
const cancellation = (val) => {
  close(false);
};

/** 关闭弹窗 */
const close = (val) => {
  open1.value = val;
};


payAccountTypePageList()
getList();
</script>
  
  <style scoped>
.dep-card {
  min-height: calc(100vh - 160px);
}


</style>