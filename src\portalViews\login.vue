<template>
  <div class="login_content">
    <img class="login_2" src="@/assets/images/login_2.png" alt="">
    <div class="form_content">
      <Transition appear enter-active-class="animate__animated animate__bounceInLeft">
        <div>
          <img class="login_1" src="@/assets/images/login_1.png" alt="">
        </div>
          
      </Transition>
      <Transition appear enter-active-class="animate__animated animate__bounceInRight">
        <div class="LoginForm">
          <LoginForm />
        </div>
      </Transition>
    </div>
  </div>
</template>

<script setup>
import { onMounted } from 'vue';
import LoginForm from './LoginForm.vue';

// 组件挂载时清除 localStorage
onMounted(() => {
  localStorage.clear();
});
</script>

<style lang="less" scoped>
.login_content {
  width: 100%;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;

  .login_2 {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .form_content {
    position: absolute;
    z-index: 2;
    width: 1284px;
    height: 746px;

    .login_1 {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }

    .LoginForm {
      position: absolute;
      top: 20%;
      right: 10%;
      width: 400px;  /* 建议添加具体尺寸 */
    }
  }
}
</style>