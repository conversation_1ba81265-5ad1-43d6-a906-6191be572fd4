<!--通知公告-->
<template>
  <el-row>
    <el-col :span="24">
      <div class="padding-box-4">
        <div class="big-common-box-one" style="  height: 310px;"
        >
          <div class="common-header">
            <div class="left-header-text">通知公告</div>
            <div class="flex-1"/>
            <div class="header-right-btn"  @click="moreExt('system_notice')">
              更多
            </div>
          </div>
          <div class="big-common-content">
            <div class="news-content">
              <el-scrollbar style="height: 100%">
                <el-empty v-if="notice.length == 0" :image-size="100"/>
                <div v-else-if="notice.length > 0" class="news-content">
                    <div style="padding-right: 14px">
                      <div
                          v-for="(item, index) of notice"
                          :key="index"
                          class="news-row-one"
                      >
                        <router-link
                            style="width: 100%"
                            :to="portalIndex+`/detail/${item.programaType}/${item.noticeId}`"
                            target="_blank"
                        >
                          <div class="right-infos">
                            <div class="top-infos">
                              <span class="top-infos-dotted"></span>
                              <span class="news-top-infos-text">{{ item.noticeTypeName }}</span>
                              <span class="new-times" :title="item.noticeTitle">{{ item.noticeTitle }}</span>
                            </div>
                          </div>
                        </router-link>
                      </div>
                    </div>
                </div>
              </el-scrollbar>
            </div>
          </div>
        </div>
      </div>
    </el-col>
  </el-row>
</template>


<script setup name="CaseNotice">
import { selectTopPage } from "@/api/release/notice";

const { proxy } = getCurrentInstance();
const portalIndex = ref(proxy.portalIndex);
const caseNotice = ref(false);
const notice = ref([]);
const index = ref(0);
const router = useRouter();

function getNotice() {
  caseNotice.value = true;
  selectTopPage({
    programaType: "system_notice",
  }).then((res) => {
    notice.value = res.data.records;
    caseNotice.value = false;
  });
}
const moreExt = (type) => {
  router.push(portalIndex.value+`/list/${type}`);
}
const add = () => {
  if (index.value < notice.value.length - 1) {
    index.value++;
  } else {
    proxy.$modal.msg("没有更多数据了！");
  }
}

const reduce = () => {
  if (index.value > 0) {
    index.value--;
  } else {
    proxy.$modal.msg("没有更多数据了！");
  }
}
getNotice();
</script>

<style scoped lang="scss">
.big-common-box-one {
  background: #ffffff;
  border-radius: 4px;
  width: 100%;
  padding:0 10px 10px;

  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); // 添加阴影效果
  transition: box-shadow 0.3s ease;
  box-sizing: border-box;
  .big-common-content{
    height: calc(100% - 43px);
    overflow: auto;
  }
  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2); // 悬停时加深阴影
  }
}

.common-header {
  height: 45px;
  line-height: 45px;
  border-bottom: 1px solid #e8eaeb;
  box-sizing: border-box;
  padding-bottom: 0px;

  .left-header-text {
    font-size: 15px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #333333;
  }

  .header-right-btn {
    font-size: 13px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #9ea4aa;
    cursor: pointer;
  }
}

.common-content {
  height: calc(100% - 45px);
}

.news-content {
  height: 100%;
  width: 100%;

  .news-row-one {
    margin-top: 16px;
    display: flex;
    align-items: center;
    cursor: pointer;

    .left-img {
      width: 85px;
      height: 84px;
      margin-right: 12px;

      img {
        height: 100%;
        width: 100%;
      }
    }

    .right-infos {
      flex: 1;

      .top-infos {
        width: 100%;
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        line-height: 22px;

        .top-infos-text {
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #333333;
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 1;
          word-break: break-all;
        }

        .top-infos-dotted {
          width: 7px;
          height: 7px;
          background: #c20000;
          border-radius: 50%;
          flex-shrink: 0;
          margin-right: 6px;
        }
      }

      .bottom-infos {
        font-size: 13px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #9d9fa1;
        padding-left: 12px;
      }
    }
  }
}

.news-top-infos-text {
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #9d9fa1;
  padding-right: 6px;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 0;
  display: block;
  width: 50px;
}
.new-times {
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #333333;
  width: calc(100% - 52px);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
