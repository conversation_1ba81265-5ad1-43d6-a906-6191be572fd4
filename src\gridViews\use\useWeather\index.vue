<template>
  <el-card :class="bgColor === true ? 'weather-card' : 'unshow-weather-card'" v-loading="loading" shadow="never">
    <template v-if="showTitle">
      <span class="card-title">
        <span><el-icon class="icon iconfont"></el-icon>天气预报</span>
      </span>
    </template>
    <div class="hot home-card-body">
      <div
          class="he-plugin-standard"
          :class="
          list.icon === '100'
            ? 'd100'
            : '' || list.icon === '101'
            ? 'd101'
            : '' || list.icon === '104'
            ? 'd104'
            : '' || list.icon === '300'
            ? 'd300'
            : '' || list.icon === '303'
            ? 'd303'
            : '' || list.icon === '305'
            ? 'd305'
            : '' || list.icon === '306'
            ? 'd306'
            : '' || list.icon === '404'
            ? 'd404'
            : '' || list.icon === '500'
            ? 'd500'
            : '' || list.icon === '502'
            ? 'd502'
            : '' || list.icon === '150'
            ? 'd150'
            : ''
        "
      >
        <div style="margin: 10px 10px 0px 20px; color: white; font-size: 18px">{{ location }}</div>
        <div class="he-plugin-standard-content">
          <div class="leftBox">
            <div style="display: flex;justify-content: space-around;">
              <div class="imgs">
                <el-icon class="qi-100"></el-icon>
                <img
                    v-if="list.icon === '100'"
                    src="@/assets/images/pageDesign/use/weather100-fill.svg"
                    width="50"
                    height="50"
                    class="sun"

                />
                <img
                    v-if="list.icon === '101'"
                    src="@/assets/images/pageDesign/use/weather101-fill.svg"
                    width="50"
                    height="50"
                    class="cloudy"

                />
                <img
                    v-if="list.icon === '104'"
                    src="@/assets/images/pageDesign/use/weather104-fill.svg"
                    width="50"
                    height="50"
                    class="cloudyDay"

                />
                <img
                    v-if="list.icon === '150'"
                    src="@/assets/images/pageDesign/use/weather150-fill.svg"
                    width="50"
                    height="50"
                    class="night"

                />
                <img
                    v-if="list.icon === '300'"
                    src="@/assets/images/pageDesign/use/weather300-fill.svg"
                    width="50"
                    height="50"
                    class="rains"

                />
                <img
                    v-if="list.icon === '303'"
                    src="@/assets/images/pageDesign/use/weather303.svg"
                    width="50"
                    height="50"
                    class="lighting"

                />
                <img
                    v-if="list.icon === '305'"
                    src="@/assets/images/pageDesign/use/weather305.svg"
                    width="50"
                    height="50"
                    class="littleRain"

                />
                <!-- 中雨 -->
                <img
                    v-if="list.icon === '306'"
                    src="@/assets/images/pageDesign/use/weather306.svg"
                    width="50"
                    height="50"
                    class="Moderate-Rain"

                />
                <img
                    v-if="list.icon === '404'"
                    src="@/assets/images/pageDesign/use/weather404.svg"
                    width="50"
                    height="50"
                    class="snow"

                />
                <img
                    v-if="list.icon === '500'"
                    src="@/assets/images/pageDesign/use/weather500-fill.svg"
                    width="50"
                    height="50"
                    class="fog"

                />
                <img
                    v-if="list.icon === '502'"
                    src="@/assets/images/pageDesign/use/weather502.svg"
                    width="50"
                    height="50"
                    class="haze"

                />
              </div>
              <div class="weather-temperature">
                <div class="rain">体感温度</div>
                <div class="level">{{ list.feelsLike }}°</div>
              </div>
            </div>
            <div>
              <div class="future-rain">风向：{{ list.windDir }}</div>
            </div>
          </div>
          <div class="rightBox">
            <div class="today-weather">
              <div style="margin-left: 3px; color: white">今天</div>
              <div class="today-imgs">
                <img
                    v-if="list.icon === '100'"
                    src="@/assets/images/pageDesign/use/weather100-fill.svg"
                    width="50"
                    height="50"
                    class="right-sun"

                />
                <img
                    v-if="list.icon === '101'"
                    src="@/assets/images/pageDesign/use/weather101-fill.svg"
                    width="50"
                    height="50"
                    class="right-cloudy"

                />
                <img
                    v-if="list.icon === '104'"
                    src="@/assets/images/pageDesign/use/weather104-fill.svg"
                    width="50"
                    height="50"
                    class="right-cloudyDay"

                />
                <img
                    v-if="list.icon === '150'"
                    src="@/assets/images/pageDesign/use/weather150-fill.svg"
                    width="50"
                    height="50"
                    class="eveving"

                />
                <img
                    v-if="list.icon === '300'"
                    src="@/assets/images/pageDesign/use/weather300-fill.svg"
                    width="50"
                    height="50"
                    class="right-rain"

                />
                <img
                    v-if="list.icon === '303'"
                    src="@/assets/images/pageDesign/use/weather303.svg"
                    width="50"
                    height="50"
                    class="right-lighting"

                />
                <img
                    v-if="list.icon === '305'"
                    src="@/assets/images/pageDesign/use/weather305.svg"
                    width="50"
                    height="50"
                    class="right-littleRain"

                />
                <img
                    v-if="list.icon === '306'"
                    src="@/assets/images/pageDesign/use/weather306.svg"
                    width="50"
                    height="50"
                    class="right-Moderate-Rain"

                />
                <img
                    v-if="list.icon === '404'"
                    src="@/assets/images/pageDesign/use/weather404.svg"
                    width="50"
                    height="50"
                    class="right-snow"

                />
                <img
                    v-if="list.icon === '500'"
                    src="@/assets/images/pageDesign/use/weather500-fill.svg"
                    width="50"
                    height="50"
                    class="right-fog"

                />
                <img
                    v-if="list.icon === '502'"
                    src="@/assets/images/pageDesign/use/weather502.svg"
                    width="50"
                    height="50"
                    class="right-haze"

                />
              </div>
              <div style="font-size: 16px; margin-bottom: 10px">
                <span
                    style="margin: 0; padding: 0; color: white; margin-left: 10px"
                >{{ list.temp }}°</span>
                <span
                    style="
                    margin: 0;
                    padding: 0;
                    margin-left: 10px;
                    color: #ddc307;
                  "
                >{{ list.text }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script setup name="UseWeather">
import {getLocationId} from "@/api/grid/useWeather";

const loading = ref(false)
const list = ref([])
const location = ref("济南")
const key = ref("5b539d64e94e4c04a01c2d95b9bbbc41")
const icons = ref("301")
const props = defineProps({
  showTitle: Boolean,
  bgColor: Boolean
})

function getLocation() {
  getLocationId().then((res) => {
    list.value = res.data;
  });
}

getLocation();
</script>

<style scoped lang="scss">
.weather-card {
  height: 100%;
  background: #fff;
  .home-card-body {
    padding-top: 20px;
  }
  .he-plugin-standard {
    // width: 450px;
    height: 100%;
    background-repeat: no-repeat;
    background-position: left top;
    background-size: 100%;
    border-radius: 0px;
    // overflow: hidden;
    .leftBox {
      float: left;
      width: 55%;
      height: 100%;
      border-right: 1px solid white;
    }
    .imgs {
      width: 50px;
      height: 50px;
      margin: 0px 10px auto 15px;
      img {
        width: 100%;
      }
    }
    .weather-temperature {
      color: white;
      margin-right: 20px;
    }
    .weather-temperature p {
      margin: 0;
      padding: 0;
    }
    .rain {
      width: 80px;
      height: 25px;
      color: #ff9d1e;
      text-align: center;
      border-radius: 10px;
      line-height: 25px;
      margin: 15px 0 0 25px;
    }
    .level {
      margin: 2px 0 0 70px;
      color: #e59c65;
      font-size: 20px;
    }
    .future-rain {
      margin: 12px 0 0 20px;
      color: white;
    }
    .rightBox {
      float: left;
      width: 40%;
      padding-left: 10px;
    }
    .today-imgs {
      width: 40px;
      height: 40px;
      margin: 10px 0 10px 100px;
      img {
        width: 100%;
        height: 100%;
      }
    }
  }
  .d100 {
    background-image: url("../../../assets/images/pageDesign/use/weather100d.png");
    background-repeat: no-repeat;
    background-position: left top;
    background-size: 100%;
    border-radius: 0px;
    overflow: hidden;
  }
  .d101 {
    background-image: url("../../../assets/images/pageDesign/use/weather101d.png");
    background-repeat: no-repeat;
    background-position: left top;
    background-size: 100%;
    border-radius: 0px;
    overflow: hidden;
  }
  .d104 {
    background-image: url("../../../assets/images/pageDesign/use/weather104d.png");
    background-repeat: no-repeat;
    background-position: left top;
    background-size: 100%;
    border-radius: 0px;
    overflow: hidden;
  }
  .d300 {
    background-image: url("../../../assets/images/pageDesign/use/weather300d.png");
    background-repeat: no-repeat;
    background-position: left top;
    background-size: 100%;
    border-radius: 0px;
    overflow: hidden;
  }
  .d303 {
    background-image: url("../../../assets/images/pageDesign/use/weather303d.png");
    background-repeat: no-repeat;
    background-position: left top;
    background-size: 100%;
    border-radius: 0px;
    overflow: hidden;
  }
  .d305 {
    background-image: url("../../../assets/images/pageDesign/use/weather300d.png");
    background-repeat: no-repeat;
    background-position: left top;
    background-size: 100%;
    border-radius: 0px;
    overflow: hidden;
  }
  .d306 {
    background-image: url("../../../assets/images/pageDesign/use/weather306d.png");
    background-repeat: no-repeat;
    background-position: left top;
    background-size: 100%;
    border-radius: 0px;
    overflow: hidden;
  }
  .d404 {
    background-image: url("../../../assets/images/pageDesign/use/weather101d.png");
    background-repeat: no-repeat;
    background-position: left top;
    background-size: 100%;
    border-radius: 0px;
    overflow: hidden;
  }
  .d500 {
    background-image: url("../../../assets/images/pageDesign/use/weather500d.png");
    background-repeat: no-repeat;
    background-position: left top;
    background-size: 100%;
    border-radius: 0px;
    overflow: hidden;
  }
  .d502 {
    background-image: url("../../../assets/images/pageDesign/use/weather502d.png");
    background-repeat: no-repeat;
    background-position: left top;
    background-size: 100%;
    border-radius: 0px;
    overflow: hidden;
  }
  .d150 {
    background-image: url("../../../assets/images/pageDesign/use/weather101d.png");
    background-repeat: no-repeat;
    background-position: left top;
    background-size: 100%;
    border-radius: 0px;
    overflow: hidden;
  }
  .sun,
  .night,
  .lighting,
  .eveving,
  .right-sun,
  .right-lighting
   {
    filter: drop-shadow(10000px 0 #edaf27);
    transform: translateX(-10000px);
  }

  .cloudy,
  .cloudyDay,
  .rains,
  .littleRain,
  .snow,
  .fog,
  .haze,
  .Moderate-Rain,
  .right-cloudy,
  .right-cloudyDay,
  .right-rain,
  .right-littleRain,
  .right-snow,
  .right-fog,
  .right-Moderate-Rain,
  .right-haze {
    //fill: #e0f1ff;
    filter: drop-shadow(10000px 0 #e0f1ff);
    transform: translateX(-10000px);
    margin-left: 20px;
  }
}
.unshow-weather-card {
  height: 100%;
  border: 0px;
  background: #fff;
  .home-card-body {
    padding-top: 20px;
  }
  .he-plugin-standard {
    // width: 450px;
    height: 100%;
    background-repeat: no-repeat;
    background-position: left top;
    background-size: 100%;
    border-radius: 0px;
    // overflow: hidden;
    .leftBox {
      float: left;
      width: 55%;
      height: 100%;
      border-right: 1px solid white;
    }
    .imgs {
      width: 50px;
      height: 50px;
      margin: 15px 10px auto 15px;
      img {
        width: 100%;
      }
    }
    .weather-temperature {
      color: white;
      margin-right: 20px;
    }
    .weather-temperature p {
      margin: 0;
      padding: 0;
    }
    .rain {
      width: 80px;
      height: 25px;
      color: #ff9d1e;
      text-align: center;
      border-radius: 10px;
      line-height: 25px;
      margin: 15px 0 0 25px;
    }
    .level {
      margin: 2px 0 0 70px;
      color: #e59c65;
      font-size: 20px;
    }
    .future-rain {
      margin: 12px 0 0 20px;
      color: white;
    }
    .rightBox {
      float: left;
      width: 40%;
      padding-left: 10px;
    }
    .today-imgs {
      width: 40px;
      height: 40px;
      margin: 10px 0 10px 100px;
      img {
        width: 100%;
        height: 100%;
      }
    }
  }
  .d100 {
    background-image: url("../../../assets/images/pageDesign/use/weather100d.png");
    background-repeat: no-repeat;
    background-position: left top;
    background-size: 100%;
    border-radius: 0px;
    overflow: hidden;
  }
  .d101 {
    background-image: url("../../../assets/images/pageDesign/use/weather101d.png");
    background-repeat: no-repeat;
    background-position: left top;
    background-size: 100%;
    border-radius: 0px;
    overflow: hidden;
  }
  .d104 {
    background-image: url("../../../assets/images/pageDesign/use/weather104d.png");
    background-repeat: no-repeat;
    background-position: left top;
    background-size: 100%;
    border-radius: 0px;
    overflow: hidden;
  }
  .d300 {
    background-image: url("../../../assets/images/pageDesign/use/weather300d.png");
    background-repeat: no-repeat;
    background-position: left top;
    background-size: 100%;
    border-radius: 0px;
    overflow: hidden;
  }
  .d303 {
    background-image: url("../../../assets/images/pageDesign/use/weather303d.png");
    background-repeat: no-repeat;
    background-position: left top;
    background-size: 100%;
    border-radius: 0px;
    overflow: hidden;
  }
  .d305 {
    background-image: url("../../../assets/images/pageDesign/use/weather300d.png");
    background-repeat: no-repeat;
    background-position: left top;
    background-size: 100%;
    border-radius: 0px;
    overflow: hidden;
  }
  .d306 {
    background-image: url("../../../assets/images/pageDesign/use/weather306d.png");
    background-repeat: no-repeat;
    background-position: left top;
    background-size: 100%;
    border-radius: 0px;
    overflow: hidden;
  }
  .d404 {
    background-image: url("../../../assets/images/pageDesign/use/weather101d.png");
    background-repeat: no-repeat;
    background-position: left top;
    background-size: 100%;
    border-radius: 0px;
    overflow: hidden;
  }
  .d500 {
    background-image: url("../../../assets/images/pageDesign/use/weather500d.png");
    background-repeat: no-repeat;
    background-position: left top;
    background-size: 100%;
    border-radius: 0px;
    overflow: hidden;
  }
  .d502 {
    background-image: url("../../../assets/images/pageDesign/use/weather502d.png");
    background-repeat: no-repeat;
    background-position: left top;
    background-size: 100%;
    border-radius: 0px;
    overflow: hidden;
  }
  .d150 {
    background-image: url("../../../assets/images/pageDesign/use/weather101d.png");
    background-repeat: no-repeat;
    background-position: left top;
    background-size: 100%;
    border-radius: 0px;
    overflow: hidden;
  }
  .sun,
  .night,
  .lighting,
  .right-sun,
  .eveving,
  .right-lighting {
    fill: #edaf27;
  }
  .cloudy,
  .cloudyDay,
  .rains,
  .littleRain,
  .snow,
  .fog,
  .haze,
  .Moderate-Rain,
  .right-cloudy,
  .right-cloudyDay,
  .right-rain,
  .right-littleRain,
  .right-snow,
  .right-fog,
  .right-Moderate-Rain,
  .right-haze {
    fill: #e0f1ff;
    margin-left: 20px;
  }
}
</style>
