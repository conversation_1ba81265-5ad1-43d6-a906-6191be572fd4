<template>
  <div class="app-container">
    <el-card shadow="never">
      <!--    查询-->
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">

        <el-form-item label="租户" v-if="userType === 'admin'">
          <el-select
              v-model="queryParams.tenantId"
              placeholder="请选择租户"
              remote
              style="width: 240px"
              :remote-method="initTenantList"
              :loading="getTenantLoading"
              @change="handleTenantChange"
          >
            <el-option
                v-for="item in tenantList"
                :key="item.tenantId"
                :label="item.tenantName"
                :value="item.tenantId"
            />

          </el-select>
        </el-form-item>

        <el-form-item label="页面名称" prop="pageName">
          <el-input v-model="queryParams.pageName" placeholder="请输入页面名称" clearable style="width: 180px"
                    @keyup.enter="handleQuery">
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <!--  刷新按钮-->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="Plus" @click="handleAdd"
                     v-hasPermi="['system:extend:page-designer:add']">新增
          </el-button>
        </el-col>
        <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>
      <!--      展示-->
      <el-table v-loading="loading" :data="dataList">
        <el-table-column
            label="页面名称"
            prop="pageName"
            :show-overflow-tooltip="true"
        />
        <el-table-column prop="pageCode" label="页面编码" align="center"/>
        <el-table-column
            prop="isMenu"
            label="是否为菜单"
            align="center"
            width="120"
        >
          <template #default="scope">
            <el-tag
                v-if="scope.row.isMenu == '1'"
                type="success"
                disable-transitions
            >是
            </el-tag>
            <el-tag
                v-if="scope.row.isMenu == '0'"
                type="warning"
                disable-transitions
            >否
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
            prop="pageStatus"
            label="页面状态"
            align="center"
            width="120"
        >
          <template #default="scope">
              <dict-tag :options="effective_status" :value="scope.row.pageStatus" />
          </template>
        </el-table-column>
        <el-table-column
            label="创建人"
            prop="createBy"
            align="center"
            width="120"
        />
        <el-table-column
            label="创建时间"
            align="center"
            prop="createDate"
            width="200"
        >
        </el-table-column>
        <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
        >
          <template #default="scope">
            <el-button
                size="small"
                link
                type="primary"
                icon="Tickets"
                v-hasPermi="['system:extend:page-designer:set-menu']"
                @click="setToMenu(scope.row)"
                v-if="
                (scope.row.tenantId === customParam.tenantId ||
                userType === 'admin') && scope.row.isMenu === 0
              "
            >设为菜单
            </el-button>
            <el-button
                size="small"
                link
                type="primary"
                icon="Close"
                @click="cancelToMenu(scope.row)"
                v-hasPermi="['system:extend:page-designer:set-menu']"
                v-if="
                (scope.row.tenantId === customParam.tenantId ||
                userType === 'admin')&& scope.row.isMenu === 1
              "
            >撤销菜单
            </el-button>
            <el-button
                size="small"
                link
                type="primary"
                icon="Edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['system:extend:page-designer:edit']"
                v-if="
                scope.row.tenantId === customParam.tenantId ||
                userType === 'admin'
              "
            >修改
            </el-button>
            <el-button
                size="small"
                link
                type="primary"
                icon="Delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['system:extend:page-designer:remove']"
                v-if="
                scope.row.tenantId === customParam.tenantId ||
                userType === 'admin'
              "
            >删除
            </el-button>
            <el-button
                size="small"
                link
                type="primary"
                icon="DocumentCopy"
                @click="handleCopy(scope.row)"
                v-hasPermi="['system:extend:page-designer:copy']"
                v-if="
                scope.row.tenantId === customParam.tenantId ||
                userType === 'admin'
              "
            >复制
            </el-button>
            <el-button
                size="small"
                link
                type="primary"
                icon="Rank"
                v-hasPermi="['system:extend:page-designer:design']"
                @click="goDefinition(scope.row, 1)"
                v-if="
                scope.row.tenantId === customParam.tenantId ||
               userType === 'admin'
              "
            >设计
            </el-button>
            <el-button
                size="small"
                link
                type="primary"
                icon="View"
                v-hasPermi="['system:extend:page-designer:design']"
                v-if="
                scope.row.tenantId === customParam.tenantId ||
                userType === 'admin'
              "
                @click="goPreview(scope.row, 0)"
            >预览
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
      />

      <!-- 添加或修改对话框 -->
      <el-dialog
          :title="title"
          v-model="open"
          width="500px"
          append-to-body
      >
        <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
          <el-form-item label="页面名称" prop="pageName">
            <el-input v-model="form.pageName" placeholder="请输入页面名称"/>
          </el-form-item>
          <el-form-item label="页面编码" prop="pageCode">
            <el-input v-model="form.pageCode" placeholder="请输入页面编码"/>
          </el-form-item>
          <el-form-item label="页面状态" prop="pageStatus">
            <el-radio-group v-model="form.pageStatus">
              <el-radio
                  v-for="dict in effective_status"
                  :label="dict.value"
              >{{ dict.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input
                v-model="form.remark"
                type="textarea"
                placeholder="请输入内容"
            />
          </el-form-item>
          <el-form-item label="租户">
            <el-input
                v-model="tenantName"
                placeholder="租户"
                maxlength="50"
                disabled
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForm" :loading="saveLoading">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </div>
        </template>
      </el-dialog>
<!--      菜单—-->
      <!-- 添加或修改菜单对话框 -->
      <el-dialog :title="menuTitle" v-model="menuOpen" width="750px" append-to-body>
        <el-form ref="menuRef" :model="menuForm" :rules="menuRules" label-width="100px">
          <el-row>
            <el-col :span="24">
              <el-form-item label="租户">
                <el-input v-model="menuForm.tenantName" placeholder="租户" maxlength="50" disabled />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="上级菜单">
                <el-tree-select v-model="menuForm.parentId" :data="menuOptions"
                                :props="{ value: 'permissionId', label: 'permissionName', children: 'children' }"
                                value-key="permissionId" placeholder="选择上级菜单" check-strictly @node-click="onParentChange" />
              </el-form-item>
            </el-col>
            <el-col :span="24" v-if="menuForm.parentId === 0">
              <el-form-item label="菜单归属" prop="permissionScope" required>
                <el-radio-group v-model="menuForm.permissionScope">
                  <el-radio v-for="dict in scope" :key="dict.value" :label="dict.value">
                    {{ dict.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="菜单类型" prop="permissionType">
                <el-radio-group v-model="menuForm.permissionType">
                  <el-radio v-for="dict in menu_type" :key="dict.value" :label="dict.value">
                    {{ dict.label }}
                  </el-radio>
                  <el-radio v-if="menuForm.permissionScope === 'mb_app'" v-for="dict in mobile_jump_mode"
                            :key="dict.value" :label="dict.value">
                    {{ dict.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="菜单名称" prop="permissionName">
                <el-input v-model="menuForm.permissionName" placeholder="请输入菜单名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="menuForm.permissionType != 'operation'">
              <el-form-item label="菜单图标" prop="icon">
                <el-popover placement="bottom-start" :width="540" trigger="click">
                  <template #reference>
                    <el-input v-model="menuForm.icon" placeholder="点击选择图标" @blur="showSelectIcon" readonly>
                      <template #prefix>
                        <svg-icon v-if="menuForm.icon" :icon-class="menuForm.icon" class="el-input__icon"
                                  style="height: 32px;width: 16px;" />
                        <el-icon v-else style="height: 32px;width: 16px;">
                          <search />
                        </el-icon>
                      </template>
                    </el-input>
                  </template>
                  <icon-select ref="iconSelectRef" @selected="selected" :active-icon="form.icon" />
                </el-popover>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="menuForm.permissionType !== 'operation'">
              <el-form-item label="显示排序" prop="permissionSort">
                <el-input-number v-model="menuForm.permissionSort" controls-position="right" :min="0" />
              </el-form-item>
            </el-col>
            <el-col :span="16" v-if="menuForm.permissionType !== 'operation'">
              <el-form-item prop="code">
                <template #label>
                        <span>
                           <el-tooltip placement="top">
                              <template #content>
                                 组件路径：以'systemViews'或者'portalViews'目录开头
                                 <br />
                                 链接地址：以http://、https://、inner://开头
                              </template>
                              <el-icon><question-filled /></el-icon>
                           </el-tooltip>
                           组件路径
                        </span>
                </template>
                <el-input v-model="menuForm.code" placeholder="请输入组件路径" disabled/>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="menuForm.permissionType !== 'operation'">
              <el-form-item prop="permissionFrame">
                <template #label>
                        <span>
                           <el-tooltip content="当菜单地址为外部链接时,是否需要拼接单点登录的 mmy 参数" placement="top">
                              <el-icon><question-filled /></el-icon>
                           </el-tooltip>
                           单点登录
                        </span>
                </template>
                <el-radio-group v-model="menuForm.permissionFrame">
                  <el-radio label="Y">是</el-radio>
                  <el-radio label="N">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="menuForm.permissionType != 'operation'">
              <el-form-item prop="uri" required>
                <template #label>
                        <span>
                           <el-tooltip content="由各层级路由标识，拼接最终的路由路径" placement="top">
                              <el-icon><question-filled /></el-icon>
                           </el-tooltip>
                           路由标识
                        </span>
                </template>
                <el-input v-model="menuForm.uri" placeholder="请输入路由标识" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="checkCode">
                <el-input v-model="menuForm.checkCode" placeholder="请输入权限标识" maxlength="100" />
                <template #label>
                        <span>
                           <el-tooltip content="权限控制器中使用的权限字符" placement="top">
                              <el-icon><question-filled /></el-icon>
                           </el-tooltip>
                           权限字符
                        </span>
                </template>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item>
                <template #label>
                        <span>
                           <el-tooltip content="选择停用则路由将不会出现在侧边栏，也不能被访问，子菜单状态同步更改" placement="top">
                              <el-icon><question-filled /></el-icon>
                           </el-tooltip>
                           菜单状态
                        </span>
                </template>
                <el-radio-group v-model="menuForm.permissionStatus">
                  <el-radio v-for="dict in menu_status" :key="dict.value" :label="dict.value">
                    {{ dict.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="menuForm.permissionType != 'operation'">
              <el-form-item>
                <template #label>
                        <span>
                           <el-tooltip content="选择隐藏则路由将不会出现在侧边栏，但仍然可以访问" placement="top">
                              <el-icon><question-filled /></el-icon>
                           </el-tooltip>
                           显示状态
                        </span>
                </template>
                <el-radio-group v-model="menuForm.permissionVisible">
                  <el-radio v-for="dict in menu_visible" :key="dict.value" :label="dict.value">
                    {{ dict.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="menuSubmitForm">确 定</el-button>
            <el-button @click="menuCancel">取 消</el-button>
          </div>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup name="PageDesigner">
import {
  addPageDesigner,
  copyPageDesigner,
  deletePageDesigner,
  getPageDesigner,
  getPageDesignerPage,
  updatePageDesigner, updatePageMenuStatus,
} from "@/api/extend/pageDesigner";
import {getTenants} from "@/api/tenant/tenant";
import useUserStore from "@/store/modules/user";
import {ref} from "vue";
import {ElMessage, ElMessageBox} from "element-plus";
const {proxy} = getCurrentInstance();
//字典
const userStore = useUserStore();
const {effective_status,
  menu_status,
  scope,
  menu_visible,
  menu_type,
  mobile_jump_mode} = proxy.useDict("effective_status","menu_status", "scope", "menu_visible", "menu_type", "mobile_jump_mode")

const customParam = userStore.userInfo.customParam
const userType = customParam.userType
const tenantName=customParam.tenantName
const total = ref(0)
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  pageName: "",
  tenantId:customParam.tenantId
})
const showSearch = ref(true);
const dataList = ref([])
const loading = ref(true)
/** 获取租户列表*/
const getTenantLoading=ref(false)
const tenantList = ref([]);
const iconSelectRef = ref(null);
function initTenantList(tenantName) {
  getTenantLoading.value = true;
  let query = {};
  if (tenantName !== undefined && tenantName !== "") {
    query.tenantName = tenantName;
    query.tenantId = undefined;
  } else {
    query.tenantId = queryParams.value.tenantId;
  }
  getTenants(query)
      .then((response) => {
        tenantList.value = response.data;
      })
      .finally(() => {
        getTenantLoading.value = false;

      });
}
// 下拉框切换租户回调
function handleTenantChange() {
  handleQuery();
}
/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList();
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 分页查询*/
/** 查询列表 */
function getList() {
  loading.value=true;
  getPageDesignerPage({
    ...queryParams.value,
  }).then(response => {
    if (response.success) {
      dataList.value=response.data.records;
      total.value = response.data.total;
    }
    loading.value = false;
  }).catch(() => {
    loading.value = false;
  })
}

/** 新增/修改操作*/
const rules={
  pageName: [
    {required: true, message: "页面名称不能为空", trigger: "blur"},
  ],
  pageCode: [
    {required: true, message: "页面编码不能为空", trigger: "blur"},
  ],
  pageStatus: [
    {required: true, message: "请选择页面状态", trigger: "blur"},
  ],
}
const form=ref({
  id: undefined,
  pageName: "",
  pageCode: "",
  pageStatus: "",
})
const formRef=ref()
const title=ref("")
const open = ref(false);
const saveLoading=ref(false)
/** 重置表单*/
function reset(){
  form.value={
    id: undefined,
    pageName: "",
    pageCode: "",
    pageStatus: "valid",
  }
  proxy.resetForm(formRef)
}
function handleAdd(){
  reset()
  title.value="添加页面"
  open.value=true
}
function handleUpdate(row){
  reset()
  title.value="修改页面"
  getPageDesigner(row.id).then((res) => {
    if (res.success){
      form.value={
        id: res.data.id,
        pageName: res.data.pageName,
        pageCode: res.data.pageCode,
        pageStatus: res.data.pageStatus,
        remark: res.data.remark,
      }
      open.value=true
    }
  })
}
// 删除
function handleDelete(row) {
  ElMessageBox.confirm(
      "此操作将永久删除名称为【" + row.pageName + "】的页面, 是否继续?",
      "提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }
  )
      .then(() => {
        deletePageDesigner(row.id).then((res) => {
          if (res.code === "1") {
            ElMessage({
              type: "success",
              message: "删除成功!",
            });
          } else {
            ElMessage({
              type: "error",
              message: "删除失败!",
            });
          }
          getList();
        });
      })
      .catch(() => {
        ElMessage({
          type: "info",
          message: "已取消删除",
        });
      });
}
//提交
function submitForm() {
  proxy.$refs["formRef"].validate((valid) => {
    if (form.value.id === undefined) {
      addPageDesigner(form.value).then((res) => {
        if (res.code === "1") {
          open.value = false;
          ElMessage.success("新增成功！");
          getList();
        } else {
          ElMessage.error(res.message);
        }
      });
    } else {
      updatePageDesigner(form.value).then((res) => {
        if (res.code === "1") {
          open.value = false;
          ElMessage.success("修改成功！");
          getList();
        } else {
          ElMessage.error(res.message);
        }
      });
    }
  })

}
//取消
function cancel() {
  open.value = false;
  reset();
}

/** 复制 */
function handleCopy(row){
  ElMessageBox.confirm(
      "此操作将复制名称为【" + row.pageName + "】的页面, 是否继续?",
      "提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }
  )
      .then(() => {
        copyPageDesigner(row.id).then((res) => {
          if (res.code === "1") {
            ElMessage.success("复制成功！");
            getList();
          } else {
            ElMessage.error(res.message);
          }
        });
      })
      .catch(() => {
        ElMessage({
          type: "info",
          message: "已取消复制",
        });
      });
}


/** 菜单对话框 **/
const menuQueryParams=ref({
  tenantName: userStore.userInfo.tenantName,
  tenantId: userStore.userInfo.tenantId,
  permissionName: undefined,
  permissionStatus: undefined,
  permissionScope: undefined
})
//菜单树选项
import SvgIcon from "@/components/SvgIcon";
import IconSelect from "@/components/IconSelect";
import { addMenu, delMenu, getMenuDetail, listMenu, updateMenu } from "@/api/system/menu";
const menuOptions=ref([])
const pageId=ref("")
const menuForm=ref({})
const menuOpen=ref(false)
const menuRef=ref()
const menuTitle = ref("");
//菜单校验规则
/** 校验规则 */
const validatorPermissionScope = (rule, value, callback) => {
  if (form.value.parentId === 0 && !value) {
    return callback(new Error('请选择菜单归属'))
  }
  if (form.value.parentId === 0) {
    const brotherMenus = menuOptions.value[0].children
    const sameScopeMenu = brotherMenus.find(m => (m.permissionScope === value && m.permissionId !== form.value.permissionId))
    if (sameScopeMenu) {
      return callback(new Error('同一种归属，只能存在一个根目录'))
    }
    return callback()
  }
  return callback()
}
const validatorUri = (rule, value, callback) => {
  if (form.value.permissionType === 'folder' || form.value.permissionType === 'menu') {
    if (!value) {
      return callback(new Error('请输入路由标识'))
    }
    if (/^\/.*\/$/.test(value)) {
      return callback(new Error('不以“/”开头或者结尾'))
    }
    return callback()
  }
  return callback()
}
const validatorCode = (rule, value, callback) => {
  if (form.value.permissionType === 'folder' || form.value.permissionType === 'menu') {
    if (value && !/^(\/systemViews\/|\/portalViews\/|https:\/\/|http:\/\/|inner:\/\/).+/.test(value)) {
      return callback(new Error('需以 /systemViews、/portalViews、https://、http://、inner:// 开头'))
    }
    return callback()
  }
  return callback()
}
const menuRules = {
  permissionScope: [{ validator: validatorPermissionScope, trigger: 'blur' }],
  permissionType: [{ required: true, message: "菜单类型不能为空", trigger: "blur" }],
  permissionName: [{ required: true, message: "菜单名称不能为空", trigger: "blur" }],
  uri: [{ validator: validatorUri, trigger: "blur" }],
  code: [{ validator: validatorCode, trigger: "blur" }]
}
function getTreeselect(permissionScope) {
  const params = { tenantId: menuQueryParams.value.tenantId }
  if (permissionScope) {
    params.permissionScope = permissionScope
  }
  listMenu(params).then(response => {
    if (response.success) {
      const menu = { permissionId: 0, permissionName: "主类目", children: [] };
      menu.children = proxy.handleTree(response.data, "permissionId");
      menuOptions.value = [menu];
    }
  });
}
function menuCancel() {
  menuOpen.value = false;
  menuReset();
}
function menuReset() {
  menuForm.value = {
    permissionId: undefined,
    parentId: 0,
    permissionName: undefined,
    icon: undefined,
    permissionType: undefined,
    permissionSort: undefined,
    permissionVisible: "show",
    permissionStatus: "valid"
  };
  proxy.resetForm("menuRef");
}
function showSelectIcon() {
  iconSelectRef.value.reset();
}
function selected(name) {
  menuForm.value.icon = name;
}
function onParentChange(item) {
  if (item.permissionScope) {
    menuForm.value.permissionScope = item.permissionScope;
  }
}
/** 获取租户名称 */
function getTenantName(id) {
  const tenant = tenantList.value.find(t => t.tenantId === id)
  return tenant && tenant.tenantName
}
/** 设置菜单*/
function setToMenu(row) {
  menuReset();
  getTreeselect();
  pageId.value=row.id
  if (row && row.permissionId) {
    menuForm.value.parentId = row.permissionId;
  } else {
    menuForm.value.parentId = 0;
  }
  if (row.permissionScope) {
    menuForm.value.permissionScope = row.permissionScope;
  }
  menuForm.value.tenantId = menuQueryParams.value.tenantId;
  menuForm.value.tenantName =getTenantName(menuQueryParams.value.tenantId);
  menuForm.value.code = "pageView://" + row.id;
  menuOpen.value = true;
  menuTitle.value = "添加菜单";
}
/** 撤销菜单 */
function cancelToMenu(row) {
  ElMessageBox.confirm(
      "此操作将在菜单中删除名称为【" + row.pageName + "】的菜单, 是否继续?",
      "提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(()=>{
    updatePageMenuStatus({id: row.id, flag: false}).then(res =>{
      if (res.data){
        ElMessage.success("删除菜单成功！");
      }else {
        ElMessage.error("删除菜单失败")
      }
      getList();
    })
  }).catch(() => {
    ElMessage({
      type: "info",
      message: "已取消操作",
    });
  });

}

/** 提交菜单按钮 */
function menuSubmitForm() {
  proxy.$refs["menuRef"].validate(valid => {
    if (valid) {
      addMenu(menuForm.value).then(response => {
        if (response.success) {
          return updatePageMenuStatus({id: pageId.value, flag: true});
        }else {
          proxy.$modal.msgError(response.message);
        }
        proxy.$modal.msgSuccess("新增成功");
        menuOpen.value = false;
        getList();
      }).then(
          res =>{
            if (res.data){
              getList();
              proxy.$modal.msgSuccess("生成菜单成功，请前往菜单对应目录查看");
              menuOpen.value = false;
            }
      }
      )
    }
  });
}

/** 设计页面*/
function goDefinition(row, editLayout) {
  // proxy.$router.push({
  //   path: `/system/extended/pageDefinition/${row.id}`,
  // });
  const routeUrl = proxy.$router.resolve({
    path: "/page/grid",
    query: { id: row.id }
  });
  window.open(routeUrl.href, '_blank');
}
/** 预览页面**/
function goPreview(row) {
  const routeUrl = proxy.$router.resolve({
    path: "/page/grid/preview",
    query: { id: row.id }
  });
  window.open(routeUrl.href, '_blank');
}
getList()
initTenantList()
</script>

<style scoped>

</style>
