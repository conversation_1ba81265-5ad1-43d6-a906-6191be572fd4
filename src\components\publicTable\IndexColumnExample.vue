<template>
  <div class="index-column-example">
    <h3>序号列宽度调整示例</h3>
    
    <!-- 表格组件 -->
    <public-table
      :table-data="tableData"
      :columns="columns"
      :config-flag="configFlag"
      :index-width="indexWidth"
      :index-name="indexName"
      :index-fixed="indexFixed"
      @index-width-change="handleIndexWidthChange"
    />
    
    <!-- 控制面板 -->
    <div class="control-panel">
      <h4>序号列配置</h4>
      <el-form :model="form" label-width="120px">
        <el-form-item label="序号列宽度:">
          <el-slider 
            v-model="indexWidth" 
            :min="50" 
            :max="150" 
            show-input
            @change="handleSliderChange"
          />
        </el-form-item>
        
        <el-form-item label="序号列名称:">
          <el-input v-model="indexName" placeholder="请输入序号列名称" style="width: 200px;" />
        </el-form-item>
        
        <el-form-item label="是否固定:">
          <el-switch v-model="indexFixed" />
        </el-form-item>
        
        <el-form-item label="显示序号列:">
          <el-switch v-model="configFlag.index" />
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import PublicTable from './index.vue'

// 序号列配置
const indexWidth = ref(60)
const indexName = ref('#')
const indexFixed = ref(false)

// 表格配置
const configFlag = reactive({
  loading: false,
  needPage: true,
  selection: false,
  index: true, // 显示序号列
  height: 400
})

// 表格数据
const tableData = ref([
  { id: 1, name: '张三', age: 25, department: '技术部', salary: 8000 },
  { id: 2, name: '李四', age: 30, department: '产品部', salary: 9000 },
  { id: 3, name: '王五', age: 28, department: '设计部', salary: 7500 },
  { id: 4, name: '赵六', age: 32, department: '运营部', salary: 8500 },
  { id: 5, name: '钱七', age: 27, department: '市场部', salary: 7800 }
])

// 表格列配置
const columns = ref([
  {
    fieldIndex: 'name',
    label: '姓名',
    visible: true,
    width: 120,
    align: 'center'
  },
  {
    fieldIndex: 'age',
    label: '年龄',
    visible: true,
    width: 80,
    align: 'center'
  },
  {
    fieldIndex: 'department',
    label: '部门',
    visible: true,
    width: 150,
    align: 'center'
  },
  {
    fieldIndex: 'salary',
    label: '薪资',
    visible: true,
    width: 120,
    align: 'center',
    type: 'dollor'
  }
])

// 表单数据
const form = reactive({
  indexWidth: 60,
  indexName: '#',
  indexFixed: false
})

// 处理序号列宽度变化
const handleIndexWidthChange = (newWidth) => {
  console.log('序号列宽度变化:', newWidth)
  indexWidth.value = newWidth
}

// 处理滑块变化
const handleSliderChange = (value) => {
  console.log('滑块值变化:', value)
}
</script>

<style scoped>
.index-column-example {
  padding: 20px;
}

.control-panel {
  margin-top: 20px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #f9f9f9;
}

.control-panel h4 {
  margin-top: 0;
  color: #303133;
}
</style>
