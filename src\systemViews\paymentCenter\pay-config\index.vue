<!-- 支付中心-账号管理 -->
 

<template>
    <div>
        <TabContainers :tabList="tabList" :activeName="activeName"></TabContainers>
    </div> 

</template>

<script setup name="accountManagement">
// tab切换
import TabContainers from '@/components/TabContainer/index';
import payScene from '@/systemViews/paymentCenter/pay-config/components/payScene/index'
import paymentType from '@/systemViews/paymentCenter/pay-config/components/payment-type/index'
import { ref } from "vue";
// 定义 tab 列表
const tabList = ref([
  {
    label: '支付场景',
    value: '01',
    component: payScene
  },
  {
    label: '支付类型',
    value: '02',
    component: paymentType
  },
 
])
// 默认激活的 tab
const activeName = ref('01')
</script>

<style scoped lang="scss">
:deep(.el-tabs .el-tabs__content .content) {
    height: calc(100vh - 250px);
}
</style>