<template>
  <div class="gridstyle">
    <!-- 标题 -->
    <h2>{{ datas.config.moduleTitle }}</h2>

    <!-- 提示 -->
    <p style="color: #969799; font-size: 12px; margin-top: 10px">
      拖动选中的导航可对其排序
    </p>

    <!-- 图片广告 v-if="datas.data[0]"-->
    <div>
      <vuedraggable
          :list="datas.data"
          item-key="index"
          :forceFallback="true"
          v-bind="dragOptions" >
        <template #item="{element,index}">
          <section class="imgBanner">
            <van-icon
                class="el-icon-circle-close"
                name="close"
                @click="deleteimg(index)"
            />
            <div class="imag">
              <img draggable="false" :src="element.routerIcon" alt="" />
            </div>
            <div class="imgText">
              <el-input
                  placeholder="请输入标题，也可不填"
                  v-model="element.routerTitle"
                  size="small"
              />
              <div class="select-type">
                <el-select
                    style="width: 100%"
                    v-model="element.routerType"
                    placeholder="请选择路由类型"
                    size="small"
                >
                  <el-option
                      v-for="item in routerTypeList"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                  >
                  </el-option>
                </el-select>
              </div>
              <el-input
                  v-model="element.routerUrl"
                  placeholder="请输入http地址，本地路由页面地址、本地html路径"
                  size="small"
                  type="textarea"
              ></el-input>
            </div>
          </section>
        </template>
      </vuedraggable>
    </div>

    <!-- 上传图片 -->
    <el-button @click="showUpload('0')" class="uploadImg" type="primary" plain
    ><i class="el-icon-plus" />点击添加导航</el-button
    >

    <div class="bor"></div>

    <!-- 表单 -->
    <el-form label-width="80px" :model="datas" size="small" style="margin-top: 20px">
      <div style="height: 10px" />

      <el-form-item label="标题">
        <el-input
          v-model="datas.config.moduleTitle"
          placeholder="请输入标题"
          show-word-limit
        />
      </el-form-item>

      <div style="height: 10px" />

      <el-form-item class="lef" label="是否显示标题图标" label-width="150px">
        <el-radio-group v-model="datas.config.isShowIcon">
          <el-radio-button :label="true">是</el-radio-button>
          <el-radio-button :label="false">否</el-radio-button>
        </el-radio-group>
      </el-form-item>

      <div style="height: 10px" />

      <!-- 一屏显示 -->
      <el-form-item v-show="datas.config.imgStyle === 1" class="lef" label="一行显示数量" label-width="120px">
        <el-select
          v-model="datas.config.colNum"
          placeholder="请选择活动区域"
          style="margin-left: 90px"
        >
          <el-option
            v-for="index in 6"
            :label="index + '个导航'"
            :value="index"
            :key="index"
          ></el-option>
        </el-select>
      </el-form-item>

      <div style="height: 10px" />

      <!-- 图片倒角 -->
      <el-form-item label="图片倒角" class="lef borrediu">
        <el-slider
          v-model="datas.config.borderRadius"
          :max="50"
          input-size="small"
          show-input
        >
        </el-slider>
      </el-form-item>

      <div style="height: 10px" />

      <!-- 背景颜色 -->
      <el-form-item class="lef" label="背景颜色">
        <!-- 颜色选择器 -->
        <el-color-picker
          v-model="datas.config.backgroundColor"
          show-alpha
          class="picke"
          :predefine="predefineColors"
        >
        </el-color-picker>
      </el-form-item>

      <div style="height: 10px" />

      <!-- 文字颜色 -->
      <el-form-item class="lef" label="文字颜色">
        <!-- 颜色选择器 -->
        <el-color-picker
          v-model="datas.config.textColor"
          show-alpha
          class="picke"
          :predefine="predefineColors"
        >
        </el-color-picker>
      </el-form-item>
    </el-form>

    <!-- 上传图片 -->
    <uploadimg ref="upload" @uploadInformation="uploadInformation" />
  </div>
</template>

<script>
import uploadimg from '../../uploadImg' //图片上传
import vuedraggable from 'vuedraggable' //拖拽组件

export default {
  name: 'gridstyle',
  props: {
    datas: Object,
  },
  data() {
    return {
      dragOptions: {
        //拖拽配置
        animation: 200,
      },
      predefineColors: [
        // 颜色选择器预设
        '#ff4500',
        '#ff8c00',
        '#ffd700',
        '#90ee90',
        '#00ced1',
        '#1e90ff',
        '#c71585',
        '#409EFF',
        '#909399',
        '#C0C4CC',
        'rgba(255, 69, 0, 0.68)',
        'rgb(255, 120, 0)',
        'hsv(51, 100, 98)',
        'hsva(120, 40, 94, 0.5)',
        'hsl(181, 100%, 37%)',
        'hsla(209, 100%, 56%, 0.73)',
        '#c7158577',
      ],
      routerTypeList: [
        {
          value: 'netWeb',
          label: '网页h5页面',
        },
        {
          value: 'localPage',
          label: '内部路由',
        },
        {
          value: 'localWeb',
          label: '本地网页',
        },
      ], // 选择跳转类型
      uploadImgDataType: null,
    }
  },
  created() {
    if (this.datas.data.length > this.datas.config.colNum) {
      this.datas.imageList.splice(0)
      this.datas.config.rowNum = this.datas.data.length / this.datas.config.colNum
      for(var i = 0,len = this.datas.data.length; i < len; i += this.datas.config.colNum){
        this.datas.imageList.push(this.datas.data.slice(i, i + this.datas.config.colNum));
      }
    } else {
      this.datas.imageList.splice(0)
      this.datas.imageList.push(this.datas.data);
    }
  },
  watch: {
    'datas.config.colNum'(newVal, oldVal){
      if (this.datas.data.length > this.datas.config.colNum) {
        this.datas.imageList.splice(0)
        this.datas.config.rowNum = this.datas.data.length / this.datas.config.colNum
        for(var i = 0,len = this.datas.data.length; i < len; i += this.datas.config.colNum){
          this.datas.imageList.push(this.datas.data.slice(i, i + this.datas.config.colNum));
        }
      } else {
        this.datas.imageList.splice(0)
        this.datas.imageList.push(this.datas.data);
      }
    },
    'datas.data'(newVal, oldVal){
      if (this.datas.data.length > this.datas.config.colNum) {
        this.datas.imageList.splice(0)
        this.datas.config.rowNum = this.datas.data.length / this.datas.config.colNum
        for(var i = 0,len = this.datas.data.length; i < len; i += this.datas.config.colNum){
          this.datas.imageList.push(this.datas.data.slice(i, i + this.datas.config.colNum));
        }
      } else {
        this.datas.imageList.splice(0)
        this.datas.imageList.push(this.datas.data);
      }
    }
  },
  methods: {
    showUpload(type) {
      this.uploadImgDataType = type
      this.$refs.upload.showUpload()
    },
    // 提交
    uploadInformation(res) {
      if (this.uploadImgDataType === '0') {
        this.datas.data.push({
          routerIcon: res,
          routerIconType: 'localUrl',
          routerType: 'localPage',
          customParams: {},
        })
      } else if (this.uploadImgDataType === '1') {
        this.datas.config.bgImg = res
      }
    },

    // 清空背景图片
    clear() {
      this.datas.config.bgImg = ''
    },
    /* 删除图片列表的图片 */
    deleteimg(index) {
      this.datas.data.splice(index, 1)
    },
  },
  components: { uploadimg, vuedraggable },
}
</script>

<style scoped lang="less">
.gridstyle {
  width: 100%;
  position: relative;
  left: 0;
  top: 0;
  padding: 0 10px 20px;
  box-sizing: border-box;
  max-height: 500px;
  /* 标题 */
  h2 {
    padding: 24px 16px 24px 0;
    margin-bottom: 15px;
    border-bottom: 1px solid #f2f4f6;
    font-size: 18px;
    font-weight: 600;
    color: #323233;
  }

  .lef {
    :deep(.el-form-item__label) {
      text-align: left;
    }
  }

  /* 上传图片按钮 */
  .uploadImg {
    width: 345px;
    height: 40px;
    margin-top: 10px;
  }

  /* 商品列表 */
  .imgList {
    padding: 6px 12px;
    margin: 16px 7px;
    border-radius: 2px;
    background-color: #fff;
    box-shadow: 0 0 4px 0 rgba(10, 42, 97, 0.2);
    display: flex;
    position: relative;

    /* 删除图标 */
    .el-icon-circle-close {
      position: absolute;
      right: -10px;
      top: -10px;
      cursor: pointer;
      font-size: 19px;
    }

    /* 图片 */
    .imag {
      width: 60px;
      height: 60px;
      border-radius: 5px;
      overflow: hidden;
      position: relative;
      cursor: pointer;
      img {
        width: 100%;
        height: 100%;
        display: inline-block;
      }
      span {
        background: rgba(0, 0, 0, 0.5);
        font-size: 12px;
        position: relative;
        left: 0px;
        bottom: 0px;
        display: inline-block;
        width: 100%;
        text-align: center;
        color: #fff;
        height: 20px;
        line-height: 20px;
      }
    }

    /* 图片字 */
    .imgText {
      width: 80%;
      display: flex;
      flex-direction: column;
      box-sizing: border-box;
      padding-left: 20px;
      justify-content: space-between;
      .select-type {
        display: flex;
        :deep(.el-select) {
          .el-input {
            input {
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
        }
      }
    }
  }

  /* 图片样式 */
  .weiz {
    text-align: right;
    i {
      padding: 5px 14px;
      margin-left: 10px;
      border-radius: 0;
      border: 1px solid #ebedf0;
      font-size: 20px;
      font-weight: 500;
      cursor: pointer;

      &:last-child {
        font-size: 22px;
      }

      &.active {
        color: #155bd4;
        border: 1px solid #155bd4;
        background: #e0edff;
      }
    }
  }

  .shop-head-pic {
    color: #ababab;
    display: flex;
    flex-direction: column;
    .home-bg {
      width: 100px;
      height: 100px;
      margin: 10px auto;
    }
    .shop-head-pic-btn {
      display: flex;
      flex-direction: row;
      .el-button {
        flex: 1;
      }
    }
  }
  /* 颜色选择器 */
  .picke {
    float: right;
  }
  /* 图片广告列表 */
  .imgBanner {
    padding: 6px 12px;
    margin: 16px 7px;
    border-radius: 2px;
    background-color: #fff;
    box-shadow: 0 0 4px 0 rgba(10, 42, 97, 0.2);
    display: flex;
    position: relative;

    /* 删除图标 */
    .el-icon-circle-close {
      position: absolute;
      right: -10px;
      top: -10px;
      cursor: pointer;
      font-size: 19px;
    }

    /* 图片 */
    .imag {
      width: 60px;
      height: 60px;
      border-radius: 5px;
      overflow: hidden;
      position: relative;
      cursor: pointer;
      img {
        width: 100%;
        height: 100%;
        display: inline-block;
      }
      span {
        background: rgba(0, 0, 0, 0.5);
        font-size: 12px;
        position: relative;
        left: 0px;
        bottom: 0px;
        display: inline-block;
        width: 100%;
        text-align: center;
        color: #fff;
        height: 20px;
        line-height: 20px;
      }
    }

    /* 图片字 */
    .imgText {
      width: 80%;
      display: flex;
      flex-direction: column;
      box-sizing: border-box;
      padding-left: 20px;
      justify-content: space-between;
      .select-type {
        display: flex;
        :deep(.el-select) {
          .el-input {
            input {
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
        }
      }
    }
  }
}
</style>
