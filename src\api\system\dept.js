import request from "@/utils/request";

// 查询部门列表
export function listDept(data) {
  return request({
    url: "/user/orgs",
    method: "get",
    params: data
  });
}

// 查询部门列表（排除节点）
export function listDeptExcludeChild(deptId) {
  return request({
    url: "/system/dept/list/exclude/" + deptId,
    method: "get"
  });
}

// 查询部门详细
export function getDept(orgId) {
  return request({
    url: "/user/orgs/" + orgId,
    method: "get"
  });
}

// 查询部门下拉树结构
export function treeselect(data) {
  return request({
    url: "/user/orgs/upAndDown",
    method: "post",
    data
  });
}

// 查询侧边栏组织树
export function getOrgSidebar(data) {
  return request({
    url: "/user/orgs/getOrgSidebar",
    method: "post",
    data
  });
}

// 根据角色ID查询部门树结构
export function roleDeptTreeselect(roleId) {
  return request({
    url: "/system/dept/roleDeptTreeselect/" + roleId,
    method: "get"
  });
}

// 新增部门
export function addDept(data) {
  return request({
    url: "/user/orgs",
    method: "post",
    data: data
  });
}

// 修改部门
export function updateDept(data) {
  return request({
    url: "/user/orgs/edit",
    method: "post",
    data: data
  });
}

// 删除部门
export function delDept(data) {
  return request({
    url: "/user/orgs/delete",
    method: "post",
    data: data
  });
}

export function treeAll(params) {
  return request({
    url: "/user/orgs/all",
    method: "get",
    params
  });
}


// 导入
export function upload(data) {
  return request({
    url: "/user/orgs/upload",
    method: "post",
    data
  });
}
