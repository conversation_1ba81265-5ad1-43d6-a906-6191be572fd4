import { login, logout, getInfo } from "@/api/login";
import { getToken, setToken, removeToken } from "@/utils/auth";
import { isRelogin } from "@/utils/request";
import { ElMessageBox } from "element-plus";

const useUserStore = defineStore("user", {
  state: () => ({
    stateJwtToken: "",
    additionalInformation: {},
    userInfo: {},
    headImg: "",
  }),
  getters: {
    jwtToken() {
      if (this.stateJwtToken) {
        return this.stateJwtToken;
      }
      const storageJwtToken = getToken();
      if (storageJwtToken) {
        this.stateJwtToken = storageJwtToken;
        return storageJwtToken;
      }
    },
    InforAvailable() {
      if (this.userInfo && this.userInfo.staffName) {
        return true;
      } else {
        return false;
      }
    },
    permissions() {
      if (this.userInfo && this.userInfo.authorities) {
        return this.userInfo.authorities;
      } else {
        return [];
      }
    },
  },
  actions: {
    // 登录
    login(params) {
      return new Promise((resolve, reject) => {
        isRelogin.show = true;
        const dataForm = new FormData();
        dataForm.append("tenantId", params.tenantId.trim());
        dataForm.append("username", params.username.trim());
        dataForm.append("password", params.password);
        dataForm.append("captcha", params.captcha);
        dataForm.append("uid", params.uid);
        login(dataForm)
          .then((res) => {
            isRelogin.show = false;
            if (res.success) {
              this.stateJwtToken = res.data.value;
              setToken(res.data.value);
              this.additionalInformation = res.data.additionalInformation;
              resolve();
            } else {
              reject();
            }
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
    // 获取用户信息
    getInfo() {
      return new Promise((resolve, reject) => {
        getInfo({ token: this.jwtToken })
          .then((res) => {
            this.userInfo = res;
            resolve();
          })
          .catch(() => {
            resolve();
          });
      });
    },
    // 退出系统
    logOut() {
      ElMessageBox.confirm("确定注销并退出系统吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          logout()
            .then((res) => {
              if (res.success) {
                this.stateJwtToken = "";
                this.additionalInformation = {};
                this.userInfo = {};
                removeToken();
                location.href = "";
                // location.href = '/system/index';
              }
            })
            .catch((error) => {
              console.log(error);
            });
        })
        .catch(() => {});
    },
  },
});

export default useUserStore;
