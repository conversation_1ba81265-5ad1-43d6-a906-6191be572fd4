<template>
  <el-card
      :class="bgShow === true ?'notice-card':'unshow-notice-card'"
      v-loading="loading"
      shadow="never"
  >
    <div slot="header" v-if="titleShow">
      <span class="card-title">
        <div id="title">
          <i class="icon iconfont icon-yewudongtai"></i>{{ title }}
        </div>
        <el-button link class="exec" icon="DArrowRight" v-if="moreShow"
        >更多</el-button>
      </span>
    </div>
    <div class="home-card-body">
      <div class="cardBox">
        <div class="toDo">待办 <span>3</span></div>
        <div class="warn">预警 <span>2</span></div>
        <div class="solved">已办 <span>125</span></div>
        <div class="official">公文签批</div>
      </div>

      <el-table :data="tableData" stripe class="table">
        <el-table-column
            prop="order"
            label="序号"
            width="180"
            header-align="center"
            align="center"
        >
        </el-table-column>
        <el-table-column
            prop="name"
            label="事件描述"
            width="180"
            header-align="center"
            align="center"
        >
        </el-table-column>
        <el-table-column
            prop="thing"
            label="事件来源"
            header-align="center"
            align="center"
        >
        </el-table-column>
        <el-table-column
            prop="area"
            label="区县"
            header-align="center"
            align="center"
        >
        </el-table-column>
        <el-table-column
            prop="town"
            label="乡镇"
            header-align="center"
            align="center"
        >
        </el-table-column>
        <el-table-column
            prop="now"
            label="当前步骤"
            header-align="center"
            align="center"
        >
        </el-table-column>
        <el-table-column
            prop="name"
            label="操作"
            header-align="center"
            align="center"
        >
        </el-table-column>
      </el-table>
    </div>
  </el-card>
</template>

<script setup name="StaticRoutine">
const props = defineProps({
  titleShow:{
    type: Boolean,
    default: true
  },
  title:{
    type: String,
    default: "日常工作"
  },
  moreShow:{
    type: Boolean,
    default: true
  },
  moreLink:{
    type: String,
    default: ""
  },
  bgShow: {
    type: Boolean,
    default: true
  }
})
const loading=ref(false)
const tableData=[
  {
    order: "1",
    date: "2016-05-02",
    name: "渣土料堆和裸露的土地未覆盖防尘网或防尘网不达标，产生扬尘污染",
    thing: "登记事件",
    area: "任城区",
    town: "越河街道",
    now: "市级审核",
  },
  {
    order: "2",
    date: "2016-05-04",
    name: "生活污水直排环境或雨污混排",
    address: "上海市普陀区金沙江路 1517 弄",
    thing: "巡查登记",
    area: "任城区",
    town: "越河街道",
    now: "市级审核",
  },
  {
    order: "3",
    date: "2016-05-04",
    name: "企业生产废气无组织排放，有明显异味",
    address: "上海市普陀区金沙江路 1517 弄",
    thing: "巡查登记",
    area: "任城区",
    town: "越河街道",
    now: "市级审核",
  },
]
</script>

<style scoped lang="scss">
.notice-card {
  background: #fff;
  height: 100%;
  overflow-y: scroll;
}
.unshow-notice-card {
  background: #f5f9fa;
  height: 100%;
  border: 0px;
  overflow-y: scroll;

}
.card-title {
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .icon {
    font-size: 18px;
    color: #419eee;
    padding: 0px 5px;
    -webkit-transition: font-size 0.25s linear, width 0.25s linear;
    -moz-transition: font-size 0.25s linear, width 0.25s linear;
    transition: font-size 0.25s linear, width 0.25s linear;
  }
  .exec {
    padding: 3px 0;
  }
}
.notice {
  display: flex;
  justify-content: space-between;
  flex-direction: row;
  padding: 4px 0;
  font-size: 14px;
  .notice-title {
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all;
    white-space: nowrap;
    flex-basis: 70%;
  }
  .notice-time {
    flex-basis: 20%;
    text-align: right;
  }
}
.cardBox {
  width: 100%;
  display: flex;
  color: #fff;
  text-align: center;
  line-height: 50px;
  font-size: 18px;
  justify-content: space-between;
  .toDo {
    width: 22%;
    height: 50px;
    background-color: #fb9678;
  }
  .warn {
    width: 22%;
    height: 50px;
    background-color: #a234ba;
  }
  .solved {
    width: 22%;
    height: 50px;
    background-color: #429ded;
  }
  .official {
    width: 22%;
    height: 50px;
    background-color: #1963b6;
  }
}
.table {
  margin-top: 10px;
}
</style>
