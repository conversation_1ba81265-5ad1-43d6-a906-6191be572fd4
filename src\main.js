import { createApp } from "vue";
// import "default-passive-events";
import ElementPlus from "element-plus";
import "element-plus/dist/index.css";
import zhCN from "element-plus/dist/locale/zh-cn.mjs"; // 中文语言

import "@/assets/styles/index.scss"; // global css
import "@/assets/styles/js-pro.v.1.1/index.css"; // global css

import '@/assets/iconfont/iconfont.css' // iconfont.css
import "@/utils/rem.js"; // 自适应分辨率

import App from "./App";
import store from "./store";
import router from "./router";
import directive from "./directive"; // directive

// 注册指令
import plugins from "./plugins"; // plugins
import { download,downloadJson } from "@/utils/request";
import { formatDictLabel,getSelectedLabels,formatDictLabels } from "@/utils/common";
// svg图标
import "virtual:svg-icons-register";
import SvgIcon from "@/components/SvgIcon";
import elementIcons from "@/components/SvgIcon/svgicon";

import "./permission"; // permission control

import { useDict } from "@/utils/dict";
import { parseTime, resetForm, addDateRange, handleTree, selectDictLabel, selectDictLabels } from "@/utils/common";
import { cmsImg } from '@/utils/config'; 
// 分页组件
import Pagination from "@/components/Pagination";
// 自定义表格工具组件
import RightToolbar from "@/components/RightToolbar";
// 富文本组件
import Editor from "@/components/Editor";
// 图片上传组件
import ImageUpload from "@/components/ImageUpload";
// 图片预览组件
import ImagePreview from "@/components/ImagePreview";
import PreviewImage from "@/components/PreviewImage";
// 字典标签组件
import DictTag from "@/components/DictTag";
// 引入窗格拆分组件
import {Splitpanes, Pane} from "splitpanes";
import 'splitpanes/dist/splitpanes.css'

//table 二次封装
import PublicTable from '@/components/publicTable/index';
// 搜索框
import DialogSearch from '@/components/dialogSearch/index';
// 弹窗

import DialogBox from '@/components/Dialog/index';

// 租户封装

import TenantSelect from '@/components/TenantSelect/index';


// 封装选择树
import TreeSelect from '@/components/TreeSelect/index';
import TreeSelects from '@/components/TreeSelects/index';
// 封装 数字输入框
import NumberInput  from '@/components/NumberInput/index';


// 人员信息

// import 'amfe-flexible'
import faceInfo  from '@/components/face/face';
// 添加checkPermi
import { checkPermi } from "@/utils/permission"


const app = createApp(App);

// 全局方法挂载
app.config.globalProperties.useDict = useDict;
app.config.globalProperties.download = download;
app.config.globalProperties.downloadJson = downloadJson;
app.config.globalProperties.parseTime = parseTime;
app.config.globalProperties.resetForm = resetForm;
app.config.globalProperties.handleTree = handleTree;
app.config.globalProperties.addDateRange = addDateRange;
app.config.globalProperties.selectDictLabel = selectDictLabel;
app.config.globalProperties.selectDictLabels = selectDictLabels;
app.config.globalProperties.PublicTable = PublicTable;
app.config.globalProperties.DialogSearch = DialogSearch;
app.config.globalProperties.DialogBox = DialogBox;
app.config.globalProperties.TenantSelect = TenantSelect;
app.config.globalProperties.TreeSelect = TreeSelect;
app.config.globalProperties.TreeSelects = TreeSelects;
app.config.globalProperties.portalIndex = "/portal/index";
app.config.globalProperties.$formatDictLabel = formatDictLabel
app.config.globalProperties.$formatDictLabels = formatDictLabels
app.config.globalProperties.$getSelectedLabels = getSelectedLabels
app.config.globalProperties.NumberInput = NumberInput
app.config.globalProperties.cmsImg = cmsImg
app.config.globalProperties.$checkPermi = checkPermi
app.config.globalProperties.faceInfo = faceInfo
// 全局组件挂载
app.component("DictTag", DictTag);
app.component("Pagination", Pagination);
app.component("ImageUpload", ImageUpload);
app.component("ImagePreview", ImagePreview);
app.component("PreviewImage", PreviewImage);
app.component("RightToolbar", RightToolbar);
app.component("Editor", Editor);
app.component("Splitpanes", Splitpanes);
app.component("Pane", Pane);
app.component("PublicTable", PublicTable);
app.component("DialogSearch", DialogSearch);
app.component("DialogBox", DialogBox);
app.component("TenantSelect", TenantSelect);
app.component("TreeSelect", TreeSelect);
app.component("TreeSelects", TreeSelects);
app.component("NumberInput", NumberInput);
app.component("faceInfo", faceInfo);

import 'vant/lib/index.css'
import Vant from 'vant'


app.use(router);
app.use(store);
app.use(plugins);
app.use(elementIcons);
app.use(Vant)
app.component("svg-icon", SvgIcon);

directive(app);

// 使用element-plus 并且设置全局的大小
app.use(ElementPlus, {
  locale: zhCN,
  // 支持 large、default、small
  size: localStorage.getItem("layout-size-setting") || "default",
});


app.mount("#app");

// 知识库文档编辑组件
import VMdEditor from '@kangc/v-md-editor';
import '@kangc/v-md-editor/lib/style/base-editor.css';
import githubTheme from '@kangc/v-md-editor/lib/theme/github.js';
import '@kangc/v-md-editor/lib/theme/style/github.css';
VMdEditor.use(githubTheme);
app.use(VMdEditor);

// 知识库文档预览组件
import VMdPreview from '@kangc/v-md-editor/lib/preview';
import '@kangc/v-md-editor/lib/style/preview.css';
import vuepressTheme from '@kangc/v-md-editor/lib/theme/vuepress.js';
import '@kangc/v-md-editor/lib/theme/style/vuepress.css';
import Prism from 'prismjs';
import 'prismjs/components/prism-json';
VMdPreview.use(vuepressTheme, {
  Prism
})
app.use(VMdPreview)

import * as echarts from 'echarts';
app.config.globalProperties.$echarts = echarts

//skywalking
const skywalkingEnable = import.meta.env.VITE_SKYWALKING ? import.meta.env.VITE_SKYWALKING:"false";
//skywalking监控系统
import ClientMonitor from 'skywalking-client-js';


if("true"===skywalkingEnable){
  ClientMonitor.register({
    service: 'Portal-Vue3',//应用名称
    serviceVersion:'3.0.1',//应用版本号
    traceSDKInternal:true,//追踪sdk
    pagePath: location.href,//当前路由地址
    enableSPA:true,
    useFmp: true,
  });
}
