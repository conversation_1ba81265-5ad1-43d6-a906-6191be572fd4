import request from '@/utils/request'

/**
 * 车辆充值记录
 * @param data
 * @param params
 */
export function vehicleRechargeRecords(data, params) {
  return request({
    url: "/parking/vehicleRechargeManagement/vehicleRechargeRecords",
    method: "post",
    data: data,
    params: params
  })
}

/**
 * 初始化车辆充值信息
 * @param data
 */
export function initVehicleRechargeInfo(data) {
  return request({
    url: "/parking/vehicleRechargeManagement/initVehicleRechargeInfo",
    method: "post",
    data: data
  })
}

/**
 * 保存车辆充值记录
 * @param data
 */
export function saveVehicleRechargeRecord(data) {
  return request({
    url: "/parking/vehicleRechargeManagement/saveVehicleRechargeRecord",
    method: "post",
    data: data
  })
}

/**
 * 初始化月租金金额
 */
export function initMonthlyAmount() {
  return request({
    url: "/parking/vehicleRechargeManagement/initMonthlyAmount",
    method: "post"
  })
}

/**
 * 保存车辆月租金额
 * @param data
 */
export function saveVehicleMonthlyAmount(data) {
  return request({
    url: "/parking/vehicleRechargeManagement/saveVehicleMonthlyAmount",
    method: "post",
    data: data
  })
}

/**
 * 相关车牌号
 * @param data
 */
export function relevantLicensePlates(data) {
  return request({
    url: "/parking/licensePlateManagement/relevantLicensePlates",
    method: "post",
    data: data
  })
}

/**
 * 车辆充值图表数据
 * @param data
 */
export function vehicleRechargeChartData(data) {
  return request({
    url: "/parking/vehicleRechargeManagement/vehicleRechargeChartData",
    method: "post",
    data: data
  })
}
