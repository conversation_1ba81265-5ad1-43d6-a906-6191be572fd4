<!-- 账户设置 -->

<template>
  <div class="app-container-other">
    <Splitpanes class="default-theme">
      <Pane :size="100" :min-size="65">
        <el-card class="dep-card">
          <dialog-search @getList="getList" formRowNumber="4" :columns="tabelForm.columns"
           :isShowRightBtn="$checkPermi(['pay:accountManage:list'])">
            <template v-slot:formList>
              <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="75px">
                <el-form-item label="账户名称">
                  <el-input v-model="queryParams.accountName" placeholder="请输入账户名称" clearable></el-input>

                </el-form-item>
                <el-form-item label="账户编码">
                  <el-input v-model="queryParams.accountCode" placeholder="请输入账户编码" clearable></el-input>
                </el-form-item>
                <el-form-item label="账户类型">
                  <el-select v-model="queryParams.accountType" placeholder="请选择账户类型" clearable>
                    <el-option v-for="(item, index) of accountTypeList" :key="index" :label="item.label"
                      :value="item.value" />
                  </el-select>
                </el-form-item>
                <el-form-item label="账户状态" prop="accountStatus">
                  <el-select v-model="queryParams.accountStatus" placeholder="请选择账户状态" clearable>
                    <el-option v-for="(item, index) of account_status" :key="index" :label="item.label"
                      :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-form>
            </template>
            <template v-slot:searchList>
              <el-button type="primary" icon="Search" @click="handleQuery"
                v-hasPermi="['pay:accountManage:list']">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery" v-hasPermi="['pay:accountManage:list']">重置</el-button>
            </template>

            <template v-slot:searchBtnList>
              <el-button v-hasPermi="['pay:accountManage:add']" type="primary" icon="Plus" size="mini"
                @click="handleAdd">新增账户</el-button>
              <el-button icon="Download" @click="handleExport" v-hasPermi="['pay:accountManage:export']">导出 </el-button>
            </template>
          </dialog-search>

          <public-table ref="publictable" :rowKey="tabelForm.tableKey" :tableData="list" :columns="tabelForm.columns"
            :configFlag="tabelForm.tableConfig" :pageValue="pageParams" :total="total" :getList="getList">
            <template #operation="{ scope }">
              <el-button size="mini" type="text" v-hasPermi="['pay:accountManage:info']" title="查看" icon="View"
                @click="handleView(scope.row)">
              </el-button>
              <el-button v-hasPermi="['pay:accountManage:edit']" size="mini" type="text" title="修改" icon="Edit"
                @click="handleEdit(scope.row)">
              </el-button>

              <el-button v-hasPermi="['pay:accountManage:remove']" size="mini" type="text" title="删除" icon="Delete"
                @click="handleDelete(scope.row)">
              </el-button>
            </template>
 <template #zeroClearingSlot="{ scope }">
 <dict-tag :options="whether_clear" :value="scope.row.zeroClearing" />
 </template>
            
          </public-table>
        </el-card>
      </Pane>
    </Splitpanes>

    <DialogBox :visible="open1" :dialogWidth="diaWindow.dialogWidth" :dialogFooterBtn="diaWindow.dialogFooterBtn"
      @save="save" @cancellation="cancellation" @close="close" :dialogTitle="diaWindow.headerTitle">
      <template #content>
        <accountAdd ref="accountAddRef" :rowData="diaWindow.rowData" :popupType="diaWindow.popupType"
          @closeBtn="cancellationRefsh"></accountAdd>
      </template>
    </DialogBox>
  </div>
</template>

<script setup name="accountManagement">
import { ElMessage, ElMessageBox } from "element-plus";
import { ref, reactive, getCurrentInstance } from "vue";
import {
  screenIndex
} from "@/api/paymentCenter/account-management/account-settings/index";
import { apiUrl } from "@/utils/config";
import { formatMinuteTime } from "@/utils";
import accountAdd from "./components/add.vue";
const { proxy } = getCurrentInstance();
const { account_status, whether_open, whether_clear } = proxy.useDict(
  "account_status",
  "whether_open",
  "whether_clear"
);

const accountAddRef = ref(null);
const open1 = ref(false);
const diaWindow = reactive({
  headerTitle: "",
  popupType: "",
  rowData: "",
  dialogWidth: "30%",
  dialogFooterBtn: false,
});

const pageParams = ref({
  pageNum: 1,
  pageSize: 10,
});
const queryParams = ref({

});
const list = ref([]);
const total = ref(0);
const accountTypeList = ref([])
const tenantIdsList = ref([])
let tabelForm = reactive({
  tableKey: "1", //表格key值
  isShowRightToolbar: true, //是否显示右侧显示和隐藏
  showSearch: true,
  // 表格表格数据
  columns: [
    {
      fieldIndex: "accountName", // 对应列内容的字段名
      label: "账户名称", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "150", //最小宽度%
      width: "", //宽度
      align: "left", //表格对齐方式
    },
    {
      fieldIndex: "accountCode", // 对应列内容的字段名
      label: "账户编码", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "120", //最小宽度%
      width: "", //宽度
      align: "", //表格对齐方式
    },
    {
      fieldIndex: "accountType", // 对应列内容的字段名
      label: "账户类型", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "120", //最小宽度%
      width: "", //宽度
      align: "", //表格对齐方式
    },
    {
      fieldIndex: "accountStatus", // 对应列内容的字段名
      label: "账户状态", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "120", //最小宽度%
      width: "", //宽度
      align: "", //表格对齐方式
      type: "dict",
      dictList: account_status,
    },

    // {
    //   fieldIndex: "accountDescribe", // 对应列内容的字段名
    //   label: "账户说明", // 显示的标题
    //   resizable: true, // 对应列是否可以通过拖动改变宽度
    //   visible: true, // 展示与隐藏
    //   sortable: true, // 对应列是否可以排序
    //   fixed: "", //固定
    //   minWidth: "180", //最小宽度%
    //   width: "", //宽度
    //   align: "left", //表格对齐方式
    // },
    {
      fieldIndex: "paymentOrder", // 对应列内容的字段名
      label: "支付顺序", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "110", //最小宽度%
      width: "", //宽度
      align: "", //表格对齐方式
    },
    {
      fieldIndex: "opened", // 对应列内容的字段名
      label: "默认开通", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "110", //最小宽度%
      width: "", //宽度
      align: "", //表格对齐方式
      type: "dict",
      dictList: whether_open
    },
    {
      fieldIndex: "strSupplierName", // 对应列内容的字段名
      label: "供应商", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "150", //最小宽度%
      width: "", //宽度
      align: "left", //表格对齐方式
    },

    {
      fieldIndex: "strAdministratorsName", // 对应列内容的字段名
      label: "充值管理员", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "250", //最小宽度%
      width: "", //宽度
      align: "left", //表格对齐方式
    },

    {
      fieldIndex: "tenantId", // 对应列内容的字段名
      label: "分配租户", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "140", //最小宽度%
      width: "", //宽度
      type: "dict",
      dictList: tenantIdsList

    },

    {
      fieldIndex: "zeroClearing", // 对应列内容的字段名
      label: "是否清零", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "110", //最小宽度%
      width: "", //宽度
      align: "", //表格对齐方式
   slotname: "zeroClearingSlot",
      
    },
    {
      fieldIndex: "createDate", // 对应列内容的字段名
      label: "创建时间", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "180", //最小宽度%
      width: "", //宽度
    },

    {
      fieldIndex: "operator", // 对应列内容的字段名
      label: "操作人", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "120", //最小宽度%
      width: "", //宽度
    },

    {
      label: "操作",
      slotname: "operation",
      width: "100",
      fixed: "right", //固定
      visible: true,
    },
  ],
  // 表格基础配置项，看个人需求添加
  tableConfig: {
    needPage: true, // 是否需要分页
    index: true, // 是否需要序号
    selection: false, // 是否需要多选
    reserveSelection: false, // 是否需要支持跨页选择
    indexFixed: false, // 序号列固定 left true right
    selectionFixed: false, //多选列固定left true right
    indexWidth: "50", //序号列宽度
    loading: false, //表格loading
    showSummary: false, //是否开启合计
    height: null, //表格固定高度 比如：300px
  },
});


// 账户类型
const payAccountTypePageList = async () => {
  const res = await screenIndex.payAccountTypePageList({});

  accountTypeList.value = res.data.map(item => ({
    value: item.typeCode,    // 对应 typeCode
    label: item.typeName,    // 对应 typeName
    // 保留原始数据（根据需要可选）
    ...item
  }))
  .filter((value, index, self) => 
      index === self.findIndex((t) => (
        t.typeName === value.typeName // 这里根据 typeCode 进行去重
      ))
    );
  console.log(accountTypeList.value);

};

// 租户
const juniorList = async () => {
  const res = await screenIndex.juniorList({});
  tenantIdsList.value = res.data.map(item => ({
    value: item.tenantId,    // 对应 typeCode
    label: item.tenantName,    // 对应 typeName
    // 保留原始数据（根据需要可选）
    ...item
  }));
  tabelForm.columns[9].dictList = tenantIdsList.value

};
/** 查询列表 */
const getList = () => {
  tabelForm.tableConfig.loading = true;
  screenIndex.findPage(queryParams.value, pageParams.value).then((response) => {
    // 将英文的strAdministratorsName转换为中文
    list.value = response.data.records.map(item => {
      if (item.strAdministratorsName) {
        // 这里可以添加具体的英文到中文的映射逻辑
        // 例如可以通过字典或映射表来转换
        // 以下是一个简单示例，实际应根据业务需求实现
        item.strAdministratorsName = translateAdministratorNames(item.strAdministratorsName);
      }
      return item;
    });
    total.value = response.data.total;
    tabelForm.tableConfig.loading = false;
  });
};

// 将管理员名称从英文转为中文
const translateAdministratorNames = (names) => {
  // 如果有多个管理员名称（以逗号分隔）
  if (names.includes(',')) {
    return names.split(',').map(name => translateSingleName(name.trim())).join('，');
  }
  return translateSingleName(names);
};

// 单个名称翻译
const translateSingleName = (name) => {
  // 这里添加英文名到中文名的映射
  const nameMap = {
    // 示例映射，需要根据实际数据填充
    'admin': '管理员',
    'finance': '财务',
    'manager': '经理'
    // 添加更多映射...
  };
  
  return nameMap[name] || name; // 如果没有找到映射，返回原始名称
};
/** 搜索按钮操作 */
const handleQuery = () => {
  pageParams.value.pageNum = 1;
  getList();
};
/** 重置按钮操作 */
const resetQuery = () => {
  proxy.resetForm("queryForm");
  queryParams.value = {

  };
  handleQuery();
};

/** 导出按钮操作 */

const handleExport = () => {
  proxy.download(
    `pay${apiUrl}/pay/PayAccountManage/export`,
    {
      ...queryParams.value,
    },
    `账户设置列表_${formatMinuteTime(new Date())}.xlsx`
  );
};
/** 删除 */
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm("确认要删除吗？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });

    const res = await screenIndex.PayAccountManageDelete({ id: row.id });
    if (res.code == "1") {
      ElMessage.success("删除成功");
      await getList();
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    console.error("删除失败:", error);
    // 用户取消删除或其他错误
  }
};

/** 查看 */
const handleView = (data) => {
  diaWindow.headerTitle = "查看账户设置";
  diaWindow.popupType = "view";
  diaWindow.rowData = data;
  diaWindow.dialogWidth = "50%";
  diaWindow.dialogFooterBtn = false;
  open1.value = true;
};

// 新增
const handleAdd = () => {
  diaWindow.headerTitle = "新增账户设置";
  diaWindow.popupType = "add";
  diaWindow.rowData = {}; // 如果需要传递数据到弹窗
  diaWindow.dialogFooterBtn = true;
  diaWindow.dialogWidth = "50%";
  open1.value = true;
};

// 修改
const handleEdit = (row) => {
  diaWindow.headerTitle = "修改账户设置";
  diaWindow.popupType = "edit";
  diaWindow.rowData = row; // 如果需要传递数据到弹窗
  diaWindow.dialogFooterBtn = true;
  diaWindow.dialogWidth = "50%";
  open1.value = true;
};
/** 点击确定保存 */
const save = () => {
  if (diaWindow.popupType == "add") {
    accountAddRef.value.saveForm();
  }

  if (diaWindow.popupType == "edit") {
    accountAddRef.value.saveForm();
  }
};
/** 点击确定后刷新 */
const cancellationRefsh = () => {
  close(false);
  getList();
};
/** 点击取消保存 */
const cancellation = (val) => {
  close(false);
};

/** 关闭弹窗 */
const close = (val) => {
  open1.value = val;
};

juniorList()
payAccountTypePageList()
getList();
</script>

<style scoped>
.dep-card {
  min-height: calc(100vh - 160px);
}
</style>