import { getPersonalThemeConfig } from "@/api/system/config";
import { defaultSize, configCode } from "@/grid/config";
import useUserStore from "./user";

const useAppStore = defineStore("app", {
  state: () => ({
    sidebar: {
      opened: !!localStorage.getItem("sidebar-status-setting") || true,
      withoutAnimation: false,
      hide: false,
    },
    device: "desktop",
    size: localStorage.getItem("layout-size-setting") || "default",
    homeLayout: [],
    homeLayoutSize: defaultSize,
    homeThemeMode: "",
    showHomeDrawer: false,
  }),
  actions: {
    toggleSideBar(withoutAnimation) {
      if (this.sidebar.hide) {
        return false;
      }
      this.sidebar.opened = !this.sidebar.opened;
      this.sidebar.withoutAnimation = withoutAnimation;
      if (this.sidebar.opened) {
        localStorage.setItem("sidebar-status-setting", 1);
      } else {
        localStorage.setItem("sidebar-status-setting", 0);
      }
    },
    closeSideBar({ withoutAnimation }) {
      localStorage.setItem("sidebar-status-setting", 0);
      this.sidebar.opened = false;
      this.sidebar.withoutAnimation = withoutAnimation;
    },
    toggleDevice(device) {
      this.device = device;
    },
    setSize(size) {
      this.size = size;
      localStorage.setItem("layout-size-setting", size);
    },
    toggleSideBarHide(status) {
      this.sidebar.hide = status;
    },
    updateHomeDesign() {
      const userStore = useUserStore();
      const params = {
        staffOrgId: userStore.userInfo.staffOrgId,
        tenantId: userStore.userInfo.tenantId,
        configCode
      }
      return new Promise((resolve, reject) => {
        getPersonalThemeConfig(params).then((res) => {
          if (res.success && res.data.configValue) {
            this.homeThemeMode = res.data.themeMode
            const config = JSON.parse(res.data.configValue)
            this.homeLayout = config.layout || []
            this.homeLayoutSize = config.size || defaultSize
          }
          resolve()
        }).catch((err) => {
          console.log(err)
          resolve()
        })
      })
    },
    openHomeDrawer() {
      this.showHomeDrawer = true
    }
  },
});

export default useAppStore;
