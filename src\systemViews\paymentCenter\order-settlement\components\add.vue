<template>
  <div class="attenDance-box">
    <div class="top-flow" v-if="popupType == 'add'">
      <template v-for="(item, index) of flowList" >
        <div
          v-if="item.isShow"
          class="flow-one"
          :class="current == index ? 'actived' : ''"
          :key="index"
        >
          <div class="flow-ciycle">{{ index + 1 }}</div>
          <div class="flow-text">{{ item.flowText }}</div>
          <div class="flow-line" v-if="flowList.length - 1 != index"></div>
        </div>
      </template>
    </div>

    <div class="top-flow" v-if="popupType == 'edit'">
      <template v-for="(item, index) of flowListEdit">
        <div
          v-if="item.isShow"
          class="flow-one"
          :class="current == index ? 'actived' : ''"
           :key="index"
        >
          <div class="flow-ciycle">{{ index + 1 }}</div>
          <div class="flow-text">{{ item.flowText }}</div>
          <div class="flow-line" v-if="flowListEdit.length - 1 != index"></div>
        </div>
      </template>
    </div>

    <div class="top-flow" v-if="popupType == 'view'">
      <template v-for="(item, index) of flowListView">
        <div
          v-if="item.isShow"
          class="flow-one"
          :class="current == index ? 'actived' : ''"
           :key="index"
           @click="viewFlow(index)"
        >
          <div class="flow-ciycle">{{ index + 1 }}</div>
          <div class="flow-text">{{ item.flowText }}</div>
          <div class="flow-line" v-if="flowListView.length - 1 != index"></div>
        </div>
      </template>
    </div>

    <div class="top-flow" v-if="popupType == 'review'">
      <template v-for="(item, index) of flowListView">
        <div
          v-if="item.isShow"
          class="flow-one"
          :class="current == index ? 'actived' : ''"
           :key="index"
           @click="viewFlow(index)"
        >
          <div class="flow-ciycle">{{ index + 1 }}</div>
          <div class="flow-text">{{ item.flowText }}</div>
          <div class="flow-line" v-if="flowListView.length - 1 != index"></div>
        </div>
      </template>
    </div>
    <div class="flow-components" v-if="popupType == 'add'">
      <component
       ref="flowComponent"
        :popupType="popupType"
        :row-data="rowData"
        :is="flowList[current].url"
        :flow="flowList[current]"
        :flowNumber="current"
        @prevButton="prevButton"
        @nextButton="nextButton"
        @finshFlow="submitClose"
         @refreshList="refreshList"
      />
      <div class="b"></div>
    </div>

    <div class="flow-components" v-if="popupType == 'edit'">
      <Splitpanes class="default-theme">
        <Pane :size="steps.length>0?80:100" :min-size="20">
          <el-card class="dep-card">
            <component
             ref="flowComponent"
              :popupType="popupType"
              :row-data="rowData"
              :is="flowListEdit[current].url"
              :flow="flowListEdit[current]"
              :flowNumber="current"
              @prevButton="prevButton"
              @nextButton="nextButton"
              @finshFlow="submitClose"
               @refreshList="refreshList"
            />
            <div class="b"></div>
          </el-card>
        </Pane>

        <Pane :size="20" :min-size="10" v-if="steps.length>0">
          <el-card class="dep-card" style="height: 697px;">
            <div class="steps" v-if="steps.length>0">
              <div class="step" v-for="(step, index) in steps" :key="index">
                <div class="step-icon">{{ index + 1 }}</div>
                <div class="step-content">
                  <div class="step-title">{{ $formatDictLabel(step.auditStatus, audit_status) || "" }}</div>
                  <div class="step-info">
                    <div>操作人：{{ step.createBy }}</div>
                    <div><span v-if="step.auditStatus=='1'||step.auditStatus=='2'">通过时间</span><span v-if="step.auditStatus=='3'">退回时间</span>：{{ step.createDate }}</div>
                  </div>
                </div>
              </div>
            </div>
            <div v-else  class="text-center">无流程数据</div> 
          </el-card>
        </Pane>
      </Splitpanes>
    </div>

    <div class="flow-components" v-if="popupType == 'view'">
      <Splitpanes class="default-theme">
        <Pane :size="steps.length>0?80:100" :min-size="20">
          <el-card class="dep-card">
            <component
              ref="flowComponent"
              :popupType="popupType"
              :row-data="rowData"
              :is="flowListView[current].url"
              :flow="flowListView[current]"
              :flowNumber="current"
              @prevButton="prevButton"
              @nextButton="nextButton"
              @finshFlow="submitClose"
               @refreshList="refreshList"
            />
            <div class="b"></div>
          </el-card>
        </Pane>

        <Pane :size="20" :min-size="10" v-if="steps.length>0"> 
          <el-card class="dep-card" style="height: 702px;">
            <div class="steps" v-if="steps.length>0">
              <div class="step" v-for="(step, index) in steps" :key="index">
                <div class="step-icon">{{ index + 1 }}</div>
                <div class="step-content">
                  <div class="step-title">{{ $formatDictLabel(step.auditStatus, audit_status) || "" }}</div>
                  <div class="step-info">
                    <div>操作人：{{ step.createBy }}</div>
                    <div><span v-if="step.auditStatus=='1'||step.auditStatus=='2'">通过时间</span><span v-if="step.auditStatus=='3'">退回时间</span>：{{ step.createDate }}</div>
                  </div>
                </div>
              </div>
            </div>
             <div v-else  class="text-center">无流程数据</div> 
          </el-card>
        </Pane>
      </Splitpanes>
    </div>

    <div class="flow-components" v-if="popupType == 'review'">
      <Splitpanes class="default-theme">
        <Pane :size="steps.length>0?80:100" :min-size="20">
          <el-card class="dep-card">
            <component
             ref="flowComponent"
              :popupType="popupType"
              :row-data="rowData"
              :is="flowListView[current].url"
              :flow="flowListView[current]"
              :flowNumber="current"
              @prevButton="prevButton"
              @nextButton="nextButton"
              @finshFlow="submitClose"
              @refreshList="refreshList"
            />
            <div class="b"></div>
          </el-card>
        </Pane>

        <Pane :size="20" :min-size="10"  v-if="steps.length>0">
          <el-card class="dep-card" style="height: 702px;">
            <div class="steps" v-if="steps.length>0">
              <div class="step" v-for="(step, index) in steps" :key="index">
                <div class="step-icon">{{ index + 1 }}</div>
                <div class="step-content">
                  <div class="step-title">{{ $formatDictLabel(step.auditStatus, audit_status) || "" }}</div>
                  <div class="step-info">
                    <div>操作人：{{ step.createBy }}</div>
                    <div><span v-if="step.auditStatus=='1'||step.auditStatus=='2'">通过时间</span><span v-if="step.auditStatus=='3'">退回时间</span>：{{ step.createDate }}</div>
                  </div>
                </div>
              </div>
            </div>
             <div v-else class="text-center">无流程数据</div> 
          </el-card>
        </Pane>
      </Splitpanes>
    </div>
  </div>
</template>
  
  <script setup>
import { onMounted, ref,getCurrentInstance } from "vue";
import flow1 from "./flow1.vue";
import { screenIndex } from "@/api/paymentCenter/order-settlement/index";
const props = defineProps({
  rowData: {
    type: Object,
    default: () => ({}),
  },
  popupType: {
    type: String,
    default: "",
  },
});
const { proxy } = getCurrentInstance();
const { audit_status } = proxy.useDict("audit_status");
const emit = defineEmits(["submitClose","refreshList"]);
const flowComponent = ref(null);
const current = ref(0);

const flowList = ref([
  {
    flowText: "新增结算",
    id: "1",
    url: flow1,
    isShow: true,
  },
  {
    flowText: "结算明细",
    id: "2",
    url: flow1,
    isShow: true,
  },
  {
    flowText: "结算单",
    id: "3",
    url: flow1,
    isShow: true,
  },
]);

const flowListEdit = ref([
  {
    flowText: "生成结算",
    id: "1",
    url: flow1,
    isShow: true,
  },
  {
    flowText: "结算明细",
    id: "2",
    url: flow1,
    isShow: true,
  },
  {
    flowText: "结算单",
    id: "3",
    url: flow1,
    isShow: true,
  },
]);

const flowListView = ref([
  {
    flowText: "结算明细",
    id: "1",
    url: flow1,
    isShow: true,
  },
  {
    flowText: "结算单",
    id: "2",
    url: flow1,
    isShow: true,
  },
]);
const steps = ref([
]);

// 查询提交流程
const settleFlowList = async () => {
   try {
    const res = await screenIndex.settleFlowList({
      settleId: props.rowData.id,
    });
    if (res.code == "1") {
     steps.value = res.data
    }
  } catch (error) {
    console.error("获取供应商信息失败:", error);
  }
}
const submitClose = () => {
  emit("closeBtn");
};
const refreshList = () => {
  emit("refreshList"); 
}
const nextButton = (flowNumber) => {
  current.value = flowNumber + 1;
};

const prevButton = (flowNumber) => {
  current.value = flowNumber - 1;
};

const viewFlow = (index) => {
  current.value = index; 
  if(current.value==0){
    flowComponent.value.settleDetailsList()

  }
   if(current.value==1){

flowComponent.value.settleSheetList()
  }
}
onMounted(() => {
  if (props.rowData.id) {
    settleFlowList();
  }
});
</script>
  
  <style lang="scss" scoped>
.attenDance-box {
  min-height: 650px;
  display: flex;
  flex-direction: column;

  .top-flow {
    display: flex;
    align-items: center;
    justify-content: center;
    padding-top: 10px;
    padding-bottom: 12px;
    border-bottom: 1px solid #eee;

    .flow-one {
      display: flex;
      align-items: center;
      cursor: pointer;

      &.actived {
        .flow-ciycle {
          border: 1px solid #c20000;
          color: #c20000;
        }

        .flow-text {
          color: #c20000;
          font-size: 14px;
        }

        .flow-line {
          background: #c20000;
        }
      }

      .flow-ciycle {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 30px;
        height: 30px;
        border: 1px solid #eee;
        border-radius: 50%;
        margin-right: 10px;
      }

      .flow-text {
        color: #333;
        font-size: 14px;
      }

      .flow-line {
        width: 200px;
        height: 1px;
        background: #eee;
        margin-left: 8px;
        margin-right: 10px;
      }
    }
  }

  .flow-components {
    flex: 1;
    padding-top: 20px;
    box-sizing: border-box;
  }
}
.steps {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.step {
  position: relative;
  display: flex;
  align-items: flex-start;
}

.step:before {
  content: "";
  position: absolute;
  left: 20px;
  top: 20px;
  bottom: -40px;
  width: 1px;
  background-color: #e0e0e0;
}

.step:last-child:before {
  display: none;
}

.step-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #c20000;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 18px;
  margin-right: 20px;
  position: relative;
  z-index: 1;
}

.step-content {
  flex: 1;
}

.step-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}

.step-info {
  font-size: 14px;
  color: #666;
  line-height: 1.8;
}

.step-info div {
  margin-bottom: 5px;
}
@media (max-width: 1680px) and (min-width: 1442px) {
  .flow-ciycle {
    width: 25px !important;
    height: 25px !important;
    font-size: 12px;
  }

  .attenDance-box .top-flow .flow-one .flow-text {
    font-size: 12px;
  }

  .attenDance-box .top-flow .flow-one.actived .flow-text {
    font-size: 12px;
  }

  .step-icon {
                width: 38px;
                height: 38px;
                font-size: 16px;
            }
            
            .container {
                padding: 12px;
            }
            
            .step-title {
                font-size: 15px;
            }
            
            .step-info {
                font-size: 13px;
            }
}

@media (max-width: 1442px) {
  .flow-ciycle {
    width: 25px !important;
    height: 25px !important;
    font-size: 12px;
  }

  .attenDance-box .top-flow .flow-one .flow-text {
    font-size: 12px;
  }

  .attenDance-box .top-flow .flow-one.actived .flow-text {
    font-size: 12px;
  }

  .attenDance-box .top-flow .flow-one .flow-line {
    width: 160px !important;
  }

  .step-icon {
                width: 35px;
                height: 35px;
                font-size: 14px;
            }
            
            .container {
                padding: 10px;
            }
            
            .step-title {
                font-size: 14px;
            }
            
            .step-info {
                font-size: 12px;
            }
}
</style>