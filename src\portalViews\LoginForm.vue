<template>
  <div class="login-content">
    <div class="title mb-35px">
     <img src="@/assets/logo/logo.png" alt=""> 
     <span>{{ siteName }}</span>
    </div>
    <el-form ref="loginRef" :model="loginForm" :rules="loginRules" class="login-form" label-width="auto" style="width: 350px" size="large" > 
      <el-form-item prop="tenantId">
        <el-input v-model="loginForm.tenantId" type="text"  auto-complete="off" placeholder="租户">
          <template #prefix><svg-icon icon-class="user" /></template>
        </el-input>
        
      </el-form-item>
      <el-form-item prop="username">
        <el-input v-model="loginForm.username" type="text"  auto-complete="off" placeholder="账号">
          <template #prefix><svg-icon icon-class="user"  /></template>
        </el-input>
      </el-form-item>
      <el-form-item prop="password">
        <el-input v-model="loginForm.password" type="password" auto-complete="off" placeholder="密码"
          @keyup.enter="handleLogin">
          <template #prefix><svg-icon icon-class="password" /></template>
        </el-input>
      </el-form-item>

      <el-form-item prop="captcha" v-if="captchaEnabled">
        <el-input v-model="loginForm.captcha"  @keyup.enter="handleLogin" prefix-icon="Picture" placeholder="验证码">
          <template #suffix>
            <img :src="codeUrl" @click="getCode" class="login-code-img code" />
          </template>
        </el-input>
      </el-form-item>
      <el-form-item style="width:100%;">
        <el-button :loading="loading" size="large" type="primary" style="width:100%;" @click.prevent="handleLogin">
          <span v-if="!loading">登 录</span>
          <span v-else>登 录 中...</span>
        </el-button>
      </el-form-item>
      <!-- <el-form-item prop="captcha" v-if="captchaEnabled">
        <el-input v-model="loginForm.captcha" size="large" auto-complete="off" placeholder="验证码" style="width: 63%"
          @keyup.enter="handleLogin">
          <template #prefix><svg-icon icon-class="validCode" class="el-input__icon input-icon" /></template>
        </el-input>
        <div class="login-code">
          <img :src="codeUrl" @click="getCode" class="login-code-img" />
        </div>
      </el-form-item> -->
    </el-form>
  </div>
</template>

<script setup>
import config from '@/utils/config'
import { v4 as uuidv4 } from 'uuid';
import { doCrypt } from "@/utils/sm2Encrypt";
import useUserStore from '@/store/modules/user'

const siteName = config.siteName
const userStore = useUserStore()
const route = useRoute();
const router = useRouter();
const { proxy } = getCurrentInstance();
const form = ref({})
const loginForm = ref({
  tenantId: "",
  username: "",
  password: "",
  captcha: "",
  uid: ""
});

const loginRules = {
  tenantId: [{ required: true, trigger: "blur", message: "请输入租户登录名" }],
  username: [{ required: true, trigger: "blur", message: "请输入您的账号" }],
  password: [{ required: true, trigger: "blur", message: "请输入您的密码" }],
  captcha: [{ required: true, trigger: "change", message: "请输入验证码" }]
};

const codeUrl = ref("");
const loading = ref(false);
// 验证码开关
const captchaEnabled = ref(true);

const redirect = ref(undefined);

watch(route, (newRoute) => {
  redirect.value = newRoute.query && newRoute.query.redirect;
}, { immediate: true });

function handleLogin() {
  proxy.$refs.loginRef.validate(async (valid) => {
    if (valid) {
      loading.value = true;
      const cryptPassword = await doCrypt(loginForm.value.password, 1)
      // 调用action的登录方法
      userStore.login({
        ...loginForm.value,
        password: cryptPassword
      }).then(() => {
        if (userStore?.additionalInformation?.userJobDetailVOList?.length > 1) {
          //选择岗位
          router.push({ path: redirect.value || "/" });

          // router.push({ path: 'system/index'|| "/" });
        } else {
          router.push({ path: redirect.value || "/" });
          // router.push({ path: 'system/index'|| "/" });
        }
      }).catch(() => {
        loading.value = false;
        // 重新获取验证码
        if (captchaEnabled.value) {
          getCode();
        }
      });
    }
  });
}

function getCode() {
  const uuid = uuidv4();
  const newUrl = `${import.meta.env.VITE_APP_BASE_API}/auth/captcha?uid=${uuid}`
  codeUrl.value = newUrl;
  loginForm.value.uid = uuid;
}

getCode();
</script>




<style lang="less" scoped>
.title {
  font-family: HYSongYangTiW;
  font-size: 35px;
  position: relative;
  span {
    position: relative;
  }
}

.btn {
  width: 100%;
  height: 44px;
  background: linear-gradient(90deg, #FF372A 0%, #C20000 100%);
  border-radius: 24px;
  color: #fff;
  text-align: center;
  cursor: pointer;
  margin-top: 15px;
}

.code {
  border-radius: 24px;
  width: 130px;
  height: 100%;
  margin-right: -15px;
  cursor: pointer;
}
.mb-100px{
  margin-bottom: 100px;
}
.mb-35px{
  margin-bottom: 35px;
}
</style>

<style lang="scss" scoped>
:deep(.el-input__wrapper) {
    border-radius: 24px;
}
</style>