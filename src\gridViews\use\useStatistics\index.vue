<template>
  <el-card :class="bgColor === true? 'notice-card':'unshow-notice-card'" v-loading="loading" shadow="never">
    <template #header v-if="showTitle">
      <span class="card-title">
        <div class="title">
          <div><el-icon class="icon iconfont icon-yewudongtai"></el-icon> 统计数据</div>
        </div>
      </span>
    </template>
    <div class="home-card-body">
      <div class="content" v-if="total">
        <div class="organization" >
          <div class="text" >
            <h5 style="color: #0b1d30; margin-bottom: 5px">
              {{ total.staffTotal }}
            </h5>
            <h6 style="color: #515356">用户</h6>
          </div>
        </div>
        <div class="personnel">
          <div class="text">
            <h5 style="color: #0b1d30; margin-bottom: 5px">
              {{ total.orgTotal }}
            </h5>
            <h6 style="color: #515356">组织</h6>
          </div>
        </div>
        <div class="application">
          <div class="text">
            <h5 style="color: #0b1d30; margin-bottom: 5px">
              {{ total.tenantTotal }}
            </h5>
            <h6 style="color: #515356">租户</h6>
          </div>
        </div>
        <div class="routine" style="border-right: none">
          <div class="text">
            <h5 style="color: #0b1d30; margin-bottom: 5px">
              {{ total.appTotal }}
            </h5>
            <h6 style="color: #515356">应用</h6>
          </div>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script setup name="UseStatistics">
import {getTotals} from "@/api/grid/useStatistics";

const loading = ref(false)
const total = ref({})
const props = defineProps({
  showTitle: Boolean,
  bgColor: Boolean
})

function getTotal() {
  getTotals().then((res) => {
    total.value = res.data;
  });
}

getTotal();
</script>

<style scoped lang="scss">
.unshow-notice-card {
  background: #f5f9fa;
  border: 0px;
  height: 100%;
  display: flex;
  flex-direction: column;
  .home-card-body {
    height: 100%;
    .content {
      display: flex;
      height: 80px;
    }
    .content > div {
      width: 25%;
      height: 80px;
      border-right: 1px solid #d5d5d5;
      font-size: 20px;
      text-align: center;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }
    .content h5 {
      font-size: 28px;
      font-weight: 600;
    }
    .content h6 {
      font-size: 16px;
    }
  }
}
.notice-card {
  height: 100%;
  display: flex;
  background: #fff;
  flex-direction: column;
  .home-card-body {
    height: 100%;
    .content {
      display: flex;
      height: 80px;
    }
    .content > div {
      width: 25%;
      height: 80px;
      border-right: 1px solid #d5d5d5;
      font-size: 20px;
      text-align: center;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }
    .content h5 {
      font-size: 28px;
      font-weight: 600;
    }
    .content h6 {
      font-size: 16px;
      font-weight: 600;
    }
  }
}
</style>
