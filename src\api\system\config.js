import request from '@/utils/request'
import { cmsImg } from '@/utils/config';
// 查询参数列表
export function listConfig(query) {
    return request({
        url: '/user/configs/page',
        method: 'get',
        params: query
    })
}

// 查询参数详细
export function getConfig(configId) {
    return request({
        url: '/user/configs/findOne',
        method: 'get',
        params: {
          id: configId
        }
    })
}

// 根据参数键名查询参数值
export function selectByConfigCode(data) {
    return request({
        url: '/user/configs/selectByConfigCode',
        method: 'post',
        data
    })
}

// 新增参数配置
export function addConfig(data) {
    return request({
        url: '/user/configs/add',
        method: 'post',
        data: data
    })
}

// 修改参数配置
export function updateConfig(data) {
    return request({
        url: '/user/configs/edit',
        method: 'post',
        data: data
    })
}

// 删除参数配置
export function delConfig(configId) {
    return request({
        url: '/user/configs/delete/' + configId,
        method: 'get'
    })
}

export function getPersonalConfig(params) {
    return request({
        url: '/user/personalConfig/getPersonalConfig',
        method: 'post',
        params
    })
}

export function getPersonalThemeConfig(params) {
  return request({
    url: '/user/personalConfig/getPersonalThemeConfig',
    method: 'post',
    data: params
  })
}

export function getAllLayoutList(params) {
    return request({
        url: '/user/personalConfig/getAllLayoutList',
        method: 'post',
        data: params
    })
}

export function selectAllTenantConfig(params) {
  return request({
    url: '/user/personalConfig/selectConfigForTenant',
    method: 'post',
    params
  })
}

export function selectPersonalTenantConfig(params) {
  return request({
    url: '/user/personalConfig/selectConfigByTenantId',
    method: 'post',
    params
  })
}

export function selectGeneralConfigForTenant(params) {
  return request({
    url: '/user/personalConfig/selectGeneralConfigForTenant',
    method: 'post',
    params
  })
}

export function updateStatus(data) {
    return request({
        url: '/user/personalConfig/updateStatus',
        method: 'post',
        data
    })
}

export function updateTenantStatus(data) {
  return request({
    url: '/user/personalConfig/updateTenantStatus',
    method: 'post',
    data
  })
}

export function updateOrgStatus(data) {
  return request({
    url: '/user/personalConfig/updateOrgStatus',
    method: 'post',
    data
  })
}

export function saveSysPersonalConfig(data) {
    return request({
        url: '/user/personalConfig/SysPersonalConfig',
        method: 'post',
        data
    })
}

export function saveSysTenantConfig(data) {
  return request({
    url: '/user/personalConfig/addConfigForTenant',
    method: 'post',
    data
  })
}

export function addLayout(data) {
  return request({
    url: '/user/personalConfig/addLayout',
    method: 'post',
    data
  })
}


export function updateConfigInfo(data) {
    return request({
        url: '/user/personalConfig/updateConfigInfo',
        method: 'post',
        data
    })
}

export function updatePersonalThemeMode(data) {
  return request({
    url: '/user/personalConfig/updatePersonalThemeMode',
    method: 'post',
    data
  })
}

export function deletePersonalId(personalId) {
    return request({
        url: '/user/personalConfig/delete/' + personalId,
        method: 'get',
    })
}


export function statSpeed() {
    return request({
        url: '/auth/stat/speed',
        method: 'post',
    })
}

export function getImageUrl(id) {
  return request({
      url: `${cmsImg}${id}`,
      method: 'get',
  })
}
export function findDeptUserTree(data) {
  return request({
      url: '/user/orgStaff/sync/findDeptUserTree',
      method: 'post',
      data
  })
}

export function syncDeptAndUserToSystem(data) {
  return request({
      url: '/user/orgStaff/sync/syncDeptAndUserToSystem',
      method: 'post',
      data
  })
}


export function syncDataToSystem(data) {
  return request({
      url: '/user/orgStaff/sync/syncDataToSystem',
      method: 'post',
      data
  })
}

export function deleteDataToSystem(data) {
  return request({
      url: '/user/orgStaff/sync/deleteDataToSystem',
      method: 'post',
      data
  })
}

export function getWillSyncUserData(data) {
  return request({
      url: '/user/orgStaff/sync/getWillSyncUserData',
      method: 'post',
      data
  })
}
export function syncBatchSystem(param,data) {
  return request({
      url: '/user/orgStaff/sync/syncBatchSystem',
      method: 'post',
      data:{...param,...data}
  })
}


export function pullLatestData(param,data) {
  return request({
      url: '/user/orgStaff/sync/pullLatestData',
      method: 'post',
      data:{...param,...data}
  })
}


