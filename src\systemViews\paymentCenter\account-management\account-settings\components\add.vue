<!-- 新增账户设置 -->

<template>
  <div class="dialog-box" :class="popupType === 'view' ? 'dialog-box-view' : 'dialog-box-edit'">
    <el-form ref="formRef" :model="formData" label-width="125px" :rules="popupType !== 'view' ? rules : ''">
      <el-row>
        <el-col :span="12">
          <el-form-item label="账户名称" prop="accountName">
            <!-- 添加或编辑时显示输入框 -->

            <el-input v-model="formData.accountName" :disabled="popupType === 'view'" placeholder="请输入账户名称" clearable
              v-if="popupType !== 'view'" />

            <!-- 查看时显示文本 -->

            <div v-else>
              {{ formData.accountName || "" }}
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="账户编码" prop="accountCode">
            <el-input v-model="formData.accountCode" :disabled="popupType === 'view'" placeholder="请输入账户编码" clearable
              v-if="popupType !== 'view'" />

            <div v-else>
              {{ formData.accountCode || "" }}
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <el-form-item label="账户类型" prop="accountTypeId">
            <!-- 添加或编辑时显示选择器 -->
            <el-select v-model="formData.accountTypeId" :disabled="popupType === 'view'" placeholder="请选择账户类型"
            @change="handleMulteOrSingle"
              v-if="popupType !== 'view'">
              <el-option v-for="(item, index) in accountTypeList" :key="index" :label="item.typeName"
                :value="item.id" />
            </el-select>
          
            <!-- 查看时显示文本 -->
            <div v-else>
              {{
                 formData.accountType
              }}
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="支付顺序" prop="paymentOrder">
            <!-- 添加或编辑时显示选择器 -->
            <NumberInput v-if="popupType !== 'view'" v-model="formData.paymentOrder" customPlaceholder="请输入支付顺序"
              input-type="integer" />

            <!-- 查看时显示文本 -->

            <div v-else>
              {{ formData.paymentOrder }}
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="默认开通" prop="opened">
            <!-- 添加或编辑时显示单选按钮 -->

            <el-radio-group v-model="formData.opened" v-if="popupType !== 'view'">
              <el-radio-button :label="item.value" v-for="(item, index) of whether_open"
                :key="index">{{ item.label }}</el-radio-button>

            </el-radio-group>

            <!-- 查看时显示文本 -->

            <span class="dialog-text" v-else>
              {{ $formatDictLabel(formData.opened, whether_open) || "" }}
            </span>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="账户状态" prop="accountStatus">
            <!-- 添加或编辑时显示单选按钮 -->

            <el-radio-group v-model="formData.accountStatus" v-if="popupType !== 'view'">
              <el-radio-button :label="item.value" v-for="(item, index) of account_status"
                :key="index">{{ item.label }}</el-radio-button>
            </el-radio-group>

            <!-- 查看时显示文本 -->

            <span class="dialog-text" v-else>
              {{ $formatDictLabel(formData.accountStatus, account_status) || "" }}
            </span>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="是否清零" prop="zeroClearing">
            <!-- 添加或编辑时显示单选按钮 -->

            <el-radio-group v-model="formData.zeroClearing" v-if="popupType !== 'view'">
              <el-radio-button :label="item.value" v-for="(item, index) of whether_clear"
                :key="index">{{ item.label }}</el-radio-button>

            </el-radio-group>

            <!-- 查看时显示文本 -->

            <span class="dialog-text" v-else>
              {{ $formatDictLabel(formData.zeroClearing, whether_clear) || "" }}
            </span>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="供应商" prop="supplierIds">
            <!-- 添加或编辑时显示选择器 -->
            <el-select v-model="supplierIds" :disabled="popupType === 'view'" multiple filterable placeholder="请选择供应商"
            @change="handleSupplierChange "
              v-if="popupType !== 'view'">
              <el-option v-for="(item, index) in supplierIdsList" :key="index" :label="item.providerName"
                :value="item.id" />
            </el-select>

            <!-- 查看时显示文本 -->

            <div v-else>
              {{
                formData.strSupplierName
              }}
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="分配租户" prop="tenantIds">
            <!-- 添加或编辑时显示选择器 -->
            <el-select v-model="formData.tenantIds" :disabled="popupType === 'view'" @change="handleChange" filterable placeholder="请选择分配租户"
              v-if="popupType !== 'view'">
              <el-option v-for="(item, index) in tenantIdsList" :key="index" :label="item.tenantName"
                :value="item.tenantId" />
            </el-select>

            <!-- 查看时显示文本 -->
            <div v-else>
              {{ $formatDictLabel(formData.tenantId, tenantIdsList,"tenantId",'tenantName') || "" }}
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="充值管理员" prop="administratorsIds">


            <el-select v-if="popupType !== 'view'" v-model="formData.administratorsIds" multiple placeholder="请输入人员进行选择"
              clearable @change="changeUser" filterable remote reserve-keyword :remote-method="getUserList">
              <el-option v-for="(item, index) in administratorsIdsList" :key="index" :label="item.staffName"
                :value="item.staffId">
                <div>
                  {{
                    item.staffName +
                    "(" +
                    item.orgName +
                    "/)" 
                  }}
                </div>
              </el-option>
            </el-select>
            <div v-else>
              {{ formData.strAdministratorsName || "" }}
            </div>
          </el-form-item>
        </el-col>

       
      </el-row>


    </el-form>


  </div>
</template>



<script setup>
import { ref, reactive, watch, getCurrentInstance, defineProps } from "vue";

import { ElMessage } from "element-plus";


import {
  screenIndex
} from "@/api/paymentCenter/account-management/account-settings/index";


// 定义组件 props

const props = defineProps({
  closeBtn: {
    type: Function,

    default: null,
  },

  popupType: {
    type: String,

    default: "",
  },

  rowData: {
    type: Object,
    default: () => ({}),
  },
});

// 字典
const { proxy } = getCurrentInstance();
const { account_status, whether_open, whether_clear } = proxy.useDict(
  "account_status",
  "whether_open",
  "whether_clear"
);


// 账户类型

const accountTypeList = ref([])

const supplierIdsList = ref([])
const administratorsIdsList = ref([])
const tenantIdsList = ref([])

const supplierIds = ref([])
const administratorsIds = ref([])
const tenantIds = ref('')
// 定义 emits
const emit = defineEmits(["closzeBtn"]);

// 定义状态

let formData = ref({
  providerIds: [],
  administratorsIds: [],
  tenantIds: '',
  opened: '2',
  accountStatus: '1',
  zeroClearing: '2',
});
//定义当前选中的账户类型
const currentAccountType=reactive({
  id:"",
  typeName:"",
  typeCode:"",
  accountTypeDict:"",
  status:"",
  tenantId:""
})

// 定义校验规则

const rules = reactive({
  accountName: [
    { required: true, message: '请输入账户名称', trigger: ['blur', 'change'] }
  ],
  accountCode: [
    {
      required: true, message: '请输入账户编码', trigger: ['blur', 'change'],


    },

    {
      pattern: /^[\x21-\x7E]+$/, // 允许所有可打印的 ASCII 字符（不包含空格）
      message: "账户编码只能包含字母、数字和特殊符号",
      trigger: ["blur", "change"]
    }
  ],

  accountTypeId: [
    { required: true, message: '请选择账户类型', trigger: 'change' }
  ],
  paymentOrder: [
    { required: true, message: '请输入支付顺序', trigger: 'blur' },
  ],
  opened: [
    { required: true, message: '请选择默认开通状态', trigger: 'change' }
  ],
  accountStatus: [
    { required: true, message: '请选择账户状态', trigger: 'change' }
  ],
  tenantIds: [
    { required: true, message: '请选择分配租户', trigger: 'change' }
  ],
  //administratorsIds: [
    //{ required: true, message: '请选择充值管理员', trigger: 'change' }
  //],
  supplierIds: [
    // { 
    //   validator: (rule, value, callback) => {
    //     // 获取当前账户类型
    //     const accountType = getSelectedAccountType.value;
        
    //     // 如果是供应商类型且选择了多个供应商
    //     if (accountType.accountTypeDict !== "0" && supplierIds.value.length > 1) {
    //       message:'该账户类型只能选择一个供应商'
    //       callback(new Error('该账户类型只能选择一个供应商'));
    //     } else {
    //       callback();
    //     }
    //   },
    //   trigger: ['change', 'blur']
    // }
  ],

  zeroClearing: [
    { required: true, message: '请选择是否清零', trigger: 'change' }
  ]
})

// 表单引用

const formRef = ref(null);



// 定义方法


const payAccountTypePageList = async () => {
  const res = await screenIndex.payAccountTypePageList({
    status:'1'
  });
  accountTypeList.value = res.data

};

const payProviderPageList = async () => {
  const res = await screenIndex.payProviderPageList({});
  supplierIdsList.value = res.data

};

const juniorList = async () => {
  const res = await screenIndex.juniorList({});
  tenantIdsList.value = res.data

};
// 租户切换
const handleChange = () =>{
  // 立即清空列表
  administratorsIdsList.value = []; 
  formData.value.administratorsIds = [];
}

const getUserList = async (name) => {
  if (!name || name.trim().length < 2) {
    administratorsIdsList.value = []; // 立即清空列表
    return;
  }
  
  try {
    const res = await screenIndex.selectUserList({
      staffName: name,
      tenantId: formData.value.tenantIds
    });
    administratorsIdsList.value = res.data;
  } catch (error) {
    console.error("获取用户列表失败:", error);
  }
};

// 选择事件（根据需要补充逻辑）
const changeUser = (selectedIds) => {
  console.log("已选择的ID:", selectedIds);
  // 可以在这里添加选择变化后的处理逻辑

  // formData.administratorsIds = selectedIds.map(id => ({
  //   administratorsId: id
  // }))

};
const initData = async () => {
  if (props.popupType === "add") {
  } else {
    const res = await screenIndex.PayAccountManageGet({ id: props.rowData.id });
    const data = res.data || {};
    formData.value = props.rowData
    if (props.rowData.strAdministratorsName) {
      formData.value.administratorsIds = props.rowData.stringAdminIds.split(',')

      const idArray = props.rowData.stringAdminIds.split(',').filter(Boolean);
      const nameArray = props.rowData.strAdministratorsName.split(',').filter(Boolean);

      // 同步补充选项列表数据
      idArray.forEach((id, index) => {
        if (!administratorsIdsList.value.some(u => u.staffId === id)) {
          administratorsIdsList.value.push({
            staffId: id,
            staffName: nameArray[index] || ``,
          });
        }
      });



    
    }
    if (props.rowData.strSupplierIds) {
      supplierIds.value = res.data.strSupplierIds.split(',')
    }

    if (props.rowData.tenantId) {
      formData.value.tenantIds = props.rowData.tenantId

    }

  }

};

const transformForSubmit = () => {
  const selectedTenant = tenantIdsList.value.find(item => item.tenantId ===  formData.value.tenantIds);
  const tenantName = selectedTenant ? selectedTenant.tenantName : '';
  return {
    providerIds: supplierIds.value.map(id => ({
      providerId: id
    })),
    tenantIds: [{
      applicableTenantsId:  formData.value.tenantIds
    }],
    tenantId: formData.value.tenantIds,
    strTenantsName:tenantName,
    administratorsIds: !formData.value.administratorsIds ? null:formData.value.administratorsIds.map(id => ({
      administratorsId: id
    }))
  }
}

const saveForm = async () => {
  try {
    await formRef.value.validate();
    const requestData = {
      ...formData.value,
      ...transformForSubmit()
    };
    if (props.popupType === "edit") {
      const res = await screenIndex.PayAccountManageUpdate(requestData);

      if (res.code == "1") {
        ElMessage.success("修改成功");

        emit("closeBtn");
      }
    } else if (props.popupType === "add") {
      const res = await screenIndex.PayAccountManageAdd(requestData);

      if (res.code == "1") {
        ElMessage.success("新增成功");

        emit("closeBtn");
      }
    }
  } catch (error) {
    console.error("表单校验失败:", error);
  }
};
// 处理供应商选择变化
const handleSupplierChange = (selectedIds) => {
  // 获取当前账户类型
  const accountType = getSelectedAccountType.value;
  
  // 如果是供应商类型且选择了多个供应商
  if (accountType.accountTypeDict !== "0" && selectedIds.length > 1) {
    // 保留最后一个选择的供应商
    // supplierIds.value = [selectedIds[selectedIds.length - 1]];
    // 这里不需要显示错误信息，因为表单验证会自动显示
  }
};

//处理账户类型选择变化
const handleMulteOrSingle=()=>{
   // 获取当前账户类型
   currentAccountType.value = getSelectedAccountType.value;
  
  // 手动触发供应商验证
  formRef.value.validateField('supplierIds');

}

//根据id获取当前选中的item对象
const getSelectedAccountType = computed(() => {
  return accountTypeList.value.find(item => item.id === formData.value.accountTypeId) || {};
});


// 初始化数据

onMounted(() => {
  payProviderPageList()
  payAccountTypePageList()
  juniorList()
  getUserList()
  initData();

});

defineExpose({
  saveForm,
});
</script>



<style scoped lang="scss"></style>