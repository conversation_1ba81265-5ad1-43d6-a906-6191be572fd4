<!-- 停车记录 -->
<template>
  <div class="container-table1">
    <el-card>
      <el-row :gutter="24">
        <el-col :span="24" :xs="24">
          <dialog-search
            @getList="getList"
            formRowNumber="3"
            v-model:isTable="isTable"
            :tableBtnShow="true"
            :columns="tabelForm.columns"
          >
            <template #formList>
              <el-form
                :inline="true"
                :model="formData"
                ref="queryForm"
                size="medium"
                label-width="70px"
              >
                <!-- 表单元素保持不变 -->

                <el-form-item label="租户">
                  <TenantSelect v-model="formData.tenantId"></TenantSelect>
                </el-form-item>
                <el-form-item label="停车场" prop="parkingId">
                  <TreeSelect v-model:parkingId="formData.parkingId" />
                </el-form-item>

                <el-form-item label="入场时间">
                  <el-date-picker
                    v-model="daySelects"
                    type="datetimerange"
                    format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    range-separator="至"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    @change="handleDateChange"
                  />
                </el-form-item>

                <!-- <el-form-item label="自动刷新">
                  <el-radio-group
                    v-model="autoRefreshInterval"
                    @change="handleAutoRefreshChange"
                    size="small"
                  >
                    <el-radio-button :label="0">关闭</el-radio-button>
                    <el-radio-button :label="5">5秒</el-radio-button>
                    <el-radio-button :label="10">10秒</el-radio-button>
                    <el-radio-button :label="15">15秒</el-radio-button>
                  </el-radio-group>
                </el-form-item> -->
              </el-form>
            </template>

            <template #searchList>
              <el-button
                class="search"
                type="primary"
                icon="Search"
                size="small"
                @click="handleQuery"
                >搜索</el-button
              >
              <el-button
                class="reset"
                icon="Refresh"
                size="small"
                @click="resetQuery"
                >重置</el-button
              >
            </template>
            <template v-slot:searchBtnList>
              <el-button type="primary" icon="Download" @click="handleExport"
                >导出
              </el-button>
            </template>
          </dialog-search>
          <div v-loading="loloading2">
            <public-table
              v-show="isTable"
              ref="publictable"
              :rowKey="tabelForm.tableKey"
              :tableData="dataList"
              :columns="tabelForm.columns"
              :configFlag="tabelForm.tableConfig"
              :pageValue="pageParams"
              :total="total"
              :getList="getList"
            >
              <template #enterLicenseImgSlot="{ scope }">
                <PreviewImage
                  :previewQueryUrl="previewQueryUrl"
                  :photo-id="scope.row.enterLicenseImg"
                  v-if="scope.row.enterLicenseImg"
                ></PreviewImage>
              </template>

              <template #enterCarImgSlot="{ scope }">
                <PreviewImage
                  :photo-id="scope.row.enterCarImg"
                  :previewQueryUrl="previewQueryUrl"
                  v-if="scope.row.enterCarImg"
                ></PreviewImage>
              </template>

              <template #outLicenseImgSlot="{ scope }">
                <PreviewImage
                  :previewQueryUrl="previewQueryUrl"
                  :photo-id="scope.row.outLicenseImg"
                  v-if="scope.row.outLicenseImg"
                ></PreviewImage>
              </template>

              <template #outCarImgSlot="{ scope }">
                <PreviewImage
                  :previewQueryUrl="previewQueryUrl"
                  :photo-id="scope.row.outCarImg"
                  v-if="scope.row.outCarImg"
                ></PreviewImage>
              </template>

              <template #operation="{ scope }">
                <el-button
                  size="mini"
                  icon="View"
                  type="text"
                  v-hasPermi="['parking:record:list']"
                  @click="handleView(scope.row)"
                  title="查看记录"
                ></el-button>

                <!-- <el-button
                size="mini"
                icon="Edit"
                type="text"
                v-hasPermi="['parking:record:edit']"
                @click="handleEdit(scope.row)"
                title="修改"
              ></el-button>

              <el-button
                size="mini"
                type="text"
                v-hasPermi="['parking:record:gateControl']"
                v-if="scope.row.hasPermissions != '0'"
                @click="gateControlByRecordsFun(scope.row)"
                title="开闸"
              >
                <i class="icon iconfont icon-kaizha1"></i>
              </el-button> -->
              </template>
            </public-table>

            <div
              class="table-card-box"
              v-show="!isTable && dataList.length > 0"
            >
              <div
                class="table-card-one"
                v-for="(item, index) of dataList"
                :key="index"
              >
                <div class="cars-place">
                  <div class="cars-place-box">
                    <img src="@/assets/images/zhtc.png" alt="" />

                    <span>
                      {{ item.areaName + " / " + item.parkingName || "" }}</span
                    >
                  </div>
                  <div class="flex-1"></div>

                  <el-button
                    title="查看记录"
                    size="mini"
                    type="text"
                    icon="View"
                    v-hasPermi="['parking:record:list']"
                    @click="handleView(item)"
                  ></el-button>
                  <!-- <el-button
                  size="mini"
                  icon="Edit"
                  title="修改"
                  type="text"
                  v-hasPermi="['parking:record:edit']"
                  @click="handleEdit(item)"
                ></el-button> -->
                </div>
                <div class="card-top-row">
                  <div class="cars-plateNo">
                    <div
                      title="点击可复制车牌"
                      @click="copyPlateNumber(item.plateNo)"
                      class="cars-plateNo-c"
                      :class="getPlateClass(item.plateNo)"
                    >
                      {{ formatPlateNo(item.plateNo) }}
                    </div>
                  </div>

                  <div class="left-dollor">
                    <div class="changft-2" v-if="item.staffName">
                      {{ item.staffName }}
                    </div>
                  </div>
                  <div class="flex-1"></div>
                  <div
                    class="car-card"
                    v-if="item.hasPermissions == '0'"
                    :class="item.dataStatus == '0' ? 'lanColor' : ''"
                  >
                    {{ $formatDictLabel(item.dataStatus, park_data_status) }}
                  </div>

                  <div class="car-card" v-if="item.hasPermissions == '1'">
                    {{
                      $formatDictLabel(
                        item.hasPermissions,
                        park_has_permissions
                      )
                    }}
                  </div>

                  <!-- <el-button
                  plain
                  v-hasPermi="['parking:record:gateControl']"
                  class="openDoorBtn"
                  type="primary"
                  v-if="item.hasPermissions != '0'"
                  @click="gateControlByRecordsFun(item)"
                >
                  <i
                    class="icon iconfont icon-kaizha1"
                    style="margin-right: 3px"
                  ></i>
                  开闸
                </el-button> -->
                </div>
                <div class="card-content">
                  <el-row>
                    <el-col :span="12" style="padding-right: 4px">
                      <div class="top-image">
                        <PreviewImage
                          :photo-id="item.enterCarImg"
                          :previewQueryUrl="previewQueryUrl"
                        ></PreviewImage>
                      </div>
                      <div class="mid-content" :title="item.parkingEnterName">
                        入场：{{ item.parkingEnterName || "" }}
                      </div>
                      <div class="bottom-info">
                        <div class="info-row">
                          <div class="info-point"></div>
                          <div class="info-text" :title="item.enterTime">
                            {{ item.enterTime }}
                          </div>
                        </div>
                      </div>
                    </el-col>

                    <el-col :span="12" style="padding-left: 4px">
                      <div class="top-image">
                        <PreviewImage
                          :photo-id="item.outCarImg"
                          :previewQueryUrl="previewQueryUrl"
                        ></PreviewImage>
                      </div>
                      <div class="mid-content" :title="item.parkingOutName">
                        出场：{{ item.parkingOutName || "" }}
                      </div>
                      <div class="bottom-info">
                        <div class="info-row">
                          <div class="info-point"></div>
                          <div class="info-text" :title="item.outTime">
                            {{ item.outTime }}
                          </div>
                        </div>
                      </div>
                    </el-col>
                  </el-row>
                </div>
              </div>
            </div>
            <el-empty
              :image-size="200"
              v-show="!isTable && dataList.length == 0"
            />
            <pagination
              v-show="total > 0 && !isTable"
              :total="total"
              class="table-card-box-pagination"
              v-model:page="pageParams.pageNum"
              v-model:limit="pageParams.pageSize"
              @pagination="getList"
            />
          </div>
        </el-col>
      </el-row>
    </el-card>
    <DialogBox
      :visible="diaWindow.open1"
      :dialogWidth="diaWindow.dialogWidth"
      :dialogFooterBtn="diaWindow.dialogFooterBtn"
      @cancellation="cancellation"
      @close="close"
      :dialogTitle="diaWindow.dialogTitle"
      @save="submits"
      dialogTop="20vh!important"
    >
      <template #content>
        <viewTCom
          :rowData="diaWindow.rowData"
          :popupType="diaWindow.popupType"
          v-if="diaWindow.popupType == 'view'"
        ></viewTCom>
        <edits
          :rowData="diaWindow.rowData"
          :popupType="diaWindow.popupType"
          v-if="diaWindow.popupType == 'edit'"
          ref="editRef"
          @cancellationRefresh="cancellationRefreshFun"
        ></edits>
      </template>
    </DialogBox>
  </div>
</template>

<script setup name="parkingRecord">
import { checkPermi } from "@/utils/permission";
import { ref, unref, reactive, onMounted, getCurrentInstance } from "vue";
import { ElMessage, ElMessageBox } from "element-plus"; // 引入 Element Plus 的提示组件
import dayjs from "dayjs";
import {
  parkRecordsPage,
  findparkinglot,
  getFileInfoById,
  gateControlByRecords,
} from "@/api/park/record";
import useUserStore from "@/store/modules/user";
import { apiUrl } from "@/utils/config";
import { formatMinuteTime } from "@/utils";
import viewTCom from "./view";
import edits from "./edit";
const userStore = useUserStore();
// 响应式数据
const parkSystemList = ref([]);
const dataList = ref([]);
const total = ref(0);
const publictable = ref(null);
import { cmsImg } from "@/utils/config";

const loloading2 = ref(false);

const previewQueryUrl = ref(
  import.meta.env.VITE_APP_BASE_API +
    "/park" +
    apiUrl +
    "/majorParking/parkRecords/downloadBinary/"
);
// 获取车牌样式类
const formatPlateNo = (plateNo) => {
  return plateNo.replace(/\s/g, "").toUpperCase();
};

const getPlateClass = (plateNo) => {
  const formatted = formatPlateNo(plateNo);
  return {
    "green-plate": formatted.length === 8, // 新能源绿牌
    "blue-plate": formatted.length === 7, // 传统蓝牌
  };
};
// 直接生成图片地址
const getImageUrl = (photoId) => {
  if (!photoId) return "";
  return `${cmsImg}${photoId}`;
};
// 自动刷新相关逻辑
const autoRefreshTimer = ref(null); // 定时器引用
// 自动刷新
const autoRefreshInterval = ref(0);
// 处理自动刷新选项变化
const handleAutoRefreshChange = (interval) => {
  clearInterval(autoRefreshTimer.value); // 清除旧定时器
  if (interval > 0) {
    autoRefreshTimer.value = setInterval(() => {
      getList(); // 触发数据刷新
    }, interval * 1000); // 转换为毫秒
  }
};

// 组件卸载时清除定时器
onBeforeUnmount(() => {
  clearInterval(autoRefreshTimer.value);
});

// 表单数据
const formData = reactive({
  tenantId: userStore.userInfo.tenantId,
  enterOutStatus: "0",
  hasPermissions: "1",
});

const editRef = ref(null);
const isTable = ref(false);

// 分页参数
const pageParams = reactive({
  pageNum: 1,
  pageSize: 10,
});
// 字典
const { proxy } = getCurrentInstance();
const {
  park_data_status,
  enter_out_status,
  ORDER_STATUS,
  park_has_permissions,
  entry_exit_type,
  car_type,
} = proxy.useDict(
  "park_data_status",
  "enter_out_status",
  "ORDER_STATUS",
  "park_has_permissions",
  "entry_exit_type",
  "car_type"
);
const diaWindow = reactive({
  open1: false,
  popupType: "view",
  rowData: "",
  dialogWidth: "50%",
  dialogTitle: "",
  dialogFooterBtn: false,
});

// 入场时间
const daySelects = ref([]);
// 出场时间
const daySelects2 = ref([]);
// 初始化默认值
const setDefaultDateRange = () => {
  const now = dayjs(); // 当前时间
  const startDate = now.format("YYYY-MM-DD"); // 当前日期
  const endDate = now.format("YYYY-MM-DD"); // 当前日期

  // 开始时间：当前时间
  const startTime = "00:00:00";

  // 结束时间：当天的 24:00:00
  const endTime = "23:59:59";

  // 设置默认日期范围
  daySelects.value = [`${startDate} ${startTime}`, `${endDate} ${endTime}`];

  // 设置默认日期范围
  daySelects2.value = [`${startDate} ${startTime}`, `${endDate} ${endTime}`];

  formData.inStartTime = `${startDate} ${startTime}`;
  formData.inEndTime = `${endDate} ${endTime}`;

  // formData.outStartTime = `${startDate} ${startTime}`;
  // formData.outEndTime = `${endDate} ${endTime}`;
};
const handleDateChange = (dates) => {
  if (dates && dates.length === 2) {
    // 更新绑定数据
    formData.inStartTime = dates[0];
    formData.inEndTime = dates[1];
  } else {
    formData.inStartTime = "";
    formData.inEndTime = "";
  }
};

const handleDateChange2 = (dates) => {
  if (dates && dates.length === 2) {
    formData.outStartTime = dates[0];
    formData.outEndTime = dates[1];
  } else {
    formData.outStartTime = "";
    formData.outEndTime = "";
  }
};
// 表格配置
const tabelForm = reactive({
  tableKey: "1",
  tableConfig: {
    needPage: true,
    index: true,
    selection: false,
    reserveSelection: false,
    indexFixed: false,
    selectionFixed: false,
    indexWidth: "50",
    loading: false,
    showSummary: false,
    height: null,
  },
  columns: [
    {
      fieldIndex: "plateNo",
      label: "车牌号码",
      resizable: true,
      minWidth: "110px",
      visible: true,
      sortable: true,
    },
    {
      fieldIndex: "staffName",
      label: "人员姓名",
      resizable: true,
      minWidth: "110px",
      visible: true,
      sortable: true,
    },

    {
      fieldIndex: "cellphone",
      label: "手机号码",
      resizable: true,
      minWidth: "120px",
      visible: true,
      sortable: true,
    },

    {
      fieldIndex: "parkingName",
      label: "停车场",
      resizable: true,
      minWidth: "150px",
      visible: true,
      sortable: true,
      align: "left",
    },
    {
      fieldIndex: "parkingEnterName",
      label: "停车场入口",
      resizable: true,
      minWidth: "150px",
      visible: true,
      sortable: true,
      align: "center",
    },
    {
      fieldIndex: "parkingOutName",
      label: "停车场出口",
      resizable: true,
      minWidth: "150px",
      visible: true,
      sortable: true,
      align: "center",
    },

    {
      fieldIndex: "dataStatus",
      label: "数据状态",
      resizable: true,
      minWidth: "120px",
      visible: true,
      sortable: true,
      type: "dict",
      dictList: park_data_status,
    },

    {
      fieldIndex: "hasPermissions",
      label: "授权状态",
      resizable: true,
      minWidth: "110px",
      visible: true,
      sortable: true,
      type: "dict",
      dictList: park_has_permissions,
    },

    {
      fieldIndex: "parkingDuration",
      label: "停车时长(小时)",
      resizable: true,
      minWidth: "160px",
      visible: true,
      sortable: true,
    },

    {
      fieldIndex: "enterTime",
      label: "入场时间",
      resizable: true,
      minWidth: "180px",
      visible: true,
      sortable: true,
    },

    {
      fieldIndex: "enterCarImg",
      label: "入场车身照片",
      resizable: true,
      minWidth: "150px",
      slotname: "enterCarImgSlot",
      visible: true,
      sortable: true,
    },
    {
      fieldIndex: "outTime",
      label: "出场时间",
      resizable: true,
      minWidth: "180px",
      visible: true,
      sortable: true,
    },
    // {
    //   fieldIndex: "enterLicenseImg",
    //   label: "入场车牌照片",
    //   resizable: true,
    //   minWidth: "200px",
    //   slotname: "enterLicenseImgSlot",
    //   visible: true,
    //   sortable: true,
    // },

    // {
    //   fieldIndex: "outLicenseImg",
    //   label: "出场车牌照片",
    //   resizable: true,
    //   minWidth: "150px",
    //   slotname: "outLicenseImgSlot",
    //   visible: true,
    //   sortable: true,
    // },

    {
      fieldIndex: "outCarImg",
      label: "出场车身照片",
      resizable: true,
      minWidth: "150px",
      slotname: "outCarImgSlot",
      visible: true,
      sortable: true,
    },

    {
      fieldIndex: "parkingDays",
      label: "停车天数",
      resizable: true,
      minWidth: "120px",
      visible: true,
      sortable: true,
    },

    // {
    //   fieldIndex: "feeDays",
    //   label: "计费天数",
    //   resizable: true,
    //   minWidth: "120px",
    //   visible: true,
    //   sortable: true,
    // },

    {
      fieldIndex: "enterOutStatus",
      label: "车辆进出场",
      resizable: true,
      minWidth: "140px",
      visible: true,
      sortable: true,
      type: "dict",
      dictList: enter_out_status,
    },

    {
      fieldIndex: "parkingRemark",
      label: "停车说明",
      resizable: true,
      minWidth: "150px",
      visible: true,
      sortable: true,
      align: "left",
    },

    {
      label: "操作",
      slotname: "operation",
      width: "100",
      fixed: "right", //固定
      visible: true,
    },
  ],
});

// 初始化
const init = () => {
  setDefaultDateRange();
  loloading2.value = true;
  getParkSystemList();
  getList();
};

// 获取停车场列表
const getParkSystemList = async () => {
  try {
    const res = await findparkinglot({}, { status: "0" });

    parkSystemList.value = res.data.records;
  } catch (error) {
    console.error("获取停车场失败:", error);
  }
};

// 获取表格数据
const getList = async () => {
  try {
    loloading2.value = true;
    tabelForm.tableConfig.loading = true;
    const res = await parkRecordsPage(formData, pageParams);
    if (res.success) {
      dataList.value = res.data.records;
      total.value = res.data.total;
    } else {
      clearInterval(autoRefreshTimer.value);
    }
  } catch (error) {
    clearInterval(autoRefreshTimer.value);
    console.error("获取数据失败:", error);
  } finally {
    tabelForm.tableConfig.loading = false;
    loloading2.value = false;
  }
};
/** 导出按钮操作 */
const handleExport = () => {
  proxy.download(
    "park" + apiUrl + "/majorParking/parkRecords/export",
    {
      ...formData,
    },
    `停车记录_${formatMinuteTime(new Date())}.xlsx`
  );
};

// 在手动搜索时重置定时器
const handleQuery = () => {
  pageParams.pageNum = 1;
  loloading2.value = true;
  clearInterval(autoRefreshTimer.value); // 清除旧定时器
  if (autoRefreshInterval.value > 0) {
    autoRefreshTimer.value = setInterval(() => {
      getList();
    }, autoRefreshInterval.value * 1000);
  }
  getList();
};
// 重置
const resetQuery = () => {
  daySelects.value = [];
  daySelects2.value = [];
  formData.tenantId = userStore.userInfo.tenantId; // 重置租户ID为当前用户租户
  formData.enterOutStatus = "0"; // 重置进出状态为默认值
  formData.hasPermissions = "1"; // 重置授权状态为默认值
  formData.inStartTime = "";
  formData.inEndTime = "";
  proxy.resetForm("queryForm");
  handleQuery();
};
/** 查看 */
const handleView = (data) => {
  diaWindow.popupType = "view";
  diaWindow.rowData = data;
  diaWindow.dialogWidth = "50%";
  diaWindow.dialogTitle = "查看";
  diaWindow.dialogFooterBtn = false;
  diaWindow.open1 = true;
};
/** 修改 */
const handleEdit = (data) => {
  diaWindow.popupType = "edit";
  diaWindow.rowData = data;
  diaWindow.dialogTitle = "修改";
  diaWindow.dialogWidth = "30%";
  diaWindow.dialogFooterBtn = true;
  diaWindow.open1 = true;
};
/** 点击取消保存 */
const cancellation = (val) => {
  close(false);
};

/** 开闸 */
const gateControlByRecordsFun = async (row) => {
  try {
    const { id } = row; // 从 row 对象中解构出 id
    // 弹出确认框
    await ElMessageBox.confirm("确定要开闸吗？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning", // 确认框类型（warning、success、info、error）
    });
    // 用户点击确认后，执行开闸操作
    const res = await gateControlByRecords({ id: id });

    // 根据返回结果提示用户
    if (res.code == "1") {
      ElMessage.success(res.data);
      getList();
    } else {
      ElMessage.error(res.data);
    }
  } catch (error) {
    if (error !== "cancel") {
      // 如果错误不是用户点击取消导致的
      console.error("控制闸门时发生错误:", error);
    }
  }
};

/** 复制车牌 */
const copyPlateNumber = async (plateNo) => {
  const text = plateNo.replace(/\s/g, "").toUpperCase();

  const textarea = document.createElement("textarea");
  textarea.value = text;
  document.body.appendChild(textarea);
  textarea.select();

  try {
    // 尝试执行复制操作
    const success = document.execCommand("copy");
    if (success) {
      ElMessage.success("车牌号已复制");
    } else {
      ElMessage.error("复制失败，请手动复制");
    }
  } catch (error) {
    ElMessage.error("复制失败，请手动复制");
  }

  document.body.removeChild(textarea);
};
/** 关闭弹窗 */
const close = (val) => {
  diaWindow.open1 = val;
};
/** 提交 */
const submits = () => {
  editRef.value.submitForm();
};

const cancellationRefreshFun = () => {
  close(false);
  handleQuery();
};

// 生命周期钩子
onMounted(() => {
  init();
});
</script>

<style scoped lang="scss">
.formRangeTime {
  display: flex;
  align-items: center;
  gap: 8px;

  &-tip {
    color: #999;
  }
}

:deep(.el-select) {
  width: 100%;
}

:deep(.table-img) {
  width: 100px;
  height: 26px;
}

:deep(.image-error) {
  .el-icon {
    font-size: 34px !important;
  }
}
</style>