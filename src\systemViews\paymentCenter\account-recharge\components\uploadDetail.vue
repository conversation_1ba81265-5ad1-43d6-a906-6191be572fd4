<!-- 账户明细 -->
<template>
  <div class="container-table-box">
    <div class="flex" style="margin-bottom: 12px;" v-if="uoloadStatus == 'failed'">
      <div class="flex-1"></div>
      <el-button type="primary" size="mini" icon="Download" @click="handleExport">下载批量充值失败列表
      </el-button>
    </div>
    <public-table ref="publictable" :rowKey="tabelForm.tableKey" :tableData="list" :columns="tabelForm.columns"
      :configFlag="tabelForm.tableConfig" :pageValue="pageParams" :total="total">
    </public-table>
  </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from "vue";
import { ElMessageBox, ElMessage } from "element-plus";
import { apiUrl } from "@/utils/config";
import { formatMinuteTime } from "@/utils";
import { screenIndex } from "@/api/paymentCenter/account-recharge/index";
const { proxy } = getCurrentInstance();
const { staff_type } = proxy.useDict(
  "staff_type"
);
const emit = defineEmits(["closeBtn"]);

const props = defineProps({
  detailList: {
    type: Array,
    default: () => [],
  },
  uoloadStatus: {
    type: String,
    default: "",
  },
  redisLock: {
    type: String,
    default: "",
  }
});
const pageParams = ref({
  pageNum: 1,
  pageSize: 10,
});
const list = ref([]);
const total = ref(0);
const tabelForm = reactive({
  tableKey: "1", //表格key值
  isShowRightToolbar: true, //是否显示右侧显示和隐藏
  showSearch: true,
  // 表格表格数据
  columns: [
    {
      fieldIndex: "staffName",
      label: "员工姓名",
      show: true,
      sortable: true,
      visible: true, // 展示与隐藏
      minWidth: "120px",
    },
    {
      fieldIndex: "loginName",
      label: "人员账号",
      show: true,
      sortable: true,
      visible: true, // 展示与隐藏
      minWidth: "120px",
    },
    {
      fieldIndex: "cellphone",
      label: "联系方式",
      show: true,
      sortable: true,
      visible: true, // 展示与隐藏
      minWidth: "120px",
    },

    {
      fieldIndex: "orgName",
      label: "员工部门",
      show: true,
      sortable: true,
      visible: true, // 展示与隐藏
      minWidth: "120px",
    },

    {
      fieldIndex: "staffType",
      label: "人员类型",
      show: true,
      visible: true, // 展示与隐藏
      sortable: true,
      minWidth: "120px",
      type: 'dict',
      dictList: staff_type
    },
    {
      fieldIndex: "accountName",
      label: "账号名称",
      show: true,
      visible: true, // 展示与隐藏
      sortable: true,
      minWidth: "120px",
    },

    {
      fieldIndex: "accountCode",
      label: "账号编码",
      show: true,
      visible: true, // 展示与隐藏
      sortable: true,
      minWidth: "120px",
    },

    {
      fieldIndex: "rechargeAmount", // 对应列内容的字段名
      label: "充值金额(元)", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "120", //最小宽度%
      width: "", //宽度
      type: "dollor",
    },
  ],
  // 表格基础配置项，看个人需求添加
  tableConfig: {
    needPage: true, // 是否需要分页
    index: true, // 是否需要序号
    selection: false, // 是否需要多选
    reserveSelection: false, // 是否需要支持跨页选择
    indexFixed: false, // 序号列固定 left true right
    selectionFixed: false, //多选列固定left true right
    indexWidth: "50", //序号列宽度
    loading: false, //表格loading
    showSummary: false, //是否开启合计
    height: null, //表格固定高度 比如：300px
  },
});
/** 查询列表 */
const getList = () => {
  if (props.uoloadStatus === 'failed') {
    list.value = [];
    total.value = 0;
    return;
  }
  
  tabelForm.tableConfig.loading = true;
  screenIndex.importDataList({
    redisLock: props.redisLock
  }, pageParams.value).then((response) => {
    list.value = response.data.records;
    total.value = response.data.total;
    tabelForm.tableConfig.loading = false;
  });
};
const handleExport = () => {
  proxy.download(
    `pay${apiUrl}/pay/payRechargeDetails/exportErrorData`,
    {
      redisLock: props.redisLock,
    },
    `批量开通导入失败列表_${formatMinuteTime(new Date())}.xlsx`
  );
}
// 保存
const saveBtn = async (redisLock) => {
  try {
    // 3. 调用接口（假设接口接受数组格式）
    const res = await screenIndex.batchAdd({ redisLock: redisLock });

    // 4. 处理结果
    if (res.success) {
      ElMessage.success("操作成功");
      emit("closeBtn");
    }
  } catch (error) {
    console.error("保存失败:", error);
  } finally {
  }
};


onMounted(() => {
  getList();
})
defineExpose({
  saveBtn,
});
</script>

<style scoped>
</style>