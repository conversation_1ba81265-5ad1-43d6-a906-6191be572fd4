<!-- 员工车辆 -->
<template>
  <div class="container-table-box">
      <el-card>
    <el-row :gutter="24">
      <el-col :span="24" :xs="24">
        <dialog-search @getList="getList" formRowNumber="4" :columns="tabelForm.columns"  :isShowRightBtn="$checkPermi(['parking:userCar:list'])">
          <template #formList>
            <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="80px">
              <el-form-item label="租户" prop="tenantId">
                <TenantSelect v-model="queryParams.tenantId"></TenantSelect>
              </el-form-item>
              <el-form-item label="车牌号码" prop="plateNo">
                <el-input v-model="queryParams.plateNo" placeholder="请输入车牌号码" clearable @keyup.enter="handleQuery" />
              </el-form-item>
              <el-form-item label="人员姓名" prop="staffName">
                <el-input v-model="queryParams.staffName" placeholder="请输入人员姓名" clearable @keyup.enter="handleQuery" />
              </el-form-item>
              <el-form-item label="车辆状态" prop="carStatus">
                <el-select @change="getList" v-model="queryParams.carStatus" placeholder="请选择车辆状态" clearable>
                  <el-option v-for="(item, index) of car_status" :key="index" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
              <el-form-item label="车辆编组" prop="vehicleType">
                <el-select @change="getList" v-model="queryParams.vehicleType" placeholder="请选择车辆编组" clearable>
                  <el-option v-for="(item, index) of parkingRuleVehicleTypeList" :key="index" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
              <el-form-item label="车辆类型" prop="carType">
                <el-select @change="getList" v-model="queryParams.carType" placeholder="请选择车辆类型" clearable>
                  <el-option v-for="(item, index) of car_type" :key="index" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
              <el-form-item label="车牌颜色" prop="plateColor">
                <el-select @change="getList" v-model="queryParams.plateColor" placeholder="请选择车牌颜色" clearable>
                  <el-option v-for="(item, index) of plate_color" :key="index" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
              <el-form-item label="车辆规格" prop="vehicleSpecification">
                <el-select @change="getList" v-model="queryParams.vehicleSpecification" placeholder="请选择车辆规格" clearable>
                  <el-option v-for="(item, index) of vehicle_specification" :key="index" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-form>
          </template>
          <template #searchList>
            <el-button type="primary" icon="Search" @click="handleQuery"  v-hasPermi="['parking:userCar:list']">
              搜索
            </el-button>
            <el-button icon="Refresh" @click="resetQuery" v-hasPermi="['parking:userCar:list']"> 重置 </el-button>
          </template>
          <template #searchBtnList>
            <el-button type="primary" icon="Plus" @click="handleAdd" v-hasPermi="['parking:userCar:add']">
              新增
            </el-button>
            <el-button icon="Download" @click="handleExport" v-hasPermi="['parking:userCar:export']">
              导出
            </el-button>
            <el-button icon="Upload" @click="handleImport" v-hasPermi="['parking:userCar:import']">
              导入
            </el-button>

            <el-button icon="Download" @click="importTemplate" v-hasPermi="['parking:userCar:import']">
              模板导出
            </el-button>
          </template>
        </dialog-search>

        <PublicTable ref="publictable" :rowKey="tabelForm.tableKey" :tableData="userList" :columns="tabelForm.columns"
          :configFlag="tabelForm.tableConfig" :pageValue="pageParams" :total="total" :getList="getList">
          <!-- 有效期限 -->
          <template #validDate="{ scope }">
            {{ scope.row.validDate || "长期有效" }}
          </template>

          <!-- 手机号码 -->
          <template #telephone="{ scope }">
            <div>
              {{
                scope.row.cellphone
                  ? scope.row.cellphone.replace(
                    /(\d{3})(\d{4})(\d{4})/,
                    "$1****$3"
                  )
                  : "-"
              }}
            </div>
          </template>

          <!-- 车辆类型 -->
          <template #vehicleoperation="{ scope }">
            <div>
              {{
                fromatComonDict(
                  scope.row.vehicleType,
                  parkingRuleVehicleTypeList
                )
              }}
            </div>
          </template>

          
          <template #operationEditIsDoing="{ scope }">
            <el-switch
            v-hasPermi="['parking:userCar:edit']"
          active-text="通知"
          inactive-text="不通知"
          inline-prompt
          v-model="scope.row.isDing"
          :active-value="'0'"
          :inactive-value="'1'"
          :before-change="() => handleSwitchChange(scope.row)"
        />
          </template>
          <!-- 操作列 -->
          <template #operation="{ scope }">
            <el-button link icon="Tickets" type="primary" title="查看历史记录" @click="handleHistoryView(scope.row)"  v-hasPermi="['parking:userCar:list']">
            </el-button>
            <el-button link icon="View" type="primary" title="查看" @click="handleView(scope.row)"  v-hasPermi="['parking:userCar:list']">
            </el-button>
            <el-button link icon="Edit" type="primary" title="修改" @click="handleUpdate(scope.row)"    v-hasPermi="['parking:userCar:edit']">
            </el-button>
            <el-button link icon="Delete" type="primary" title="删除" @click="handleDelete(scope.row)"    v-hasPermi="['parking:userCar:remove']">
            </el-button>
          </template>
        </PublicTable>


        <DialogBox :visible="open" :dialogWidth="dialogWidth" @save="submits" @cancellation="cancellation"
          @close="close" :dialogFooterBtn="dialogFooterBtn" CloseSubmitText="取消" SaveSubmitText="确定"
          :dialogTitle="headerTitle">
          <template #content>
            <div class="dialog-box dialog-box-edit" v-if="popupType == 'upload'">

              <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="68px" >

                <el-form-item label="租户" prop="tenantId" style="width:100%;">
                  <TenantSelect v-model="extraUploadFileData.tenantId"></TenantSelect>
                </el-form-item>

                <el-form-item label="截止日期" prop="validDate" style="width:100%;">
                  <el-date-picker v-model="extraUploadFileData.validDate" value-format="YYYY-MM-DD" type="date"
                    placeholder="选择截止日期" />
                </el-form-item>


              </el-form>
            </div>

            <UploadFile v-if="popupType == 'upload'" ref="uploadRef" :uploadData="extraUploadFileData"
              :action="upload.url" :limit="1" :accept="'.xlsx, .xls'" :disabled="upload.isUploading"
              :auto-upload="false" tip="提示：仅允许导入“xls”或“xlsx”格式文件！<br>上传数据必须包含车牌号码、车牌颜色、人员姓名、人员账号、手机号码！"
              @file-success="handleFileSuccess" @file-error="handleFileError" />
            <userCarEdit ref="userCarEditRef" v-if="popupType == 'add' || popupType == 'edit' || popupType == 'view'"
              @submitClose="submitClose" :row-data="rowData" :list="parkingRuleVehicleTypeList" :carType="carType"
              :popup-type="popupType" />

          </template>
        </DialogBox>


        <DialogBox :visible="open2" dialogWidth="70%"   @cancellation="cancellation2"
          @close="close2" :dialogFooterBtn="false" CloseSubmitText="取消" SaveSubmitText="确定"
          dialogTitle="查看导入记录">
          <template #content>
            <views :rowData="rowData2"></views>
           
          </template>
        </DialogBox>

        <!-- 查询车辆历史记录 -->
        <DialogBox :visible="openHistory" dialogWidth="70%"   @cancellation="cancellation3"
          @close="closeHistory" :dialogFooterBtn="false" CloseSubmitText="取消" SaveSubmitText="确定"
          dialogTitle="查看历史车牌记录">
          <template #content>
            <PublicTable ref="publictable" :rowKey="tabelHistoryForm.tableKey" :tableData="userCarList" :columns="tabelHistoryForm.columns"
              :configFlag="tabelHistoryForm.tableConfig" :pageValue="pageCarParams" :total="carTotal" :getList="getHistoryList">
              <!-- 有效期限 -->
              <template #validDate="{ scope }">
                {{ scope.row.validDate || "长期有效" }}
              </template>

              <!-- 手机号码 -->
              <template #telephone="{ scope }">
                <div>
                  {{
                    scope.row.cellphone
                      ? scope.row.cellphone.replace(
                        /(\d{3})(\d{4})(\d{4})/,
                        "$1****$3"
                      )
                      : "-"
                  }}
                </div>
              </template>

              <!-- 车辆类型 -->
              <template #vehicleoperation="{ scope }">
                <div>
                  {{
                    fromatComonDict(
                      scope.row.vehicleType,
                      parkingRuleVehicleTypeList
                    )
                  }}
                </div>
              </template>
            </PublicTable>
          </template>
        </DialogBox>
      </el-col>
    </el-row>
    </el-card>
  </div>
</template>

<script setup name="UserCar">
import UploadFile from "@/components/UploadFile/index";
import views from "@/systemViews/vehicle-management/temporaryCar/components/view";
import { reactive, ref, onMounted, getCurrentInstance } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import userCarEdit from './components/userCarEdit.vue';
import { screenIndex } from "@/api/majorParking/userCar";
import { getDictInfo } from "@/api/system/dict/data";
import useUserStore from "@/store/modules/user";
import { formatMinuteTime } from "@/utils";
import { apiUrl } from "@/utils/config";
const { proxy } = getCurrentInstance();
// 定义组件属性和事件
defineProps({
  // 定义组件属性
});
defineEmits(["updateRow"]);
// 车辆编组列表
const parkingRuleVehicleTypeList = ref([]);
// 车辆类型列表
const carType = ref([]);
// 字典数据
const { car_status, vehicle_specification, plate_color,car_type} = proxy.useDict(
  "car_status",
  "vehicle_specification",
  "plate_color",
    "car_type"
);
const userStore = useUserStore();
// 表格配置
const tabelForm = ref({
  tableKey: "1",
  isShowRightToolbar: true,
  showSearch: true,
  columns: [
    {
      fieldIndex: "plateNo",
      label: "车牌号码",
      resizable: true,
      visible: true,
      sortable: true,
      minWidth: "120px", //最小宽度%
    },
    {
      fieldIndex: "vehicleType",
      label: "车辆编组",
      resizable: true,
      visible: true,
      sortable: true,
      minWidth: "160px", //最小宽度%
      type: "dict",
      dictList: []
    },
    {
      fieldIndex: "carType",
      label: "车辆类型",
      resizable: true,
      visible: true,
      sortable: true,
      minWidth: "160px", //最小宽度%
      type: "dict",
      dictList: car_type,
    },
    {
      fieldIndex: "staffName",
      label: "人员姓名",
      resizable: true,
      minWidth: "120px", //最小宽度%
      visible: true,
      sortable: true,
    },
    {
      fieldIndex: "orgName",
      label: "人员部门",
      resizable: true,
      minWidth: "120px", //最小宽度%
      visible: true,
      sortable: true,
    },
    {
      fieldIndex: "cellphone",
      label: "手机号码",
      resizable: true,
      visible: true,
      minWidth: "120px", //最小宽度%
      sortable: true,
      slotname: "telephone",
    },
    {
      fieldIndex: "carStatus",
      label: "车辆状态",
      resizable: true,
      visible: true,
      sortable: true,
      minWidth: "120px", //最小宽度%
      type: "dict",
      dictList: car_status,
    },
    {
      fieldIndex: "validDate",
      slotname: "validDate",
      label: "有效期限",
      resizable: true,
      minWidth: "120px", //最小宽度%
      visible: true,
      sortable: true,
    },
    {
      fieldIndex: "carBrand",
      label: "车辆品牌",
      resizable: true,
      minWidth: "120px", //最小宽度%
      visible: true,
      sortable: true,
    },
    {
      fieldIndex: "carModel",
      label: "品牌型号",
      resizable: true,
      visible: true,
      minWidth: "120px", //最小宽度%
      sortable: true,
    },
    {
      fieldIndex: "vehicleSpecification",
      label: "车辆规格",
      resizable: true,
      visible: true,
      sortable: true,
      minWidth: "120px", //最小宽度%
      type: "dict",
      dictList: vehicle_specification,
    },
    {
      fieldIndex: "carColor",
      label: "车辆颜色",
      resizable: true,
      minWidth: "120px", //最小宽度%
      visible: true,
      sortable: true,
    },
    {
      fieldIndex: "plateColor",
      label: "车牌颜色",
      resizable: true,
      minWidth: "110px", //最小宽度%
      visible: true,
      sortable: true,
      type: "dict",
      dictList: plate_color,
    },
    {
      fieldIndex: "remark",
      label: "车辆备注",
      resizable: true,
      minWidth: "150px", //最小宽度%
      visible: true,
      sortable: true,
      align: "left",
    },

    {
      fieldIndex: "isDing",
      label: "消息通知",
      slotname: "operationEditIsDoing",
      minWidth: "120px", //最小宽度%
      visible: true,
      fixed: "right",
      sortable: true,
    },
    {
      label: "操作",
      slotname: "operation",
      width: "150",
      fixed: "right",
      visible: true,
    },
  ],
  tableConfig: {
    needPage: true,
    index: true,
    selection: false,
    reserveSelection: false,
    indexFixed: false,
    selectionFixed: false,
    indexWidth: "50",
    loading: false,
    showSummary: false,
    height: null,
  },
});

// 历史记录表格配置
const tabelHistoryForm = ref({
  tableKey: "1",
  isShowRightToolbar: true,
  showSearch: true,
  columns: [
    {
      fieldIndex: "plateNo",
      label: "车牌号码",
      resizable: true,
      visible: true,
      sortable: true,
      minWidth: "120px", //最小宽度%
    },
    {
      fieldIndex: "vehicleType",
      label: "车辆编组",
      resizable: true,
      visible: true,
      sortable: true,
      minWidth: "160px", //最小宽度%
      type: "dict",
      dictList: []
    },
    {
      fieldIndex: "carType",
      label: "车辆类型",
      resizable: true,
      visible: true,
      sortable: true,
      minWidth: "160px", //最小宽度%
      type: "dict",
      dictList: car_type,
    },
    {
      fieldIndex: "staffName",
      label: "人员姓名",
      resizable: true,
      minWidth: "120px", //最小宽度%
      visible: true,
      sortable: true,
    },
    {
      fieldIndex: "orgName",
      label: "人员部门",
      resizable: true,
      minWidth: "120px", //最小宽度%
      visible: true,
      sortable: true,
    },
    {
      fieldIndex: "cellphone",
      label: "手机号码",
      resizable: true,
      visible: true,
      minWidth: "120px", //最小宽度%
      sortable: true,
      slotname: "telephone",
    },
    // {
    //   fieldIndex: "carStatus",
    //   label: "车辆状态",
    //   resizable: true,
    //   visible: true,
    //   sortable: true,
    //   minWidth: "120px", //最小宽度%
    //   type: "dict",
    //   dictList: car_status,
    // },
    {
      fieldIndex: "validDate",
      slotname: "validDate",
      label: "有效期限",
      resizable: true,
      minWidth: "120px", //最小宽度%
      visible: true,
      sortable: true,
    },
    {
      fieldIndex: "carBrand",
      label: "车辆品牌",
      resizable: true,
      minWidth: "120px", //最小宽度%
      visible: true,
      sortable: true,
    },
    {
      fieldIndex: "carModel",
      label: "品牌型号",
      resizable: true,
      visible: true,
      minWidth: "120px", //最小宽度%
      sortable: true,
    },
    {
      fieldIndex: "vehicleSpecification",
      label: "车辆规格",
      resizable: true,
      visible: true,
      sortable: true,
      minWidth: "120px", //最小宽度%
      type: "dict",
      dictList: vehicle_specification,
    },
    {
      fieldIndex: "carColor",
      label: "车辆颜色",
      resizable: true,
      minWidth: "120px", //最小宽度%
      visible: true,
      sortable: true,
    },
    {
      fieldIndex: "plateColor",
      label: "车牌颜色",
      resizable: true,
      minWidth: "110px", //最小宽度%
      visible: true,
      sortable: true,
      type: "dict",
      dictList: plate_color,
    },
    {
      fieldIndex: "remark",
      label: "车辆备注",
      resizable: true,
      minWidth: "150px", //最小宽度%
      visible: true,
      sortable: true,
      align: "left",
    },
    {
      fieldIndex: "updateDate",
      label: "维护时间",
      resizable: true,
      minWidth: "200px", //最小宽度%
      visible: true,
      sortable: true,
      align: "center",
    },
  ],
  tableConfig: {
    needPage: true,
    index: true,
    selection: false,
    reserveSelection: false,
    indexFixed: false,
    selectionFixed: false,
    indexWidth: "50",
    loading: false,
    showSummary: false,
    height: null,
  },
});

// 查询参数
const pageParams = ref({
  pageNum: 1,
  pageSize: 10,
});

// 表格数据
const userList = ref([]);

// 查询参数
const pageCarParams = ref({
  pageNum: 1,
  pageSize: 10,
});

// 表格数据
const userCarList = ref([]);

// 日期范围
const dateRange = ref([]);

// 用户导入参数
const upload = reactive({
  title: "",
  isUploading: false,
  updateSupport: 0,
  url: `${import.meta.env.VITE_APP_BASE_API}/park${apiUrl}/majorParking/userCar/importData`,
});

const extraUploadFileData = ref({
  validDate: '',
  tenantId: userStore.userInfo.tenantId,
})


// 表格总数
const total = ref(0);
// 表格总数
const carTotal = ref(0);
// 是否显示弹窗底部按钮
const dialogFooterBtn = ref(true)
// 弹窗标题
const headerTitle = ref("");

// 点击行信息
const rowData = ref({});
const rowData2 = ref([])

// 是否显示弹窗
const open = ref(false);

// 弹窗宽度
const dialogWidth = ref("");


// 是否显示弹窗
const open2 = ref(false);

// 是否显示弹窗
const openHistory = ref(false);

// 弹窗类型
const popupType = ref("");
// 人员id
const staffId = ref("");

// form
const queryParams = ref({
  tenantId: userStore.userInfo.tenantId,
});

// upload ref

const uploadRef = ref(null);

// 修改 ref

const userCarEditRef = ref(null)
// 主要方法
// 获取车辆类型
const getVehicleType = async () => {
  const res = await getDictInfo("parking_rule_vehicle_type");
  parkingRuleVehicleTypeList.value = res.data
  parkingRuleVehicleTypeList.value.forEach((item) => {
    item.value = item.dictValue
    item.label = item.dictLabel
  })
  tabelForm.value.columns[1].dictList = parkingRuleVehicleTypeList.value;


  getList();
};
// 获取车辆类型
const getCarType = async () => {
  const res = await getDictInfo("car_type");
  carType.value = res.data
  carType.value.forEach((item) => {
    item.value = item.dictValue
    item.label = item.dictLabel
  })
  getList();
};

// 列表请求
const getList = () => {
  tabelForm.value.tableConfig.loading = true;
  screenIndex.stUserCar(pageParams.value, queryParams.value).then((res) => {
    // 处理数据：isDing 不存在/空时默认设为 '0'
    userList.value = (res.data.records || []).map(item => ({
      ...item,
      isDing: item.isDing || '0' // 逻辑：空值/不存在 → '0'
    }));
    total.value = res.data.total;
    tabelForm.value.tableConfig.loading = false;
  });
};
// 根据人员id查询历史记录
const getHistoryList = () =>{
  tabelForm.value.tableConfig.loading = true;
  let params = {
    staffId: staffId.value
  }
  screenIndex.stPastUserCar(pageCarParams.value, params).then((res) => {
    // 处理数据：isDing 不存在/空时默认设为 '0'
    userCarList.value = (res.data.records || []).map(item => ({
      ...item,
      isDing: item.isDing || '0' // 逻辑：空值/不存在 → '0'
    }));
    tabelHistoryForm.value.columns[1].dictList = parkingRuleVehicleTypeList.value;
    carTotal.value = res.data.total;
    tabelForm.value.tableConfig.loading = false;
  });
}
// 查询
const handleQuery = () => {
  pageParams.value.pageNum = 1;
  getList();
};

// 重置
const resetQuery = () => {
  dateRange.value = [];
  queryParams.value = {};
  pageParams.value.pageNum = 1;
  pageParams.value.pageSize = 10;
  getList();
};

// 新增
const handleAdd = () => {
  headerTitle.value = "新增员工车辆";
  popupType.value = "add";
  dialogWidth.value = '50%'
  dialogFooterBtn.value = true;
  open.value = true;
};

// 修改
const handleUpdate = (row) => {
  headerTitle.value = "修改员工车辆";
  popupType.value = "edit";
  dialogWidth.value = '50%'
  dialogFooterBtn.value = true;
  rowData.value = row;
  open.value = true;
};

// 查看详情
const handleView = (row) => {
  const formattedRow = {
    ...row,
    telephone: row.telephone
      ? row.telephone.replace(/(\d{3})(\d{4})(\d{4})/, "$1****$3")
      : "-",
  };
  dialogFooterBtn.value = false;
  headerTitle.value = "查看员工车辆";
  rowData.value = formattedRow;
  dialogWidth.value = '50%'
  popupType.value = "view";
  open.value = true;
};

// 查看历史记录
const handleHistoryView = (row) => {
  staffId.value = row.staffId;
  getHistoryList();
  dialogFooterBtn.value = false;
  openHistory.value = true;
};

// 删除
const handleDelete = (row) => {
  ElMessageBox.confirm("确认要删除该条数据吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      screenIndex.deleteUserCarById(row.id).then((res) => {
        if (res.code == '1') {
          ElMessage.success("删除成功");
          getList();
        } else {
          ElMessage.error(res.msg);
        }
      });
    })
    .catch(() => {
      // 用户取消删除
    });
};

// 文件上传成功处理
const handleFileSuccess = (response) => {
  if (response.response.code == '1') {
    ElMessage.success("导入成功");
    setTimeout(() => {
      upload.isUploading = false;
      open.value = false;
      open2.value = true
      rowData2.value = response.response.data
      getList();
    }, 800);
  } else {
    ElMessage.error(response.response.message);
  }

};

// 文件上传失败处理
const handleFileError = (res) => {
  ElMessage.error('导入失败');
};

// 导入
const handleImport = () => {
  popupType.value = "upload";
  headerTitle.value = "用户导入";
  dialogWidth.value = "400px";
  open.value = true;
};

// 导出
const handleExport = () => {
  proxy.download(
    `park${apiUrl}/majorParking/userCar/exportUserCar`,
    {
      ...queryParams.value,
    },
    `员工车辆表_${formatMinuteTime(new Date())}.xlsx`
  );
};

// 模板导出
const importTemplate = () => {
  proxy.download(
    `park${apiUrl}/majorParking/userCar/importTemplateForUser`,
    {
      ...queryParams.value,
    },
    `员工车辆导入模版_${formatMinuteTime(new Date())}.xlsx`
  );
};
// 处理开关状态变更
const handleSwitchChange = async (row) => {
  try {
    // 计算新状态（当前状态为旧值，需取反）
    const newStatus = row.isDing === '0' ? '1' : '0';

    // 调用更新接口（需根据实际API调整参数）
    const { success } = await screenIndex.updataUserCar({
      ...row,        // 假设行数据有唯一标识
      isDing: newStatus  // 传递新状态
    });

    // 根据接口结果处理
    if (success) {
      ElMessage.success("状态更新成功");
      return true; // 允许切换
    } 
  } catch (error) {
    console.error("状态更新失败:", error);
    ElMessage.error("网络错误，请重试");
    return false; // 阻止切换
  }
};

// 提交关闭
const submitClose = () => {
  open.value = false;
  setTimeout(() => {
    getList();
  }, 100);
};

// 车辆类型格式化方法
const fromatComonDict = (value, list) => {
  const item = list.find((item) => item.value === value);
  return item ? item.label : "";
};
/** 点击提交 */
const submits = () => {
  if (popupType.value == "upload") {
    uploadRef.value.submitFileForm();
  }

  if (popupType.value == "add" || popupType.value == "edit") {
    userCarEditRef.value.saveBtn();
  }

};
/** 点击取消保存 */
const cancellation = (val) => {
  close(false);
};
/** 关闭弹窗 */
const close = (val) => {
  open.value = val;
};

/** 点击取消保存 */
const cancellation2 = (val) => {
  close2(false);
};
/** 关闭弹窗 */
const close2 = (val) => {
  open2.value = val;
};

/** 点击取消保存 */
const cancellation3 = (val) => {
  closeHistory(false);
};
/** 关闭弹窗 */
const closeHistory = (val) => {
  openHistory.value = val;
};
// 生命周期钩子：组件挂载时调用
onMounted(() => {
  getVehicleType();
  getCarType();
});
</script>

<style scoped>
/* 添加自定义样式 */
</style>
