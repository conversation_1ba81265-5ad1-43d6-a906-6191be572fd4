<template>
  <div class="app-container">
    <Splitpanes class="default-theme">
      <Pane :size="15" :min-size="15">
        <el-card class="dep-card" style="height: 100%">
          <template #header>
            <div class="card-header">
              <span>系统组织</span>
              <el-button style="float: right; padding: 3px 0" link icon="Refresh" @click="reloadTree">刷新
              </el-button>
              <el-form style="margin-top: 20px;margin-bottom: -20px;" v-if="userType === 'admin'">
                <el-form-item label="租户：">
                  <el-select v-model="queryParams.tenantId" style="width: 120px;" remote :remote-method="getTenantList"
                    :loading="getTenantLoading" @change="tenantChange">
                    <el-option v-for="item in tenantList" :key="item.tenantId" :label="item.tenantName"
                      :value="item.tenantId" />
                  </el-select>
                </el-form-item>
              </el-form>
            </div>
          </template>
          <!-- 组织树 -->
          <!-- <el-tree
              :props="{label:'orgName',children:'children',isLeaf:(data) => !data.isParent} "
              :load="loadNode"
              lazy
              :expand-on-click-node="false"
              ref="asyncTree"
              @node-click="handleNodeClick"
              :default-expanded-keys="[defaultOrgId]"
              node-key="orgId"
              highlight-current
          >
          </el-tree> -->

          <el-input placeholder="输入名称进行查询" v-model="filterText" clearable style="margin-bottom: 8px">
            <template #append>
              <el-button icon="search" @click="searchFiler(filterText)"></el-button>
            </template>
          </el-input>
          <treeLoad ref="asyncTree" :key="treeFatherIndex" :isShowSearch="false" :idDefaultExpandAll="false"
            :defaultProps="defaultProps" :treeBtnEdit="false" @loadFirstNodeFather="loadNodeRooter"
            @loadChildNodeFather="loadNode" :treeBtnDelete="false" tree-name="orgName" nodeKey="orgId"
            :treeData="treeData" :defaultExpandedKeys="[defaultOrgId]" :isLazy="isLazy" :treeindex="treeindex"
            @checkedKeys="handleNodeClick" @editNodes="editNodesEdit" />
        </el-card>
      </Pane>
      <Pane :size="85" :min-size="65">
        <el-card class="dep-card" shadow="never">
          <template #header>
            <div class="clearfix">
              <span>{{ selectNodeName }}</span>
              <el-button style="float: right; padding: 3px 0" link icon="Refresh" @click="allUser">全部组织
              </el-button>
            </div>
          </template>


          <dialog-search @getList="getList" formRowNumber="4" :columns="columns">
            <template v-slot:formList>
              <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px"
                @submit.native.prevent>
                <el-form-item label="组织名称" prop="orgName">
                  <el-input v-model="queryParams.orgName" placeholder="请输入组织名称" clearable style="width: 240px"
                    @keyup.enter.native="handleQuery" />
                </el-form-item>
              </el-form>
            </template>
            <template v-slot:searchList>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </template>
            <template v-slot:searchBtnList>

              <el-button type="primary" icon="Plus" @click="handleAdd" v-hasPermi="['sys:base:dept:add']"
                v-if="selectNode && selectNode.orgType !== 'group'">新增
              </el-button>

            </template>
          </dialog-search>


          <!-- <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
              <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['sys:base:dept:add']"
                v-if="selectNode && selectNode.orgType !== 'group'">新增
              </el-button>
            </el-col> -->
            <!-- <el-col :span="1.5">
              <input v-if="isShowFile" type="file" id="fileExport"
                     @change="handleFileChange" ref="inputer" style="display: none"/>
              <el-button
                  type="primary" plain icon="Upload" v-hasPermi="['sys:base:dept:import']" @click="handleUpload">导入
              </el-button>
            </el-col>
            <el-col :span="1.5">
              <a :href="`组织导入.xlsx`" download="组织导入.xlsx"
                 v-hasPermi="['sys:base:dept:template']">
                <el-button type="primary" plain icon="Download">模板</el-button>
              </a>
            </el-col> -->
            <!-- <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
          </el-row> -->

          <el-table v-loading="loading" :data="deptList">
            <el-table-column label="组织编号" align="left" key="code" prop="code" v-if="columns[0].visible" />
            <el-table-column label="组织名称" align="left" key="orgName" prop="orgName" v-if="columns[1].visible"
              :show-overflow-tooltip="true" />
            <el-table-column label="组织类别" align="center" key="orgType" prop="orgType" v-if="columns[2].visible"
              :show-overflow-tooltip="true">
              <template #default="scope">
                <dict-tag :options="org_type" :value="scope.row.orgType" />
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="260" class-name="small-padding fixed-width">
              <template #default="scope">
                <el-button link icon="Edit" type="primary" :loading="getDeptLoading && scope.row.orgId === loadOrgId"
                  v-hasPermi="['sys:base:dept:edit']" @click="handleUpdate(scope.row)">修改
                </el-button>
                <el-button v-if="scope.row.userId !== 1 && scope.row.orgId != defaultOrgId" link icon="Delete"
                  v-hasPermi="['sys:base:dept:remove']" type="primary" @click="handleDelete(scope.row)">删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize" @pagination="getList" />
        </el-card>
      </Pane>
    </Splitpanes>
    <!-- 添加或修改参数配置对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="formRef" :model="form" :rules="rules" label-width="110px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="上级组织" prop="parentName">
              <el-input v-model="form.parentName" placeholder="请选择上级组织" disabled>
                <template #append>
                  <el-link @click="proxy.$refs.treeSelect.open()" :disabled="isTopNode">选择</el-link>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item v-show="false" prop="parentId">
              <el-input v-model="form.parentId"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="组织名称" prop="orgName">
              <el-input v-model="form.orgName" placeholder="请输入组织名称" maxlength="64" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="组织类别" prop="orgType">
              <el-select v-model="form.orgType" :disabled="isTopNode">
                <el-option v-for="item in org_type" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="组织层级" prop="orgLevel">
              <el-select v-model="form.orgLevel" :disabled="isTopNode">
                <el-option v-for="item in org_level" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="行政区划" prop="regionName">
              <el-input v-model="regionName" placeholder="请选择行政区划" disabled>
                <template #append>
                  <el-link @click="proxy.$refs.regionSelect.open()">选择</el-link>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item prop="regionCode" v-show="false">
              <el-input v-model="form.regionCode"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="组织电话" prop="phone">
              <el-input v-model="form.phone" placeholder="请输入组织电话" maxlength="11" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="组织传真" prop="fax">
              <el-input v-model="form.fax" placeholder="请输入组织传真" maxlength="60" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="组织邮箱" prop="email">
              <el-input v-model="form.email" placeholder="请输入组织邮箱" maxlength="50" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="租户">
              <el-input v-model="form.tenantName" placeholder="租户" maxlength="50" disabled />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitLoading">确 定</el-button>
         
        </div>
      </template>
      <dept-select ref="treeSelect" name="" value="" :tenant-id="queryParams.tenantId" @selected="selected" />
    </el-dialog>

    <region-select ref="regionSelect" name="" value="" tenant-id="" @selectedRegion="selectedRegion" />
  </div>
</template>

<script setup name="Dept">
import {
  listDept,
  getDept,
  delDept,
  addDept,
  updateDept,
  upload
} from "@/api/system/dept";
import { getTenants } from "@/api/tenant/tenant";
import { treeselect } from "@/api/system/dept";
import { findByOrgName } from "@/api/system/role";
import DeptSelect from "@/systemViews/system/dept/components/deptSelect";
import TransferTable from "@/components/TransferTable";
import RegionSelect from "@/systemViews/system/region/components/regionSelect";
import { findByRegionCode } from '@/api/system/region';
import { ref } from "vue";
import useUserStore from "@/store/modules/user";
import treeLoad from "@/components/Tree/treeLoad";
const { proxy } = getCurrentInstance();
const { org_type, org_level } = proxy.useDict("org_type", "org_level");

//手机号验证
const checkPhone = (rule, value, callback) => {
  if (!value) {
    callback();
  } else {
    const reg = /^1[3456789]\d{9}$/
    if (reg.test(value)) {
      callback();
    } else {
      return callback(new Error('请输入正确的手机号'))
    }
  }
}
//邮箱验证
const checkEmail = (rule, value, callback) => {
  if (!value) {
    callback()
  } else {
    const reg = /^[a-z0-9]+([._\\-]*[a-z0-9])*@([a-z0-9]+[-a-z0-9]*[a-z0-9]+.){1,63}[a-z0-9]+$/;
    if (reg.test(value)) {
      callback()
    } else {
      return callback(new Error('请输入正确的邮箱'))
    }
  }
}
const isLazy = ref(true);
const treeData = ref([]);
const treeindex = ref(0);
const treeFatherIndex = ref(0);
/** 树结构数据默认 */
const defaultProps = reactive({
  children: "children",
  label: "orgName",
  isLeaf: (data) => !data.isParent,
});
const userStore = useUserStore();

const userType = userStore.userInfo.customParam.userType;
// 遮罩层
const loading = ref(true);
// 显示搜索条件
const showSearch = ref(true);
const submitLoading = ref(false);
const getTenantLoading = ref(false);
// 总条数
const total = ref(0);
// 弹出层标题
const title = ref("");
// 表单参数
const form = ref({});
// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  orgName: undefined,
  orgId: userStore.userInfo.orgId,
  tenantId: userStore.userInfo.customParam.tenantId,
  tenantName: userStore.userInfo.customParam.tenantName
});
// 列信息
const columns = ref([
  { key: 0, label: `组织编号`, visible: true },
  { key: 1, label: `组织名称`, visible: true },
  { key: 2, label: `组织类别`, visible: true },
]);
// 表单校验
const rules = {
  parentName: [
    { required: true, message: "请选择上级组织", trigger: "blur" },
  ],
  orgName: [{ required: true, message: "请输入组织名称", trigger: "blur" }],
  orgType: [
    { required: true, message: "请选择组织类别", trigger: "blur" },
  ],
  phone: [
    {
      validator: checkPhone, trigger: 'blur'
    },
  ],
  email: [
    {
      validator: checkEmail, trigger: 'blur'
    }
  ]
}
const treeLoading = ref(false);
const selectNodeName = ref("全部组织");
const selectNode = ref(undefined);
const getDeptLoading = ref(false);
const loadOrgId = ref(undefined);
const deptList = ref([]);
const tenantList = ref([]);
const open = ref(false);
const defaultOrgId = userStore.userInfo.orgId;
const isTopNode = ref(false);
const isShowFile = ref(true);
const regionName = ref("");

function getTenantList(tenantName) {
  getTenantLoading.value = true;
  let query = {};
  if (tenantName !== undefined && tenantName !== '') {
    query.tenantName = tenantName;
    query.tenantId = undefined;
  } else {
    query.tenantId = queryParams.value.tenantId;
  }
  getTenants(query).then((response) => {
    tenantList.value = response.data;
  }).finally(() => {
    getTenantLoading.value = false;
  });
}

/** 查询用户列表 */
function getList(type) {
  loading.value = true;
  listDept(queryParams.value).then(
    (response) => {
      deptList.value = response.data.records;
      total.value = response.data.total;
      loading.value = false;


      if (deptList.value.length > 0 && type) {
        selectNodeName.value = deptList.value[0].orgName;
      }
    }
  );
}

// 节点单击事件
function handleNodeClick(data) {
  queryParams.value.orgId = data.orgId;
  selectNode.value = data;
  selectNodeName.value = data.orgName;
  queryParams.value.orgName = data.orgName;
  getList();
}

function allUser() {
  // const node = proxy.$refs.asyncTree.root;
  // node.loaded = false;
  // node.expand();
  // queryParams.value.orgId = defaultOrgId;
  // selectNodeName.value = "全部组织";
  // selectNode.value = undefined;
  // getList();

  proxy.resetForm("queryForm");
  queryParams.value.pageNum = 1;
  queryParams.value.orgId = defaultOrgId;
  treeFatherIndex.value++
  filterText.value = ''
  selectNodeName.value = "全部组织";
  selectNode.value = undefined;
  getList();
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    parentName: undefined,
    orgName: undefined,
    parentId: undefined,
    orgType: undefined,
    tenantName: undefined,
    regionCode: undefined,
    orgLevel: undefined
  };
  isTopNode.value = false
  proxy.resetForm("formRef");
  regionName.value = '';
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageSize = 10;
  getList("noFirst");
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryForm");
  queryParams.value.pageNum = 1;
  filterText.value = "";
  selectNode.value = "";
  queryParams.value.orgId = "";
  selectNodeName.value = "全部组织";
  treeData.value = [];
  isLazy.value = true;
  treeFatherIndex.value++;
  getList();
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  if (selectNode.value) {
    form.value = {
      ...form.value,
      parentId: selectNode.value.orgId,
      parentName: selectNodeName.value,
    };
  }
  form.value.tenantName = queryParams.value.tenantName;
  form.value.tenantId = queryParams.value.tenantId;
  open.value = true;
  title.value = "添加组织";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  loadOrgId.value = row.orgId;
  if (row.orgId === defaultOrgId || (queryParams.value.tenantId !== 'system' && row.parentId === null)) {
    isTopNode.value = true
  }
  getDeptLoading.value = true;
  getDept(row.orgId)
    .then((response) => {
      if (response.data.parentId != '' && response.data.parentId != null) {
        getDept(response.data.parentId)
          .then((r) => {
            handleResponse(response, r.data.orgName)
          })
          .catch((e) => {
            getDeptLoading.value = false;
            loadOrgId.value = undefined;
            proxy.$modal.msgError("数据异常！");
          });
      } else {
        handleResponse(response, '已经是根组织,不允许修改')
      }
    })
    .catch((e) => {
      getDeptLoading.value = false;
      loadOrgId.value = undefined;
      proxy.$modal.msgError("数据异常！");
    });
}

function handleResponse(response, parentName) {
  getDeptLoading.value = false;
  loadOrgId.value = undefined;
  if (response.data) {
    form.value = {
      ...form.value,
      ...response.data,
      parentName: parentName,
      parentId: response.data.parentId,
    };
    if (response.data.regionCode) {
      findByRegionCode(response.data.regionCode).then(res => {
        regionName.value = res.data.regionName;
        open.value = true;
      })
    } else {
      open.value = true;
    }
  } else {
    proxy.$modal.msgError("数据异常！");
  }
  title.value = "修改组织";
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      submitLoading.value = true;
      if (form.value.orgId !== undefined) {
        updateDept(form.value).then((response) => {
          submitLoading.value = false;
          if (response.success) {
            proxy.$modal.msgSuccess("修改成功");
            open.value = false;
            reloadTree();
            getList();
          } else {
            proxy.$modal.msgError(response.message);
          }
        });
      } else {
        addDept(form.value).then((response) => {
          submitLoading.value = false;
          if (response.success) {
            proxy.$modal.msgSuccess("新增成功");
            open.value = false;
            reloadTree();
            getList();
          } else {
            proxy.$modal.msgError(response.message);
          }
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const orgName = row.orgName;
  const orgId = row.orgId
  proxy.$modal.confirm('是否确认删除组织名称为"' + orgName + '"的数据项?')
    .then(() => {
      return delDept({ orgId: orgId });
    })
    .then((reponse) => {
      reloadTree();
      getList();
      if (reponse.success === false) {
        // proxy.$modal.msgError(reponse.message)
      } else {
        proxy.$modal.msgSuccess("删除成功");
        reloadTree()
      }
    });
}


const filterText = ref("");
const isSearchFirst = ref(false)
const searchFiler = () => {

  isSearchFirst.value = true;

  if (filterText.value) {
    isLazy.value = false;
    findByOrgNames();
  } else {
    treeData.value = [];
    isLazy.value = true;

    if (selectNode.value) {
      proxy.$refs.asyncTree.reloadTree();
    } else {
      treeindex.value++;
    }
  }
};

//懒加载树形结构的组织
function loadNode(node, resolve) {
  treeLoading.value = true;
  if (node.level === 0) {
    treeselect({
      orgId: defaultOrgId,
      queryType: 'current',
      tenantId: queryParams.value.tenantId,
    }).then((response) => {
      resolve(response.data);
      treeLoading.value = false;
    });
  } else {
    treeselect({
      orgId: node.data.orgId,
      queryType: 'down',
      tenantId: queryParams.value.tenantId,
      orgName: isSearchFirst.value ? filterText.value : '',
    }).then((response) => {
      resolve(response.data);
      treeLoading.value = false;
      isSearchFirst.value = false
    });
  }
}
//懒加载树形结构的组织
function loadNodeRooter(resolve) {
  treeLoading.value = true;
  treeselect({
    orgId: defaultOrgId,
    queryType: "current",
    tenantId: queryParams.value.tenantId,
    orgName: filterText.value,
  }).then((response) => {
    resolve(response.data);
    treeLoading.value = false;
  });
}

function findByOrgNames() {
  treeLoading.value = true;
  findByOrgName({
    tenantId: queryParams.value.tenantId,
    orgName: filterText.value,
  }).then((response) => {
    treeLoading.value = false;
    treeData.value = response.data;
  });
}
// 重新加载树形结构的组织
function reloadTree() {
  if (filterText.value) {
    isLazy.value = false;
    findByOrgNames();
  } else {
    isLazy.value = true
    if (selectNode.value) {
      proxy.$refs.asyncTree.reloadTree();
    } else {
      treeindex.value++;
    }
  }

  // if (filterText.value) {
  //   isLazy.value = false;
  //   findByOrgNames();
  // } else {
  //   treeData.value = [];
  //   isLazy.value = true;

  //   if (selectNode.value) {
  //   proxy.$refs.asyncTree.reloadTree();
  // } else {
  //   treeindex.value++;
  // }
  // }
  // if (selectNode.value) {
  //   const node = proxy.$refs.asyncTree.getNode(selectNode.value);
  //   node.childNodes = [];
  //   node.loaded = false;
  //   node.expand();
  // } else {
  //   proxy.$refs.asyncTree.root.loaded = false;
  //   proxy.$refs.asyncTree.root.expand();
  // }
}

// 组织选择的回调
function selected(data) {
  form.value = {
    ...form.value,
    parentId: data.orgId,
    parentName: data.orgName,
  };
  proxy.$refs.treeSelect.close();
}

function tenantChange(tenantId) {
  if (tenantId !== '') {
    queryParams.value.tenantName = tenantList.value.find(item => item.tenantId === tenantId).tenantName
  }
  proxy.$refs.asyncTree.root.loaded = false;
  proxy.$refs.asyncTree.root.expand();
  selectNode.value = undefined;
  handleQuery();
}

// 导入按钮点击事件
function handleUpload() {
  proxy.$refs.inputer.dispatchEvent(new MouseEvent("click")); // 触发input框的click事件
}

function handleFileChange(e) {
  isShowFile.value = false
  // 触发input选择文件事件
  let inputDOM = proxy.$refs.inputer;
  var file = inputDOM.files[0]; // 通过DOM取文件数据
  var fileName = file.name;

  var formData = new FormData(); // new一个formData事件
  formData.append("multipartFile", file); // 将file属性添加到formData里
  formData.append("orgId", queryParams.value.orgId);
  loading.value = true
  // 使用axios提交到后台
  upload(formData).then((reponse) => {
    isShowFile.value = true
    loading.value = false
    if (reponse.success === false) {
      proxy.$modal.msgError(reponse.message);
    } else {
      proxy.$modal.msgSuccess("导入成功");
      getList();
    }
  });
}

// 区划选择的回调
function selectedRegion(data) {
  form.value = {
    ...form.value,
    regionCode: data.regionCode,
  };
  regionName.value = data.regionName;
  proxy.$refs.regionSelect.close();
}

getTenantList();
getList();
</script>

<style scoped>
.dep-card {
  min-height: calc(100vh - 120px);
}
</style>
