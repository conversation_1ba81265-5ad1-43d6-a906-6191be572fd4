<!-- 供应商管理 -->



<template>
    <div class="container-table-box">
      <Splitpanes class="default-theme">
        <Pane :size="100" :min-size="65">
          <el-card class="dep-card">
            <dialog-search @getList="getList" formRowNumber="4" :columns="tabelForm.columns" :isShowRightBtn="$checkPermi(['pay:provider:list'])">
              <template v-slot:formList>
                <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="100px">
                  <el-form-item label="供应商名称">
                    <el-input v-model="queryParams.providerName" placeholder="请输入供应商名称" clearable></el-input>
                  </el-form-item>
                  <el-form-item label="供应商编码">
                    <el-input v-model="queryParams.providerCode" placeholder="请输入供应商编码" clearable></el-input>
                  </el-form-item>

                  <el-form-item label="供应商简称">
                    <el-input v-model="queryParams.providerShortName" placeholder="请输入供应商简称" clearable></el-input>
                  </el-form-item>
                  <el-form-item label="状态" prop="providerStatus">
                    <el-select v-model="queryParams.providerStatus" placeholder="请选择状态" clearable>
                      <el-option v-for="(item, index) of provider_state" :key="index" :label="item.label"
                        :value="item.value" />
                    </el-select>
                  </el-form-item>

                  <el-form-item label="联系人">
                    <el-input v-model="queryParams.providerContact" placeholder="请输入联系人" clearable></el-input>
                  </el-form-item>

                  <el-form-item label="联系电话">
                    <el-input v-model="queryParams.providerPhone" placeholder="请输入联系电话" clearable></el-input>
                  </el-form-item>

                  <el-form-item label="供应商信息">
                    <el-input v-model="queryParams.providerInfo" placeholder="请输入供应商信息" clearable></el-input>
                  </el-form-item>


                  <el-form-item label="客服电话">
                    <el-input v-model="queryParams.kefuPhone" placeholder="请输入客服电话" clearable></el-input>
                  </el-form-item>

                </el-form>
              </template>
              <template v-slot:searchList>
                <el-button type="primary" icon="Search" @click="handleQuery"  v-hasPermi="['pay:provider:list']">搜索</el-button>
                <el-button icon="Refresh" @click="resetQuery"  v-hasPermi="['pay:provider:list']">重置</el-button>
              </template>
  
              <template v-slot:searchBtnList>
                <el-button type="primary" icon="Plus" size="mini" @click="handleAdd"  v-hasPermi="['pay:provider:add']">新增</el-button>
                <el-button icon="Download" @click="handleExport" v-hasPermi="['pay:provider:export']">导出 </el-button>
              </template>
            </dialog-search>
  
            <public-table ref="publictable" :rowKey="tabelForm.tableKey" :tableData="list" :columns="tabelForm.columns"
              :configFlag="tabelForm.tableConfig" :pageValue="pageParams" :total="total" :getList="getList">
  
              <template #statusType="{ scope }" >
                <el-radio-group v-model="scope.row.providerStatus" @change="radioChange(scope.row)" v-hasPermi="['pay:provider:edit']"> 
                  <el-radio-button :label="item.value" v-for="(item, index) of provider_state"
                    :key="index">{{ item.label }}</el-radio-button>
                </el-radio-group>
              </template>
              
              <template #operation="{ scope }">
                <el-button size="mini" type="text" title="查看" icon="View" @click="handleView(scope.row)"  v-hasPermi="['pay:provider:info']">
                </el-button>
                <el-button size="mini" type="text" title="修改" icon="Edit" @click="handleEdit(scope.row)" v-hasPermi="['pay:provider:edit']">
                </el-button>
  
                <el-button size="mini" type="text" title="删除" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['pay:provider:remove']">
                </el-button>
              </template>
            </public-table>
          </el-card>
        </Pane>
      </Splitpanes>
  
      <DialogBox :visible="open1" :dialogWidth="diaWindow.dialogWidth" :dialogFooterBtn="diaWindow.dialogFooterBtn"
        @save="save" @cancellation="cancellation" :custom-class="diaWindow.customClass" @close="close"
        :dialogTitle="diaWindow.headerTitle">
        <template #content>
          <accountAdd ref="accountAddRef" :rowData="diaWindow.rowData" :popupType="diaWindow.popupType"
            @closeBtn="cancellationRefsh"></accountAdd>
        </template>
      </DialogBox>
    </div>
  </template>
  
  <script setup name="Supplier">
  import { ElMessage, ElMessageBox } from "element-plus";
  import { ref, reactive, getCurrentInstance } from "vue";
  import {
    screenIndex
  } from "@/api/paymentCenter/supplier/index";
  import { apiUrl } from "@/utils/config";
  import { formatMinuteTime } from "@/utils";
  import accountAdd from "./components/add.vue";
  const { proxy } = getCurrentInstance();
  const { provider_state } = proxy.useDict(
    "provider_state",
  );
  
  const accountAddRef = ref(null);
  const open1 = ref(false);
  const diaWindow = reactive({
    headerTitle: "",
    popupType: "",
    rowData: "",
    dialogWidth: "20%",
    dialogFooterBtn: false,
    customClass: ''
  });
  
  const pageParams = ref({
    pageNum: 1,
    pageSize: 10,
  });
  const queryParams = ref({
  
  });
  const list = ref([]);
  const total = ref(0);
  const tabelForm = reactive({
    tableKey: "1", //表格key值
    isShowRightToolbar: true, //是否显示右侧显示和隐藏
    showSearch: true,
    // 表格表格数据
    columns: [
      {
        fieldIndex: "providerName", // 对应列内容的字段名
        label: "供应商名称", // 显示的标题
        resizable: true, // 对应列是否可以通过拖动改变宽度
        visible: true, // 展示与隐藏
        sortable: true, // 对应列是否可以排序
        fixed: "", //固定
        minWidth: "160", //最小宽度%
        width: "", //宽度
        align: "left", //表格对齐方式
      },
      {
        fieldIndex: "providerCode", // 对应列内容的字段名
        label: "供应商编码", // 显示的标题
        resizable: true, // 对应列是否可以通过拖动改变宽度
        visible: true, // 展示与隐藏
        sortable: true, // 对应列是否可以排序
        fixed: "", //固定
        minWidth: "140", //最小宽度%
        width: "", //宽度
        align: "", //表格对齐方式
      },

      {
        fieldIndex: "providerShortName", // 对应列内容的字段名
        label: "供应商简称", // 显示的标题
        resizable: true, // 对应列是否可以通过拖动改变宽度
        visible: true, // 展示与隐藏
        sortable: true, // 对应列是否可以排序
        fixed: "", //固定
        minWidth: "130", //最小宽度%
        align: "left", //表格对齐方式
      },
      {
        fieldIndex: "providerContact", // 对应列内容的字段名
        label: "负责人", // 显示的标题
        resizable: true, // 对应列是否可以通过拖动改变宽度
        visible: true, // 展示与隐藏
        sortable: true, // 对应列是否可以排序
        fixed: "", //固定
        minWidth: "120", //最小宽度%
        width: "", //宽度
      },

      {
        fieldIndex: "providerPhone", // 对应列内容的字段名
        label: "联系电话", // 显示的标题
        resizable: true, // 对应列是否可以通过拖动改变宽度
        visible: true, // 展示与隐藏
        sortable: true, // 对应列是否可以排序
        fixed: "", //固定
        minWidth: "120", //最小宽度%
        width: "", //宽度
        type:'phoneHidden'
      },
      {
        fieldIndex: "providerStatus", // 对应列内容的字段名
        label: "状态", // 显示的标题
        resizable: true, // 对应列是否可以通过拖动改变宽度
        visible: true, // 展示与隐藏
        sortable: true, // 对应列是否可以排序
        fixed: "", //固定
        minWidth: "120", //最小宽度%
        width: "", //宽度
        align: "", //表格对齐方式
        type: "dict",
        dictList: provider_state,
      },
      {
        fieldIndex: "applicableAccounts", // 对应列内容的字段名
        label: "适用账户", // 显示的标题
        resizable: true, // 对应列是否可以通过拖动改变宽度
        visible: true, // 展示与隐藏
        sortable: true, // 对应列是否可以排序
        fixed: "", //固定
        minWidth: "160px", //最小宽度%
        width: "", //宽度
        align:'left'
      },
      {
        fieldIndex: "providerInfo", // 对应列内容的字段名
        label: "供应商信息", // 显示的标题
        resizable: true, // 对应列是否可以通过拖动改变宽度
        visible: true, // 展示与隐藏
        sortable: true, // 对应列是否可以排序
        fixed: "", //固定
        minWidth: "160", //最小宽度%
        width: "", //宽度
        align:'left'
      },
      {
      fieldIndex: "providerAdministratorsName", // 对应列内容的字段名
      label: "供应商管理员", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "250", //最小宽度%
      width: "", //宽度
      align: "left", //表格对齐方式
    },
      {
        fieldIndex: "kefuPhone", // 对应列内容的字段名
        label: "客服电话", // 显示的标题
        resizable: true, // 对应列是否可以通过拖动改变宽度
        visible: true, // 展示与隐藏
        sortable: true, // 对应列是否可以排序
        fixed: "", //固定
        minWidth: "120", //最小宽度%
        width: "", //宽度
        type:'phoneHidden'
      },
      {
        fieldIndex: "unifiedSocialCode", // 对应列内容的字段名
        label: "信用代码", // 显示的标题
        resizable: true, // 对应列是否可以通过拖动改变宽度
        visible: true, // 展示与隐藏
        sortable: true, // 对应列是否可以排序
        fixed: "", //固定
        minWidth: "200", //最小宽度%
        align: "center", //表格对齐方式
      },
      // {
      //   fieldIndex: "createDate", // 对应列内容的字段名
      //   label: "创建时间", // 显示的标题
      //   resizable: true, // 对应列是否可以通过拖动改变宽度
      //   visible: true, // 展示与隐藏
      //   sortable: true, // 对应列是否可以排序
      //   fixed: "", //固定
      //   minWidth: "180", //最小宽度%
      //   width: "", //宽度
      // },


      // {
      //   fieldIndex: "operator", // 对应列内容的字段名
      //   label: "操作人", // 显示的标题
      //   resizable: true, // 对应列是否可以通过拖动改变宽度
      //   visible: true, // 展示与隐藏
      //   sortable: true, // 对应列是否可以排序
      //   fixed: "", //固定
      //   minWidth: "120", //最小宽度%
      //   align:'center',
      //   width: "", //宽度
      // },
      {
      fieldIndex: "providerStatus", // 对应列内容的字段名
      label: "启用/禁用", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "150", //最小宽度%
      width: "", //宽度
      slotname: 'statusType',
      fixed: "right", //固定
    },
      {
        label: "操作",
        slotname: "operation",
        width: "120",
        fixed: "right", //固定
        headerAlign:'center',
        align:'center',
        visible: true,
      },
    ],
    // 表格基础配置项，看个人需求添加
    tableConfig: {
      needPage: true, // 是否需要分页
      index: true, // 是否需要序号
      selection: false, // 是否需要多选
      reserveSelection: false, // 是否需要支持跨页选择
      indexFixed: false, // 序号列固定 left true right
      selectionFixed: false, //多选列固定left true right
      indexWidth: "50", //序号列宽度
      loading: false, //表格loading
      showSummary: false, //是否开启合计
      height: null, //表格固定高度 比如：300px
    },
  });
  
  /** 查询列表 */
  const getList = () => {
    tabelForm.tableConfig.loading = true;
    screenIndex.pageList(queryParams.value, pageParams.value).then((response) => {
      // 替换英文逗号为中文逗号
      response.data.records.forEach(item => {
        if (item.applicableAccounts) {
          item.applicableAccounts = item.applicableAccounts.replace(/,/g, '，');
        }
        if (item.providerAdministratorsName) {
          item.providerAdministratorsName = item.providerAdministratorsName.replace(/,/g, '，');
        }
      });
      list.value = response.data.records;
      total.value = response.data.total;
      tabelForm.tableConfig.loading = false;
    });
  };
  
  /** 搜索按钮操作 */
  const handleQuery = () => {
    pageParams.value.pageNum = 1;
    getList();
  };
  /** 重置按钮操作 */
  const resetQuery = () => {
    proxy.resetForm("queryForm");
    queryParams.value = {
  
    };
    handleQuery();
  };
  
  /** 导出按钮操作 */
  
  const handleExport = () => {
    proxy.download(
      `pay${apiUrl}/pay/payProvider/export`,
      {
        ...queryParams.value,
      },
      `供应商列表_${formatMinuteTime(new Date())}.xlsx`
    );
  };
  /** 删除 */
  const handleDelete = async (row) => {
    try {
      await ElMessageBox.confirm("确认要删除名称为【" + row.providerName +  "】的供应商吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      });
  
      const res = await screenIndex.delete({ id: row.id });
      if(res.success){
           ElMessage.success("删除成功");
        await getList();
      }
    
    } catch (error) {
      console.error("删除失败:", error);
      // 用户取消删除或其他错误
    }
  };
  
  /** 查看 */
  const handleView = (data) => {
    diaWindow.headerTitle = "查看供应商信息";
    diaWindow.popupType = "view";
    diaWindow.rowData = data;
    diaWindow.dialogWidth = "48%";
    diaWindow.dialogFooterBtn = false;
    open1.value = true;
  };
  
  // 新增
  const handleAdd = () => {
    diaWindow.headerTitle = "新增供应商";
    diaWindow.popupType = "add";
    diaWindow.rowData = {}; // 如果需要传递数据到弹窗
    diaWindow.dialogFooterBtn = true;
    diaWindow.dialogWidth = "48%";
    open1.value = true;
  };
  
  // 修改
  const handleEdit = (row) => {
    diaWindow.headerTitle = "修改供应商";
    diaWindow.popupType = "edit";
    diaWindow.rowData = row; // 如果需要传递数据到弹窗
    diaWindow.dialogFooterBtn = true;
    diaWindow.dialogWidth = "48%";
    open1.value = true;
  };
  


  // 状态变更处理
const radioChange = async (row) => {
  let text  = ''
  if(row.providerStatus == '2'){
      text = `确定要禁用供应商名称【 ${row.providerName} 】的状态吗？`
  }else{
       text = `确定要启用供应商名称【 ${row.providerName} 】的状态吗？`
  }
 
  try {
    // 添加确认对话框
    await ElMessageBox.confirm(
      text,
      "操作确认",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }
    );
    const res = await screenIndex.updateState({
      id:row.id,
      providerStatus:row.providerStatus
    });
    if (res.code == "1") {
      ElMessage.success("状态更新成功");
      emit("closeBtn");
    }else{
      getList();
    }
  } catch (error) {
    getList();
  }
};
  /** 点击确定保存 */
  const save = () => {
    if (diaWindow.popupType == "add") {
      accountAddRef.value.saveForm();
    }
  
    if (diaWindow.popupType == "edit") {
      accountAddRef.value.saveForm();
    }
  };
  /** 点击确定后刷新 */
  const cancellationRefsh = () => {
    close(false);
    getList();
  };
  /** 点击取消保存 */
  const cancellation = (val) => {
    close(false);
  };
  
  /** 关闭弹窗 */
  const close = (val) => {
    open1.value = val;
  };
  
  
  
  getList();
  </script>
  
  <style scoped>
  .dep-card {
    min-height: calc(100vh - 160px);
  }
  </style>