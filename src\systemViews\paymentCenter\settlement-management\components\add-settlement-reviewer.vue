<!-- 新增结算审核员 -->

<template>
  <div
    class="dialog-box"
    :class="popupType === 'view' ? 'dialog-box-view' : 'dialog-box-edit'"
  >
    <el-form
      ref="formRef"
      :model="formData"
      label-width="125px"
      :rules="popupType !== 'view' ? rules : ''"
    >
      <el-row>
        <el-col :span="24">
          <el-form-item label="人员选择" prop="staffId">
            <el-select
              v-if="popupType !== 'view'"
              v-model="formData.staffId"
              placeholder="请输入人员进行选择"
              clearable
              @change="changeUser"
              filterable
              remote
              reserve-keyword
              :remote-method="getUserList"
            >
              <el-option
                v-for="(item, index) in administratorsIdsList"
                :key="index"
                :label="item.staffName"
                :value="item.staffId"
              >
                <div>
                  {{
                    item.staffName +
                    "(" +
                    item.orgName +
                    "/" +
                    item.loginName +
                    ")"
                  }}
                </div>
              </el-option>
            </el-select>
            <div v-else>
              {{ formData.strAdministratorsName || "" }}
            </div>
          </el-form-item>
        </el-col>

      </el-row>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, watch, getCurrentInstance, defineProps } from "vue";
import { ElMessage } from "element-plus";
import { screenIndex } from "@/api/paymentCenter/settlement-management/index";
import useUserStore from "@/store/modules/user";
const userStore = useUserStore();
// 定义组件 props
const props = defineProps({
  closeBtn: {
    type: Function,

    default: null,
  },

  popupType: {
    type: String,
    default: "",
  },

  rowData: {
    type: Object,
    default: () => ({}),
  },
});
// 人员选择
const administratorsIdsList = ref([]);
// 供应商列表
const providerList = ref([]);
// 字典
const { proxy } = getCurrentInstance();
const { account_type_status } = proxy.useDict("account_type_status");
// 定义 emits
const emit = defineEmits(["closeBtn"]);
// 定义状态
let formData = ref({
  staffId: "",
  tenantId: userStore.userInfo.tenantId,
});

// 定义校验规则

const rules = reactive({
  staffId: [
    { required: true, message: " 请选择人员", trigger: ["blur", "change"] },
  ],
});

// 表单引用

const formRef = ref(null);

// 定义方法
const getUserList = async (name) => {
  if (!name || name.trim().length < 2) {
    administratorsIdsList.value = []; // 立即清空列表
    return;
  }

  try {
    const res = await screenIndex.selectUserList({
      staffName: name,
    });
    administratorsIdsList.value = res.data;
  } catch (error) {
    console.error("获取用户列表失败:", error);
  }
};

// 选择事件（根据需要补充逻辑）
const changeUser = (selectedIds) => {
  console.log("已选择的ID:", selectedIds);
  // 可以在这里添加选择变化后的处理逻辑
  formData.value.staffId = selectedIds
};
// 供应商列表
const supplierPageList = async () => {
  try {
    const res = await screenIndex.supplierPageList({
      providerStatus:'1'
    });
    providerList.value = res.data.records;
  } catch (error) {
    console.error("获取供应商列表失败:", error);
  }
};
const initData = async () => {
  if (props.popupType === "add") {
  } else {
    formData.value = JSON.parse(JSON.stringify(props.rowData));
    getUserList(props.rowData.staffName)
  }
};

const saveForm = async () => {
  try {
    await formRef.value.validate();

    if (props.popupType === "edit") {
      const requestData = {
      id: formData.value.id,
      staffId: formData.value.staffId,
      tenantId: formData.value.tenantId
    };

      const res = await screenIndex.editAuditor(requestData);

      if (res.code == "1") {
        ElMessage.success("修改成功");

        emit("closeBtn");
      }
    } else if (props.popupType === "add") {
      const res = await screenIndex.addAuditor(formData.value);

      if (res.code == "1") {
        ElMessage.success("新增成功");
        emit("closeBtn");
      }
    }
  } catch (error) {
    console.error("表单校验失败:", error);
  }
};

// 初始化数据

onMounted(() => {
  initData();
});

defineExpose({
  saveForm,
});
</script>

<style scoped lang="scss"></style>
