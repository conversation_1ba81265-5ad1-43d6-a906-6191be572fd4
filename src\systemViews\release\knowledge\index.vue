<template>
  <div class="app-container">
    <el-card style="height: 100%" shadow="never">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
        <el-form-item label="租户" prop="tenantId" v-if="userStore.userInfo.customParam.userType === 'admin'">
          <el-select v-model="queryParams.tenantId" placeholder="请选择租户" style="width: 180px" @change="handleQuery">
            <el-option v-for="item in tenantList" :key="item.tenantId" :label="item.tenantName" :value="item.tenantId"/>
          </el-select>
        </el-form-item>
        <el-form-item label="知识库名称" prop="documentLibName">
          <el-input v-model="queryParams.name" placeholder="请输入知识库名称" clearable style="width: 240px" @keyup.enter="handleQuery"/>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="Plus" v-hasPermi="['sys:release:knowledge:add']" @click="handleAdd">新增</el-button>
        </el-col>
        <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <!--  表格数据展示  -->
      <el-table v-loading="loading" :data="knowledgeList">
        <el-table-column label="知识库名称" align="left" prop="name" :show-overflow-tooltip="true">
          <template #default="scope">
            <router-link
                :to="{path:'/system/notice/knowledgeDetail', query:{documentLibId: scope.row.id, documentLibName: scope.row.name}}"
                :key="scope.row.id"
                class="link-type"><span>{{ scope.row.name }}</span>
            </router-link>
          </template>
        </el-table-column>
        <el-table-column label="页面数量" prop="docNum" :show-overflow-tooltip="true"/>
        <el-table-column label="显示顺序" prop="sort" :show-overflow-tooltip="true"/>
        <el-table-column label="创建人" prop="createUserName" :show-overflow-tooltip="true"/>
        <el-table-column label="创建时间" align="left" prop="createDate" width="300">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createDate) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="300" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button link type="primary" icon="User" v-hasPermi="['sys:release:knowledge:member']"
                       @click="handleMemberManager(scope.row)">
              {{currentLoginUser.staffId === scope.row.createBy ? "成员管理" : "查看成员"}}
            </el-button>
            <el-button link type="primary" icon="Edit" v-hasPermi="['sys:release:knowledge:edit']"
                       @click="handleUpdate(scope.row)" v-if="currentLoginUser.staffId === scope.row.createBy">
              修改
            </el-button>
            <el-button link type="primary" icon="Delete" v-hasPermi="['sys:release:knowledge:remove']"
                       @click="handleDelete(scope.row)" v-if="currentLoginUser.staffId === scope.row.createBy">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize"
                  @pagination="getList"/>

      <!-- 添加或修改知识库对话框 -->
      <el-dialog :title="title" v-model="open" width="500px" append-to-body>
        <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
          <el-form-item label="知识库名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入知识库名称" />
          </el-form-item>
          <el-form-item label="知识库描述" label-width="100px" prop="description">
            <el-input v-model="form.description" type="textarea" placeholder="请输入内容"/>
          </el-form-item>
          <el-form-item label="租户">
            <el-input v-model="tenantName" placeholder="租户" maxlength="50" disabled/>
          </el-form-item>
          <el-form-item label="排序" prop="sort">
            <el-input-number v-model="form.sort" controls-position="right" :min="1"/>
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel" >取 消</el-button>
          </div>
        </template>
      </el-dialog>

      <!--   知识库成员管理 - 列表页   -->
      <el-dialog title="知识库成员管理" v-model="openMember" append-to-body>
        <el-card class="dep-card" shadow="never">
          <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
              <el-button type="primary" plain icon="Plus" @click="handleAddMember"
                         v-if="currentLoginUser.staffId === currentLib.createBy">添加成员
              </el-button>
              <el-button type="danger" plain icon="Delete" @click="handleDelMember" :disabled="rowChecked"
                         v-if="currentLoginUser.staffId === currentLib.createBy">删除成员
              </el-button>
            </el-col>
          </el-row>
          <el-table v-loading="loading" :data="memberList" empty-text="该知识库暂无成员" @selection-change="memberHandleSelectionChange">
            <el-table-column type="selection" width="100" align="center" />
            <el-table-column label="用户编号" align="left" key="staffId" prop="staffId" v-if="false"/>
            <el-table-column label="登录名称" align="left" key="loginName" prop="loginName" v-if="false"/>
            <el-table-column label="用户名称" align="left" key="staffName" prop="staffName" :show-overflow-tooltip="true"/>
            <el-table-column label="手机号码" align="left" key="cellphone" prop="cellphone"/>
            <el-table-column label="邮箱" align="left" key="email" prop="email"/>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width" v-if="currentLoginUser.staffId === currentLib.createBy">
              <template #default="scope">
                <el-button link type="primary" icon="Delete" @click="handleDelMember(scope.row)"
                    v-if="scope.row.staffId !== currentLib.createBy">删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination v-show="totalMember > 0" :total="totalMember" v-model:page="memberQueryParams.pageNum" v-model:limit="memberQueryParams.pageSize"
                      @pagination="getMemberList"/>

        </el-card>
      </el-dialog>

      <!--   知识库成员管理 - 添加成员   -->
      <el-dialog title="成员管理" v-model="openUser" width="1100px" height="800px" append-to-body>
        <template #default>
          <div class="app-container">
            <Splitpanes class="default-theme">
              <Pane :size="15" :min-size="15">
                <el-card class="dep-card">
                  <!-- 组织树 -->
                  <el-tree :props="{label:'orgName',children:'children',isLeaf:(data) => !data.isParent} "
                      :load="loadNode" lazy :expand-on-click-node="false" ref="asyncTree" @node-click="handleNodeClick"
                      :default-expanded-keys="[defaultOrgId]" node-key="orgId" highlight-current>
                  </el-tree>
                </el-card>
              </Pane>
              <Pane :size="85" :min-size="65">
                <el-card class="dep-card">
                  <template #header>
                    <div class="card-header">
                      <span>{{ selectNodeName }}</span>
                      <el-button type="primary" style="float: right; padding: 3px 0" link icon="Refresh" @click="allUser" >全部人员</el-button>
                    </div>
                  </template>
                  <!-- 搜索表单 -->
                  <el-form :model="userQueryParams" ref="userQueryRef" :inline="true" v-show="showSearch" label-width="68px">
                    <el-form-item label="用户名称" prop="staffName">
                      <el-input v-model="userQueryParams.staffName" placeholder="请输入用户名称" clearable
                          style="width: 240px" @keyup.enter="handleQuery"/>
                    </el-form-item>
                    <el-form-item label="邮箱" prop="email">
                      <el-input v-model="userQueryParams.email" placeholder="请输入邮箱" clearable style="width: 240px" @keyup.enter="handleQuery"/>
                    </el-form-item>
                    <el-form-item>
                      <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                      <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                  </el-form>
                  <!-- 人员表格 -->
                  <el-table v-loading="loading" :data="userList" ref="userListRef" @selection-change="handleSelectionChange">
                    <el-table-column type="selection" align="center" width="100"/>
                    <el-table-column label="用户编号" align="left" key="staffId" prop="staffId" v-if="false"/>
                    <el-table-column label="登录名称" align="left" key="loginName" prop="loginName" v-if="false"/>
                    <el-table-column label="用户名称" align="left" key="staffName" prop="staffName" :show-overflow-tooltip="true"/>
                    <el-table-column label="工作组名称" align="left" key="orgName" prop="orgName" :show-overflow-tooltip="true"/>
                    <el-table-column label="手机号码" align="left" key="cellphone" prop="cellphone"/>
                    <el-table-column label="邮箱" align="left" key="email" prop="email"/>
                  </el-table>
                  <pagination v-show="totalUser > 0" :total="totalUser" v-model:page="userQueryParams.pageNum"
                              v-model:limit="userQueryParams.pageSize" @pagination="getUserList"/>
                </el-card>
              </Pane>
            </Splitpanes>
          </div>
        </template>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="memberSubmitForm">确 定</el-button>
            <el-button @click="cancel" >取 消</el-button>
          </div>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup name="Knowledge">
import {getTenants} from "@/api/tenant/tenant";
import useUserStore from "@/store/modules/user";
import {listUser} from "@/api/system/user";
import {
  addDocumentLib,
  addDocumentLibMember,
  deleteDocumentLib,
  deleteDocumentLibMember,
  getDocumentLibMember,
  getDocumentList,
  getMemberPage,
  updateDocumentLib
} from "@/api/release/knowledge";
import {treeselect} from "@/api/system/dept";
import {ref} from "vue";

const { proxy } = getCurrentInstance();
const showSearch = ref(true)
const loading = ref(false)

/** 初始化租户数据 */
const userStore = useUserStore();
const tenantList = ref([]);
const tenantName = ref(userStore.userInfo.customParam.tenantName);
function getTenantList() {
  getTenants().then(res => {
    tenantList.value = res.data;
  }).catch(() => {
    tenantList.value = [];
  })
}
// 获取租户名
function getTenantName() {
  tenantList.value.forEach(item => {
    if (item.tenantId === queryParams.value.tenantId) {
      tenantName.value = item.tenantName;
    }
  })
}
// 当前登录用户
const currentLoginUser = computed(() => {
  return userStore.$state.userInfo
})

/** 知识库表格数据展示 */
const knowledgeList = ref([]) // 知识库列表
const total = ref(0)
const queryRef = ref(null)
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  tenantId: userStore.userInfo.customParam.tenantId,
})
function getList() {
  loading.value = true
  getDocumentList(queryParams.value).then(res => {
    knowledgeList.value = res.data.records;
    total.value = res.data.total;
    loading.value = false;
  })
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getTenantName();
  getUserList();
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  proxy.resetForm("queryRef");
  proxy.resetForm("userQueryRef");
  handleQuery();
}

/** 删除按钮操作 */
const handleDelete = (row) => {
  proxy.$modal.confirm('是否确认删除名称为"' + row.name + '"的知识库？').then(() => {
    deleteDocumentLib(row.id).then(res => {
      if (res.data) {
        proxy.$modal.msgSuccess("删除成功！")
      } else {
        proxy.$modal.msgError("删除失败！")
      }
      getList();
    })
  }).catch(() => {
    proxy.$modal.msg("取消操作");
  })
}

/**
 * 新增 & 修改
 */
const title = ref("")
const open = ref(false)
const formRef = ref(null)
const form = ref({})
const rules = {
  name: [
    { required: true, message: "知识库名称不能为空", trigger: "blur" },
    { min:1, max:40, message: "请输入1到40个字符", trigger: "blur",},
  ],
  description: [
    { required: true, message: '知识库描述不能为空', trigger: 'blur' },
    { min:1, max:60, message:"请输入1到60个字符", trigger:"blur" }
  ]
}

// 表单重置
function reset() {
  form.value = {
    name: undefined,
    description: undefined,
  };
  proxy.resetForm("formRef");
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  open.value = true;
  title.value = "添加知识库";
  form.value.tenantId = queryParams.value.tenantId;
}

/** 修改按钮操作 */
const handleUpdate = (row) => {
  title.value = "修改知识库";
  reset();
  open.value = true;
  form.value = {
    id: row.id,
    name: row.name,
    description: row.description,
    sort: row.sort,
    tenantId: queryParams.value.tenantId
  }
}

/** 提交新增/修改操作 */
const submitForm = () => {
  proxy.$refs["formRef"].validate(valid => {
    if (valid) {
      if (form.value.id !== undefined) {
        updateDocumentLib(form.value).then(res => {
          if (res.success) {
            proxy.$modal.msgSuccess("修改成功！")
            open.value = false;
            getList();
          } else {
            proxy.$modal.msgError(res.message)
          }
        })
      } else {
        addDocumentLib(form.value).then(res => {
          if (res.success) {
            proxy.$modal.msgSuccess("新增成功！")
            open.value = false;
            getList();
          } else {
            proxy.$modal.msgError(res.message)
          }
        })
      }
    }
  })
}

/** 取消按钮操作 */
const cancel = () => {
  open.value = false;
  openMember.value = false;
  openUser.value = false;
  reset();
}

/**
 * 成员管理/查看成员
 */
const openMember = ref(false)
const totalMember = ref(0)
const memberList = ref([]) // 知识库成员列表
const memberQueryParams = ref({
  pageNum: 1,
  pageSize: 10,
  docLibId: undefined,
})
const memberMultipleSelection = ref([]) //知识库成员勾选列表（删除使用）
const rowChecked = ref(false) // 是否选中知识库成员
const currentLib = ref({}) // 当前知识库
const currentDocumentLibId = ref("") // 当前知识库id
const currentMember = ref([]) // 当前成员列表
const multipleSelection = ref([]) // 人员勾选列表（添加成员使用）

/** 成员管理按钮操作 */
const handleMemberManager = (row) => {
  currentLib.value = row;
  currentDocumentLibId.value = row.id
  // 知识库成员列表数据展示
  openMember.value = true
  memberQueryParams.value.docLibId = row.id;
  getMemberList();
  // 获取知识树结构
  getDocumentLibMember({documentLibId: row.id}).then(res => {
    currentMember.value = res.data
    multipleSelection.value = res.data
  })
}

/** 查询知识库成员列表 */
const getMemberList = () => {
  loading.value = true;
  getMemberPage(memberQueryParams.value).then(res => {
    memberList.value = res.data.records;
    if (memberList.value !== null) {
      currentMember.value = memberList.value.map(v => v.staffId);
      multipleSelection.value = memberList.value.map(v => v.staffId);
    }
    totalMember.value = res.data.total;
    loading.value = false;
  })
}

// 成员列表勾选事件
function memberHandleSelectionChange(val) {
  memberMultipleSelection.value = val.map((value, index, array) => {
    return value.staffId;
  })
  rowChecked.value = !val.length;
}

/** 删除成员按钮操作 */
const handleDelMember = (row) => {
  const userIds = row.staffId ? [row.staffId] : memberMultipleSelection.value;
  if (userIds.includes(currentLib.value.createBy)) {
    proxy.$modal.msgError(`不允许删除该知识库创建者：'${currentLib.value.createUserName}'，请重新选择`)
    return;
  }
  proxy.$modal.confirm("是否确认删除当前选中的共" + userIds.length + "个该知识库内成员?").then(() => {
    deleteDocumentLibMember({userIds: userIds, documentLibId: currentDocumentLibId.value}).then(res => {
      proxy.$modal.msgSuccess("删除成功！")
      getMemberList();
    })
  }).catch(() => {
    proxy.$modal.msg("取消操作")
  })
}

/** 添加成员按钮操作 */
const handleAddMember = () => {
  openUser.value = true;
  getUserList();
}

/** 查询人员列表 */
const openUser = ref(false)
const userList = ref([])
const userListRef = ref(null)
const totalUser = ref(0)
const userQueryRef = ref(null)
const userQueryParams = ref({
  pageNum: 1,
  pageSize: 10,
  orgId: userStore.userInfo.orgId,
  staffOrgType: "F",
  tenantId: userStore.userInfo.customParam.tenantId,
  tenantName: userStore.userInfo.customParam.tenantName,
})

const getUserList = () => {
  loading.value = true;
  listUser(userQueryParams.value).then(res => {
    userList.value = res.data.records;
    if (currentMember.value.length > 0 ) {
      nextTick(() => {
        userList.value.forEach(i => {
          if (currentMember.value.indexOf(i.staffId) > -1) {
            proxy.$refs.userListRef.toggleRowSelection(i, true);
          } else {
            proxy.$refs.userListRef.toggleRowSelection(i, false);
          }
        })
      })
    }
    totalUser.value = res.data.total;
    loading.value = false;
  })
}

// 全部人员
const allUser = () => {
  const node = proxy.$refs.asyncTree.root;
  node.loaded = false;
  node.expand();
  userQueryParams.value.orgId = "";
  selectNodeName.value = "全部人员";
  selectNode.value = undefined;
  getUserList();
}

// 用户列表勾选事件
function handleSelectionChange(val) {
  multipleSelection.value = val.map((value, index, array) => {
    return value.staffId;
  });
}

// 提交新增成员
const memberSubmitForm = () => {
  let userIds = Array.from(new Set([...multipleSelection.value, ...currentMember.value]));
  addDocumentLibMember({
    userIds: userIds,
    documentLibId: currentDocumentLibId.value
  }).then(res => {
    if (res.success) {
      proxy.$modal.msgSuccess("添加成员成功！")
      openUser.value = false;
      getMemberList();
    } else {
      proxy.$modal.msgError("添加成员失败！")
    }
  })
}
/**
 * 组织树
 */
const defaultOrgId = ref(userStore.userInfo.orgId);
const asyncTree = ref(null)
const treeLoading = ref(false)
const selectNode = ref(undefined)
const selectNodeName = ref("全部人员");

//懒加载树形结构的组织
function loadNode(node, resolve) {
  treeLoading.value = true;
  if (node.level === 0) {
    treeselect({
      orgId: defaultOrgId.value,
      queryType: 'current',
      tenantId: queryParams.value.tenantId,
    }).then((res) => {
      resolve(res.data);
      treeLoading.value = false;
    });
  } else {
    treeselect({
      orgId: node.data.orgId,
      queryType: 'down',
      tenantId: queryParams.value.tenantId,
    }).then((res) => {
      resolve(res.data);
      treeLoading.value = false;
    });
  }
}

// 节点单击事件
function handleNodeClick(data) {
  userQueryParams.value.orgId = data.orgId;
  selectNode.value = data;
  selectNodeName.value = data.orgName;
  getUserList();
}

getTenantList();
getList();
</script>

