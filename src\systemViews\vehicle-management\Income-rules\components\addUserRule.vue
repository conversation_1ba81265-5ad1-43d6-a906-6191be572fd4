<!-- 新增停车规则组件 -->
<template>
  <div
    class="dialog-box"
    :class="popupType === 'view' ? 'dialog-box-view' : 'dialog-box-edit'"
  >
    <Splitpanes class="default-theme">
      <Pane :size="18" :min-size="10">
        <el-scrollbar style="height:71vh; background: #fff;">
          <el-card class="dep-card">
            <div class="common-box-one" style="margin-bottom: 12px">
              <div class="common-header">
                <div class="common-header-line"></div>
                <div class="common-header-text">基础信息</div>
              </div>
            </div>

            <el-form
              ref="formRef"
              :model="formData"
              label-width="140px"
              :rules="popupType !== 'view' ? rules : ''"
            >
              <el-row>
                <!-- 停车场选择 -->
                <el-col :span="12">
                  <el-form-item label="所属车场" prop="parkingId">
                    <!-- 添加时显示选择器 -->
                    <TreeSelects
                      v-model:parkingId="formData.parkingId"
                      v-model:parkingName="formData.parkingName"
                        v-if="popupType !== 'view'"
                    />
                    <div v-else>
                      {{ formData.parkingName || "" }}
                    </div>
                  </el-form-item>
                </el-col>
                <!-- 规则名称 -->
                <el-col :span="12">
                  <el-form-item label="规则名称" prop="ruleName">
                    <!-- 添加或编辑时显示输入框 -->
                    <el-input
                      v-model="formData.ruleName"
                      :disabled="popupType === 'view'"
                      placeholder="请输入规则名称"
                      clearable
                      v-if="popupType !== 'view'"
                    />
                    <!-- 查看时显示文本 -->
                    <div v-else>
                      {{ formData.ruleName || "" }}
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>

              <!-- 进出类型 -->
              <el-row>
                <el-col :span="12">
                  <el-form-item label="免费时间(分钟)" prop="freeDuration">
                    <el-input-number
                      v-model="formData.freeDuration"
                      v-if="popupType !== 'view'"
                      :disabled="popupType === 'view'"
                      :precision="0"
                      controls-position="right"
                      :formatter="(value) => value.toString()"
                      :min="0"
                    />
                    <div v-else>
                      {{ formData.freeDuration || "" }}
                    </div>
                  </el-form-item>
                </el-col>

                <el-col :span="12">
                  <el-form-item label="收费模式" prop="peopleOrCarCharge">
                    <el-radio-group
                      v-model="formData.peopleOrCarCharge"
                      v-if="popupType !== 'view'"
                    >
                      <el-radio-button
                        :label="item.value"
                        v-for="(item, index) in park_charge_people_car_charge"
                        :key="index"
                        >{{ item.label }}</el-radio-button
                      >
                    </el-radio-group>

                    <div v-else>
                      {{
                        $formatDictLabel(
                          formData.peopleOrCarCharge,
                          park_charge_people_car_charge
                        )
                      }}
                    </div>
                  </el-form-item>
                </el-col>

                <el-col :span="12">
                  <el-form-item label="公休日收费" prop="isHolidaysCharge">
                    <!-- 添加或编辑时显示单选按钮 -->
                    <el-radio-group
                      v-model="formData.isHolidaysCharge"
                      v-if="popupType !== 'view'"
                    >
                      <el-radio-button label="0">是</el-radio-button>
                      <el-radio-button label="1">否</el-radio-button>
                    </el-radio-group>
                    <!-- 查看时显示文本 -->
                    <span class="dialog-text" v-else>
                      {{ formData.isHolidaysCharge == "0" ? "是" : "否" }}
                    </span>
                  </el-form-item>
                </el-col>

                <el-col :span="12">
                  <el-form-item label="是否启用" prop="chargeStatus">
                    <el-radio-group
                      v-model="formData.chargeStatus"
                      v-if="popupType !== 'view'"
                    >
                      <el-radio-button
                        :label="item.value"
                        v-for="(item, index) in park_charge_status"
                        :key="index"
                        >{{ item.label }}</el-radio-button
                      >
                    </el-radio-group>

                    <div v-else>
                      {{
                        $formatDictLabel(
                          formData.chargeStatus,
                          park_charge_status
                        )
                      }}
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
              <!-- 连续停车判断标准 -->
              <el-row>
                <el-col :span="12">
                  <el-form-item
                    label="连续停车判断标准"
                    prop="continuousStopCriteria"
                  >
                    <el-radio-group
                      v-model="formData.continuousStopCriteria"
                      v-if="popupType !== 'view'"
                      @change="handleStopCriteriaChange"
                    >
                      <el-radio
                        v-for="item in continuous_stopCriteria"
                        :key="item.value"
                        :label="item.value"
                      >
                        {{ item.label }}
                      </el-radio>
                    </el-radio-group>
                    <div v-else>
                      {{
                        $formatDictLabel(
                          formData.continuousStopCriteria,
                          continuous_stopCriteria
                        )
                      }}
                    </div>
                  </el-form-item>
                </el-col>

                <!-- 连续停车判断时间 -->
                <el-col
                  :span="12"
                  v-if="formData.continuousStopCriteria === '0'"
                >
                  <el-form-item label="判断时间" prop="continuousStopTime">
                  
                    <el-select
                      v-model="formData.continuousStopTime"
                      v-if="popupType !== 'view'"
                      placeholder="请选择判断时间"
                      @change="validateTime"
                      filterable
                      clearable
                    >
                      <el-option
                        v-for="time in halfHourOptions"
                        :key="time"
                        :label="formatTimeLabel(time)"
                        :value="time"
                      />
                    </el-select>
                    <span class="dialog-text" v-else>
                      {{ formData.continuousStopTime }}
                    </span>
                  </el-form-item>
                </el-col>
              </el-row>

              <div class="common-box-one" style="margin-bottom: 12px">
                <div class="common-header">
                  <div class="common-header-line"></div>
                  <div class="common-header-text">免费时段</div>
                </div>
              </div>

              <el-row>
                <el-col :span="12">
                  <el-form-item label="时间段类型" prop="timePeriodType">
                    <el-radio-group
                      v-model="formData.timePeriodType"
                      @change="handlePeriodTypeChange"
                      v-if="popupType !== 'view'"
                    >
                      <el-radio
                        v-for="item in time_period_type"
                        :key="item.value"
                        :label="item.value"
                      >
                        {{ item.label }}
                      </el-radio>
                    </el-radio-group>

                    <div v-else>
                      {{
                        formatCommonDict(
                          formData.timePeriodType,
                          time_period_type
                        )
                      }}
                    </div>
                  </el-form-item>
                </el-col>

                <!-- 连续停车标志 -->
                <el-col :span="12">
                  <el-form-item label="连续停车标志" prop="continuousStopSign">
                    <el-radio-group
                      v-model="formData.continuousStopSign"
                      v-if="popupType !== 'view'"
                      @change="handleStopCriteriaChange"
                    >
                      <el-radio
                        v-for="item in continuous_stopSign"
                        :key="item.value"
                        :label="item.value"
                      >
                        {{ item.label }}
                      </el-radio>
                    </el-radio-group>
                    <div v-else>
                      {{
                        $formatDictLabel(
                          formData.continuousStopSign,
                          continuous_stopSign
                        )
                      }}
                    </div>
                  </el-form-item>
                </el-col>

                <!-- 修改后的时间范围选择 -->
                <!-- 开始时间 -->
                <el-col
                  :span="12"
                  v-if="
                    formData.timePeriodType == '1' ||
                    formData.timePeriodType == '2'
                  "
                >
                  <el-form-item label="开始时间" prop="startTimePeriod">
                    <el-time-picker
                      v-if="popupType !== 'view'"
                      v-model="formData.startTimePeriod"
                      placeholder="选择开始时间"
                      format="HH:mm:ss"
                      value-format="HH:mm:ss"
                      :disabled="formData.timePeriodType === '0'"
                      @change="validateTime"
                    />

                    <span class="dialog-text" v-else>
                      {{ formData.startTimePeriod }}
                    </span>
                  </el-form-item>
                </el-col>

                <!-- 结束时间 -->
                <el-col
                  :span="12"
                  v-if="
                    formData.timePeriodType == '1' ||
                    formData.timePeriodType == '2'
                  "
                >
                  <el-form-item
                    :label="
                      formData.timePeriodType === '2'
                        ? '次日结束时间'
                        : '结束时间'
                    "
                    prop="endTimePeriod"
                  >
                    <el-time-picker
                      v-model="formData.endTimePeriod"
                      v-if="popupType !== 'view'"
                      :placeholder="
                        formData.timePeriodType === '2'
                          ? '选择次日结束时间'
                          : '选择结束时间'
                      "
                      format="HH:mm:ss"
                      value-format="HH:mm:ss"
                      :disabled="formData.timePeriodType === '0'"
                      @change="validateTime"
                    />

                    <span class="dialog-text" v-else>
                      {{ formData.endTimePeriod }}
                    </span>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>

         

            <el-form
              ref="formRef2"
              :model="formData"
              label-width="120px"
              :rules="popupType !== 'view' ? rules : ''"
            >
              <!-- 加收条件 -->
              <div class="common-box-one" style="margin-bottom: 12px">
                <div class="common-header">
                  <div class="common-header-line"></div>
                  <div class="common-header-text">加收条件</div>
                </div>
              </div>

              <el-row v-if="popupType !== 'view'">
                <el-col :span="24">
                  <el-form-item label="加收条件" prop="rulesDays">
                    <el-button type="primary" icon="Plus" @click="rulesDaysAdd"
                      >新增</el-button
                    >
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col
                  :span="12"
                  v-for="(item, index) in formData.rulesDays"
                  :key="index"
                >
                  <el-form-item :label="labelShow(item, index)">
                    <el-input-number
                      v-model="item.amount"
                      @input="amountInput($event, index)"
                      @blur="amountBlur"
                      :disabled="popupType === 'view'"
                      controls-position="right"
                      :min="0"
                      :precision="0"
                      v-if="popupType !== 'view'"
                      :formatter="(value) => value.toString()"
                      :parser="(value) => parseFloat(value)"
                    />
                    <span v-else>{{ item.amount }}</span>
                    （元）
                    <!--删除-->
                    <el-button
                      type="primary"
                      v-if="
                        index == formData.rulesDays.length - 1 &&
                        popupType !== 'view'
                      "
                      icon="Delete"
                      @click="rulesDaysDelete(index)"
                      >删除</el-button
                    >
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>


               <div class="common-box-one" style="margin-bottom: 12px">
              <div class="common-header">
                <div class="common-header-line"></div>
                <div class="common-header-text">适用人员</div>
              </div>
            </div>
            <el-form
            v-if="popupType !== 'view'"
              ref="formRef3"
              :model="formData"
              label-width="70px"
              :rules="popupType !== 'view' ? rules : ''"
            >
              <el-form-item label="人员" prop="nameList">
                <el-select
                  style="width: 300px"
                  collapse-tags
                  placeholder="请输入人员进行选择"
                  clearable
                  @change="changeUser"
                  filterable
                  remote
                  reserve-keyword
                  :remote-method="getUserList"
                >
                  <el-option
                    v-for="(item, index) in applyUserList"
                    :key="index"
                    :label="item.staffName"
                    :value="item.staffId"
                  >
                    <div>
                      {{
                        item.staffName +
                        "(" +
                        item.orgName +
                        "/" +
                        item.loginName +
                        ")"
                      }}
                    </div>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-form>
            <el-table
              :key="tableKey1"
              class="limit-cell-table"
              :data="userInfo"
              border
            >
              <el-table-column
                type="index"
                label="序号"
                width="80"
                align="center"
                sortable
              />
              <template v-for="item in userColumns" :key="item.prop">
                <el-table-column
                  show-overflow-tooltip
                  :prop="item.prop"
                  align="center"
                  :label="item.label"
                  :min-width="item.minWidth"
                  :resizable="item.resizable"
                >
                  <template #default="scope">
                    <div v-if="item.prop === 'staffName'">
                      {{ scope.row.staffName }}
                    </div>
                    <div v-if="item.prop === 'loginName'">
                      {{ scope.row.loginName }}
                    </div>
                    <div v-if="item.prop === 'orgName'">
                      {{ scope.row.orgName }}
                    </div>
                  </template>
                </el-table-column>
              </template>

              <el-table-column
                label="操作"
                align="center"
                v-if="popupType !== 'view'"
              >
                <template #default="scope">
                  <el-button
                    size="mini"
                    type="text"
                    icon="Delete"
                    @click="handleDelete(scope.row)"
                  />
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-scrollbar>
      </Pane>
    </Splitpanes>

    <!-- <div class="my_tip" v-if="formData.rulesDays.length>0 ">
      <el-icon><Warning /></el-icon>
      <span
        >请注意：【第{{ formData.rulesDays.length  }}天及以上天数】每天收费<span class="red"
          >【{{ formData.rulesDays[formData.rulesDays.length-1].amount }}元】。</span
        ></span
      >
    </div> -->
  </div>
</template>

<script setup>
import { ref, reactive, watch, getCurrentInstance, defineProps } from "vue";
import { ElMessage } from "element-plus";
import { screenIndex } from "@/api/majorParking/Income-rules";

// 定义组件 props
const props = defineProps({
  closeBtn: {
    type: Function,
    default: null,
  },
  popupType: {
    type: String,
    default: "",
  },
  rowData: {
    type: Object,
    default: () => ({}),
  },
});

// 字典
const { proxy } = getCurrentInstance();
const {
  park_charge_people_car_charge,
  car_type,
  time_period_type,
  continuous_stopCriteria,
  continuous_stopSign,
  park_charge_status,
} = proxy.useDict(
  "park_charge_people_car_charge",
  "car_type",
  "time_period_type",
  "continuous_stopCriteria",
  "continuous_stopSign",
  "park_charge_status"
);
const isShowlabel = ref(false);
const lastDataShow = ref(0);
const lastPriceShow = ref(0);
// 定义 emits
const emit = defineEmits(["closeBtn"]);

// 定义状态
const formData = reactive({
  id: "",
  rulesDays: [
    //加收条件
    { chargingRulesId: "", daySort: "1", isLastDay: "", amount: "0" },
  ],
  parkingId: "", //所属车场id
  parkingName: "", //所属车场名称
  ruleName: "", //规则名称
  freeDuration: "", //免费时间(分钟)
  isHolidaysCharge: "0", //节假日是否收费
  timePeriodType: "0",
  startTimePeriod: "",
  endTimePeriod: "",
  chargeStatus: "0",
  continuousStopSign: "0",
});

const tableKey1 = ref(1);
const applyUserList = ref([]);
const userInfo = ref([]);
const userColumns = reactive([
  {
    prop: "staffName",
    label: "人员姓名",
    show: true,
    sortable: true,
    minWidth: "80%",
  },
  {
    prop: "loginName",
    label: "人员账号",
    show: true,
    sortable: true,
    minWidth: "100%",
  },
  {
    prop: "orgName",
    label: "人员部门",
    show: true,
    sortable: true,
    minWidth: "150%",
  },
]);
// 方法
const handleDelete = (row) => {
  userInfo.value = userInfo.value.filter(
    (item) => item.staffId !== row.staffId
  );
  applyUserList.value.push(row);
};

const changeUser = async (staffId) => {
  const selectedUser = applyUserList.value.find(
    (item) => item.staffId === staffId
  );
  if (selectedUser) {
    // 先检查是否已选中
    const isExist = userInfo.value.some(item => item.staffId === staffId);
    if (isExist) {
      ElMessage.warning("该人员已添加，请勿重复选择");
      return;
    }

    // 再校验人员是否存在收费规则
    try {
      const res = await screenIndex.checkRulesUser({
        staffId: staffId,
        rulesId: formData.id
      });

      if (res.data === true) {
        ElMessage.warning("该人员收费规则已存在");
        return;
      }

      // 校验通过后添加人员
      userInfo.value.push(selectedUser);

    } catch (error) {
      console.error("校验人员失败:", error);
    }
  }
};

const getUserList = async (name) => {
  if (!name || name.trim().length < 2) {
    applyUserList.value = []; // 立即清空列表
    return;
  }
  try {
    const res = await screenIndex.selectUserList({
      staffName: name,
    });
    applyUserList.value = res.data;
  } catch (error) {
    console.error("获取用户列表失败:", error);
  }
};

//新增数组formData。rulesDays 加一条数据
const rulesDaysAdd = () => {
  let daySort = formData.rulesDays.length + 1;
  formData.rulesDays.push({
    chargingRulesId: formData.id,
    daySort: daySort,
    isLastDay: "",
    amount: "0",
  });
};

const labelShow = (item, index) => {
  return formData.rulesDays.length - 1 == index
    ? "连续" + item.daySort + "天及以上"
    : "连续" + item.daySort + "天";
};

// 删除数组formData。rulesDays一条数据
const rulesDaysDelete = (index) => {
  formData.rulesDays.splice(index, 1);
};

const parkingList = ref([]);
// 定义校验规则
const rules = reactive({
  parkingId: [{ required: true, message: "请选择所属车场", trigger: "blur" }],
  ruleName: [{ required: true, message: "请输入规则名称", trigger: "blur" }],
  freeDuration: [
    { required: true, message: "请输入免费时间(分钟)", trigger: "blur" },
  ],
  peopleOrCarCharge: [
    { required: true, message: "请选择收费模式", trigger: "blur" },
  ],
  timePeriodType: [
    { required: true, message: "请选择时间段类型", trigger: "change" },
  ],

  // 其他规则保持不变...
  startTimePeriod: [
    {
      required: true,
      message: "请选择开始时间",
      trigger: ["change", "blur"],
    },
  ],
  chargeStatus: [
    {
      required: true,
      message: "请选择是否启用",
      trigger: ["change", "blur"],
    },
  ],
  endTimePeriod: [
    {
      required: true,
      trigger: ["change", "blur"],
      validator: (rule, value, callback) => {
        if (!formData.startTimePeriod || !value) {
          callback(new Error("请选择完整时间范围"));
          return;
        }

        const start = parseTimeToSeconds(formData.startTimePeriod);
        const end = parseTimeToSeconds(value);
        let diff = end - start;

        // 处理跨天时间差
        if (formData.timePeriodType === "2" && end < start) {
          diff += 86400; // 24 * 3600秒
        }

        // showTimeTip.value = diff > 43200; // 12小时
        if (diff <= 0) {
          callback(new Error("结束时间必须晚于开始时间"));
        } else if (diff > 43200) {
          callback(new Error("免费时段不得超过12小时"));
        } else {
          callback();
        }
      },
    },
  ],

  continuousStopCriteria: [
    { required: true, message: "请选择停车判断标准", trigger: "change" },
  ],
  continuousStopTime: [
    {
      required: true,
      message: "请选择判断时间",
      trigger: ["change", "blur"],
    },
  ],
  continuousStopSign: [
    {
      required: true,
      message: "请选择连续停车标志",
      trigger: "change",
    },
  ],
});

// 表单引用
const formRef = ref(null);
// 组件定义
const addPlateRef = ref(null);
// 定义方法

// 修改时间处理逻辑
const handlePeriodTypeChange = (type) => {
  if (type === "1") {
    // 当天模式
    formData.startTimePeriod = "09:00:00";
    formData.endTimePeriod = "18:59:59";
  } else if (type === "2") {
    // 跨天模式
    formData.startTimePeriod = "22:00:00";
    formData.endTimePeriod = "06:00:00";
  }
  validateTime();
};
// 时间处理函数（HH:mm:ss转秒数）
const parseTimeToSeconds = (timeStr) => {
  if (!timeStr) return 0;
  const [hours, minutes, seconds] = timeStr.split(":").map(Number);
  return hours * 3600 + minutes * 60 + seconds;
};
// 新增时间验证方法
const validateTime = () => {
  formRef.value.validateField(["startTimePeriod", "endTimePeriod"]);
};
const handleParkingChange = (value) => {
  const selectedParking = parkingList.value.find((item) => item.id === value);
  if (selectedParking) {
    formData.parkingName = selectedParking.parkingName;
  } else {
    formData.parkingName = ""; // 如果没有找到匹配项，清空 parkingName
  }
};

const lableComtent = (index, item) => {
  return `第${index + 1}天及以上收费 【${item.amount}元】`;
};

const amountInput = (value, e) => {
  lastDataShow.value = ref(e + 1);
  lastPriceShow.value = ref(value);
  isShowlabel.value = true;
};
const amountBlur = (value) => {
  // isShowlabel.value = false;
};

const initData = async () => {
  if (props.popupType === "add") {
    const res = await screenIndex.getParkingRuleId();
    formData.id = res.data;
    formData.rulesDays[0].chargingRulesId = res.data;

    // 新增时设置默认空数组
    applyUserList.value = [];
    userInfo.value = [];
    formData.rulesStaffs = [];
  } else {
    const res = await screenIndex.findParkingRuleById({ id: props.rowData.id });
    const data = res.data || {};

    formData.id = data.id ?? "";
    formData.rulesDays = data.rulesDays ?? [];
    formData.parkingId = data.parkingId ?? "";
    formData.parkingName = data.parkingName ?? "";
    formData.ruleName = data.ruleName ?? "";
    formData.freeDuration = data.freeDuration ?? "";
    formData.isHolidaysCharge = data.isHolidaysCharge ?? "";
    formData.peopleOrCarCharge = data.peopleOrCarCharge ?? "";
    formData.timePeriodType = data.timePeriodType ? data.timePeriodType : "0";

    formData.startTimePeriod = data.startTimePeriod ?? "";

    formData.endTimePeriod = data.endTimePeriod ?? "";

    formData.continuousStopCriteria = data.continuousStopCriteria ?? "";

    formData.continuousStopTime = data.continuousStopTime
      ? data.continuousStopTime.slice(0, 5)
      : "";

    formData.continuousStopSign = data.continuousStopSign
      ? data.continuousStopSign
      : "";
    formData.chargeStatus = data.chargeStatus ? data.chargeStatus : "";

    // 编辑时从接口获取或设置默认值
    userInfo.value = data.rulesStaffs || [];
  }

  const res = await screenIndex.findparkinglot({}, { status: "0" });
  parkingList.value = res.data.records || [];
};

const selectPark = async () => {
  const res = await screenIndex.findparkinglot({}, { status: "0" });
  parkingList.value == res.data.records || [];
};
const formatCommonDict = (value, dict) => {
  if (!value) return "";
  // 统一处理字符串，分割后映射标签
  const values = Array.isArray(value) ? value : value.split(",");
  const labels = values.map((v) => {
    const item = dict.find((item) => item.value === v);
    return item ? item.label : v;
  });
  return labels.join(", ");
};

// 处理判断标准变化
const handleStopCriteriaChange = (value) => {
  if (value !== "0") {
    formData.continuousStopTime = "";
  }
};
const saveForm = async () => {
  try {
    await formRef.value.validate();

    // 从applyUserList获取并格式化人员信息
    const rulesStaffs =  userInfo.value.map((item) => ({
      unionId: item.orgId, // 假设orgId对应unionId
      staffId: item.staffId,
    }));
    const formData1 = {
      ...formData,
      continuousStopTime: formData.continuousStopTime
        ? `${formData.continuousStopTime}:59`
        : null,
      rulesType: "2",
      rulesStaffs, // 添加格式化后的人员信息
    };

    if (props.popupType === "edit") {
      const res = await screenIndex.updateParkingRule(formData1);
      if (res.code == "1") {
        ElMessage.success("修改成功");
        emit("closeBtn");
      }
    } else if (props.popupType === "add") {
      const res = await screenIndex.saveParkingRule(formData1);
      if (res.code == "1") {
        ElMessage.success("新增成功");
        emit("closeBtn");
      }
    }
  } catch (error) {
    console.error("表单校验失败:", error);
  }
};

const halfHourOptions = computed(() => {
  const options = [];
  for (let hour = 0; hour < 24; hour++) {
    for (let minute = 0; minute < 60; minute += 30) {
      const time = `${hour.toString().padStart(2, "0")}:${minute
        .toString()
        .padStart(2, "0")}`;
      options.push(time);
    }
  }
  return options;
});

const formatTimeLabel = (time) => {
  return time; // 可根据需要格式化显示文本
};

/** 弹窗提交保存 */
const save = (val) => {
  addPlateRef.value.submitForm();
};

/** 弹窗点击取消保存 */
const cancellation = (val) => {
  close(false);
};

/** 关闭弹窗 */
const close = (val) => {
  diaWindow.value.open = val;
};

// 初始化数据
onMounted(() => {
  initData();
});
defineExpose({
  saveForm,
});
</script>

<style scoped lang="scss">
.el-icon.wentiLabel {
  position: relative;
  left: -10px;
  top: -10px;
  cursor: pointer;
  color: #c20000;
  font-size: 16px;
}

.my_tip {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  padding-left: 50px;
  margin-top: 20px;
  font-size: 18px;
  color: #c20000;

  .el-icon {
    color: #c20000;
    font-size: 22px;
    margin-right: 8px;
  }

  .red {
    color: #c20000;
  }
}

.tip-text {
  color: #e6a23c;
  margin-left: 10px;
  font-size: 12px;
}

.el-card{
  height: 100%;
}
</style>
