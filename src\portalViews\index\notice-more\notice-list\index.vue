<template>
  <div class="app-container" style="padding: 0; margin: 0; background: #fff">
    <div class="banner-img">
      <img
          src="@/assets/images/newsTendency/banner.png"
          alt="图片加载中..."
      />
    </div>
    <div class="crumb">
      <div class="bread">
        <el-breadcrumb separator="/" class="breadCrumb">
          <i
              class="el-icon-location-outline"
              style="margin-right: 10px; font-size: 20px; color: #c3c3c3"
          ></i>
          <span style="color: #333333">您的位置：</span>
          <el-breadcrumb-item :to="{ path: portalIndex }" replace
          >智慧园区平台
          </el-breadcrumb-item>
          <el-breadcrumb-item
          ><a>{{ title }}</a></el-breadcrumb-item
          >
        </el-breadcrumb>

        <el-form
            :inline="true"
            v-if="userType === 'admin'"
        >
          <el-form-item label="租 户:">
            <el-select
                v-model="queryParams.tenantId"
                style="width: 200px"
                @change="tenantChange"
            >
              <el-option
                  v-for="item in tenantList"
                  :key="item.tenantId"
                  :label="item.tenantName"
                  :value="item.tenantId"
              />
            </el-select>
          </el-form-item>
        </el-form>

        <div class="search-title">
          <el-input
              placeholder="标题搜索"
              v-model="queryParams.noticeTitle"
              clearable
              style="width: 250px"
              @keyup.enter.native="getList"
              @clear="getList"
          >
            <template #suffix>
              <el-icon class="el-input__icon" @click="getList"><search /></el-icon>
            </template>
          </el-input>
        </div>

        <div class="filter">
          <span>类型：</span>
          <el-radio-group v-model="queryParams.noticeType" @change="change">
            <el-radio
                label="all"
                key="all"
            >全部</el-radio>
            <el-radio
                v-for="item in notice_type_list"
                :label="item.value"
                :key="item.value"
            >{{ item.label }}</el-radio>

          </el-radio-group>
        </div>
      </div>
    </div>

    <el-card class="note-card" shadow="never" v-loading="loading">
      <div slot="header" class="card-title"></div>
      <div class="home-card-body">
        <router-link
            v-for="item in list"
            :key="item.noticeId"
            :to="`${proxy.portalIndex}/detail/${item.programaType}/${item.noticeId}`"
            target="_blank"
        >
          <div class="notice">
            <div class="noticeContent">
              <div class="imgs" v-if="item.coverUrl">
                <img :src="item.coverUrl" alt="" />
              </div>
              <div class="noimgs" v-if="!item.coverUrl">
                <img src="@/assets/images/pageDesign/case/nopicture.png" alt="" />
              </div>
              <div class="contentFont">
                <div class="notice-title">
                  <div>{{ item.noticeTitle }}</div>
                </div>
                <div class="content" v-html="item.noticeContentText">

                </div>
              </div>
              <div class="notice-time">
                {{ parseTime(item.createDate, "{y}-{m}-{d}") }}
              </div>
            </div>
          </div>
        </router-link>
        <el-empty
            style="padding: 20px 0"
            v-if="list.length <= 0"
            :image-size="30"
            :description="`暂无${title}`"
        >
        </el-empty>
      </div>
      <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
      />
    </el-card>
  </div>
</template>

<script setup name="noticeList">
import useUserStore from "@/store/modules/user";
const userStore = useUserStore();
const userType = userStore.userInfo.customParam.userType;
const tenantId = userStore.userInfo.customParam.tenantId;
const {proxy} = getCurrentInstance();
const portalIndex = ref(proxy.portalIndex);
const list =ref([])
const total =ref(0)
const queryParams=ref({
  pageNum: 1,
  pageSize: 10,
  programaType: undefined,
  noticeTitle: undefined,
  noticeType: "all",
  showType: 2,
  tenantId: tenantId,
})
const loading=ref(false)
const title=ref('')
const tenantList=ref([])

import { selectAllForPage } from "@/api/release/notice";
import { list as listTenantList } from "@/api/tenant/tenant";
import {onUpdated} from "vue";
const { notice_type_list } = proxy.useDict("notice_type_list");

function  getList() {
  loading.value=true
  selectAllForPage({
    ...queryParams.value,
    noticeType: queryParams.value.noticeType=== "all" ? undefined : queryParams.value.noticeType
  }).then((response)=>{
    list.value=response.data.records
    for (var i = 0; i < list.value.length; i++) {
      list.value[i].createDate = list.value[i].createDate.substr(0, 10);
    }
    total.value=response.data.total
    loading.value=false
  })
}
function change(value){
  queryParams.value.noticeType =value
  getList()
}
function getTenantList(){
  listTenantList().then((response)=>{
    tenantList.value = response.data
  })
}
function tenantChange() {
  queryParams.value.pageNum = 1;
  getList()
}
queryParams.value.programaType = proxy.$route.params.type;
title.value = queryParams.value.programaType=== "business_news" ? "业务动态" : "通知公告";

getList()
getTenantList()

</script>

<style scoped lang="scss">

.note-card {
  max-width: 1443px;
  min-height: calc(100vh - 150px);
  border: 0px;
  margin: 0 auto;

  :deep(.el-card__body){
    margin: 0;
    padding: 0;
  }

  .card-title {
    .el-card__header {
      border: 0px;
    }
    .filter {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;

      > span {
        margin-left: 10px;
      }
    }

    .c-r-t {
      display: flex;
      flex-direction: row;
      align-items: center;
    }

    .card-title-left {
      display: flex;
      flex-direction: row;
      align-items: center;

      .icon {
        font-size: 24px;
        color: #419eee;
        padding: 0px 5px;
        -webkit-transition: font-size 0.25s linear, width 0.25s linear;
        -moz-transition: font-size 0.25s linear, width 0.25s linear;
        transition: font-size 0.25s linear, width 0.25s linear;
      }
    }
  }

  .home-card-body {
    display: flex;
    flex-direction: column;

    a {
      width: 100%;
      font-size: 14px;
      font-weight: 500;
      color: #606266;

      //:hover {
      //  color: $--color-primary;
      //}

      .notice {
        padding: 20px 0px 20px 0px;
        width: 100%;
        height: 198px;
        position: relative;
        border-bottom: 2px solid #e7e7e7;

        .noticeContent {
          height: 100%;
          display: -webkit-box;
          overflow: hidden;
          text-overflow: ellipsis;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
          display: flex;
          justify-content: space-between;

          .contentFont {
            width: 62%;
            padding: 10px 0 10px 0;

            .notice-title {
              overflow: hidden;
              text-overflow: ellipsis;
              word-break: break-all;
              white-space: nowrap;
              flex-basis: 70%;
              margin-bottom: 15px;
              font-size: 18px;
              font-family: Microsoft YaHei;
              font-weight: 400;
              color: #4c86e3;

              .content {
                width: 70%;
                display: inline;
                font-size: 14px;
                font-weight: 400;
                color: #999999;
                text-overflow: ellipsis;
                word-break: break-all;
                white-space: nowrap;
              }

              .content {
                :deep(span) {
                  color: red;
                }
              }
            }

            .content {
              //text-overflow: -o-ellipsis-lastline;
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              line-clamp: 2;
              -webkit-box-orient: vertical;
              width: 100%;

              :deep(span) {
                color: red;
              }
            }
          }

          .noimgs {
            width: 23%;
            height: 100%;
            margin-right: 10px;
            img {
              width: 100%;
              height: 100%;
            }
          }

          .imgs {
            width: 23%;
            height: 100%;
            margin-right: 10px;

            img {
              width: 100%;
              height: 100%;
            }
          }

          .notice-time {
            margin-top: 10px;
            width: 10%;
            text-align: center;
          }
        }

        .more {
          height: 38px;
          position: absolute;
          bottom: 40px;
          right: 20px;
          background: #4c86e3;
          border: 1px solid #4c86e3;
          color: #fff;
        }
      }
    }
  }
}
//新闻动态通知公告列表
.app-container {
  .banner-img {
    width: 100%;
    height: 380px;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .crumb {
    width: 100%;
    display: flex;
    border: 2px solid #e7e7e7;
    height: 68px;

    .bread {
      width: 1443px;
      margin: 0 auto;
      height: 68px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      :deep(.el-form--inline .el-form-item) {
        margin: 0 auto;
      }

      .breadCrumb {
        width: 30%;
        height: 68px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        font-size: 16px;
      }

      .search-title {
        :deep(.el-input__inner) {
          border-radius: 20px;
          position: relative;
        }

        .myButton {
          position: absolute;
          right: 0;
          border-radius: 20px;
        }
      }
    }
  }
}
</style>
