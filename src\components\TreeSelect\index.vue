<!-- TreeSelect.vue -->
<template>
    <el-select
      filterable
      :filter-method="dataFilter"
      popper-class="opperView"
      :popper-append-to-body="true"
      class="main-select-tree"
      ref="selectTreeRef"
      clearable
      v-model="selectedParkingId"
      placeholder="请选择停车场"
    >
      <el-option
        :label="selectedParkingName"
        :value="selectedParkingId"
        style="display: none"
      />
      <el-tree
        :check-strictly="true"
        :data="areaTreeData"
        :filter-node-method="filterNode"
        @node-click="handleNodeClick"
        default-expand-all
        node-key="id"
        ref="areaTreeRef"
        highlight-current
        :props="treeProps"
      />
    </el-select>
  </template>
  
  <script setup>
  import { ref, watch, computed } from 'vue'
  import { ElMessage } from 'element-plus'
    // 接口方法
  import { screenIndex  } from '@/api/majorParking/Income-rules'
  import { findparkingTree  } from '@/api/park/record'
  
  
  // 定义 props
  const props = defineProps({
    popupType: {
      type: String,
      default: 'edit'
    },
    parkingId: {
      type: [String, Number],
      required: true
    },
    parkingName: {
      type: String,
      required: false // 设为可选
    },
    isRecord:{
        type:Boolean,
        default:false
    }
  })
  
  // 定义 emits
  const emit = defineEmits(['update:parkingId', 'update:parkingName'])
  
  // 树形数据
  const areaTreeData = ref([])
  
  // 组件引用
  const selectTreeRef = ref(null)
  const areaTreeRef = ref(null)
  
  // 当前选中的停车场名称（本地状态）
  const selectedParkingName = ref(props.parkingName || '')
  
  // 计算属性：用于 v-model 绑定 parkingId
  const selectedParkingId = computed({
    get: () => props.parkingId,
    set: (value) => emit('update:parkingId', value)
  })
  
  // 初始化加载树数据
  watchEffect(async () => {
    try {
      const res = await (props.isRecord ? findparkingTree() : screenIndex.findparkinglotTree());
      areaTreeData.value = res.data
    } catch (error) {
      console.error('加载树数据失败:', error)
      ElMessage.error('加载树数据失败')
    }
  })
  
  // 过滤方法
  const dataFilter = (val) => {
    if (val && areaTreeRef.value) {
      areaTreeRef.value.filter(val)
    }
  }
  
  const filterNode = (value, data) => {
    if (!value) return true
    return data.label.indexOf(value) !== -1
  }
  
  // 节点点击处理
  const handleNodeClick = (data) => {
    if (data.type !== 'parking') {
      ElMessage.error('请选择停车场！')
      return
    }
  
    // 更新选中的停车场 ID
    selectedParkingId.value = data.id
  
    // 更新本地状态中的停车场名称
    selectedParkingName.value = data.label
  
    // 如果父组件需要 parkingName，则更新
    if (props.parkingName !== undefined) {
      emit('update:parkingName', data.label)
    }
  
    // 关闭下拉框
    selectTreeRef.value?.blur()
  }
  </script>
  
  <style scoped>
  .main-select-tree {
    width: 100%;
  }
  </style>