<!-- 补贴补扣 -->
<template>
  <div class="container-table-box">
    <Splitpanes class="default-theme">
      <Pane :size="100" :min-size="65">
        <el-card class="dep-card">
          <!-- 搜索区域 -->
          <dialog-search @getList="getList" formRowNumber="4" :columns="tabelForm.columns"
            :isShowRightBtn="$checkPermi(['pay:deduction:list'])">
            <template v-slot:formList>
              <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="90px">
                <el-form-item label="模糊搜索">
                  <el-input v-model="queryParams.staffName" placeholder="请输入人员姓名、账号进行搜索" clearable />
                </el-form-item>
                <el-form-item label="账户名称">
                  <el-input v-model="queryParams.accountName" placeholder="请输入账户名称" clearable />
                </el-form-item>
                <el-form-item label="支付场景">
                  <el-select v-model="queryParams.sceneId" placeholder="请选择支付场景" clearable
                    @change="handlePayTypeChange">
                    <el-option v-for="item in largeCategoryOptions" :key="item.id" :label="item.label"
                      :value="item.id" />
                  </el-select>
                </el-form-item>
                <el-form-item label="支付类型">
                  <el-select v-model="queryParams.typeId" placeholder="请选择支付类型" clearable>
                    <el-option v-for="item in orderSubclassOptions" :key="item.id" :label="item.label"
                      :value="item.id" />
                  </el-select>
                </el-form-item>
                <el-form-item label="支付时间">
                  <el-date-picker
                    v-model="dateRange.paymentTime"
                    type="datetimerange"
                    range-separator="至"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    @change="handlePaymentTimeChange"
                  />
                </el-form-item>
                <el-form-item label="订单日期">
                  <el-date-picker
                    v-model="dateRange.orderDate"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    value-format="YYYY-MM-DD"
                    @change="handleOrderDateChange"
                  />
                </el-form-item>
              </el-form>
            </template>

            <template v-slot:searchList>
              <el-button type="primary" icon="Search" @click="handleQuery"
                v-hasPermi="['pay:deduction:list']">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery" v-hasPermi="['pay:deduction:list']">重置</el-button>
            </template>

            <template v-slot:searchBtnList>
              <el-button type="primary" icon="Plus" @click="handleAdd" v-hasPermi="['pay:deduction:add']">新增</el-button>
              <el-button size="mini" icon="Download" @click="handleExport"
                  v-hasPermi="['pay:deduction:export']">导出</el-button
                >
            </template>
          </dialog-search>

          <!-- 表格区域 -->
          <public-table ref="publictable" :rowKey="tabelForm.tableKey" :tableData="list" :columns="tabelForm.columns"
            :configFlag="tabelForm.tableConfig" :pageValue="pageParams" :total="total" :getList="getList" @clickTextDetail="clickTextDetail">
            <template #operation="{ scope }">
              <!-- <el-button type="text" title="查看" v-hasPermi="['pay:settle:info']" icon="View"
                @click="handleView(scope.row)"></el-button> -->
              <el-button text title="退款" type="danger" v-if="(scope.row.paymentStatus == 'success' && scope.row.tradeType == '0' )|| (scope.row.paymentStatus == 'failed' && scope.row.tradeType == '1' )"
                @click="handleRefund(scope.row)" v-hasPermi="['pay:deduction:refund']">
              <i class="icon icon-tuikuan iconfont" style="font-size: 26px!important;"></i>
              </el-button>
            </template>
          </public-table>
        </el-card>
      </Pane>
    </Splitpanes>

    <DialogBox :visible="diaWindow.open1" :dialogWidth="diaWindow.dialogWidth"
      :dialogFooterBtn="diaWindow.dialogFooterBtn" @save="save" @cancellation="cancellation"
      :custom-class="diaWindow.customClass" @close="close" :dialogTitle="diaWindow.headerTitle"
      :dialogTop="diaWindow.dialogTop">
      <template #content>
        <accountAdd  v-if="diaWindow.popupType != 'faceInfo'"  ref="accountAddRef" :rowData="diaWindow.rowData" :popupType="diaWindow.popupType"
          @closeBtn="cancellationRefsh"></accountAdd>
            <faceInfo v-if="diaWindow.popupType == 'faceInfo'"   :rowData="diaWindow.rowData" ></faceInfo>
      </template>
    </DialogBox>
  </div>
</template>

<script setup>
import { screenIndex } from "@/api/paymentCenter/subsidy-deduction/index";
import { ref, reactive, getCurrentInstance, onMounted, watch } from "vue";
import { formatMinuteTime } from "@/utils";
import { apiUrl } from "@/utils/config";
import accountAdd from "./components/add.vue";
import { ElMessageBox, ElMessage } from 'element-plus';
const { proxy } = getCurrentInstance();
const {
  payment_status,
  trade_type,
} = proxy.useDict(

  "payment_status",
  "trade_type"
);
const accountAddRef = ref(null);
const diaWindow = reactive({
  open1: false,
  headerTitle: "",
  popupType: "",
  rowData: "",
  dialogWidth: "20%",
  dialogFooterBtn: false,
  customClass: "",
  dialogTop: '12%'
});
// 支付大类选项
const largeCategoryOptions = ref([
]);

// 支付小类选项
const orderSubclassOptions = ref([
]);

// 供应商选项
const providerOptions = ref([]);

// 表格配置
const tabelForm = reactive({
  tableKey: "orderId",
  columns: [
    {
      fieldIndex: "staffName",
      label: "员工姓名",
      minWidth: 120,
      sortable: true,
      visible: true,
        type:'clickText'
    },
    {
      fieldIndex: "accountName",
      label: "账户名称",
      minWidth: 140,
      sortable: true,
      visible: true,
      align: "left",
    },
    {
      fieldIndex: "sceneName",
      label: "支付场景",
      minWidth: 120,
      sortable: true,
      type: "tag",
      visible: true,
      align: "left",
    },
    {
      fieldIndex: "typeName",
      label: "支付类型",
      minWidth: 120,
      sortable: true,
      type: "tag",
      visible: true,
    },
    {
      fieldIndex: "payAmount",
      label: "实付金额(元)",
      minWidth: 140,
      sortable: true,
      visible: true,
      type:'dollor'
    },
    {
      fieldIndex: "orderAmount",
      label: "订单金额(元)",
      minWidth: 140,
      sortable: true,
      visible: true,
      type:'dollor'
    },
    {
      fieldIndex: "paymentTime",
      label: "支付时间",
      minWidth: 180,
      sortable: true,
      visible: true,
    },
    {
      fieldIndex: "orderDate",
      label: "订单日期",
      minWidth: 180,
      sortable: true,
      visible: true,
    },
    {
      fieldIndex: "tradeType",
      label: "订单类型",
      minWidth: 120,
      sortable: true,
      type: "dict",
      dictList: trade_type,
      visible: true,
    },
    {
      fieldIndex: "paymentStatus",
      label: "订单状态",
      minWidth: 120,
      sortable: true,
      type: "dict",
      dictList: payment_status,
      visible: true,
    },
    {
      fieldIndex: "payOrderCode",
      label: "订单编号",
      minWidth: 250,
      sortable: true,
      visible: true,
    },
    {
      fieldIndex: "providerName",
      label: "供应商名称",
      minWidth: 150,
      sortable: true,
      visible: true,
      align: "left",
    },
    {
      fieldIndex: "remark",
      label: "订单备注",
      minWidth: 150,
      sortable: true,
      visible: true,
      align: "left",
    },
    {
      label: "操作",
      slotname: "operation",
      minWidth: 120,
      fixed: "right",
      visible: true,
      slot: true,
    },
  ],
  tableConfig: {
    needPage: true,
    index: true,
    selection: false,
    indexWidth: "60",
    loading: false,
    height: null,

  },
});

// 数据列表
const list = ref([]);

// 查询参数
const queryParams = ref({});
const pageParams = ref({
  pageNum: 1,
  pageSize: 10,
});
const total = ref(1);

// 日期范围
const dateRange = ref({
  paymentTime: [],
  orderDate: []
});

// 处理支付时间变化
const handlePaymentTimeChange = (val) => {
  if (val) {
    queryParams.value.payStartTime = val[0];
    queryParams.value.payEndTime = val[1];
  } else {
    queryParams.value.payStartTime = undefined;
    queryParams.value.payEndTime = undefined;
  }
};

// 处理订单日期变化
const handleOrderDateChange = (val) => {
  if (val) {
    queryParams.value.orderStartDate = val[0];
    queryParams.value.orderEndDate = val[1];
  } else {
    queryParams.value.orderStartDate = undefined;
    queryParams.value.orderEndDate = undefined;
  }
};

// 处理支付场景变化
const handlePayTypeChange = (payType) => {
  // 清空支付类型选择
  queryParams.value.typeId = '';
  // 如果选择了支付场景，加载对应的支付类型
  if (payType) {
    payTypeTree(payType);
  } else {
    // 如果清空支付场景，也清空支付类型选项
    orderSubclassOptions.value = [];
    //重新查询支付类型
    payTypeTree();
  }
};

// 查询支付场景树
const paySceneTree = async () => {
  try {
    const res = await screenIndex.paySceneTree({});

    largeCategoryOptions.value = res.data;
  } catch (error) {
    console.error('获取支付类型树失败:', error);
  }
};

// 支付类型
const payTypeTree = async (payType = '') => {
  try {
    const res = await screenIndex.payTypeTree({
      "id": payType, // 传递支付场景值
    });

    orderSubclassOptions.value = res.data;
  } catch (error) {
    console.error('获取支付类型树失败:', error);
  }
};

// 获取供应商列表
const getProviderList = async () => {
  try {
    const res = await screenIndex.providerTree({});
    providerOptions.value = res.data;
  } catch (error) {
    console.error('获取供应商列表失败:', error);
  }
};


// 方法
const handleQuery = () => {
  pageParams.value.pageNum = 1;
  getList();
};

const resetQuery = () => {
  queryParams.value = {};
  dateRange.value.paymentTime = [];
  dateRange.value.orderDate = [];
  largeCategoryOptions.value = [];
  orderSubclassOptions.value = [];
  handleQuery();
};

/** 查看 */
const handleView = (data) => {
  diaWindow.headerTitle = "查看补贴补扣";
  diaWindow.popupType = "view";
  diaWindow.rowData = data;
  diaWindow.dialogWidth = "52%";
  diaWindow.dialogFooterBtn = false;
  diaWindow.open1 = true;
};

// 新增
const handleAdd = () => {
  diaWindow.headerTitle = "新增补贴补扣";
  diaWindow.popupType = "add";
  diaWindow.rowData = {}; // 如果需要传递数据到弹窗
  diaWindow.dialogFooterBtn = false;
  diaWindow.dialogWidth = "55%";
  diaWindow.open1 = true;
};
// 人员信息
const clickTextDetail = (row) => {
  diaWindow.headerTitle = "人员信息";
  diaWindow.popupType = "faceInfo";
  diaWindow.rowData = row; // 如果需要传递数据到弹窗
  diaWindow.dialogFooterBtn = false;
  diaWindow.dialogWidth = "530px";
  diaWindow.dialogTop = '18vh'

  diaWindow.open1 = true;
};
// 退款
const handleRefund = (row) => {
  ElMessageBox.confirm("确认要对此订单进行退款操作吗？", "退款提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    // 调用退款API
    screenIndex.refundAmount({ payOrderCode: row.payOrderCode }).then(res => {
      if (res.code == "1") {
        ElMessage.success("退款申请成功");
        getList();
      } else {
        ElMessage.error(res.msg || "退款失败");
      }
    }).catch(error => {
      console.error("退款失败:", error);
      ElMessage.error("退款失败");
    });
  }).catch(() => {
    // 取消退款
  });
};

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm("确认要删除吗？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });

    const res = await screenIndex.delete({ id: row.id });
    if (res.code == "1") {
      ElMessage.success("删除成功");
      await getList();
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    console.error("删除失败:", error);
    // 用户取消删除或其他错误
  }

};

const handleExport = () => {
  proxy.download(
    `pay${apiUrl}/pay/subsidy/deduction/export`,
    {
      ...queryParams.value,
    },
    `补贴补扣列表_${formatMinuteTime(new Date())}.xlsx`
  );
};



/** 点击确定保存 */
const save = () => {
  if (diaWindow.popupType == "add") {
    accountAddRef.value.saveForm();
  }

  if (diaWindow.popupType == "edit") {
    accountAddRef.value.saveForm();
  }
};
/** 点击确定后刷新 */
const cancellationRefsh = () => {
  close(false);
  getList();
};
/** 点击取消保存 */
const cancellation = (val) => {
  getList();
  close(false);
};

/** 关闭弹窗 */
const close = (val) => {
  getList();
  diaWindow.open1 = val;
};


const getList = () => {
  tabelForm.tableConfig.loading = true;
  screenIndex.subsidyDeductionList(queryParams.value, pageParams.value).then((response) => {
    list.value = response.data.records;
    total.value = response.data.total;
    tabelForm.tableConfig.loading = false;
  });
};
onMounted(() => {
  paySceneTree();
  payTypeTree();
  getList();
});
</script>
