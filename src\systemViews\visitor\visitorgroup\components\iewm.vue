<template>
  <div class="el-common-container">
    <div class="receive-voucher">
      <div class="receive-voucher-box">
        <div class="receive-voucher-center">
          <!-- 二维码展示区域 -->
          <div class="receive-center-qr">
            <img class="qrImg" :src="qrBase64" v-if="qrBase64" />
          </div>
          <!-- 操作按钮区域 -->
          <div class="receive-center-qr">
            <el-row>
              <el-col :span="24">
                <el-button type="warning" icon="Refresh" size="mini" class="el-refresh-btn" @click="fetchQrCode">
                  重置
                </el-button>
                <el-button type="primary" icon="download" size="mini" class="el-refresh-btn" @click="downloadQrCode">
                  保存
                </el-button>
              </el-col>
            </el-row>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { findVistorGroupQr } from '@/api/visitor/visitorgroup/index'

// 定义 props
const props = defineProps({
  rowData: {
    type: Object,
    default: () => ({})
  }
})

// 响应式数据
const qrBase64 = ref('')

// 初始化逻辑
onMounted(() => {
  props.rowData.isAccompany = '1'
  props.rowData.inviteType = '2'
  fetchQrCode()
})

// 获取二维码
const fetchQrCode = async () => {
  try {
    const res = await findVistorGroupQr(props.rowData)
    qrBase64.value = `data:image/png;base64,${res.data}`
  } catch (error) {
    console.error('获取二维码失败:', error)
    ElMessage.error('二维码生成失败，请重试')
  }
}

// 下载二维码
const downloadQrCode = () => {
  if (!qrBase64.value) {
    ElMessage.warning('请先生成二维码')
    return
  }
  const downloadLink = document.createElement('a')
  downloadLink.href = qrBase64.value
  downloadLink.download = '邀约码.jpg' // 下载的文件名
  downloadLink.click()
}
</script>

<style scoped lang="scss">
.el-common-container {
  height: 100%;
  width: 100%;
}

.receive-voucher {
  height: 100%;
  width: 100%;

  .receive-voucher-box {
    width: 100%;
    box-sizing: border-box;

    .receive-voucher-center {
      width: 100%;
      border-radius: 8px;
      background: #ffffff;
      overflow: hidden;
      box-shadow: 0px 0px 14px 1px rgba(0, 0, 0, 0.02);
    }

    .receive-center-qr {
      width: 100%;
      text-align: center;
      padding: 5px 0 10px 0px;

      .qrImg {
        width: 470px;
        display: inline-block;
      }
    }
  }
}

.el-row {
  margin-bottom: 10px;
}


</style>