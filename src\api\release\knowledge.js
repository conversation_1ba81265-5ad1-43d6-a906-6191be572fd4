import request from "@/utils/request";

// 查询文档库列表
export function getDocumentList(query) {
    return request({
        url: "/cms/v1/document/lib/page",
        method: "get",
        params: query
    });
}

//添加文档库
export function addDocumentLib(data) {
    return request({
        url: "/cms/v1/document/lib/create",
        method: "post",
        data
    });
}

//编辑文档库
export function updateDocumentLib(data) {
    return request({
        url: "/cms/v1/document/lib/update",
        method: "post",
        data
    });
}

//删除文档库
export function deleteDocumentLib(data) {
    return request({
        url: "/cms/v1/document/lib/delete",
        method: "post",
        data
    });
}

/*------------------------documents文档相关接口--------------------------*/

// 查询文档树结构
export function documentsTree(data) {
    return request({
        url: "/cms/v1/document/lib/doc/documentsTree",
        method: "post",
        data
    });
}

// 查询文档树结构
export function getDocumentLibMember(data) {
    return request({
        url: "/cms/v1/document/lib/member/getDocumentLibMember",
        method: "post",
        data
    });
}

export function getMemberPage(data) {
    return request({
        url: '/cms/v1/document/lib/getMemberPage',
        method: "post",
        data
    });
}

// 文档库添加成员
export function addDocumentLibMember(data) {
    return request({
        url: "/cms/v1/document/lib/member/add",
        method: "post",
        data
    })
}

// 文档库删除成员
export function deleteDocumentLibMember(data) {
    return request({
        url: "/cms/v1/document/lib/member/delete",
        method: "post",
        data
    })
}

//获取文档详情
export function getDocumentInfoById(data) {
    return request({
        url: "/cms/v1/document/lib/doc/getDocumentInfoById",
        method: "post",
        data
    })
}

export function updateDocument(data) {
    return request({
        url: "/cms/v1/document/lib/doc/update",
        method: "post",
        data
    })
}

export function createDocuments(data) {
    return request({
        url: "/cms/v1/document/lib/doc/create",
        method: "post",
        data
    })
}

export function deleteDocuments(data) {
    return request({
        url: "/cms/v1/document/lib/doc/delete",
        method: "post",
        data
    })
}

export function attachmentDownload(fileId) {
    return request({
        url: '/cms/v1/document/lib/doc/attachmentDownLoad/' + fileId,
        method: "post"
    })
}

export function isView(data) {
    return request({
        url: "/cms/v1/document/lib/isView/",
        method: "post",
        data
    })
}
