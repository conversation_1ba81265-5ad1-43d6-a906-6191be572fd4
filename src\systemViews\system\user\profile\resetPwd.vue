<template>
  <div class="resetPwd">
    <el-form ref="resetPwdForm" :model="user" :rules="rules" label-width="80px" v-loading="saveLoading">
      <el-form-item label="旧密码" prop="oldPassword">
        <el-input v-model="user.oldPassword" placeholder="请输入旧密码" type="password" :show-password="true" />
      </el-form-item>
      <el-form-item label="新密码" prop="newPassword">
        <el-input v-model="user.newPassword" placeholder="请输入新密码" type="password" :show-password="true" />
      </el-form-item>
      <el-form-item label="确认密码" prop="confirmPassword">
        <el-input v-model="user.confirmPassword" placeholder="请确认密码" type="password" :show-password="true" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" size="small" @click="submit">保存</el-button>
        <el-button type="danger" size="small" @click="close">关闭</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import useUserStore from "@/store/modules/user";
import useTagsViewStore from "@/store/modules/tagsView";
import {updateUserPwd} from "@/api/system/user";
import {doCrypt} from "@/utils/sm2Encrypt";
import {ElMessageBox} from "element-plus";

const {proxy} = getCurrentInstance();
const userStore = useUserStore();
const resetPwdForm = ref(null)
const saveLoading = ref(false)
const user = ref({
  oldPassword: undefined,
  newPassword: undefined,
  confirmPassword: undefined,
})

const equalToPassword = (rule, value, callback) => {
  if (user.value.newPassword !== value) {
    callback(new Error("两次输入的密码不一致"));
  } else {
    callback();
  }
}

const rules = {
  oldPassword: [{ required: true, message: "旧密码不能为空", trigger: "blur" }],
  newPassword: [
    { required: true, message: "新密码不能为空", trigger: "blur" },
    { min: 6, max: 20, message: "长度在 6 到 20 个字符", trigger: "blur" }
  ],
  confirmPassword: [
    { required: true, message: "确认密码不能为空", trigger: "blur" },
    { required: true, validator: equalToPassword, trigger: "blur" }
  ]
}

const submit = () => {
  proxy.$refs["resetPwdForm"].validate(async(valid) => {
    if (valid) {
      saveLoading.value = true;
      updateUserPwd({
        newPassword: await doCrypt(user.value.newPassword),
        oldPassword: await doCrypt(user.value.oldPassword),
        confirmPassword: await doCrypt(user.value.confirmPassword),
      }).then(res => {
        saveLoading.value = false;
        if (res.success) {
          ElMessageBox.alert('密码更新成功，请重新登录！', '密码更新成功', {
            confirmButtonText: '去登录',
            showClose: false,
            callback: (action) => {
              userStore.logOut().then(() => {
                location.href = `/login?redirect=${location.pathname}`
              });
              reset();
            }
          })
        } else {
          proxy.$modal.msgError(res.message)
        }
      })
    }
  })
}

function reset() {
  user.value = {
    oldPassword: undefined,
    newPassword: undefined,
    confirmPassword: undefined
  }
}

const close = () => {
  useTagsViewStore().delView(proxy.$route)
  window.history.back(1)
}

defineExpose({
  resetPwdForm,
});
</script>
