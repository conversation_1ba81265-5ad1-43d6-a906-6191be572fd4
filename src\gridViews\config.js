
// 必填配置
// key：组件路径
// name：组件名称，用于编辑面板显示
// type：case（案例组件）、use（功能组件）、ad（广告组件）、monitor（监控组件）、static（静态组件）

// 可选配置
// minHeight：组件最低高度，单位px
// minWidth：组件最低宽度，单位px
// props: 组件支持的自定义配置，type支持 Boolean、Number、String

const CompsConfig = {
  "/src/gridViews/case/topImg/index.vue": {
    name: "顶部轮播图11",
    type: "case",
    minHeight:100,
    minWidth: 800,
    props: {
      showTitle:{
        label: '显示标题',
        type: Boolean,
        default: true
      },
      bgColor: {
        label: '显示背景',
        type: Boolean,
        default: true
      },
      minHeight:{
        label: '最小高度',
        type: Number,
        default:280
      }
    }
  },

  "/src/gridViews/case/caseNoticeNew/index.vue": {
    name: "通知公告",
    type: "case",
    minHeight:100,
    minWidth: 300,
    props: {
      showTitle:{
        label: '显示标题',
        type: Boolean,
        default: true
      },
      bgColor: {
        label: '显示背景',
        type: Boolean,
        default: true
      },
      minHeight:{
        label: '最小高度',
        type: Number,
        default:280
      }
    }
  },

  "/src/gridViews/case/message/index.vue": {
    name: "消息提醒",
    type: "case",
    minHeight:100,
    minWidth: 300,
    props: {
      showTitle:{
        label: '显示标题',
        type: Boolean,
        default: true
      },
      bgColor: {
        label: '显示背景',
        type: Boolean,
        default: true
      },
      minHeight:{
        label: '最小高度',
        type: Number,
        default:100
      }
    }
  },

  "/src/gridViews/case/commonFunctions/index.vue": {
    name: "常用功能",
    type: "case",
    minHeight:100,
    minWidth: 300,
    props: {
      showTitle:{
        label: '显示标题',
        type: Boolean,
        default: true
      },
      bgColor: {
        label: '显示背景',
        type: Boolean,
        default: true
      },
      minHeight:{
        label: '最小高度',
        type: Number,
        default:100
      }
    }
  },

  "/src/gridViews/case/todo/index.vue": {
    name: "待办",
    type: "case",
    minHeight:100,
    minWidth: 800,
    props: {
      showTitle:{
        label: '显示标题',
        type: Boolean,
        default: true
      },
      bgColor: {
        label: '显示背景',
        type: Boolean,
        default: true
      },
      minHeight:{
        label: '最小高度',
        type: Number,
        default:280
      }
    }
  },

  // 案例组件 -- case
  "/src/gridViews/case/caseSearch/index.vue": {
    name: "全局搜索",
    type: "case",
    minHeight: 490,
    minWidth: 800,
  },
  "/src/gridViews/case/caseUtils/index.vue": {
    name: "常用工具",
    type: "case",
    minHeight: 140,
    minWidth: 800,
  },
  "/src/gridViews/case/caseNotice/index.vue": {
    name: "通知公告",
    type: "case",
    minHeight: 79,
    minWidth: 800,
  },
  "/src/gridViews/case/caseBusiness/index.vue": {
    name: "业务动态",
    type: "case",
    minHeight: 520,
    minWidth: 800,
  },
  "/src/gridViews/case/caseNews/index.vue": {
    name: "新闻中心",
    type: "case",
    minHeight: 560,
    minWidth: 800,
  },
  "/src/gridViews/case/caseTools/index.vue": {
    name: "工作流程",
    type: "case",
    minHeight: 100,
    minWidth: 800,
  },

  // 功能组件 -- use
  "/src/gridViews/use/useNoticeAndBusiness/index.vue": {
    name: "通知公告&新闻动态",
    type: "use",
    minHeight: 300,
    minWidth: 500,
    props: {
      bgColor: {
        label: '显示背景',
        type: Boolean,
        default: true
      }
    }
  },
  "/src/gridViews/use/useNotice/index.vue": {
    name: "通知公告",
    type: "use",
    minHeight: 140,
    minWidth: 500,
    props: {
      showTitle:{
        label: '显示标题',
        type: Boolean,
        default: true
      },
      bgColor: {
        label: '显示背景',
        type: Boolean,
        default: true
      }
    }
  },
  "/src/gridViews/use/useBusiness/index.vue": {
    name: "新闻动态",
    type: "use",
    minHeight: 140,
    minWidth: 500,
    props: {
      showTitle:{
        label: '显示标题',
        type: Boolean,
        default: true
      },
      bgColor: {
        label: '显示背景',
        type: Boolean,
        default: true
      },
    }
  },
  "/src/gridViews/use/useNews/index.vue": {
    name: "新闻",
    type: "use",
    minHeight: 140,
    minWidth: 500,
    props: {
      showTitle:{
        label: '显示标题',
        type: Boolean,
        default: true
      },
      bgColor: {
        label: '显示背景',
        type: Boolean,
        default: true
      },
    }
  },
  "/src/gridViews/use/useNewsImg/index.vue": {
    name: "新闻",
    type: "use",
    minHeight: 140,
    minWidth: 500,
    props: {
      showTitle:{
        label: '显示标题',
        type: Boolean,
        default: true
      },
      bgColor: {
        label: '显示背景',
        type: Boolean,
        default: true
      },

    }
  },
  "/src/gridViews/use/useWeather/index.vue": {
    name: "天气预报",
    type: "use",
    minHeight: 220,
    minWidth: 400,
    props: {
      showTitle:{
        label: '显示标题',
        type: Boolean,
        default: true
      },
      bgColor: {
        label: '显示背景',
        type: Boolean,
        default: true
      }
    }
  },
  "/src/gridViews/use/useHotSearch/index.vue": {
    name: "热搜",
    type: "use",
    minHeight: 220,
    minWidth: 400,
    props: {
      showTitle:{
        label: '显示标题',
        type: Boolean,
        default: true
      },
      bgColor: {
        label: '显示背景',
        type: Boolean,
        default: true
      }
    }
  },
  "/src/gridViews/use/useTools/index.vue": {
    name: "小工具",
    type: "use",
    minHeight: 140,
    minWidth: 300,
    props: {
      showTitle:{
        label: '显示标题',
        type: Boolean,
        default: true
      },
      bgColor: {
        label: '显示背景',
        type: Boolean,
        default: true
      }
    }
  },
  "/src/gridViews/use/useIframe/index.vue": {
    name: "URL嵌入",
    type: "use",
    minHeight: 140,
    minWidth: 100,
    props: {
      showTitle:{
        label: '显示标题',
        type: Boolean,
        default: true
      },
      bgColor: {
        label: '显示背景',
        type: Boolean,
        default: true
      },
      iframeUrl: {
        label: '嵌入页面地址',
        type: String,
        default: ""
      }
    }
  },
  "/src/gridViews/use/useAppCenter/index.vue": {
    name: "应用中心",
    type: "use",
    minHeight: 240,
    minWidth: 500,
    props: {
      showTitle:{
        label: '显示标题',
        type: Boolean,
        default: true
      },
      bgColor: {
        label: '显示背景',
        type: Boolean,
        default: true
      }
    }
  },
  "/src/gridViews/use/useStatistics/index.vue": {
    name: "统计数据",
    type: "use",
    minHeight: 140,
    minWidth: 200,
    props: {
      showTitle:{
        label: '显示标题',
        type: Boolean,
        default: true
      },
      bgColor: {
        label: '显示背景',
        type: Boolean,
        default: true
      }
    }
  },

  //广告组件
  "/src/gridViews/ad/adCarousel/index.vue": {
    name: "广告-轮播图",
    type: "ad",
    minHeight: 140,
    minWidth: 200,
    props: {
      showTitle:{
        label: '显示标题',
        type: Boolean,
        default: true
      },
      bgColor: {
        label: '显示背景',
        type: Boolean,
        default: true
      }
    }
  },
  "/src/gridViews/ad/adCommon/index.vue": {
    name: "广告-普通",
    type: "ad",
    minHeight: 140,
    minWidth: 200,
    props: {
      showTitle:{
        label: '显示标题',
        type: Boolean,
        default: true
      },
      bgColor: {
        label: '显示背景',
        type: Boolean,
        default: true
      }
    }
  },
  "/src/gridViews/ad/adFont/index.vue": {
    name: "广告-文本",
    type: "ad",
    minHeight: 140,
    minWidth: 200,
    props: {
      showTitle:{
        label: '显示标题',
        type: Boolean,
        default: true
      },
      bgColor: {
        label: '显示背景',
        type: Boolean,
        default: true
      }
    }
  },

  // 监控组件
  "/src/gridViews/monitor/browser/index.vue": {
    name: "统计数据",
    type: "monitor",
    minHeight: 140,
    minWidth: 200,
    props: {
      showTitle:{
        label: '显示标题',
        type: Boolean,
        default: true
      },
      bgColor: {
        label: '显示背景',
        type: Boolean,
        default: true
      }
    }
  },
  "/src/gridViews/monitor/brokenLine/index.vue": {
    name: "云监控-折线面积图",
    type: "monitor",
    minHeight: 140,
    minWidth: 200,
    props: {
      showTitle:{
        label: '显示标题',
        type: Boolean,
        default: true
      },
      bgColor: {
        label: '显示背景',
        type: Boolean,
        default: true
      }
    }
  },
  //静态组件 --static
  "/src/gridViews/static/staticRoutine/index.vue": {
    name: "日常工作",
    type: "static",
    minHeight: 220,
    minWidth: 800,
    props: {
      titleShow:{
        label: '显示标题栏',
        type: Boolean,
        default: true
      },
      title:{
        label: '标题',
        type: String,
        default: "日常工作"
      },
      moreShow:{
        label: '显示更多',
        type: Boolean,
        default: true
      },
      moreLink:{
        label: '更多链接',
        type: String,
        default: ""
      },
      bgShow: {
        label: '显示背景',
        type: Boolean,
        default: true
      }
    }
  },
  "/src/gridViews/static/staticIndicators/index.vue":{
    name: "重点指标",
    type: "static",
    minHeight: 400,
    minWidth: 1000,
    props: {
      titleShow:{
        label: '显示标题栏',
        type: Boolean,
        default: true
      },
      title:{
        label: '标题',
        type: String,
        default: "重点指标"
      },

      bgShow: {
        label: '显示背景',
        type: Boolean,
        default: true
      }
    }
    },
};

// 用到的组件需从此处引入，默认引入gridViews目录下文件，其他需单独配置
const Modules = import.meta.glob([
  "@/gridViews/*.vue",
  "@/gridViews/**/index.vue",
]);

export { CompsConfig, Modules };
