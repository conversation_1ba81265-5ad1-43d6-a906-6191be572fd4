import { defineConfig, loadEnv } from "vite";
import path from "path";
import createVitePlugins from "./vite/plugins";
const pxtorem = require('postcss-pxtorem');
// 配置基本大小
const postcssPx2rem = pxtorem({
  // 基准大小 baseSize，需要和rem.js中相同
  remUnit: 16
})
 
// https://vitejs.dev/config/
export default defineConfig(({ mode, command }) => {
  const env = loadEnv(mode, process.cwd());
  const { VITE_APP_ENV } = env;
  return {
    // 部署生产环境和开发环境下的URL。
    // 默认情况下，vite 会假设你的应用是被部署在一个域名的根路径上
    // 如果应用被部署在一个子路径上，你就需要用这个选项指定这个子路径
    base: VITE_APP_ENV === "production" ? "/" : "/",
    plugins: createVitePlugins(env, command === "build"),
    resolve: {
      // https://cn.vitejs.dev/config/#resolve-alias
      alias: {
        // 设置路径
        "~": path.resolve(__dirname, "./"),
        // 设置别名
        "@": path.resolve(__dirname, "./src"),
      },
      // https://cn.vitejs.dev/config/#resolve-extensions
      extensions: [".mjs", ".js", ".ts", ".jsx", ".tsx", ".json", ".vue"],
    },
    // vite 相关配置
    server: {
      port: 9090,
      host: true,
      open: true,
      proxy: {
        // https://cn.vitejs.dev/config/#server-proxy
        "/dev-api": {
          target: "http://************:18080/",
          // target: "http://************:8080/", //祥龙
          // target: "http://************:8080/", 
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/dev-api/, ""),
        },
        '/browser': {
          target: 'http://*************:31795/',
          changeOrigin: true
        },
        '/v3':{
          target:'http://*************:31795/',
          changeOrigin: true//这里是追踪报告的代理
        }
      },
    },
    //fix:error:stdin>:7356:1: warning: "@charset" must be the first rule in the file
    css: {
      postcss: {
        plugins: [
          // postcssPx2rem,
          // require('postcss-pxtorem')({
          //   // mediaQuery: false, // (Boolean) 允许在媒体查询中转换 px
          //   // rootValue: 196, //换算基数，
          //   // propList: ['*'],
          //   // unitToConvert: "px", // (String) 要转换的单位，默认是px。
          //   // widthOfDesignLayout: 1920, // (Number) 设计布局的宽度。对于pc仪表盘，一般是1920.
          //   // unitPrecision: 3, // (Number) 允许 REM 单位增长到的十进制数字.
          //   // minPixelValue: 1, // (Number) 设置要替换的最小像素值.
          // }),
          {
            postcssPlugin: "internal:charset-removal",
            AtRule: {
              charset: (atRule) => {
                if (atRule.name === "charset") {
                  atRule.remove();
                }
              },
            },
          },
        ],
      },
    },
  };
});
