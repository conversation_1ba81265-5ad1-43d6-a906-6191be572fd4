<!-- 结算明细 -->

<template>
  <div class="dialog-box" :class="popupType === 'view' || popupType === 'review'
    ? 'dialog-box-view'
    : 'dialog-box-edit'
    ">
    <div class="top-form">
      <el-form ref="formRef" :model="formData" label-width="125px" :rules="popupType !== 'review' && popupType !== 'view' && flowNumber == '0'
        ? rules
        : ''
        ">
        <el-row>
          <el-col :span="12">
            <el-form-item label="供应商名称" prop="providerName">
              <div>
                {{ formData.providerName || "" }}
              </div>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="账户名称" prop="accountCode">
              <el-select v-model="formData.accountCode" placeholder="请选择账户名称" @change="handleAccountChange" multiple
                v-if="
                  popupType !== 'review' &&
                  popupType !== 'view' &&
                  flowNumber == '0'
                ">
                <el-option v-for="(item, index) in accountList.filter(a => a.tenantId === tenantId)" :key="index"
                  :label="item.accountName" :value="item.id" />
              </el-select>

              <div v-else>
                {{ formData.accountName || "" }}
              </div>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="结算起始时间" prop="startTime">
              <el-date-picker v-model="formData.startTime" type="date" placeholder="请选择结算起始时间" value-format="YYYY-MM-DD"
                v-if="
                  popupType !== 'review' &&
                  popupType !== 'view' &&
                  flowNumber == '0'
                ">
              </el-date-picker>

              <div v-else>
                {{ formData.startTime || "" }}
              </div>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="结算结束时间" prop="endTime">
              <el-date-picker v-model="formData.endTime" type="date" placeholder="请选择结算结束时间" value-format="YYYY-MM-DD"
                v-if="
                  popupType !== 'review' &&
                  popupType !== 'view' &&
                  flowNumber == '0'
                ">
              </el-date-picker>

              <div v-else>
                {{ formData.endTime || "" }}
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <div style="margin-top: 12px" class="flex" v-if="
      (flowNumber == '0' && popupType == 'edit') ||
      (flowNumber == '1' && popupType == 'edit') ||
      (flowNumber == '0' && popupType == 'view') ||
      (flowNumber == '0' && popupType == 'review')
    ">
      <div class="flex-1"></div>
      <el-button type="primary" icon="Download" @click="handleExport1">导出
      </el-button>
    </div>
    <div style="margin-top: 12px" class="flex" v-if="
      (flowNumber == '2' && popupType == 'edit') ||
      (flowNumber == '2' && popupType == 'edit') ||
      (flowNumber == '1' && popupType == 'view') ||
      (flowNumber == '1' && popupType == 'review')
    ">
      <div class="flex-1"></div>
      <el-button type="primary" icon="Download" @click="handleExport2">导出
      </el-button>
    </div>
    <public-table v-if="flowNumber == '0' && popupType == 'add'" style="margin-top: 12px" ref="publictable"
      :rowKey="tabelForm.tableKey" :tableData="list1" :columns="tabelForm.columns" :configFlag="tabelForm.tableConfig"
      :getList="settleDetailsList">
    </public-table>


    <public-table v-if="
      (flowNumber == '0' && popupType == 'review') ||
      (flowNumber == '0' && popupType == 'view') ||
      (flowNumber == '0' && popupType == 'edit') ||
      (popupType != 'review' && popupType != 'view' && flowNumber == '1')" style="margin-top: 12px" ref="publictable2"
      :rowKey="tabelFormFlow2.tableKey" :tableData="list1" :columns="tabelFormFlow2.columns"
      :configFlag="tabelFormFlow2.tableConfig" :getList="settleDetailsList">
    </public-table>

    <public-table v-if="
      flowNumber == '2' ||
      (flowNumber == '1' && popupType == 'view') ||
      (flowNumber == '1' && popupType == 'review')
    " style="margin-top: 12px" ref="publictable3" :rowKey="tabelFormFlow3.tableKey" :tableData="list2"
      :columns="tabelFormFlow3.columns" :configFlag="tabelFormFlow3.tableConfig" :getList="settleSheetList">
    </public-table>

    <div class="dialog-footer" v-if="flowNumber == '0' && popupType == 'add'">
      <div class="dialog-footer-btn">
        <el-button type="primary" @click="generateSettlement" v-if="!settleId">生成结算</el-button>

        <el-button type="primary" @click="generateSettlement('reset')" v-if="settleId">重新生成结算</el-button>
        <el-button type="primary" @click="addNextConfirm('1')" v-if="settleId">下一步</el-button>
      </div>
    </div>
    <div class="dialog-footer" v-if="flowNumber == '0' && popupType == 'edit'">
      <div class="dialog-footer-btn">
        <el-button type="primary" @click="generateSettlement('reset')">重新生成结算</el-button>
        <el-button type="primary" @click="addNextConfirm('1')" >下一步</el-button>
      </div>
    </div>

    <!-- <div class="dialog-footer" v-if="flowNumber == '0' && popupType == 'view'">
      <div class="dialog-footer-btn">
        <el-button type="primary" @click="addNextConfirm"
          >下一步</el-button
        >
      </div>
    </div>

    <div class="dialog-footer" v-if="flowNumber == '1' && popupType=='view'">
      <div class="dialog-footer-btn">
        <el-button  type="primary"  @click="prevButton">上一步</el-button>
      </div>
    </div> -->

    <div class="dialog-footer" v-if="flowNumber == '1' && popupType != 'view' && popupType != 'review'">
      <div class="dialog-footer-btn">
        <el-button class="cancel-btn" @click="prevButton('0')">上一步</el-button>
        <el-button type="primary" @click="addNextConfirm">下一步</el-button>
      </div>
    </div>

    <div class="dialog-footer" v-if="flowNumber == '2'">
      <div class="dialog-footer-btn">
        <el-button class="cancel-btn" @click="prevButton('1')">上一步</el-button>
        <el-button type="primary" @click="submitConfirm">提交</el-button>
      </div>
    </div>


    <el-form style="margin-top: 12px;" ref="formRef" :model="formData" label-width="90px"
      v-if="flowNumber == '1' && popupType == 'review'" :rules="popupType == 'review' ? rules : ''">
      <el-row>
        <el-col :span="24">
          <el-form-item label="审批意见" prop="auditReason">
            <el-input v-model="formData.auditReason" placeholder="请输入审批意见" type="textarea" rows="3"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="dialog-footer" v-if="flowNumber == '1' && popupType == 'review'">


      <div class="dialog-footer-btn">
        <el-button class="cancel-btn" @click="submitApproal('3')">退回</el-button>
        <el-button type="primary" @click="submitApproal('2')">通过</el-button>
      </div>
    </div>


  </div>
</template>



<script setup>
import { ref, reactive, watch, getCurrentInstance, defineProps } from "vue";

import { ElMessage, ElMessageBox } from "element-plus";
import { apiUrl } from "@/utils/config";
import { formatMinuteTime } from "@/utils";
import { screenIndex } from "@/api/paymentCenter/order-settlement/index";
import useUserStore from "@/store/modules/user";
const userStore = useUserStore();
// 定义组件 props
const props = defineProps({
  closeBtn: {
    type: Function,

    default: null,
  },

  popupType: {
    type: String,
    default: "",
  },

  rowData: {
    type: Object,
    default: () => ({}),
  },

  flowNumber: {
    type: Number,
    default: 0,
  },
});
// 结算id
const settleId = ref(props.rowData.id || '');
const tenantId = userStore.userInfo.tenantId
const tableHeight = ref(""); // 默认高度
const tableHeight2 = ref(""); // 默认高度
watch(() => [props.flowNumber, props.popupType], ([flowNumber, popupType]) => {

  if (flowNumber == 0 && popupType == 'review') {
    tableHeight.value = "469px";
  } else if (flowNumber == 1 && popupType == 'review') {
    tableHeight2.value = "320px";
  } else if (flowNumber == 0 && popupType == 'view') {
    tableHeight.value = "469px";
  } else if (flowNumber == 1 && popupType == 'view') {
    tableHeight2.value = "469px";
  } else if (popupType == 'add') {
    tableHeight.value = "420px";
    tableHeight2.value = "420px";
  } else if (flowNumber == 0 && popupType == 'edit') {
    tableHeight.value = "420px";
  } else if (flowNumber == 1 && popupType == 'edit') {
    tableHeight.value = "420px";
  } else if (flowNumber == 2 && popupType == 'edit') {
    tableHeight2.value = "420px";
  }
}, { immediate: true });


// 字典
const { proxy } = getCurrentInstance();
const { payment_status } = proxy.useDict(

  "payment_status"
);

// 表单引用

let formData = ref({
  accountCode: []
});
const formRef = ref(null);

//list1 第一步新增表格数据
const list1 = ref([]);
//list2 第二步表格数据
const list2 = ref([]);
// 表格数据
const tabelForm = reactive({
  tableKey: "1", //表格key值
  isShowRightToolbar: true, //是否显示右侧显示和隐藏
  showSearch: true,
  // 表格表格数据
  columns: [
    {
      fieldIndex: "accountName", // 对应列内容的字段名
      label: "账户名称", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "150", //最小宽度%
      width: "", //宽度
      align: "left", //表格对齐方式
    },
    {
      fieldIndex: "payTypeName", // 对应列内容的字段名
      label: "支付大类", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "120", //最小宽度%
      width: "", //宽度
    },

    {
      fieldIndex: "paySubclassName", // 对应列内容的字段名
      label: "支付小类", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "120", //最小宽度%
      width: "", //宽度
    },
    {
      fieldIndex: "payAmount", // 对应列内容的字段名
      label: "实付金额", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "120", //最小宽度%
      width: "", //宽度
      type: "dollor",
    },
    {
      fieldIndex: "orderAmount", // 对应列内容的字段名
      label: "订单金额", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "120", //最小宽度%
      width: "", //宽度
      type: "dollor",
    },

    {
      fieldIndex: "payTime", // 对应列内容的字段名
      label: "结算时间", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "150", //最小宽度%
      width: "", //宽度
    },
  ],
  // 表格基础配置项，看个人需求添加
  tableConfig: {
    needPage: false, // 是否需要分页
    index: true, // 是否需要序号
    selection: false, // 是否需要多选
    reserveSelection: false, // 是否需要支持跨页选择
    indexFixed: false, // 序号列固定 left true right
    selectionFixed: false, //多选列固定left true right
    indexWidth: "50", //序号列宽度
    loading: false, //表格loading
    showSummary: false, //是否开启合计
    height: "420px", //表格固定高度 比如：300px
  },
});

const tabelFormFlow2 = reactive({
  tableKey: "1", //表格key值
  isShowRightToolbar: true, //是否显示右侧显示和隐藏
  showSearch: true,
  // 表格表格数据
  columns: [
    {
      fieldIndex: "orderCode", // 对应列内容的字段名
      label: "订单编号", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "200", //最小宽度%
      width: "", //宽度
    },
    {
      fieldIndex: "providerName", // 对应列内容的字段名
      label: "供应商名称", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "150", //最小宽度%
      width: "", //宽度
      align: "left", //表格对齐方式
    },
    {
      fieldIndex: "providerCode", // 对应列内容的字段名
      label: "供应商编码", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "140", //最小宽度%
      width: "", //宽度
    },
    {
      fieldIndex: "staffName", // 对应列内容的字段名
      label: "员工姓名", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "130", //最小宽度%
      width: "", //宽度
    },

    {
      fieldIndex: "loginName", // 对应列内容的字段名
      label: "登录账号", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "150", //最小宽度%
      width: "", //宽度
    },
    {
      fieldIndex: "accountName", // 对应列内容的字段名
      label: "账户名称", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "150", //最小宽度%
      width: "", //宽度
    },
    {
      fieldIndex: "accountCode", // 对应列内容的字段名
      label: "账户编码", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "120", //最小宽度%
      width: "", //宽度
    },
    {
      fieldIndex: "payTypeName", // 对应列内容的字段名
      label: "支付大类", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "120", //最小宽度%
      width: "", //宽度
    },
    {
      fieldIndex: "paySubclassName", // 对应列内容的字段名
      label: "支付小类", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "120", //最小宽度%
      width: "", //宽度
    },
    {
      fieldIndex: "payAmount", // 对应列内容的字段名
      label: "实付金额(元)", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "140", //最小宽度%
      width: "", //宽度
      type: "dollor",
    },
    {
      fieldIndex: "orderAmount", // 对应列内容的字段名
      label: "订单金额(元)", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "140", //最小宽度%
      width: "", //宽度
      type: "dollor",
    },

    {
      fieldIndex: "payTime", // 对应列内容的字段名
      label: "支付时间", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "180", //最小宽度%
      width: "", //宽度
    },

    {
      fieldIndex: "payStatus", // 对应列内容的字段名
      label: "支付状态", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "140", //最小宽度%
      width: "", //宽度
      align: "", //表格对齐方式
      type: "dict",
      dictList: payment_status,
    },

    {
      fieldIndex: "operate", // 对应列内容的字段名
      label: "操作人", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "120", //最小宽度%
      width: "", //宽度
    },
  ],
  // 表格基础配置项，看个人需求添加
  tableConfig: {
    needPage: false, // 是否需要分页
    index: true, // 是否需要序号
    selection: false, // 是否需要多选
    reserveSelection: false, // 是否需要支持跨页选择
    indexFixed: false, // 序号列固定 left true right
    selectionFixed: false, //多选列固定left true right
    indexWidth: "50", //序号列宽度
    loading: false, //表格loading
    showSummary: false, //是否开启合计
    get height() {
      return tableHeight.value
    }
  },
});

const tabelFormFlow3 = reactive({
  tableKey: "1", //表格key值
  isShowRightToolbar: true, //是否显示右侧显示和隐藏
  showSearch: true,
  // 表格表格数据
  columns: [
    {
      fieldIndex: "accountName", // 对应列内容的字段名
      label: "账户名称", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "150", //最小宽度%
      width: "", //宽度
      align: "left", //表格对齐方式
    },
    {
      fieldIndex: "breakfastNum", // 对应列内容的字段名
      label: "早餐次数", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "120", //最小宽度%
      width: "", //宽度
    },

    {
      fieldIndex: "breakfastSubsidy", // 对应列内容的字段名
      label: "早餐补助(元)", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "140", //最小宽度%
      width: "", //宽度
      type: "dollor",
    },

    {
      fieldIndex: "lunchNum", // 对应列内容的字段名
      label: "午餐次数", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "120", //最小宽度%
      width: "", //宽度
    },
    {
      fieldIndex: "lunchSubsidy", // 对应列内容的字段名
      label: "午餐补助(元)", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "140", //最小宽度%
      width: "", //宽度
      type: "dollor",
    },

    {
      fieldIndex: "takeawayAmount", // 对应列内容的字段名
      label: "外卖消费(元)", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "140", //最小宽度%
      width: "", //宽度
      type: "dollor",
    },
    {
      fieldIndex: "payAmount", // 对应列内容的字段名
      label: "实付总金额(元)", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "150", //最小宽度%
      width: "", //宽度
      type: "dollor",
    },
    {
      fieldIndex: "orderAmount", // 对应列内容的字段名
      label: "订单总金额(元)", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "150", //最小宽度%
      width: "", //宽度
      type: "dollor",
    },
    {
      fieldIndex: "discountAmount", // 对应列内容的字段名
      label: "优惠金额(元)", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "150", //最小宽度%
      width: "", //宽度
      type: "dollor",
    },
    {
      fieldIndex: "orderTotal", // 对应列内容的字段名
      label: "订单总数", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "120", //最小宽度%
      width: "", //宽度
    },
  ],
  // 表格基础配置项，看个人需求添加
  tableConfig: {
    needPage: false, // 是否需要分页
    index: true, // 是否需要序号
    selection: false, // 是否需要多选
    reserveSelection: false, // 是否需要支持跨页选择
    indexFixed: false, // 序号列固定 left true right
    selectionFixed: false, //多选列固定left true right
    indexWidth: "50", //序号列宽度
    loading: false, //表格loading
    showSummary: false, //是否开启合计
    get height() {
      return tableHeight2.value
    }
  },
});
// 账户列表
const accountList = ref([]);


// 定义 emits
const emit = defineEmits(["nextButton", "finshFlow", "refreshList"]);

// 定义时间规则
const validateTimeRange = (rule, value, callback) => {
  if (
    formData.value.startTime &&
    formData.value.endTime &&
    formData.value.startTime > formData.value.endTime
  ) {
    callback(new Error("开始时间不能大于结束时间"));
  } else {
    callback();
  }
};

// 在validateTimeRange函数下方添加新的验证函数
const validatePastDate = (rule, value, callback) => {
  if (!value) {
    callback();
    return;
  }
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  const selectedDate = new Date(value);

  if (selectedDate > today) {
    callback(new Error("日期不能超过今天"));
  } else {
    callback();
  }
};

// 定义校验规则

const rules = reactive({
  startTime: [
    { required: true, message: "请选择开始时间", trigger: "change" },
    { validator: validateTimeRange, trigger: "change" },
    { validator: validatePastDate, trigger: "change" }
  ],
  endTime: [
    { required: true, message: "请选择结束时间", trigger: "change" },
    { validator: validateTimeRange, trigger: "change" },
    { validator: validatePastDate, trigger: "change" }
  ],
  accountCode: [{ required: true, message: "请选择账户名称", trigger: "blur" }],
  auditReason: [
    { required: true, message: "请输入审批意见", trigger: "blur" },
  ],
});

// 定义表单数据
const handleAccountChange = (selectedIds) => {
  if (Array.isArray(selectedIds) && selectedIds.length > 0) {
    const selectedAccounts = accountList.value.filter(account =>
      selectedIds.includes(account.id)
    );
    formData.value.accountName = selectedAccounts.map(a => a.accountName).join(', ');
  } else {
    formData.value.accountName = '';
  }
};

// 供应商名称查询
const getInfoByStaffId = async () => {
  try {
    const res = await screenIndex.getInfoByStaffId({
      staffId: userStore.userInfo.staffId
    });
    if (res.code == "1") {
      formData.value.providerName = res.data.providerName;
      formData.value.providerId = res.data.providerId;
    }
  } catch (error) {
    console.error("失败:", error);
  }
}
// 账户列表
const listAccount = async () => {
  try {
    const res = await screenIndex.listAccount({
      providerId: formData.value.providerId
    });
    if (res.code == "1") {
      accountList.value = res.data
    }
  } catch (error) {
    console.error("失败:", error);
  }
}
// 生成结算
const generateSettlement = (type) => {
  formRef.value.clearValidate();
  formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        await ElMessageBox.confirm(
          type === 'reset' ? '确认要重新生成结算吗？' : '确认要生成结算吗？',
          '提示',
          { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' }
        );
        let params = {}
        if (type) {
          params = {
            providerId: formData.value.providerId,
            accountId: Array.isArray(formData.value.accountCode)
              ? formData.value.accountCode.join(',')
              : formData.value.accountCode,
            startTime: formData.value.startTime,
            endTime: formData.value.endTime,
          };
        } else {
          params = {
            providerId: formData.value.providerId,
            accountId: Array.isArray(formData.value.accountCode)
              ? formData.value.accountCode.join(',')
              : formData.value.accountCode,
            startTime: formData.value.startTime,
            endTime: formData.value.endTime,
            tenantId: tenantId // 明确赋值
          };
        }


        let res;
        if (type === 'reset') {
          res = await screenIndex.edit({
            ...params,
            id: settleId.value,
          });
        } else {
          res = await screenIndex.add(params);
        }

        if (res.code === "1") {
          ElMessage.success(type === 'reset' ? "结算重新生成成功" : "结算生成成功");
          settleId.value = res.data;
          settleDetailsList();
           emit("refreshList");
          emit("nextButton", props.flowNumber);
         
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error("操作失败:", error);
          ElMessage.error(type === 'reset' ? "结算重新生成失败" : "结算生成失败");
        }
      }
    }
  });
};

// 下一步
const addNextConfirm = (index) => {
  // 请求结算单
  if (index == '1') {
    settleDetailsList()
  } else {
    settleSheetList()
  }

  emit("nextButton", props.flowNumber);
};
// 上一步
const prevButton = (index) => {
  if (index == '1' || index == '0') {
    settleDetailsList()
  }
  emit("prevButton", props.flowNumber);
};
// 提交
const submitConfirm = async () => {
  try {
    // 弹出确认框
    await ElMessageBox.confirm(
      '确认要提交吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    const res = await screenIndex.editStatus(
      {
        id: settleId.value,
        // providerId: formData.value.providerId,
        // // 确保 accountId 是字符串
        // accountId: Array.isArray(formData.value.accountCode) ? formData.value.accountCode.join(',') : formData.value.accountCode,
        // startTime: formData.value.startTime,
        // endTime: formData.value.endTime
      }
    );

    if (res.code == "1") {
      ElMessage.success("提交成功");
      emit("finshFlow");
    }
  } catch (error) {
    if (error === 'cancel') {
      // 用户点击取消，不做处理
      return;
    }
    console.error("操作失败:", error);
  }
};
// 结算详情列表
const settleDetailsList = async () => {
  try {
    const res = await screenIndex.settleDetailsList({
      settleId: settleId.value,
    })
    if (res.code == "1") {
      list1.value = res.data.records;
    }
  } catch (error) {
    console.error("失败:", error);
  }
}
// 结算单列表
const settleSheetList = async () => {
  try {
    const res = await screenIndex.settleSheetList({
      settleId: settleId.value,
    })
    if (res.code == "1") {
      list2.value = res.data.records;
    }
  } catch (error) {
    console.error("失败:", error);
  }
}

// 详情
const getInfo = async () => {
  try {
    const res = await screenIndex.getInfo({
      id: settleId.value,
    });
    if (res.code == "1") {
      formData.value = res.data;
      // 确保 accountCode 始终是数组
      formData.value.accountCode = formData.value.accountId
        ? formData.value.accountId.split(',')
        : []; // 空值设为空数组
    }
  } catch (error) {
    console.error("获取供应商信息失败:", error);
  }
}
// 导出
const handleExport1 = () => {
  proxy.download(
    `pay${apiUrl}/pay/settleDetails/export`,
    {
      settleId: settleId.value,
    },
    `订单结算明细列表_${formatMinuteTime(new Date())}.xlsx`
  );
};
const handleExport2 = () => {
  proxy.download(
    `pay${apiUrl}/pay/settleSheet/export`,
    {
      settleId: settleId.value,
    },
    `订单结算单列表_${formatMinuteTime(new Date())}.xlsx`
  );
};
// 通过
const submitApproal = async (type) => {
  // 清除表单验证
  formRef.value.clearValidate();
  // 对表单进行验证
  formRef.value.validate(async (valid) => {
    if (valid) {

      try {
        // 根据 type 参数动态设置确认框提示信息
        const confirmMessage = type == '2' ? '确认要通过审批吗？' : '确认要拒绝审批吗？';
        // 弹出确认框
        await ElMessageBox.confirm(
          confirmMessage,
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        );

        // 构建请求参数
        const params = {
          settleId: settleId.value,
          auditStatus: type,
          auditReason: formData.value.auditReason,
          tenantId: tenantId
        };
        // 调用审批接口
        const res = await screenIndex.addFlow(params);

        if (res.code == "1") {
          // 根据 type 参数动态设置成功提示信息
          const successMessage = type == '2' ? "审批通过成功" : "审批拒绝成功";
          ElMessage.success(successMessage);
          // 触发 finshFlow 事件
          emit("finshFlow");
        }
      } catch (error) {

      }
    }
  });
};

// 初始化数据

const initData = async () => {

  if (settleId.value) {
    if (props.popupType != 'view') {
      await getInfoByStaffId();
      await listAccount();
    }
    await getInfo()
    await settleDetailsList()
  } else {
    await getInfoByStaffId();
    await listAccount();
  }
};
onMounted(() => {
  try {
    initData()
  } catch (error) {
    console.error("初始化数据失败:", error);
  }
});

defineExpose({
  settleDetailsList,
  settleSheetList
});
</script>



<style scoped lang="scss">
.dialog-footer {
  display: flex;
  justify-content: center;
  padding: 12px 0px;
  padding-bottom: 0px;
}

.top-form {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0px 0px 10px 0px rgba(155, 11, 9, 0.1);
  padding: 30px 10px;
  padding-bottom: 10px;
  box-sizing: border-box;
}
</style>