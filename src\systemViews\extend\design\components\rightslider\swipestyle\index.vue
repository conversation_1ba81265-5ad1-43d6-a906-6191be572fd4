<template>
  <div class="swipestyle">
    <!-- 标题 -->
    <h2>{{ datas.config.moduleTitle }}</h2>

    <!-- 表单 -->
    <el-form label-width="80px" :model="datas" size="small">
      <!-- 下划线 -->
      <div class="bor" />

      <h5 style="color: #000; font-size: 14px">添加图片</h5>
      <p style="color: #969799; font-size: 12px; margin-top: 10px">
        拖动选中的导航可对其排序
      </p>

      <!-- 图片广告 -->
      <div v-if="datas.data[0]">
        <vuedraggable
            :list="datas.data"
            item-key="index"
            :forceFallback="true"
            v-bind="dragOptions" >
          <template #item="{element,index}">
            <section class="imgBanner">
              <van-icon
                  class="el-icon-circle-close"
                  name="close"
                  @click="deleteimg(index)"
              />
              <div class="imag">
                <img draggable="false" :src="element.routerIcon" alt="" />
              </div>
              <div class="imgText">
                  <el-input
                      placeholder="请输入标题，也可不填"
                      v-model="element.routerTitle"
                      size="small"
                  />
                <div class="select-type">
                  <el-select
                      style="width: 100%"
                      v-model="element.routerType"
                      placeholder="请选择路由类型"
                      size="small"
                  >
                    <el-option
                        v-for="item in routerTypeList"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </div>
                <el-input
                    v-model="element.routerUrl"
                    placeholder="请输入http地址，本地路由页面地址、本地html路径"
                    size="small"
                    type="textarea"
                ></el-input>
              </div>
            </section>
          </template>
        </vuedraggable>
      </div>
      <!-- 上传图片 -->
      <el-button
          @click="$refs.upload.showUpload()"
          class="uploadImg"
          type="primary"
          plain
      >
        <i class="el-icon-plus" />点击添加图片
      </el-button>

      <!-- 下划线 -->
      <div class="bor"></div>
    </el-form>

    <!-- 上传图片 -->
    <uploadimg ref="upload" @uploadInformation="uploadInformation" />
  </div>
</template>

<script>
import vuedraggable from 'vuedraggable' //拖拽组件
import uploadimg from '../../uploadImg' //图片上传

export default {
  name: 'swipestyle',
  components: { vuedraggable, uploadimg },
  props: {
    datas: Object,
  },
  data() {
    return {
      routerTypeList: [
        {
          value: 'netWeb',
          label: '网页h5页面',
        },
        {
          value: 'localPage',
          label: '内部路由',
        },
        {
          value: 'localWeb',
          label: '本地网页',
        },
      ], // 选择跳转类型
      dragOptions: {
        animation: 200,
      },
      emptyText: '',
    }
  },

  created() {
  },

  methods: {

    // 提交
    uploadInformation(res) {
      this.datas.data.push({
        routerIcon: res,
        routerIconType: 'localUrl',
        routerType: 'localPage',
        customParams: {},
      })
    },

    /* 删除图片 */
    deleteimg(index) {
      this.datas.data.splice(index, 1)
    },
  },
}
</script>

<style scoped lang="less">
.swipestyle {
  width: 100%;
  position: relative;
  left: 0;
  top: 0;
  padding: 0 10px;
  box-sizing: border-box;

  /* 标题 */
  h2 {
    padding: 24px 16px 24px 0;
    margin-bottom: 15px;
    border-bottom: 1px solid #f2f4f6;
    font-size: 18px;
    font-weight: 600;
    color: #323233;
  }

  .lef {
    :deep(.el-form-item__label) {
      text-align: left;
    }
    margin-top: 20px;
  }

  /* 轮播图样式 */
  .swipeType {
    display: flex;
    justify-content: space-around;
    align-items: center;
    span {
      display: inline-block;
      width: 58px;
      height: 32px;
      text-align: center;
      line-height: 32px;
      background: #ebedf0;
      color: #979797;
      border: 1px solid #fff;
      cursor: pointer;
      transition: all 0.5s;

      &:hover {
        border: 1px solid #155bd4;
        color: #155bd4;
      }

      &.active {
        border: 1px solid #155bd4;
        background-color: #e0edff;
        color: #155bd4;
      }
    }
  }

  /* 圆角 */
  .borrediu {
    span {
      display: inline-block;
      width: 48px;
      height: 26px;
      text-align: center;
      line-height: 26px;
      background: #ebedf0;
      color: #979797;
      border: 1px solid #fff;
      cursor: pointer;
      transition: all 0.5s;

      &:hover {
        border: 1px solid #155bd4;
        color: #155bd4;
      }

      &.active {
        border: 1px solid #155bd4;
        background-color: #e0edff;
        color: #155bd4;
      }
    }
  }

  :deep(.radi .el-radio) {
    margin-right: 8px;
  }

  :deep(.radi1 .el-radio) {
    margin-right: 7px;
    .el-radio__label {
      padding-left: 5px;
    }
  }

  /* 上传图片按钮 */
  .uploadImg {
    width: 345px;
    height: 40px;
    margin-top: 20px;
  }

  // 上传弹框内容部分
  :deep(.uploadIMG .el-dialog__body ){
    height: 280px;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    justify-content: center;
  }

  .disable {
    :deep(.el-upload) {
      display: none !important;
    }
  }

  /* 图片广告列表 */
  .imgBanner {
    padding: 6px 12px;
    margin: 16px 7px;
    border-radius: 2px;
    background-color: #fff;
    box-shadow: 0 0 4px 0 rgba(10, 42, 97, 0.2);
    display: flex;
    position: relative;

    /* 删除图标 */
    .el-icon-circle-close {
      position: absolute;
      right: -10px;
      top: -10px;
      cursor: pointer;
      font-size: 19px;
    }

    /* 图片 */
    .imag {
      width: 60px;
      height: 60px;
      border-radius: 5px;
      overflow: hidden;
      position: relative;
      cursor: pointer;
      img {
        width: 100%;
        height: 100%;
        display: inline-block;
      }
      span {
        background: rgba(0, 0, 0, 0.5);
        font-size: 12px;
        position: relative;
        left: 0px;
        bottom: 0px;
        display: inline-block;
        width: 100%;
        text-align: center;
        color: #fff;
        height: 20px;
        line-height: 20px;
      }
    }

    /* 图片字 */
    .imgText {
      width: 80%;
      display: flex;
      flex-direction: column;
      box-sizing: border-box;
      padding-left: 20px;
      justify-content: space-between;
      .select-type {
        display: flex;
        :deep(.el-select) {
          .el-input {
            input {
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
        }
      }
    }
  }
}
</style>
