<script setup>
import { ref, onMounted, defineProps, defineEmits, reactive, getCurrentInstance } from "vue";
import { ElMessage } from "element-plus";
import { screenIndex } from "@/api/admittance/group";

const { proxy } = getCurrentInstance()
const { group_status } = proxy.useDict("group_status")

const props = defineProps({
    popupType: {
        type: String,
        default: "",
    },
    rowData: {
        type: Object,
        default: () => ({}),
    },
});

const emit = defineEmits(["submitClose"]);
const userInfo = ref([]);
const applyUserList = ref([]);
const tableKey1 = ref(1);
// 响应式数据
const formData = ref({
    visitorPermission: "1",
    groupName: "",
    status: "1",
    accessGroupStaffList: [] // 添加这个字段
});
const ruleform = ref(null);

const rules = {
    tenantId: [{ required: true, message: "请选择租户", trigger: "blur" }],
    groupName: [{ required: true, message: "请输入分组名称", trigger: "blur" }],
    status: [{ required: true, message: "请选择状态", trigger: "blur" }],
};

// 生命周期
onMounted(() => {
    // 只在编辑或查看模式下初始化数据
    if (props.popupType === "edit" || props.popupType === "view") {
        // 直接使用传入的数据，不再额外调用API
        formData.value = JSON.parse(JSON.stringify(props.rowData));
        formData.value.accessGroupStaffList = formData.value.accessGroupStaffList || [];
        
        // 初始化人员列表
        if (formData.value.id) {
            initStaffList(formData.value.id);
        }
    }
});

// 初始化人员列表数据
const initStaffList = async (groupId) => {
    try {
        const res = await screenIndex.getGroupStaffListById(groupId);
        if (res.success && res.data) {
            // 使用 groupStaffList 字段直接设置用户信息
            if (res.data.groupStaffList && res.data.groupStaffList.length > 0) {
                setUserInfo(res.data.groupStaffList);
            }
        }
    } catch (error) {
        console.error("获取人员列表失败:", error);
    }
};

// 添加方法来设置用户信息
const setUserInfo = (data) => {
    userInfo.value = data || [];
    // 强制更新表格
    tableKey1.value = tableKey1.value + 1;
};

const userColumns = reactive([
    {
        prop: "staffName",
        label: "人员姓名",
        show: true,
        sortable: true,
        minWidth: "80%",
    },
    {
        prop: "loginName",
        label: "人员账号",
        show: true,
        sortable: true,
        minWidth: "100%",
    },
    {
        prop: "orgName",
        label: "人员部门",
        show: true,
        sortable: true,
        minWidth: "150%",
    },
]);

// 方法
const handleDelete = (row) => {
    userInfo.value = userInfo.value.filter((item) => item.staffId !== row.staffId);
    formData.accessGroupStaffList = formData.accessGroupStaffList.filter((item) => item.staffId !== row.staffId);
    applyUserList.value.push(row);
};

const changeUser = (staffId) => {
    const selectedUser = applyUserList.value.find(
        (item) => item.staffId === staffId
    );
    if (selectedUser) {
        // 检查用户是否已存在于列表中
        const isDuplicate = userInfo.value.some(user => user.staffId === selectedUser.staffId);
        
        if (isDuplicate) {
            ElMessage.warning("该人员已在列表中，请勿重复添加");
            return;
        }
        
        userInfo.value.push(selectedUser);

        formData.value.accessGroupStaffList = formData.value.accessGroupStaffList || [];
        formData.value.accessGroupStaffList.push({
            staffId: selectedUser.staffId,
            unionId: selectedUser.unionId
        });
        applyUserList.value = applyUserList.value.filter(
            (item) => item.staffId !== staffId
        );
    }
};

const getUserList = async (name) => {
    if (!name || name.trim().length < 2) {
        applyUserList.value = []; // 立即清空列表
        return;
    }
    try {
        const res = await screenIndex.selectUserList({
            staffName: name
        });
        applyUserList.value = res.data;
    } catch (error) {
        console.error("获取用户列表失败:", error);
    }
};




// 提交表单
const submitForm = async () => {
    try {
        const valid = await ruleform.value.validate();
        if (valid) {
            const submitData = {
                id: formData.value.id,
                tenantId: formData.value.tenantId,
                groupName: formData.value.groupName,
                status: formData.value.status,
                count: userInfo.value.length, // 添加人数计数
                accessGroupStaffList: userInfo.value.map(item => ({
                    staffId: item.staffId,
                    unionId: item.unionId
                }))
            };

            // 根据 id 是否存在来判断是新增还是修改
            const res = formData.value.id
                ? await screenIndex.updateGroup(submitData) // 修改
                : await screenIndex.addGroup(submitData); // 新增

            if (res.success) {
                ElMessage.success(formData.value.id ? "修改成功" : "保存成功");
                emit("submitClose");
            }
        }
    } catch (error) {
        console.error("表单提交失败:", error);
    }
};
defineExpose({
    submitForm,
    setUserInfo,
    userInfo
})
</script>

<template>
    <div class="dialog-box" :class="popupType === 'view' ? 'dialog-box-view' : 'dialog-box-edit'">
        <Splitpanes class="default-theme">
            <Pane :size="100" :min-size="30">
                <el-card class="dep-card" style="height: 100%">
                    <el-form ref="ruleform" :model="formData" label-width="80px"
                        :rules="popupType !== 'view' ? rules : false">
                        <el-row>
                            <el-col :span="8">
                                <el-form-item label="租户">
                                    <TenantSelect v-model="formData.tenantId"
                                        v-if="(popupType == 'edit' && formData.tenantId) || popupType == 'add'">
                                    </TenantSelect>
                                    <div v-else>{{ formData.tenantName }}</div>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="分组名称" prop="groupName">
                                    <el-input v-model="formData.groupName" placeholder="请输入分组名称"
                                        v-if="popupType != 'view'"></el-input>
                                    <div v-else>{{ formData.groupName }}</div>
                                </el-form-item>
                            </el-col>

                            <el-col :span="8">
                                <el-form-item label="状态" prop="status">
                                    <el-radio-group v-if="popupType != 'view'" v-model="formData.status">
                                        <el-radio-button v-for="dict in group_status" :key="dict.value"
                                            :label="dict.value">
                                            {{ dict.label }}
                                        </el-radio-button>
                                    </el-radio-group>
                                    <div v-if="popupType == 'view'">
                                        {{group_status.find(item => item.value === formData.status)?.label || '-'}}
                                    </div>
                                </el-form-item>
                            </el-col>

                            <el-col :span="8">
                                <el-form-item label="人员数量">
                                    <div>{{ userInfo.length }}</div>
                                </el-form-item>
                            </el-col>

                        </el-row>
                    </el-form>

                    <el-form ref="ruleform" :model="formData" :rules="rules" label-width="80px">
                        <el-row>
                            <el-col :span="8">
                                <el-form-item label="人员" prop="nameList" v-if="popupType !== 'view'">

                                    <el-select collapse-tags placeholder="请输入人员进行选择" clearable @change="changeUser"
                                        filterable remote reserve-keyword :remote-method="getUserList">
                                        <el-option v-for="(item, index) in applyUserList" :key="index"
                                            :label="item.staffName" :value="item.staffId">
                                            <div>
                                                {{
                                                    item.staffName +
                                                    "(" +
                                                    item.orgName +
                                                    "/" +
                                                    item.loginName +
                                                    ")"
                                                }}
                                            </div>
                                        </el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>

                        </el-row>
                    </el-form>
                    <el-table :key="tableKey1" class="limit-cell-table" :data="userInfo" border>
                        <el-table-column type="index" label="序号" width="80" align="center" sortable />
                        <template v-for="item in userColumns" :key="item.prop">
                            <el-table-column show-overflow-tooltip :prop="item.prop" align="center" :label="item.label"
                                :min-width="item.minWidth" :resizable="item.resizable">
                                <template #default="scope">
                                    <div v-if="item.prop === 'staffName'">
                                        {{ scope.row.staffName }}
                                    </div>
                                    <div v-if="item.prop === 'loginName'" class="text-center">
                                        {{ scope.row.loginName }}
                                    </div>
                                    <div v-if="item.prop === 'orgName'" class="text-center">
                                        {{ scope.row.orgName }}
                                    </div>
                                </template>
                            </el-table-column>
                        </template>

                        <!-- 只有在非查看模式下才显示删除操作 -->
                        <el-table-column v-if="popupType !== 'view'" label="操作" align="center">
                            <template #default="scope">
                                <el-button size="mini" type="text" icon="Delete" @click="handleDelete(scope.row)" />
                            </template>
                        </el-table-column>
                    </el-table>
                </el-card>
            </Pane>


        </Splitpanes>

    </div>
</template>
