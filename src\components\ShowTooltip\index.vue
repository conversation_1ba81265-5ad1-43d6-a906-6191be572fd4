<template>
    <el-tooltip
      effect="dark"
      :content="props.content"
      placement="top"
      :disabled="isShow"
    >
      <div class="content" :style="{width: props.width}" @mouseover="isShowTooltip">
        <span ref="contentRef">
          <slot name="content">{{props.content}}</slot>
        </span>
      </div>
    </el-tooltip>
  </template>
  <script setup lang="ts">
    import { ref } from 'vue'
    // 定义props的类型
    interface props {
      content: string,
      width: string
    }
    // 使用withDefaults来给props赋默认值
    const props = withDefaults(defineProps<props>(), {
      content: '',
      width: ''
    })
    // 使用isShow来控制tooltip是否显示
    let isShow = ref<boolean>(true)
    // 在span标签上定义一个ref
    const contentRef  = ref()
    const isShowTooltip = function (): void {
      // 计算span标签的offsetWidth与盒子元素的offsetWidth，给isShow赋值
      if(contentRef.value.parentNode.offsetWidth > contentRef.value.offsetWidth) {
        isShow.value = true
      } else {
        isShow.value = false
      }
    }
  </script>
  <style>
    .content {
      overflow: hidden; 
      white-space: nowrap;
      text-overflow: ellipsis
    }
  </style>