import request from '@/utils/request'
import { apiUrl } from '@/utils/config'; 



export class screenIndex {

    // 查询异常调整列表
    static getAdjustList(data) {
      return request({
        url: `pay${apiUrl}/pay/adjust/list`,
        method: 'post',
        data
      })
    }

    // 获取异常调整详细信息
    static getAdjustInfo(data) {
      return request({
        url: `pay${apiUrl}/pay/adjust/getInfo`,
        method: 'post',
        data
      })
    }

    // 根据redis锁获取导入结果
    static getImportResult(data) {
      return request({
        url: `pay${apiUrl}/pay/adjust/getImportResult`,
        method: 'post',
        data
      })
    }

    // 根据redis锁获取统计结果
    static getStatisticResult(data) {
      return request({
        url: `pay${apiUrl}/pay/adjust/getStatisticResult`,
        method: 'post',
        data
      })
    }

    // 获取异常调整导入校验成功的数据
    static importDataList(data) {
      return request({
        url: `pay${apiUrl}/pay/adjust/importDataList`,
        method: 'post',
        data
      })
    }

    // 查询供应商树
    static getProviderTree(data) {
      return request({
        url: `pay${apiUrl}/pay/adjust/providerTree`,
        method: 'post',
        data
      })
    }

    // 查询支付场景树
    static getPaySceneTree(data) {
      return request({
        url: `pay${apiUrl}/pay/adjust/paySceneTree`,
        method: 'post',
        data
      })
    }

    // 查询支付类型树
    static getPayTypeTree(data) {
      return request({
        url: `pay${apiUrl}/pay/adjust/payTypeTree`,
        method: 'post',
        data
      })
    }

    // 查询账户树
    static getAccountTree(data) {
      return request({
        url: `pay${apiUrl}/pay/adjust/accountTree`,
        method: 'post',
        data
      })
    }

    // 查询内部人员信息
    static selectUserList(data) {
      return request({
        url: `pay${apiUrl}/pay/adjust/selectUserList`,
        method: 'post',
        data
      })
    }
    // 批量新增异常调整
    static batchAdd(data) {
      return request({
        url: `pay${apiUrl}/pay/adjust/batchAdd`,
        method: 'post',
        data
      })
    }
    // 批量新增导入的异常调整
    static batchImportAdd(data) {
      return request({
        url: `pay${apiUrl}/pay/adjust/batchImportAdd`,
        method: 'post',
        data
      })
    }

}