<!-- 账户明细 -->
<template>
  <div class="container-table-box">
    <Splitpanes class="default-theme">
      <Pane :size="100" :min-size="65">
        <el-card class="dep-card">
          <dialog-search @getList="getList" formRowNumber="4" :columns="tabelForm.columns" :isShowRightBtn="$checkPermi(['pay:accountDetails:list'])">
            <template v-slot:formList>
              <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="75px">
          
                <el-form-item label="模糊搜索">
                  <el-input v-model="queryParams.staffName" placeholder="请输入人员姓名、账号进行搜索" clearable></el-input>
                </el-form-item>
                <el-form-item label="租户">
                <TenantSelect v-model="queryParams.tenantId"  :key="resetKey"></TenantSelect>
              </el-form-item>
                <el-form-item label="流水编号">
                  <el-input v-model="queryParams.flowsCode" placeholder="请输入流水编号" clearable></el-input>
                </el-form-item>
                <!-- <el-form-item label="登录账户">
                  <el-input v-model="queryParams.loginName" placeholder="请输入登录账户" clearable></el-input>
                </el-form-item> -->
                <el-form-item label="账户名称">
                  <el-input v-model="queryParams.accountName" placeholder="请输入账户名称" clearable></el-input>
                </el-form-item>

                <el-form-item label="账户编码">
                  <el-input v-model="queryParams.accountCode" placeholder="请输入账户编码" clearable></el-input>
                </el-form-item>


                <el-form-item label="流水类型" prop="flowType">
                  <el-select v-model="queryParams.flowType" placeholder="请选择流水类型" clearable>
                    <el-option v-for="(item, index) of flow_type" :key="index" :label="item.label"
                      :value="item.value" />
                  </el-select>
                </el-form-item>

                <el-form-item label="支付时间" >
                  <el-date-picker v-model="daySelects"  type="datetimerange" placeholder="选择支付开始日期"
                    format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    range-separator="至"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间" clearable />
                </el-form-item>

                <!-- <el-form-item label="" prop="endTime">
                  <el-date-picker v-model="queryParams.endTime" type="date" placeholder="选择支付结束日期"
                    value-format="YYYY-MM-DD" clearable />
                </el-form-item> -->


              </el-form>
            </template>
            <template v-slot:searchList>
              <el-button type="primary" icon="Search" @click="handleQuery" v-hasPermi="['pay:accountDetails:list']">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery" v-hasPermi="['pay:accountDetails:list']">重置</el-button>
            </template>

            <template v-slot:searchBtnList>

              <el-button type="primary" size="mini" icon="Download" @click="handleExport" v-hasPermi="['pay:accountDetails:export']">导出 </el-button>
            </template>
          </dialog-search>

          <public-table ref="publictable" :rowKey="tabelForm.tableKey" :tableData="list" :columns="tabelForm.columns"
            :configFlag="tabelForm.tableConfig" :pageValue="pageParams" :total="total" :getList="getList"   @clickTextDetail="clickTextDetail">

          </public-table>
        </el-card>
      </Pane>
    </Splitpanes>

    <DialogBox :visible="diaWindow.open1" :dialogWidth="diaWindow.dialogWidth"
      :dialogFooterBtn="diaWindow.dialogFooterBtn"  @close="close"
      :dialogTitle="diaWindow.headerTitle" :dialogTop="diaWindow.dialogTop">
      <template #content>
    <faceInfo v-if="diaWindow.popupType == 'faceInfo'"   :rowData="diaWindow.rowData" ></faceInfo>
    </template>
    </DialogBox>
  </div>
</template>

<script setup name="accountDetail">
import { ElMessage, ElMessageBox } from "element-plus";
import { ref, reactive, getCurrentInstance } from "vue";
import { screenIndex } from "@/api/paymentCenter/account-detail/index";
import { apiUrl } from "@/utils/config";
import useUserStore from "@/store/modules/user";
import { formatMinuteTime } from "@/utils";
const { proxy } = getCurrentInstance();
const { flow_type } = proxy.useDict(
  "flow_type",
);

const userStore = useUserStore();
//组件挂载时保存初始的租户ID
const defaultTenantId = ref(userStore.userInfo.tenantId);
const resetKey = ref(0)  // 添加重置标识
// 表格数据
const diaWindow = reactive({
  open1: false,
  headerTitle: "",
  popupType: "",
  rowData: "",
  dialogWidth: "30%",
  dialogFooterBtn: false,
  uoloadStatus: '',
  dialogTop:''
});

const pageParams = ref({
  pageNum: 1,
  pageSize: 10,
});
const queryParams = ref({
  tenantId: userStore.userInfo.tenantId
});
const daySelects = ref([]);
const list = ref([{}]);
const total = ref(0);
const tabelForm = reactive({
  tableKey: "1", //表格key值
  isShowRightToolbar: true, //是否显示右侧显示和隐藏
  showSearch: true,
  // 表格表格数据
  columns: [
    {
      fieldIndex: "flowsCode", // 对应列内容的字段名
      label: "流水编号", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "160", //最小宽度%
      width: "", //宽度
      align: "left", //表格对齐方式
    },
    {
      fieldIndex: "staffName", // 对应列内容的字段名
      label: "员工姓名", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "120", //最小宽度%
      width: "", //宽度
       type:'clickText'
    },
    {
      fieldIndex: "loginName", // 对应列内容的字段名
      label: "登录账户", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "120", //最小宽度%
      width: "", //宽度
       align: "left", //表格对齐方式
    },


    {
      fieldIndex: "accountName", // 对应列内容的字段名
      label: "账户名称", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "150", //最小宽度%
      align: "left",
      width: "", //宽度
    },

    {
      fieldIndex: "accountCode", // 对应列内容的字段名
      label: "账户编码", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "120", //最小宽度%
      align: "",
      width: "", //宽度
    },

    {
      fieldIndex: "flowType", // 对应列内容的字段名
      label: "流水类型", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "120", //最小宽度%
      width: "", //宽度
      type: "dict",
      dictList: flow_type,
    },

    {
      fieldIndex: "balance", // 对应列内容的字段名
      label: "变动前余额(元)", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "140", //最小宽度%
      width: "", //宽度
      type: "dollor",
    },

    {
      fieldIndex: "spendingBalance", // 对应列内容的字段名
      label: "变动金额(元)", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "130", //最小宽度%
      width: "", //宽度
      type: "dollor",
    },
    {
      fieldIndex: "amount", // 对应列内容的字段名
      label: "账户余额(元)", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "140", //最小宽度%
      width: "", //宽度
      type: "dollor",
    },
    {
      fieldIndex: "paymentTime", // 对应列内容的字段名
      label: "支付时间", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "150", //最小宽度%
      width: "", //宽度
    },

    // {
    //   fieldIndex: "operator", // 对应列内容的字段名
    //   label: "操作人", // 显示的标题
    //   resizable: true, // 对应列是否可以通过拖动改变宽度
    //   visible: true, // 展示与隐藏
    //   sortable: true, // 对应列是否可以排序
    //   fixed: "", //固定
    //   minWidth: "120", //最小宽度%
    //   width: "", //宽度
    // },

  ],
  // 表格基础配置项，看个人需求添加
  tableConfig: {
    needPage: true, // 是否需要分页
    index: true, // 是否需要序号
    selection: false, // 是否需要多选
    reserveSelection: false, // 是否需要支持跨页选择
    indexFixed: false, // 序号列固定 left true right
    selectionFixed: false, //多选列固定left true right
    indexWidth: "50", //序号列宽度
    loading: false, //表格loading
    showSummary: false, //是否开启合计
    height: null, //表格固定高度 比如：300px
  },
});

/** 查询列表 */
const getList = () => {
  tabelForm.tableConfig.loading = true;
      const startTime= daySelects.value[0];
      const endTime= daySelects.value[1];
      //除了本身绑定的查询参数,额外添加时间格式化参数
      queryParams.value = {
        ...queryParams.value,
        startTime: startTime,
        endTime: endTime,
      };
  screenIndex.payFlowpageList(queryParams.value, pageParams.value).then((response) => {
    list.value = response.data.records;
    total.value = response.data.total;
    tabelForm.tableConfig.loading = false;
  });
};

/** 搜索按钮操作 */
const handleQuery = () => {
  pageParams.value.pageNum = 1;
  getList();
};
/** 重置按钮操作 */
const resetQuery = () => {
  proxy.resetForm("queryForm");
  queryParams.value = {};
daySelects.value = [];
//设置当前租户为默认值
   queryParams.value.tenantId = defaultTenantId.value;
  resetKey.value++  // 改变 key 强制组件重建
  handleQuery();
};

/** 导出按钮操作 */

const handleExport = () => {
  proxy.download(
    `pay${apiUrl}/pay/payAccountFinancialFlows/export`,
    {
      ...queryParams.value,
    },
    `账户明细列表_${formatMinuteTime(new Date())}.xlsx`
  );
};
/** 关闭弹窗 */
const close = (val) => {
  diaWindow.open1 = val;
};
// 人员信息
const clickTextDetail = (row) => {
  diaWindow.headerTitle = "人员信息";
  diaWindow.popupType = "faceInfo";
  diaWindow.rowData = row; // 如果需要传递数据到弹窗
  diaWindow.dialogFooterBtn = false;
  diaWindow.dialogWidth = "530px";
  diaWindow.dialogTop = '18vh'
  
  diaWindow.open1 = true;
};
onMounted(() => {
  getList();
});
</script>

<style scoped>

</style>