import request from '@/utils/request'
import { apiUrl } from '@/utils/config'; 



export class screenIndex {
//添加
  static insert(data) {
    return request({
      url: `pay${apiUrl}/pay/payProvider/insert`,
      method: 'post',
      data:data
    })
  }

//   列表

  static pageList(param,data) {
    return request({
      url: `pay${apiUrl}/pay/payProvider/pageList`,
      method: 'post',
      data: {...param,...data}
    })
  }
//   编辑

static update(data) {
    return request({
      url: `pay${apiUrl}/pay/payProvider/update`,
      method: 'post',
      data:data
    })
  }


  // 修改供应商状态
  static updateState(data) {
    return request({
      url: `pay${apiUrl}/pay/payProvider/updateState`,
      method: 'post',
      data:data
    }) 
  }


//   删除


static delete(data) {
    return request({
      url: `pay${apiUrl}/pay/payProvider/delete`,
      method: 'post',
      data:data
    })
  }

  // 人员选择

  static selectUserList(data) {
    return request({
      url: `pay${apiUrl}/pay/payAccountList/selectUserList`,
      method: 'post',
      data:data
    })
  }

  // 适用账户

  static PayAccountManagePageList(data) {
    return request({
      url: `pay${apiUrl}/pay/payProvider/accountTree`,
      method: 'post',
      data:data
    })
  }
}
