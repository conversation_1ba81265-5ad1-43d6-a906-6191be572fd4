<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import modal from '@/plugins/modal'
//   import { deleteDeptArea, deptAreaTree } from '@/api/admittance/accredit'
//   import SvgIcon from '@/components/SvgIcon/index.vue'

const props = defineProps({
  closeBtn: {
    type: Function,
    default: null
  },
  popupType: {
    type: String,
    default: ''
  },
  rowData: {
    type: Object,
    default: () => ({})
  }
})

const tree = ref()
const treeData = ref([
  {
    id: 1,
    label: '山东省公司',
    bak: '0',
    children: [
      {
        id: 2,
        label: '济南分公司',
        bak: '1',
        children: [
          {
            id: 3,
            label: '历下区',
            bak: null,
            children: []
          },
          {
            id: 4,
            label: '市中区',
            bak: null,
            children: []
          }
        ]
      },
      {
        id: 5,
        label: '青岛分公司',
        bak: '0',
        children: []
      }
    ]
  }
])
const loading = ref(false)
const deptName = ref('')
const formData = reactive({
  deptId: '',
  areaId: ''
})

const defaultProps = {
  children: 'children',
  label: 'label',
}

const filterNode = (value, data) => {
  if (!value) return true
  return data.label && data.label.indexOf(value) !== -1
}

const getList = async () => {
  loading.value = true
  formData.deptId = props.rowData.deptId
  deptName.value = props.rowData.deptName
  try {
    const res = await deptAreaTree(formData)
    treeData.value = res.data
  } finally {
    loading.value = false
  }
}

const delAccredit = (id) => {
  modal.confirm('确认要取消授权吗？').then(() => {
    formData.areaId = id
    deleteDeptArea(formData).then((res) => {
      if (res.code == 200) {
        ElMessage.success('取消成功')
        formData.areaId = ''
        getList()
      } else {
        ElMessage.error(res.msg)
      }
    })
  }).catch(() => { })
}

onMounted(() => {
  getList()
})
</script>
<!--部门权限展示 -->
<template>
  <Splitpanes class="default-theme">
    <Pane :size="50" :min-size="50">
      <el-card class="dep-card" style="height: 100%">
        <div class="top-inofs">
          <div class="infos-one">
            <svg-icon icon-class="tree" />
            <span class="infos-text">
              {{ deptName || '内部支撑部' }}</span>
          </div>
        </div>
        <el-scrollbar style="height: 450px">
          <!--区域树-->
          <el-row>
            <el-col :span="24">
              <!--  需要多选框就加上多选属性 看部门-->
              <el-tree ref="tree" :data="treeData" default-expand-all check-strictly node-key="id" :props="defaultProps"
                :filter-node-method="filterNode">
                <template #default="{ node, data }">
                  <span>
                    <span class="tag_text">
                      {{ data.label }}
                    </span>
                    <el-tag v-if="data.bak == '0'" size="small" type="error">部门授权</el-tag>
                    <el-tag v-if="data.bak == '1'" size="small" type="warning">人员授权</el-tag>
                    <span v-if="data.bak != null" class="button-right">
                      <el-button size="mini" type="text" title="取消授权" icon="Delete" @click.stop="delAccredit(data.id)">
                      </el-button></span>
                  </span>
                </template>
              </el-tree>
            </el-col>
          </el-row>

        </el-scrollbar>

      </el-card>
    </Pane>
  </Splitpanes>


</template>
<style scoped lang="scss">
// ... existing code ...
.top-inofs {
  width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px 5px 24px;
  margin-bottom: 16px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #3b82f6, #8b5cf6, #06b6d4);
    border-radius: 12px 12px 0 0;
  }

  .infos-one {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 16px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 8px;
    border: 1px solid rgba(59, 130, 246, 0.1);
    transition: all 0.3s ease;
    margin-bottom: 12px;
    width: calc((100% - 12px) / 2);
    margin-right: 12px;

    &:nth-child(2n) {
      margin-right: 0px;
    }

    &:hover {
      background: rgba(255, 255, 255, 0.95);
      border-color: rgba(59, 130, 246, 0.2);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
    }

    .svg-icon {
      color: #3b82f6;
      font-size: 18px;
      filter: drop-shadow(0 1px 2px rgba(59, 130, 246, 0.2));
    }

    .infos-text {
      font-weight: 600;
      font-size: 16px;
      color: #1e293b;
      letter-spacing: 0.025em;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
      position: relative;

      &::after {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 0;
        height: 2px;
        background: linear-gradient(90deg, #3b82f6, #8b5cf6);
        transition: width 0.3s ease;
      }

      &:hover::after {
        width: 100%;
      }
    }
  }
}

.button-right {
  display: inline-flex;
  align-items: center;
  margin-left: 12px;

  .el-button {
    transition: all 0.2s ease;

    &:hover {
      transform: scale(1.05);
      color: #ef4444;
    }
  }
}

.tag_text {
  padding-right: 12px;
  font-weight: 500;
  color: #374151;
  transition: color 0.2s ease;

  &:hover {
    color: #1f2937;
  }
}

// 优化树形组件的样式
:deep(.el-tree) {
  .el-tree-node__content {
    padding: 8px 12px;
    border-radius: 6px;
    margin: 2px 0;
    transition: all 0.2s ease;

    &:hover {
      background-color: #f8fafc;
      transform: translateX(4px);
    }
  }

  .el-tree-node.is-current>.el-tree-node__content {
    background-color: #dbeafe;
    color: #1e40af;
    font-weight: 500;
  }
}

// 优化标签样式
:deep(.el-tag) {
  border-radius: 6px;
  font-weight: 500;
  letter-spacing: 0.025em;
  transition: all 0.2s ease;

  &.el-tag--error {
    background: linear-gradient(135deg, #fef2f2, #fee2e2);
    border-color: #fecaca;
    color: #dc2626;

    &:hover {
      background: linear-gradient(135deg, #fee2e2, #fecaca);
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(220, 38, 38, 0.2);
    }
  }

  &.el-tag--warning {
    background: linear-gradient(135deg, #fffbeb, #fef3c7);
    border-color: #fde68a;
    color: #d97706;

    &:hover {
      background: linear-gradient(135deg, #fef3c7, #fde68a);
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(217, 119, 6, 0.2);
    }
  }
}

// 卡片样式优化
.dep-card {
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  overflow: hidden;

  :deep(.el-card__body) {
    padding: 0;
  }
}

// 滚动条样式优化
:deep(.el-scrollbar) {
  .el-scrollbar__bar {
    .el-scrollbar__thumb {
      background: linear-gradient(135deg, #cbd5e1, #94a3b8);
      border-radius: 4px;

      &:hover {
        background: linear-gradient(135deg, #94a3b8, #64748b);
      }
    }
  }
}
</style>