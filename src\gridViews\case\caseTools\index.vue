<template>
  <el-card class="line-card" style="border: 0" shadow="never">
    <div class="home-card-body">
      <div class="backlog">
        <img src="../../../assets/images/pageDesign/case/caseTools1.png" alt="" />
        <div class="item-title">待办工作</div>
      </div>

      <div class="warn">
        <img src="../../../assets/images/pageDesign/case/caseTools2.png" alt="" />
        <div class="item-title">预警工作</div>
      </div>

      <div class="done">
        <img src="../../../assets/images/pageDesign/case/caseTools3.png" alt="" />
        <div class="item-title">已办工作</div>
      </div>

      <div class="examine">
        <img src="../../../assets/images/pageDesign/case/caseTools4.png" alt="" />
        <div class="item-title">公文审批</div>
      </div>
    </div>
  </el-card>
</template>

<script setup name="CaseTools">

</script>

<style lang="scss" scoped>
.line-card {
  width: 100%;
  border-radius: 0px;
  height: 130px;
  border: 0px;
  .home-card-body {
    width: 100%;
    display: flex;
    justify-content: space-between;
    .backlog {
      width: 20%;
      height: 103px;
      background-image: url("../../../assets/images/pageDesign/case/caseTools01.png");
      background-size: 100% 100%;
      display: flex;
      align-items: center;
      img {
        width: 72px;
        height: 72px;
        display: block;
        margin-left: 28px;
      }
      .item-title {
        font-size: 20px;
        font-family: Microsoft YaHei;
        font-weight: 600;
        color: #646464;
        margin-left: 35px;
      }
    }
    .warn {
      width: 20%;
      height: 103px;
      background-image: url("../../../assets/images/pageDesign/case/caseTools02.png");
      background-size: 100% 100%;
      display: flex;
      align-items: center;
      img {
        width: 72px;
        height: 72px;
        display: block;
        margin-left: 28px;
      }
      .item-title {
        font-size: 20px;
        font-family: Microsoft YaHei;
        font-weight: 600;
        color: #646464;
        margin-left: 35px;
      }
    }
    .done {
      width: 20%;
      height: 103px;
      background-image: url("../../../assets/images/pageDesign/case/caseTools03.png");
      background-size: 100% 100%;
      display: flex;
      align-items: center;
      img {
        width: 72px;
        height: 72px;
        display: block;
        margin-left: 28px;
      }
      .item-title {
        font-size: 20px;
        font-family: Microsoft YaHei;
        font-weight: 600;
        color: #646464;
        margin-left: 35px;
      }
    }
    .examine {
      width: 20%;
      height: 103px;
      background-image: url("../../../assets/images/pageDesign/case/caseTools04.png");
      background-size: 100% 100%;
      display: flex;
      align-items: center;
      img {
        width: 72px;
        height: 72px;
        display: block;
        margin-left: 28px;
      }
      .item-title {
        font-size: 20px;
        font-family: Microsoft YaHei;
        font-weight: 600;
        color: #646464;
        margin-left: 35px;
      }
    }
  }
  :deep(.el-card__body) {
    padding: 0;
  }
}
</style>
