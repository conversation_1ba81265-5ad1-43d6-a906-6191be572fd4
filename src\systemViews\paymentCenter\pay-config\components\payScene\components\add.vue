<!-- 新增账户设置 -->

<template>
  <div class="dialog-box" :class="popupType === 'view' ? 'dialog-box-view' : 'dialog-box-edit'">
    <el-form ref="formRef" :model="formData" label-width="125px" :rules="popupType !== 'view' ? rules : ''">
      <el-row>
        <el-col :span="12">
          <el-form-item label="场景名称" prop="sceneName">
            <!-- 添加或编辑时显示输入框 -->

            <el-input v-model="formData.sceneName" :disabled="popupType === 'view'" placeholder="请输入场景名称" clearable
              v-if="popupType !== 'view'" />

            <!-- 查看时显示文本 -->

            <div v-else>
              {{ formData.sceneName || "" }}
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="场景编码" prop="sceneCode">
            <el-input v-model="formData.sceneCode" :disabled="popupType === 'view'" placeholder="请输入场景编码" clearable
              v-if="popupType !== 'view'" />

            <div v-else>
              {{ formData.sceneCode || "" }}
            </div>
          </el-form-item>
        </el-col>

       

        <el-col :span="12">
          <el-form-item label="场景顺序" prop="sceneSort">
            <!-- 添加或编辑时显示选择器 -->
            <NumberInput v-if="popupType !== 'view'" v-model="formData.sceneSort" customPlaceholder="请输入场景顺序"
              input-type="integer" />

            <!-- 查看时显示文本 -->

            <div v-else>
              {{ formData.sceneSort }}
            </div>
          </el-form-item>
        </el-col>

     

        <el-col :span="12">
          <el-form-item label="场景状态" prop="status">
            <!-- 添加或编辑时显示单选按钮 -->

            <el-radio-group v-model="formData.status" v-if="popupType !== 'view'">
              <el-radio-button :label="item.value" v-for="(item, index) of scene_status"
                :key="index">{{ item.label }}</el-radio-button>
            </el-radio-group>

            <!-- 查看时显示文本 -->

            <span class="dialog-text" v-else>
              {{ $formatDictLabel(formData.status, scene_status) || "" }}
            </span>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="供应商" prop="providerId">
            <!-- 添加或编辑时显示选择器 -->
            <el-select v-model="formData.providerId" :disabled="popupType === 'view'"  filterable placeholder="请选择供应商"
              v-if="popupType !== 'view'">
              <el-option v-for="(item, index) in supplierIdsList" :key="index" :label="item.providerName"
                :value="item.id" />
            </el-select>

            <!-- 查看时显示文本 -->

            <div v-else>
              {{
                formData.providerName
              }}
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="管理员" prop="adminId">


            <el-select v-if="popupType !== 'view'" v-model="administratorsIds" multiple placeholder="请输入人员进行选择"
              clearable @change="changeUser" filterable remote reserve-keyword :remote-method="getUserList">
              <el-option v-for="(item, index) in administratorsIdsList" :key="index" :label="item.staffName"
                :value="item.staffId">
                <div>
                  {{
                    item.staffName +
                    "(" +
                    item.orgName +
                    "/)" 
                  }}
                </div>
              </el-option>
            </el-select>
            <div v-else>
              {{ formData.adminName || "" }}
            </div>
          </el-form-item>
        </el-col>





       
      </el-row>


    </el-form>


  </div>
</template>



<script setup>
import { ref, reactive, watch, getCurrentInstance, defineProps } from "vue";

import { ElMessage } from "element-plus";
import useUserStore from "@/store/modules/user";

import {
    screenIndex
  } from "@/api/paymentCenter/payScene/index";


// 定义组件 props

const props = defineProps({
  closeBtn: {
    type: Function,

    default: null,
  },

  popupType: {
    type: String,

    default: "",
  },

  rowData: {
    type: Object,
    default: () => ({}),
  },
});

// 字典
const { proxy } = getCurrentInstance();
const { scene_status, } = proxy.useDict(
  "scene_status",
);


// 账户类型

const accountTypeList = ref([])

const supplierIdsList = ref([])
const administratorsIdsList = ref([])
const tenantIdsList = ref([])
const userStore = useUserStore();
const administratorsIds = ref([])
// 定义 emits
const emit = defineEmits(["closeBtn"]);

// 定义状态

let formData = ref({
  providerId: '',
  adminId: '',
  tenantId: userStore.userInfo.tenantId,
  status: '0',
});

// 定义校验规则

const rules = reactive({
  sceneName: [
    { required: true, message: '请输入场景名称', trigger: ['blur', 'change'] }
  ],
  sceneCode: [
    {
      required: true, message: '请输入场景编码', trigger: ['blur', 'change'],
    },

    {
      pattern: /^[\x21-\x7E]+$/, // 允许所有可打印的 ASCII 字符（不包含空格）
      message: "场景编码只能包含字母、数字和特殊符号",
      trigger: ["blur", "change"]
    }
  ],

  sceneSort: [
    { required: true, message: '请输入场景顺序', trigger: 'blur' },
  ],
  status: [
    { required: true, message: '请选择场景状态', trigger: 'change' }
  ],

})

// 表单引用

const formRef = ref(null);



// 定义方法


const payAccountTypePageList = async () => {
  const res = await screenIndex.payAccountTypePageList({
    status:'1'
  });
  accountTypeList.value = res.data

};

const payProviderPageList = async () => {
  const res = await screenIndex.payProviderPageList({});
  supplierIdsList.value = res.data

};

const juniorList = async () => {
  const res = await screenIndex.juniorList({});
  tenantIdsList.value = res.data

};
const getUserList = async (name) => {
  if (!name || name.trim().length < 2) {
    administratorsIdsList.value = []; // 立即清空列表
    return;
  }
  
  try {
    const res = await screenIndex.selectUserList({
      staffName: name,
    });
    administratorsIdsList.value = res.data;
  } catch (error) {
    console.error("获取用户列表失败:", error);
  }
};

// 选择事件（根据需要补充逻辑）
const changeUser = (selectedIds) => {
  console.log("已选择的ID:", selectedIds);
  // 可以在这里添加选择变化后的处理逻辑

  // formData.administratorsIds = selectedIds.map(id => ({
  //   administratorsId: id
  // }))

};
const initData = async () => {
  if (props.popupType === "add") {
  } else {
    const res = await screenIndex.paySceneGetInfo({ id: props.rowData.id });
    formData.value = props.rowData
    if (props.rowData.adminName) {
      administratorsIds.value = props.rowData.adminId.split(',')

      const idArray = props.rowData.adminId.split(',').filter(Boolean);
      const nameArray = props.rowData.adminName.split(',').filter(Boolean);

      // 同步补充选项列表数据
      idArray.forEach((id, index) => {
        if (!administratorsIdsList.value.some(u => u.staffId === id)) {
          administratorsIdsList.value.push({
            staffId: id,
            staffName: nameArray[index] || ``,
          });
        }
      });



    
    }




  }

};

const saveForm = async () => {
  try {
    await formRef.value.validate();
    const requestData = {
      sceneName: formData.value.sceneName,
      sceneCode: formData.value.sceneCode,
      status: formData.value.status,
      sceneSort: formData.value.sceneSort,
      providerId: formData.value.providerId,
      adminId: administratorsIds.value.join(','),
      tenantId: userStore.userInfo.tenantId,
    };
    if (props.popupType === "edit") {
      requestData.id = formData.value.id;
      const res = await screenIndex.paySceneEdit(requestData);

      if (res.code == "1") {
        ElMessage.success("修改成功");

        emit("closeBtn");
      }
    } else if (props.popupType === "add") {
      const res = await screenIndex.paySceneAdd(requestData);

      if (res.code == "1") {
        ElMessage.success("新增成功");

        emit("closeBtn");
      }
    }
  } catch (error) {
    console.error("表单校验失败:", error);
  }
};



// 初始化数据

onMounted(() => {
  payProviderPageList()
  payAccountTypePageList()
  juniorList()
  getUserList()
  initData();

});

defineExpose({
  saveForm,
});
</script>



<style scoped lang="scss"></style>