<!-- 新增账号设置 -->

<template>
  <div
    class="dialog-box"
    :class="popupType === 'view' ? 'dialog-box-view' : 'dialog-box-edit'"
  >

    <el-form
      ref="formRef"
      :model="formData"
      label-width="80px"
      :rules="rules"
     
    >
      <el-row>
        <el-col :span="24">
          <el-form-item label="人员账号" prop="loginName">
            <!-- 添加或编辑时显示输入框 -->
            <el-input
              v-model="formData.loginName"
              :disabled="popupType === 'view'"
              placeholder="请输入人员账号"
              clearable
              v-if="popupType !== 'view'"
            />
            <!-- 查看时显示文本 -->
            <div v-else>
              {{ formData.loginName || "" }}
            </div>
          </el-form-item>
        </el-col>

     


      </el-row>


    </el-form>

  
  </div>
</template>
  
    
  
  <script setup>
import { ref, reactive, watch, getCurrentInstance, defineProps } from "vue";

import { ElMessage } from "element-plus";


// 定义组件 props

const props = defineProps({
  closeBtn: {
    type: Function,

    default: null,
  },

  popupType: {
    type: String,

    default: "",
  },

  rowData: {
    type: Object,
    default: () => ({}),
  },
});

// 表单引用
const formRef = ref(null);

// 定义 emits
const emit = defineEmits(["closeBtn"]);

// 定义状态

let formData = reactive({});

// 定义校验规则

const rules = reactive({
  loginName: [
    { required: true, message: '请输入人员账号', trigger: ['blur', 'change'] }
  ],
 
  
})





// 定义方法


const initData = async () => {
   Object.assign(formData,props.rowData) 
};



const saveForm = async () => {
  try {
    await formRef.value.validate();
    emit("closeBtn",formData);
  } catch (error) {
    console.error("表单校验失败:", error);
  }
};



// 初始化数据

onMounted(() => {
  initData();
});

defineExpose({
  saveForm,
});
</script>
  
    
  
    <style scoped lang="scss">
</style>