<!-- 出入口管理 -->

<template>
  <div class="app-container">
    <Splitpanes class="default-theme">
      <Pane :size="100" :min-size="10">
        <el-card class="dep-card" style="height: 100%">
          <el-table
            class="myTreeLazyTable2"
            ref="publictable"
            v-loading="loading"
            :data="list"
          >
          <el-table-column type="index" width="40" label="#"  />
            <el-table-column prop="dataName" label="名称" min-width="180px">
              <template #default="scope">
                    <i
                  class="icon iconfont icon-ic_org"
                  v-if="scope.row.dataType == '0'"
                ></i>
                <i
                  class="icon iconfont icon-fl-renyuan"
                  v-if="scope.row.dataType == '1'"
                ></i>
                <span class="left-label">
                  {{ scope.row.ancestorsName
                  }}<span v-if="scope.row.dataType == '1'"
                    >/{{ scope.row.dataName + "/" + scope.row.mobile }}</span
                  >
                </span>
           
              </template>
            </el-table-column>

            <el-table-column class="lazy-line" minWidth="10">
              <template #default="scope">
                <div class="test"></div>
              </template>
            </el-table-column>

            <el-table-column
              prop="dataSysName"
              label="名称"
              min-width="180px"
              align="left"
            >
              <template #default="scope">
                <div class="flex">
                    <i
                  class="icon iconfont icon-ic_org"
                  v-if="scope.row.dataType == '0'"
                ></i>
                <i
                  class="icon iconfont icon-fl-renyuan"
                  v-if="scope.row.dataType == '1'"
                ></i>
                <span class="left-label" v-if="scope.row.sysAncestorsName ">
                  {{ scope.row.sysAncestorsName }}
                  <span v-if="scope.row.dataType == '1'"
                    >/{{
                      scope.row.dataSysName + "/" + scope.row.dataSysMobile
                    }}</span
                  ></span
                >
                </div>
              
              </template>
            </el-table-column>

            <el-table-column
              label="人员账号"
              align="center"
              min-width="100"
              header-align="center"
            >
              <template #default="scope">
                <el-input
                  v-model="scope.row.loginName"
                  placeholder="请输入人员账号"
                  clearable
                />
              </template>
            </el-table-column>


            <!-- <el-table-column
              label="岗位类型"
              align="center"
              min-width="150px"
              header-align="center"
            >
              <template #default="scope">
                <el-radio-group
                v-model="scope.row.staffOrgType"
              >

                <el-radio-button
                  v-for="(dict,index) in sync_staff_org_type"
                  :disabled="!scope.row.sysId && dict.value!='F'"
                  :key="dict.value"
                  :label="dict.value"
                  >{{ dict.label }}</el-radio-button
                >
              </el-radio-group>
              </template>
            </el-table-column> -->


            
            <el-table-column
              label="操作"
              min-width="60"
              align="center"
              header-align="center"
            >
              <template #default="scope">
                <el-button
                  plain
                  size="mini"
                  type="primary"
                  @click="syncDataToSystemFun(scope.row)"
                >
                  保存
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination v-show=" total > 0" :total="total"
              v-model:page="pageParams.pageNum" v-model:limit="pageParams.pageSize" @pagination="getList" />
        </el-card>
      </Pane>
    </Splitpanes>
  </div>
</template>
  
  <script setup name="EntranceExitManagement">
import { ref, reactive, getCurrentInstance } from "vue";
import { ElMessage,ElMessageBox } from "element-plus";
import { syncBatchSystem,syncDataToSystem } from "@/api/system/config";

const { proxy } = getCurrentInstance();
const { sync_staff_org_type } = proxy.useDict("sync_staff_org_type");

// 定义 emits
const emit = defineEmits(["closeBtn"]);
const props = defineProps({
  closeBtn: {
    type: Function,

    default: null,
  },

  popupType: {
    type: String,

    default: "",
  },

  rowData: {
    type: Object,
    default: () => ({}),
  },
});

const pageParams = ref({
  pageNum: 1,
  pageSize: 10,
});

const loading = ref(false);

const list = ref([]);

// 遮罩层
const total = ref(0);

/** 查询用户列表 */
const getList = () => {
  delete props.rowData.pageNum
  delete props.rowData.pageSize
  loading.value = true;
  syncBatchSystem(pageParams.value, props.rowData).then((response) => {
    list.value = response.data.records;
    total.value = response.data.total;
    loading.value = false;
  });
};

/** 搜索按钮操作 */
const handleQuery = () => {
  pageParams.value.pageNum = 1;
  getList();
};
/** 重置按钮操作 */
const resetQuery = () => {
  proxy.resetForm("queryForm");
  handleQuery();
};
const syncDataToSystemFun = async (row) => {
if(!row.loginName){
    ElMessage.error('请输入人员账号');
    return
}

// if(!row.staffOrgType){
//     ElMessage.error('请选择岗位类型');
//     return
// }
  try {
    await ElMessageBox.confirm(`确认要同步【${row.dataName}】吗？`, "提示", {
      type: "warning",
    });

    const result = await syncDataToSystem(row);
    if (result?.success) {
      ElMessage.success(result.message || "同步成功");
      Object.assign(row, result.data); // 合并新数据到当前行
      emit('closeBtn')
    }
  } catch (error) {
    if (error !== "cancel") {
      const errMsg =
        error.response?.data?.message || error.message || "同步失败";
      ElMessage.error(errMsg);
    }
  }
};


getList();
</script>
  
  <style scoped>

:deep(.myTreeLazyTable2) {
  box-shadow: 0 10px 12px rgba(0, 0, 0, 0.1);
}
:deep(.myTreeLazyTable2 tr .el-table__cell:nth-child(3)) {
  position: relative;
  border: none;
}

:deep(.myTreeLazyTable2 tr .el-table__cell:nth-child(3)::after) {
  content: "";
  display: block;
  height: 100%;
  width: 15px;
  background: #fff;
  position: absolute;
  left: 0px;
  top: 50%;
  transform: translate(0, -50%);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

:deep(.myTreeLazyTable2 tr .el-table__cell:nth-child(3)) {
  padding-left: 24px;
}
.icon-ic_org {
  padding: 2px;
  font-size: 14px;
}
.left-label{
    word-break: break-all;
}
:deep(.pagination-container){
    padding-top: 0px!important;
}
</style>