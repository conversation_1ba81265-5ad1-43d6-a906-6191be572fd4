<!--常用功能-->
<template>
  <el-row>
    <el-col :span="24">
      <div class="padding-box-4">
        <div class="big-common-box-one"
        style=" height: 206px;"
        >
          <div class="common-header">
            <div class="left-header-text">常用功能</div>
            <div class="flex-1"/>
          </div>
          <div class="mid-common-content">
            <div class="quick-box-link">
              <el-row v-for="(item,index) of menuList"  :key="index">
                <el-col :span="6" v-for="(item2,index2) of item" :key="index2" @click="goJump(item2.meta.id)">
                  <div class="quick-box-link-one">
                    <img :src="getImageUrl(item2.name)" />
                    <div class="quick-box-link-text">{{ item2.meta.title }}</div>
                  </div>
                </el-col>
              </el-row>
            </div>
          </div>
        </div>
      </div>
    </el-col>
  </el-row>
</template>


<script setup>
import {ref} from "vue";
import {useRouter} from "vue-router";

const router = useRouter();
const menuList = ref([]);
const params = reactive({
  pageNum: 1,
  pageSize: 10,
  noticeTitle: undefined,
  createBy: undefined,
  status: undefined,
});
// const images = import.meta.glob('@/assets/images/home/<USER>/*.png');


const goJump = (type) => {
  if(type=='1'){
    router.push({ path: '/portal/generalManagement/regionManagement' })
  }else if(type=='2'){
    router.push({path: "/portal/car/parking-record"});
  }else if(type=='3'){
    router.push({path: "/portal/car/parking-management"});
  }else{
    router.push({ path: '/portal/car/parkingAccredit' })
  }
}

// 在 setup 顶部添加
const getImageUrl = (name) => {
  // 替换特殊字符
  const fileName = name.replace(/\//g, '-'); // 将 / 替换为 -
  try {
    return new URL(`/src/assets/images/home/<USER>/${fileName}.png`, import.meta.url).href;
  } catch (error) {
    console.error('Error loading image:', error);
    return ''; // 返回默认图片路径或空字符串
  }
};

const menuLists = async () => {
  try {
    const response = {
      "msg": "操作成功",
      "code": 200,
      "data": [
        {
          "name": "ShortcutMenu",
          "path": "/shortcutMenu",
          "hidden": true,
          "redirect": "noRedirect",
          "component": "Layout",
          "alwaysShow": true,
          "meta": {
            "title": "首页快捷菜单",
            "icon": "icon-zhihuiwuye",
            "noCache": false,
            "link": null
          },
          "children": [
            {
              "name": "IndividualReservation",
              "hidden": false,
              "component": "/system/generalManagement/regionManagement",
              "meta": {
                "id":"1",
                "title": "区域管理",
                "icon": "icon-menjinguanli",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "WorkOrder",
              "hidden": false,
              "component": "wisdom/canteen-management/word-order/workOrder/index",
              "meta": {
                "id":"2",
                "title": "停车记录",
                "icon": "icon-zichan1",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "Repair-application",
              "hidden": false,
              "component": "wisdom/property-repair/repair-application/index",
              "meta": {
                "id":"3",
                "title": "停车场管理",
                "icon": "icon-canyinliebiao",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "IndividualReservation",
              "hidden": false,
              "component": "wisdom/canteen-management/individual-reservation/individualReservation/index",
              "meta": {
                "id":"4",
                "title": "车辆授权",
                "icon": "icon-menjinguanli",
                "noCache": false,
                "link": null
              }
            },
          ]
        },
        {
          "name": "/page",
          "path": "//page",
          "hidden": false,
          "redirect": "noRedirect",
          "component": "Layout",
          "alwaysShow": true,
          "meta": {
            "title": "驾驶舱",
            "icon": "icon-jiashicang",
            "noCache": false,
            "link": null
          },
          "children": [
            {
              "name": "Cockpit",
              "path": "cockpit",
              "hidden": false,
              "component": "cockpit/index",
              "meta": {
                "title": "智慧大屏",
                "icon": "chart",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "Digitaltwin",
              "path": "digitaltwin",
              "hidden": false,
              "component": "wisdom/digitaltwin/index.vue",
              "meta": {
                "title": "机房数字孪生",
                "icon": "cascader",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "Http://172.16.11.83:10001/wisdomdigitaltwin",
              "path": "http://172.16.11.83:10001/wisdomdigitaltwin",
              "hidden": false,
              "component": "http://172.16.11.83:10001/wisdomdigitaltwin",
              "meta": {
                "title": "数字孪生",
                "icon": "icon-shangchengguanli",
                "noCache": false,
                "link": "http://172.16.11.83:10001/wisdomdigitaltwin"
              }
            },
            {
              "name": "BigScreen",
              "path": "bigScreen",
              "hidden": false,
              "component": "data/bigScreenHome/index",
              "meta": {
                "title": "产互智慧大屏",
                "icon": "monitor",
                "noCache": true,
                "link": null
              }
            }
          ]
        },
        {
          "name": "Examine",
          "path": "/examine",
          "hidden": false,
          "redirect": "noRedirect",
          "component": "Layout",
          "alwaysShow": true,
          "meta": {
            "title": "综合考评",
            "icon": "build",
            "noCache": false,
            "link": null
          },
          "children": [
            {
              "name": "Structure",
              "path": "structure",
              "hidden": false,
              "component": "wisdom/examine/structure/tabs/index.vue",
              "meta": {
                "title": "组织架构配置",
                "icon": "icon-zuzhijiagou",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "/performanceIndicator",
              "path": "/performanceIndicator",
              "hidden": false,
              "component": "wisdom/examine/performanceIndicator/index.vue",
              "meta": {
                "title": "业绩指标配置",
                "icon": "icon-yejizhibiao1",
                "noCache": true,
                "link": null
              }
            },
            {
              "name": "Effectiveness",
              "path": "effectiveness",
              "hidden": false,
              "component": "wisdom/examine/effectiveness/index.vue",
              "meta": {
                "title": "效能评价指标配置",
                "icon": "icon-xiaoneng",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "Rating-level-setting",
              "path": "rating-level-setting",
              "hidden": false,
              "component": "wisdom/examine/rating-level-setting/index.vue",
              "meta": {
                "title": "评分等级配置",
                "icon": "icon-pingfendengjishezhi",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "FormulaSet",
              "path": "formulaSet",
              "hidden": true,
              "component": "wisdom/examine/formulaSet/index.vue",
              "meta": {
                "title": "公式配置",
                "icon": "icon-gongshipeizhiguanli",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "Auarterly-evaluation",
              "path": "auarterly-evaluation",
              "hidden": true,
              "component": "wisdom/examine/auarterly-evaluation/query/index.vue",
              "meta": {
                "title": "季度考评",
                "icon": "build",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "Dept-evaluation",
              "path": "dept-evaluation",
              "hidden": false,
              "component": "wisdom/examine/dept-evaluation/index.vue",
              "meta": {
                "title": "部门考评",
                "icon": "icon-cz-khdf",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "MiddleLevelLeaders",
              "path": "middleLevelLeaders",
              "hidden": false,
              "redirect": "noRedirect",
              "component": "ParentView",
              "alwaysShow": true,
              "meta": {
                "title": "中层领导考评",
                "icon": "icon-jifenmingxi",
                "noCache": false,
                "link": null
              },
              "children": [
                {
                  "name": "WorkReport",
                  "path": "workReport",
                  "hidden": false,
                  "component": "wisdom/examine/workReport/index.vue",
                  "meta": {
                    "title": "述职报告",
                    "icon": "24gl-fileText",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "Competency-credibility-configuration",
                  "path": "competency-credibility-configuration",
                  "hidden": false,
                  "component": "wisdom/examine/workReport/components/competency-credibility-configuration",
                  "meta": {
                    "title": "胜任度公信度配置",
                    "icon": "icon-qiyedingcan",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "Middle-management-evaluation",
                  "path": "middle-management-evaluation",
                  "hidden": false,
                  "component": "wisdom/examine/workReport/components/middle-management-evaluation",
                  "meta": {
                    "title": "中层领导考评",
                    "icon": "tab",
                    "noCache": false,
                    "link": null
                  }
                }
              ]
            }
          ]
        },
        {
          "name": "Park",
          "path": "/park",
          "hidden": false,
          "redirect": "noRedirect",
          "component": "Layout",
          "alwaysShow": true,
          "meta": {
            "title": "车辆管理",
            "icon": "time",
            "noCache": false,
            "link": null
          },
          "children": [
            {
              "name": "UserCar",
              "path": "userCar",
              "hidden": false,
              "component": "wisdom/majorParking/userCar/index",
              "meta": {
                "title": "员工车辆",
                "icon": "user",
                "noCache": false,
                "link": null
              }
            }
          ]
        },
        {
          "name": "/data",
          "path": "//data",
          "hidden": false,
          "redirect": "noRedirect",
          "component": "Layout",
          "alwaysShow": true,
          "meta": {
            "title": "数据看板",
            "icon": "icon-shujukanban",
            "noCache": false,
            "link": null
          },
          "children": [
            {
              "name": "Attendancedata",
              "path": "attendancedata",
              "hidden": false,
              "component": "data/attendance/attendance",
              "meta": {
                "title": "考勤看板",
                "icon": "icon-shujuhuizong",
                "noCache": true,
                "link": null
              }
            },
            {
              "name": "/visitordata",
              "path": "/visitordata",
              "hidden": false,
              "component": "data/visitor/index",
              "meta": {
                "title": "访客看板",
                "icon": "peoples",
                "noCache": true,
                "link": null
              }
            },
            {
              "name": "TrafficSignage",
              "path": "trafficSignage",
              "hidden": false,
              "component": "data/trafficSignage/index",
              "meta": {
                "title": "通行看板",
                "icon": "icon-icon_biaodanpeizhi",
                "noCache": true,
                "link": null
              }
            },
            {
              "name": "ConsumptionStatisticsIndex",
              "path": "consumptionStatisticsIndex",
              "hidden": false,
              "component": "data/points/consumptionStatisticsIndex",
              "meta": {
                "title": "消费看板",
                "icon": "build",
                "noCache": true,
                "link": null
              }
            },
            {
              "name": "Energydata",
              "path": "energydata",
              "hidden": false,
              "component": "wisdom/energy/enery-borad",
              "meta": {
                "title": "能耗看板",
                "icon": "chart",
                "noCache": true,
                "link": null
              }
            },
            {
              "name": "Dining",
              "path": "dining",
              "hidden": false,
              "component": "data/dining/index",
              "meta": {
                "title": "食堂看板",
                "icon": "icon-airudiantubiaohuizhi-zhuanqu_meishicaipu",
                "noCache": true,
                "link": null
              }
            },
            {
              "name": "Device",
              "path": "device",
              "hidden": false,
              "component": "data/device/index",
              "meta": {
                "title": "设备看板",
                "icon": "icon-zu1773",
                "noCache": true,
                "link": null
              }
            }
          ]
        },
        {
          "name": "/vistor",
          "path": "//vistor",
          "hidden": false,
          "redirect": "noRedirect",
          "component": "Layout",
          "alwaysShow": true,
          "meta": {
            "title": "访客管理",
            "icon": "people",
            "noCache": false,
            "link": null
          },
          "children": [
            {
              "name": "VisitorInfo",
              "path": "VisitorInfo",
              "hidden": false,
              "component": "wisdom/visitor/visitorInfo/index",
              "meta": {
                "title": "访客信息",
                "icon": "peoples",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "Visitorinvite",
              "path": "visitorinvite",
              "hidden": false,
              "component": "wisdom/visitor/visitorinvite/index",
              "meta": {
                "title": "预约记录",
                "icon": "icon-gongyingshangguanli",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "Vistorgroup",
              "path": "vistorgroup",
              "hidden": false,
              "component": "wisdom/visitor/visitorgroup/index.vue",
              "meta": {
                "title": "访客邀约",
                "icon": "documentation",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "Visitorrecords",
              "path": "visitorrecords",
              "hidden": false,
              "component": "wisdom/visitor/visitorrecords/index1",
              "meta": {
                "title": "到访记录",
                "icon": "list",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "VisitorParameter",
              "path": "visitorParameter",
              "hidden": false,
              "component": "wisdom/visitor/visitorParameterConfig/index.vue",
              "meta": {
                "title": "访客配置",
                "icon": "edit",
                "noCache": false,
                "link": null
              }
            }
          ]
        },
        {
          "path": "/",
          "hidden": true,
          "component": "Layout",
          "children": [
            {
              "name": "Http://192.168.41.108:40080/login?redirect=/index&voluntarilyLogin=true&tenantName=后台管理&username=admin&password=<EMAIL>",
              "path": "http://192.168.41.108:40080/login?redirect=/index&voluntarilyLogin=true&tenantName=后台管理&username=admin&password=<EMAIL>",
              "hidden": false,
              "component": "http://192.168.41.108:40080/login?redirect=/index&voluntarilyLogin=true&tenantName=后台管理&username=admin&password=<EMAIL>",
              "meta": {
                "title": "跳转芋道",
                "icon": "button",
                "noCache": false,
                "link": "http://192.168.41.108:40080/login?redirect=/index&voluntarilyLogin=true&tenantName=后台管理&username=admin&password=<EMAIL>"
              }
            }
          ]
        },
        {
          "name": "Datacenter",
          "path": "/datacenter",
          "hidden": false,
          "redirect": "noRedirect",
          "component": "Layout",
          "alwaysShow": true,
          "meta": {
            "title": "数据中心",
            "icon": "build",
            "noCache": false,
            "link": null
          },
          "children": [
            {
              "name": "UserInfo",
              "path": "userInfo",
              "hidden": false,
              "component": "wisdom/datacenter/invite-user/index.vue",
              "meta": {
                "title": "访客信息",
                "icon": "clipboard",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "Invite",
              "path": "invite",
              "hidden": false,
              "component": "wisdom/datacenter/invite-apply/index.vue",
              "meta": {
                "title": "访客审批",
                "icon": "dict",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "Management",
              "path": "management",
              "hidden": false,
              "component": "wisdom/datacenter/datacenterdevice/management.vue",
              "meta": {
                "title": "设备管理",
                "icon": "phone",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "Datacenterbanding",
              "path": "datacenterbanding",
              "hidden": false,
              "component": "wisdom/datacenter/datacenterdevice/banding.vue",
              "meta": {
                "title": "设备绑定",
                "icon": "row",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "Manager",
              "path": "manager",
              "hidden": false,
              "component": "wisdom/datacenter/datacentermanager/index.vue",
              "meta": {
                "title": "数据中心管理",
                "icon": "chart",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "Record",
              "path": "record",
              "hidden": false,
              "component": "wisdom/datacenter/invite-record/index.vue",
              "meta": {
                "title": "到访记录",
                "icon": "build",
                "noCache": false,
                "link": null
              }
            }
          ]
        },
        {
          "name": "Wisdom",
          "path": "/wisdom",
          "hidden": true,
          "redirect": "noRedirect",
          "component": "Layout",
          "alwaysShow": true,
          "meta": {
            "title": "智慧资管",
            "icon": "component",
            "noCache": false,
            "link": null
          },
          "children": [
            {
              "name": "AssetLending",
              "path": "assetLending",
              "hidden": false,
              "redirect": "noRedirect",
              "component": "ParentView",
              "alwaysShow": true,
              "meta": {
                "title": "资产外借",
                "icon": "icon",
                "noCache": false,
                "link": null
              },
              "children": [
                {
                  "name": "Index",
                  "path": "index",
                  "hidden": false,
                  "component": "wisdom/assetLending/index",
                  "meta": {
                    "title": "资产外借",
                    "icon": "#",
                    "noCache": false,
                    "link": null
                  }
                }
              ]
            },
            {
              "name": "Transfer-purchasing",
              "path": "transfer-purchasing",
              "hidden": false,
              "redirect": "noRedirect",
              "component": "ParentView",
              "alwaysShow": true,
              "meta": {
                "title": "采购管理",
                "icon": "24gl-fileText",
                "noCache": false,
                "link": null
              },
              "children": [
                {
                  "name": "Item-supplier/index",
                  "path": "item-supplier/index",
                  "hidden": false,
                  "component": "wisdom/transfer-purchasing/item-supplier/index",
                  "meta": {
                    "title": "物品供应商",
                    "icon": "date",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "Purchase-request/index",
                  "path": "purchase-request/index",
                  "hidden": false,
                  "component": "wisdom/transfer-purchasing/purchase-request/index",
                  "meta": {
                    "title": "采购申请",
                    "icon": "date-range",
                    "noCache": false,
                    "link": null
                  }
                }
              ]
            },
            {
              "name": "Transfer-stock",
              "path": "transfer-stock",
              "hidden": false,
              "redirect": "noRedirect",
              "component": "ParentView",
              "alwaysShow": true,
              "meta": {
                "title": "库存管理",
                "icon": "build",
                "noCache": false,
                "link": null
              },
              "children": [
                {
                  "name": "Warehouse-info/index",
                  "path": "warehouse-info/index",
                  "hidden": false,
                  "component": "wisdom/transfer-stock/warehouse-info/index",
                  "meta": {
                    "title": "仓库信息",
                    "icon": "clipboard",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "Item-type/index",
                  "path": "item-type/index",
                  "hidden": false,
                  "component": "wisdom/transfer-stock/item-type/index",
                  "meta": {
                    "title": "物品类型",
                    "icon": "documentation",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "Item-specifications/index",
                  "path": "item-specifications/index",
                  "hidden": false,
                  "component": "wisdom/transfer-stock/item-specifications/index",
                  "meta": {
                    "title": "物品规格",
                    "icon": "button",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "Item-info/index",
                  "path": "item-info/index",
                  "hidden": false,
                  "component": "wisdom/transfer-stock/item-info/index",
                  "meta": {
                    "title": "物品信息",
                    "icon": "cascader",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "In-out-bound/index",
                  "path": "in-out-bound/index",
                  "hidden": false,
                  "component": "wisdom/transfer-stock/in-out-bound/index",
                  "meta": {
                    "title": "出入库管理",
                    "icon": "clipboard",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "Transfer-application/index",
                  "path": "transfer-application/index",
                  "hidden": false,
                  "component": "wisdom/transfer-stock/transfer-application/index",
                  "meta": {
                    "title": "调拨申请",
                    "icon": "dashboard",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "Transfer-record/index",
                  "path": "transfer-record/index",
                  "hidden": false,
                  "component": "wisdom/transfer-stock/transfer-record/index",
                  "meta": {
                    "title": "调拨记录",
                    "icon": "dict",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "Inventory-management/index",
                  "path": "inventory-management/index",
                  "hidden": false,
                  "component": "wisdom/transfer-stock/inventory-management/index",
                  "meta": {
                    "title": "盘点管理",
                    "icon": "documentation",
                    "noCache": false,
                    "link": null
                  }
                }
              ]
            },
            {
              "name": "Transfer-consumables",
              "path": "transfer-consumables",
              "hidden": false,
              "redirect": "noRedirect",
              "component": "ParentView",
              "alwaysShow": true,
              "meta": {
                "title": "耗材管理",
                "icon": "druid",
                "noCache": false,
                "link": null
              },
              "children": [
                {
                  "name": "Item-requisition/index",
                  "path": "item-requisition/index",
                  "hidden": false,
                  "component": "wisdom/transfer-consumables/item-requisition/index",
                  "meta": {
                    "title": "物品领用",
                    "icon": "education",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "Item-own/index",
                  "path": "item-own/index",
                  "hidden": false,
                  "component": "wisdom/transfer-consumables/item-own/index",
                  "meta": {
                    "title": "我的物品",
                    "icon": "validCode",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "Transfer-list/index",
                  "path": "transfer-list/index",
                  "hidden": false,
                  "component": "wisdom/transfer-consumables/transfer-list/index",
                  "meta": {
                    "title": "转赠记录",
                    "icon": "email",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "Item-usage-record/index",
                  "path": "item-usage-record/index",
                  "hidden": false,
                  "component": "wisdom/transfer-consumables/item-usage-record/index",
                  "meta": {
                    "title": "物品使用记录",
                    "icon": "edit_file",
                    "noCache": false,
                    "link": null
                  }
                }
              ]
            }
          ]
        },
        {
          "name": "Task",
          "path": "/task",
          "hidden": false,
          "redirect": "noRedirect",
          "component": "Layout",
          "alwaysShow": true,
          "meta": {
            "title": "任务管理",
            "icon": "tab",
            "noCache": false,
            "link": null
          },
          "children": [
            {
              "name": "Process",
              "path": "process",
              "hidden": false,
              "component": "flowable/task/process/index",
              "meta": {
                "title": "我的流程",
                "icon": "guide",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "Todo",
              "path": "todo",
              "hidden": false,
              "component": "flowable/task/todo/index",
              "meta": {
                "title": "待办任务",
                "icon": "cascader",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "Finished",
              "path": "finished",
              "hidden": false,
              "component": "flowable/task/finished/index",
              "meta": {
                "title": "已办任务",
                "icon": "time-range",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "Message",
              "path": "message",
              "hidden": false,
              "component": "message/index",
              "meta": {
                "title": "消息通知",
                "icon": "message",
                "noCache": false,
                "link": null
              }
            }
          ]
        },
        {
          "name": "Station-management",
          "path": "/station-management",
          "hidden": false,
          "redirect": "noRedirect",
          "component": "Layout",
          "alwaysShow": true,
          "meta": {
            "title": "空间管理",
            "icon": "icon-kongjianguanli",
            "noCache": false,
            "link": null
          },
          "children": [
            {
              "name": "Station",
              "path": "station",
              "hidden": false,
              "component": "wisdom/space-management/station/banding.vue",
              "meta": {
                "title": "工位管理",
                "icon": "icon-wodehuiyi",
                "noCache": false,
                "link": null
              }
            }
          ]
        },
        {
          "name": "Work",
          "path": "/work",
          "hidden": false,
          "redirect": "noRedirect",
          "component": "Layout",
          "alwaysShow": true,
          "meta": {
            "title": "智慧办公",
            "icon": "icon-zhihuibangong",
            "noCache": false,
            "link": null
          },
          "children": [
            {
              "name": "/wisdom/conference-management",
              "path": "/wisdom/conference-management",
              "hidden": false,
              "redirect": "noRedirect",
              "component": "ParentView",
              "alwaysShow": true,
              "meta": {
                "title": "会议管理",
                "icon": "monitor",
                "noCache": false,
                "link": null
              },
              "children": [
                {
                  "name": "Conference-tabs/index",
                  "path": "conference-tabs/index",
                  "hidden": false,
                  "component": "wisdom/conference-management/conference-tabs/index",
                  "meta": {
                    "title": "会议室管理",
                    "icon": "dict",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "Info-tabs/index",
                  "path": "info-tabs/index",
                  "hidden": false,
                  "component": "wisdom/conference-management/info-tabs/index",
                  "meta": {
                    "title": "会议室预定",
                    "icon": "input",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "Info-query/query",
                  "path": "info-query/query",
                  "hidden": false,
                  "component": "wisdom/conference-management/info-query/query",
                  "meta": {
                    "title": "会议查询",
                    "icon": "chart",
                    "noCache": false,
                    "link": null
                  }
                }
              ]
            },
            {
              "name": "/wisdom/sealManager",
              "path": "/wisdom/sealManager",
              "hidden": false,
              "redirect": "noRedirect",
              "component": "ParentView",
              "alwaysShow": true,
              "meta": {
                "title": "用章管理",
                "icon": "dashboard",
                "noCache": false,
                "link": null
              },
              "children": [
                {
                  "name": "SealInfo",
                  "path": "sealInfo",
                  "hidden": false,
                  "component": "wisdom/sealManager/sealInfo/index",
                  "meta": {
                    "title": "用章类型",
                    "icon": "dict",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "SealApplication",
                  "path": "sealApplication",
                  "hidden": false,
                  "component": "wisdom/sealManager/sealApplication/index",
                  "meta": {
                    "title": "用章申请",
                    "icon": "guide",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "SealApplicationInfo",
                  "path": "sealApplicationInfo",
                  "hidden": false,
                  "component": "wisdom/sealManager/sealApplicationInfo/index",
                  "meta": {
                    "title": "用章查询",
                    "icon": "international",
                    "noCache": false,
                    "link": null
                  }
                }
              ]
            }
          ]
        },
        {
          "name": "Admittance",
          "path": "/admittance",
          "hidden": false,
          "redirect": "noRedirect",
          "component": "Layout",
          "alwaysShow": true,
          "meta": {
            "title": "门禁管理",
            "icon": "icon-menjinguanli",
            "noCache": false,
            "link": null
          },
          "children": [
            {
              "name": "DeviceBanding",
              "path": "deviceBanding",
              "hidden": false,
              "component": "wisdom/admittance/device/banding",
              "meta": {
                "title": "设备绑定",
                "icon": "swagger",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "Accredit/accredit",
              "path": "accredit/accredit",
              "hidden": false,
              "component": "wisdom/admittance/accredit/accredit",
              "meta": {
                "title": "授权管理",
                "icon": "password",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "InAndoutRecord/index",
              "path": "inAndoutRecord/index",
              "hidden": false,
              "component": "wisdom/admittance/inAndoutRecord/index",
              "meta": {
                "title": "出入记录",
                "icon": "list",
                "noCache": false,
                "link": null
              }
            }
          ]
        },
        {
          "name": "Event",
          "path": "/event",
          "hidden": false,
          "redirect": "noRedirect",
          "component": "Layout",
          "alwaysShow": true,
          "meta": {
            "title": "事件中心",
            "icon": "icon-shijianzhongxin",
            "noCache": false,
            "link": null
          },
          "children": [
            {
              "name": "Eventcenter",
              "path": "eventcenter",
              "hidden": false,
              "component": "wisdom/event/eventcenter/index.vue",
              "meta": {
                "title": "事件中心",
                "icon": "color",
                "noCache": false,
                "link": null
              }
            }
          ]
        },
        {
          "name": "Wisdom/canteen-management",
          "path": "/wisdom/canteen-management",
          "hidden": false,
          "redirect": "noRedirect",
          "component": "Layout",
          "alwaysShow": true,
          "meta": {
            "title": "智慧食堂",
            "icon": "icon-zhihuishitang",
            "noCache": false,
            "link": null
          },
          "children": [
            {
              "name": "Staticusertime",
              "path": "staticusertime",
              "hidden": false,
              "component": "wisdom/admittance/staticusertime/index.vue",
              "meta": {
                "title": "办公人数",
                "icon": "chart",
                "noCache": true,
                "link": null
              }
            },
            {
              "name": "Wisdom/canteen-management/basic-management",
              "path": "wisdom/canteen-management/basic-management",
              "hidden": false,
              "redirect": "noRedirect",
              "component": "ParentView",
              "alwaysShow": true,
              "meta": {
                "title": "基础管理",
                "icon": "cascader",
                "noCache": false,
                "link": null
              },
              "children": [
                {
                  "name": "PrivateRoomManagement",
                  "path": "privateRoomManagement",
                  "hidden": false,
                  "component": "wisdom/canteen-management/basic-management/privateRoom-management/index",
                  "meta": {
                    "title": "包间管理",
                    "icon": "job",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "CanteenManagement",
                  "path": "canteenManagement",
                  "hidden": false,
                  "component": "wisdom/canteen-management/basic-management/canteen-management/index",
                  "meta": {
                    "title": "食堂管理",
                    "icon": "row",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "Classify",
                  "path": "classify",
                  "hidden": false,
                  "component": "wisdom/canteen-management/basic-management/food-management/index.vue",
                  "meta": {
                    "title": "商品配置",
                    "icon": "list",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "FoodManagement",
                  "path": "foodManagement",
                  "hidden": false,
                  "component": "wisdom/canteen-management/basic-management/food-management/food/index.vue",
                  "meta": {
                    "title": "菜品管理",
                    "icon": "server",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "Device",
                  "path": "device",
                  "hidden": false,
                  "component": "wisdom/canteen-management/basic-management/food-device/index",
                  "meta": {
                    "title": "食堂设备",
                    "icon": "tab",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "Accredit",
                  "path": "accredit",
                  "hidden": false,
                  "component": "wisdom/canteen-management/basic-management/food-accredit/accredit",
                  "meta": {
                    "title": "食堂授权",
                    "icon": "user",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "WisdowManagement",
                  "path": "wisdowManagement",
                  "hidden": false,
                  "component": "wisdom/canteen-management/basic-management/window-management/index",
                  "meta": {
                    "title": "窗口管理",
                    "icon": "nested",
                    "noCache": false,
                    "link": null
                  }
                }
              ]
            },
            {
              "name": "Food-menu",
              "path": "food-menu",
              "hidden": false,
              "component": "wisdom/canteen-management/basic-management/food-menu/index.vue",
              "meta": {
                "title": "食堂菜谱",
                "icon": "build",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "Wisdom/canteen-management/buffet",
              "path": "wisdom/canteen-management/buffet",
              "hidden": false,
              "redirect": "noRedirect",
              "component": "ParentView",
              "alwaysShow": true,
              "meta": {
                "title": "自助餐",
                "icon": "list",
                "noCache": false,
                "link": null
              },
              "children": [
                {
                  "name": "Buffet-menu",
                  "path": "buffet-menu",
                  "hidden": true,
                  "component": "wisdom/canteen-management/commodity-management/commodity",
                  "meta": {
                    "title": "自助餐谱",
                    "icon": "date",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "Special-population",
                  "path": "special-population",
                  "hidden": false,
                  "component": "wisdom/canteen-management/buffet/special-population/index",
                  "meta": {
                    "title": "特殊人群",
                    "icon": "component",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "Dining-records",
                  "path": "dining-records",
                  "hidden": false,
                  "component": "wisdom/canteen-management/buffet/dining-records/index",
                  "meta": {
                    "title": "就餐记录",
                    "icon": "build",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "Price",
                  "path": "price",
                  "hidden": false,
                  "component": "wisdom/canteen-management/buffet/price/index",
                  "meta": {
                    "title": "餐价管理",
                    "icon": "list",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "Love-subsidy",
                  "path": "love-subsidy",
                  "hidden": false,
                  "component": "wisdom/canteen-management/buffet/love-subsidy/index.vue",
                  "meta": {
                    "title": "爱心补贴",
                    "icon": "user",
                    "noCache": false,
                    "link": null
                  }
                }
              ]
            },
            {
              "name": "Food",
              "path": "food",
              "hidden": false,
              "redirect": "noRedirect",
              "component": "ParentView",
              "alwaysShow": true,
              "meta": {
                "title": "个人点餐",
                "icon": "build",
                "noCache": false,
                "link": null
              },
              "children": [
                {
                  "name": "Food-menu",
                  "path": "food-menu",
                  "hidden": true,
                  "component": "wisdom/canteen-management/basic-management/food-menu/index.vue",
                  "meta": {
                    "title": "点餐菜谱",
                    "icon": "build",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "Food-menu-list",
                  "path": "food-menu-list",
                  "hidden": false,
                  "component": "wisdom/canteen-management/basic-management/food-menu-list/order.vue",
                  "meta": {
                    "title": "个人订单",
                    "icon": "build",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "Food-menu-canteen-list",
                  "path": "food-menu-canteen-list",
                  "hidden": false,
                  "component": "wisdom/canteen-management/basic-management/food-menu-canteen-list/order.vue",
                  "meta": {
                    "title": "点餐订单",
                    "icon": "build",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "Meal-picker",
                  "path": "meal-picker",
                  "hidden": false,
                  "component": "wisdom/canteen-management/basic-management/meal-picker/index.vue",
                  "meta": {
                    "title": "取餐人员",
                    "icon": "peoples",
                    "noCache": false,
                    "link": null
                  }
                }
              ]
            },
            {
              "name": "IndividualReservation",
              "path": "individualReservation",
              "hidden": false,
              "redirect": "noRedirect",
              "component": "ParentView",
              "alwaysShow": true,
              "meta": {
                "title": "线上预定",
                "icon": "component",
                "noCache": false,
                "link": null
              },
              "children": [
                {
                  "name": "PublishOrder",
                  "path": "publishOrder",
                  "hidden": false,
                  "component": "wisdom/canteen-management/individual-reservation/publishOrder/index",
                  "meta": {
                    "title": "发布预定",
                    "icon": "skill",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "IndividualReservation",
                  "path": "individualReservation",
                  "hidden": false,
                  "component": "wisdom/canteen-management/individual-reservation/individualReservation/index",
                  "meta": {
                    "title": "个人预定",
                    "icon": "peoples",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "OrderRecord",
                  "path": "orderRecord",
                  "hidden": false,
                  "component": "wisdom/canteen-management/individual-reservation/individualReservation/components/restaurantRecord.vue",
                  "meta": {
                    "title": "预定订单",
                    "icon": "documentation",
                    "noCache": false,
                    "link": null
                  }
                }
              ]
            },
            {
              "name": "Wisdom/word-order",
              "path": "wisdom/word-order",
              "hidden": false,
              "redirect": "noRedirect",
              "component": "ParentView",
              "alwaysShow": true,
              "meta": {
                "title": "工作订餐",
                "icon": "server",
                "noCache": false,
                "link": null
              },
              "children": [
                {
                  "name": "WorkOrder",
                  "path": "workOrder",
                  "hidden": false,
                  "component": "wisdom/canteen-management/word-order/workOrder/index",
                  "meta": {
                    "title": "工作订餐",
                    "icon": "documentation",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "WorkOrderSettlement",
                  "path": "workOrderSettlement",
                  "hidden": false,
                  "component": "wisdom/canteen-management/word-order/workOrderSettlement",
                  "meta": {
                    "title": "订餐结算",
                    "icon": "druid",
                    "noCache": false,
                    "link": null
                  }
                }
              ]
            },
            {
              "name": "Commodity-shopping",
              "path": "commodity-shopping",
              "hidden": false,
              "redirect": "noRedirect",
              "component": "ParentView",
              "alwaysShow": true,
              "meta": {
                "title": "线上超市",
                "icon": "shopping",
                "noCache": false,
                "link": null
              },
              "children": [
                {
                  "name": "Commodity",
                  "path": "commodity",
                  "hidden": false,
                  "component": "wisdom/canteen-management/commodity-management/commodity",
                  "meta": {
                    "title": "商品管理",
                    "icon": "tree-table",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "Shop",
                  "path": "shop",
                  "hidden": false,
                  "component": "wisdom/canteen-management/commodity-shopping/shop",
                  "meta": {
                    "title": "商品购买",
                    "icon": "shopping",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "Order",
                  "path": "order",
                  "hidden": false,
                  "component": "wisdom/canteen-management/commodity-shopping/order",
                  "meta": {
                    "title": "个人订单",
                    "icon": "list",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "AllOrder",
                  "path": "allOrder",
                  "hidden": false,
                  "component": "wisdom/canteen-management/commodity-shopping/allOrder",
                  "meta": {
                    "title": "商品订单",
                    "icon": "list",
                    "noCache": false,
                    "link": null
                  }
                }
              ]
            },
            {
              "name": "Customize",
              "path": "customize",
              "hidden": false,
              "redirect": "noRedirect",
              "component": "ParentView",
              "alwaysShow": true,
              "meta": {
                "title": "自主扣费",
                "icon": "build",
                "noCache": false,
                "link": null
              },
              "children": [
                {
                  "name": "Customize",
                  "path": "customize",
                  "hidden": false,
                  "component": "wisdom/canteen-management/customize-order/customize.vue",
                  "meta": {
                    "title": "结算订单",
                    "icon": "icon-jilu",
                    "noCache": false,
                    "link": null
                  }
                }
              ]
            },
            {
              "name": "Http://localhost:5173/wisdomdigitaltwin",
              "path": "http://localhost:5173/wisdomdigitaltwin",
              "hidden": true,
              "component": "Layout",
              "meta": {
                "title": "数字孪生",
                "icon": "icon-tubiao_caiwutongji",
                "noCache": false,
                "link": "http://localhost:5173/wisdomdigitaltwin"
              }
            },
            {
              "name": " stock",
              "path": " stock",
              "hidden": true,
              "redirect": "noRedirect",
              "component": "ParentView",
              "alwaysShow": true,
              "meta": {
                "title": "库存管理",
                "icon": "build",
                "noCache": false,
                "link": null
              },
              "children": [
                {
                  "name": "Store",
                  "path": "store",
                  "hidden": false,
                  "component": "wisdom/stock/store",
                  "meta": {
                    "title": "仓库管理",
                    "icon": "skill",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "Manage",
                  "path": "manage",
                  "hidden": false,
                  "component": "wisdom/stock/manage",
                  "meta": {
                    "title": "库存管理",
                    "icon": "cascader",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "Stock_in",
                  "path": "stock_in",
                  "hidden": false,
                  "component": "wisdom/stock/stock_in",
                  "meta": {
                    "title": "入库管理",
                    "icon": "druid",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "Out_stock",
                  "path": "out_stock",
                  "hidden": false,
                  "component": "wisdom/stock/out_stock",
                  "meta": {
                    "title": "出库管理",
                    "icon": "password",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "Plan",
                  "path": "plan",
                  "hidden": false,
                  "component": "wisdom/stock/plan",
                  "meta": {
                    "title": "生产采购计划",
                    "icon": "online",
                    "noCache": false,
                    "link": null
                  }
                }
              ]
            },
            {
              "name": "Food-safety",
              "path": "food-safety",
              "hidden": false,
              "redirect": "noRedirect",
              "component": "ParentView",
              "alwaysShow": true,
              "meta": {
                "title": "食品安全",
                "icon": "form",
                "noCache": false,
                "link": null
              },
              "children": [
                {
                  "name": "Resfilemanager",
                  "path": "resfilemanager",
                  "hidden": false,
                  "component": "wisdom/resfilemanager/index",
                  "meta": {
                    "title": "餐饮文件管理",
                    "icon": "pdf",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "Sample",
                  "path": "sample",
                  "hidden": false,
                  "component": "wisdom/dishessample/index",
                  "meta": {
                    "title": "菜品留样",
                    "icon": "post",
                    "noCache": false,
                    "link": null
                  }
                }
              ]
            },
            {
              "name": "Supplier",
              "path": "supplier",
              "hidden": false,
              "component": "ParentView",
              "meta": {
                "title": "食堂管理员",
                "icon": "user",
                "noCache": false,
                "link": null
              }
            }
          ]
        },
        {
          "name": "Video-surveillance",
          "path": "/video-surveillance",
          "hidden": false,
          "redirect": "noRedirect",
          "component": "Layout",
          "alwaysShow": true,
          "meta": {
            "title": "视联中心",
            "icon": "icon-zhinengmofangicon_shipinzhongxin",
            "noCache": false,
            "link": null
          },
          "children": [
            {
              "name": "AI-alerts",
              "path": "AI-alerts",
              "hidden": false,
              "component": "wisdom/video-surveillance/AI-alerts/index",
              "meta": {
                "title": "AI监控预警",
                "icon": "icon-beijianshiyongtongji",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "Wisdom/admittance/sdunictDevice/sdunictDevice",
              "path": "wisdom/admittance/sdunictDevice/sdunictDevice",
              "hidden": false,
              "component": "wisdom/admittance/sdunictDevice/sdunictDevice",
              "meta": {
                "title": "视联网设备",
                "icon": "eye-open",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "Video-record",
              "path": "video-record",
              "hidden": false,
              "component": "wisdom/video-surveillance/video-record/index",
              "meta": {
                "title": "视频监控记录",
                "icon": "component",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "Video-center",
              "path": "video-center",
              "hidden": false,
              "component": "wisdom/video-surveillance/video-center/index",
              "meta": {
                "title": "视频中心",
                "icon": "icon-tongjifenxi-xiangmubiaogetongji",
                "noCache": false,
                "link": null
              }
            }
          ]
        },
        {
          "name": "Attendance",
          "path": "/attendance",
          "hidden": false,
          "redirect": "noRedirect",
          "component": "Layout",
          "alwaysShow": true,
          "meta": {
            "title": "考勤管理",
            "icon": "icon-kaoqinguanli",
            "noCache": false,
            "link": null
          },
          "children": [
            {
              "name": "Importing",
              "path": "importing",
              "hidden": false,
              "component": "wisdom/smart-attendance/data-importing/index",
              "meta": {
                "title": "数据导入",
                "icon": "build",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "Reports",
              "path": "reports",
              "hidden": false,
              "redirect": "noRedirect",
              "component": "ParentView",
              "alwaysShow": true,
              "meta": {
                "title": "考勤查询",
                "icon": "list",
                "noCache": false,
                "link": null
              },
              "children": [
                {
                  "name": "Report/personal",
                  "path": "report/personal",
                  "hidden": false,
                  "component": "wisdom/smart-attendance/report/personal/index",
                  "meta": {
                    "title": "个人考勤记录",
                    "icon": "icon-teshurenqun",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "Report/department",
                  "path": "report/department",
                  "hidden": false,
                  "component": "wisdom/smart-attendance/report/department/index",
                  "meta": {
                    "title": "部门考勤记录",
                    "icon": "tree-table",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "Attendance",
                  "path": "attendance",
                  "hidden": false,
                  "component": "wisdom/smart-attendance/attendance/index",
                  "meta": {
                    "title": "公司考勤记录",
                    "icon": "checkbox",
                    "noCache": false,
                    "link": null
                  }
                }
              ]
            },
            {
              "name": "Attendance/notice",
              "path": "attendance/notice",
              "hidden": false,
              "component": "wisdom/smart-attendance/notice/notice",
              "meta": {
                "title": "考勤提醒",
                "icon": "24gl-fileText",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "Report/company",
              "path": "report/company",
              "hidden": false,
              "component": "wisdom/smart-attendance/report/company/index",
              "meta": {
                "title": "考勤报表",
                "icon": "example",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "Rules",
              "path": "rules",
              "hidden": false,
              "component": "wisdom/smart-attendance/rules/index.vue",
              "meta": {
                "title": "考勤规则",
                "icon": "druid",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "Confirm",
              "path": "confirm",
              "hidden": false,
              "component": "wisdom/smart-attendance/confirm/index.vue",
              "meta": {
                "title": "考勤确认",
                "icon": "cascader",
                "noCache": true,
                "link": null
              }
            }
          ]
        },
        {
          "name": "Shopping",
          "path": "/shopping",
          "hidden": true,
          "redirect": "noRedirect",
          "component": "Layout",
          "alwaysShow": true,
          "meta": {
            "title": "线上商城",
            "icon": "shopping",
            "noCache": false,
            "link": null
          },
          "children": [
            {
              "name": "Supplier",
              "path": "supplier",
              "hidden": false,
              "redirect": "noRedirect",
              "component": "ParentView",
              "alwaysShow": true,
              "meta": {
                "title": "供应商管理",
                "icon": "user",
                "noCache": false,
                "link": null
              },
              "children": [
                {
                  "name": "Inquire",
                  "path": "inquire",
                  "hidden": false,
                  "component": "supplier/inquire/index",
                  "meta": {
                    "title": "供应商查询",
                    "icon": "slider",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "Closean",
                  "path": "closean",
                  "hidden": false,
                  "component": "supplier/closean/index",
                  "meta": {
                    "title": "供应商结算",
                    "icon": "user",
                    "noCache": false,
                    "link": null
                  }
                }
              ]
            },
            {
              "name": "Shop",
              "path": "shop",
              "hidden": false,
              "redirect": "noRedirect",
              "component": "ParentView",
              "alwaysShow": true,
              "meta": {
                "title": "商城管理",
                "icon": "shopping",
                "noCache": false,
                "link": null
              },
              "children": [
                {
                  "name": "Http://172.16.11.166:30833/mall/#/ssoMall",
                  "path": "http://172.16.11.166:30833/mall/#/ssoMall",
                  "hidden": false,
                  "component": "OuterLink",
                  "meta": {
                    "title": "线上商城",
                    "icon": "shopping",
                    "noCache": false,
                    "link": "http://172.16.11.166:30833/mall/#/ssoMall"
                  }
                },
                {
                  "name": "Http://172.16.11.166:30833/mall_manager/#/ssoJump?menu_code=102223002",
                  "path": "http://172.16.11.166:30833/mall_manager/#/ssoJump?menu_code=102223002",
                  "hidden": false,
                  "component": "OuterLink",
                  "meta": {
                    "title": "商品上架审核",
                    "icon": "#",
                    "noCache": false,
                    "link": "http://172.16.11.166:30833/mall_manager/#/ssoJump?menu_code=102223002"
                  }
                },
                {
                  "name": "Http://172.16.11.166:30833/mall_manager/#/ssoJump?menu_code=102223003",
                  "path": "http://172.16.11.166:30833/mall_manager/#/ssoJump?menu_code=102223003",
                  "hidden": false,
                  "component": "OuterLink",
                  "meta": {
                    "title": "商品调价审核",
                    "icon": "#",
                    "noCache": false,
                    "link": "http://172.16.11.166:30833/mall_manager/#/ssoJump?menu_code=102223003"
                  }
                },
                {
                  "name": "Http://172.16.11.166:30833/mall_manager/#/ssoJump?menu_code=102223001",
                  "path": "http://172.16.11.166:30833/mall_manager/#/ssoJump?menu_code=102223001",
                  "hidden": false,
                  "component": "OuterLink",
                  "meta": {
                    "title": "已上架商品",
                    "icon": "#",
                    "noCache": false,
                    "link": "http://172.16.11.166:30833/mall_manager/#/ssoJump?menu_code=102223001"
                  }
                }
              ]
            }
          ]
        },
        {
          "name": "EvaluationManagement",
          "path": "/evaluationManagement",
          "hidden": false,
          "redirect": "noRedirect",
          "component": "Layout",
          "alwaysShow": true,
          "meta": {
            "title": "考评管理",
            "icon": "icon-daikaoqinqueren",
            "noCache": false,
            "link": null
          },
          "children": [
            {
              "name": "Auarterly-evaluation",
              "path": "auarterly-evaluation",
              "hidden": false,
              "redirect": "noRedirect",
              "component": "ParentView",
              "alwaysShow": true,
              "meta": {
                "title": "部门季度考评",
                "icon": "icon-bumenguanli",
                "noCache": false,
                "link": null
              },
              "children": [
                {
                  "name": "Auarterly-evaluation",
                  "path": "auarterly-evaluation",
                  "hidden": false,
                  "component": "wisdom/examine/auarterly-evaluation/query/index",
                  "meta": {
                    "title": "部门季度考评查询",
                    "icon": "icon-shengchancaigou",
                    "noCache": false,
                    "link": null
                  }
                }
              ]
            }
          ]
        },
        {
          "name": "Travel",
          "path": "/travel",
          "hidden": false,
          "redirect": "noRedirect",
          "component": "Layout",
          "alwaysShow": true,
          "meta": {
            "title": "智慧出行",
            "icon": "icon-zhihuichuhang",
            "noCache": false,
            "link": null
          },
          "children": [
            {
              "name": "Wisdom/public-car",
              "path": "wisdom/public-car",
              "hidden": false,
              "redirect": "noRedirect",
              "component": "ParentView",
              "alwaysShow": true,
              "meta": {
                "title": "档案管理",
                "icon": "form",
                "noCache": false,
                "link": null
              },
              "children": [
                {
                  "name": "Car-tabs/index",
                  "path": "car-tabs/index",
                  "hidden": false,
                  "component": "wisdom/public-car/car-tabs/index",
                  "meta": {
                    "title": "车辆档案",
                    "icon": "form",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "Cooperator/cooperator",
                  "path": "cooperator/cooperator",
                  "hidden": false,
                  "component": "wisdom/public-car/cooperator/cooperator",
                  "meta": {
                    "title": "合作单位",
                    "icon": "peoples",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "Driver-manage/driver",
                  "path": "driver-manage/driver",
                  "hidden": false,
                  "component": "wisdom/public-car/driver-manage/driver",
                  "meta": {
                    "title": "司机管理",
                    "icon": "people",
                    "noCache": false,
                    "link": null
                  }
                }
              ]
            },
            {
              "name": "Park",
              "path": "park",
              "hidden": false,
              "redirect": "noRedirect",
              "component": "ParentView",
              "alwaysShow": true,
              "meta": {
                "title": "停车管理",
                "icon": "time",
                "noCache": false,
                "link": null
              },
              "children": [
                {
                  "name": "ParkSystem",
                  "path": "parkSystem",
                  "hidden": false,
                  "component": "park/parkSystem/index",
                  "meta": {
                    "title": "停车系统管理",
                    "icon": "build",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "Manage",
                  "path": "manage",
                  "hidden": false,
                  "component": "park/manage/index",
                  "meta": {
                    "title": "停车场管理",
                    "icon": "validCode",
                    "noCache": true,
                    "link": null
                  }
                },
                {
                  "name": "ParkingRule",
                  "path": "parkingRule",
                  "hidden": false,
                  "component": "wisdom/majorParking/parkingRule/index",
                  "query": "parkingRule",
                  "meta": {
                    "title": "停车规则",
                    "icon": "icon-canshushezhi",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "Plate",
                  "path": "plate",
                  "hidden": false,
                  "component": "park/plate/index",
                  "meta": {
                    "title": "车牌管理",
                    "icon": "validCode",
                    "noCache": true,
                    "link": null
                  }
                },
                {
                  "name": "Topup",
                  "path": "topup",
                  "hidden": false,
                  "component": "park/topup/index",
                  "meta": {
                    "title": "车辆充值",
                    "icon": "validCode",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "Record",
                  "path": "record",
                  "hidden": false,
                  "component": "park/record/index",
                  "meta": {
                    "title": "停车申请",
                    "icon": "user",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "Recordlist",
                  "path": "recordlist",
                  "hidden": false,
                  "component": "park/record/list",
                  "meta": {
                    "title": "停车记录",
                    "icon": "user",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "Recordconse",
                  "path": "recordconse",
                  "hidden": false,
                  "component": "park/record/conse",
                  "meta": {
                    "title": "消费记录",
                    "icon": "user",
                    "noCache": false,
                    "link": null
                  }
                }
              ]
            },
            {
              "name": "Wisdom/carManager",
              "path": "wisdom/carManager",
              "hidden": false,
              "redirect": "noRedirect",
              "component": "ParentView",
              "alwaysShow": true,
              "meta": {
                "title": "公车管理",
                "icon": "time",
                "noCache": false,
                "link": null
              },
              "children": [
                {
                  "name": "MyApply/index",
                  "path": "myApply/index",
                  "hidden": false,
                  "component": "wisdom/carManager/myApply/index",
                  "meta": {
                    "title": "我的申请",
                    "icon": "#",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "OrderDispatch/index",
                  "path": "orderDispatch/index",
                  "hidden": false,
                  "component": "wisdom/carManager/orderDispatch/index",
                  "meta": {
                    "title": "订单派遣",
                    "icon": "#",
                    "noCache": false,
                    "link": null
                  }
                }
              ]
            },
            {
              "name": "Sendorders",
              "path": "sendorders",
              "hidden": false,
              "redirect": "noRedirect",
              "component": "ParentView",
              "alwaysShow": true,
              "meta": {
                "title": "派单管理",
                "icon": "date-range",
                "noCache": false,
                "link": null
              },
              "children": [
                {
                  "name": "Deiverdispatch/index.vue",
                  "path": "deiverdispatch/index.vue",
                  "hidden": false,
                  "component": "wisdom/deiverdispatch/index.vue",
                  "meta": {
                    "title": "出车管理",
                    "icon": "icon-gongyingshangjiesuan",
                    "noCache": false,
                    "link": null
                  }
                }
              ]
            }
          ]
        },
        {
          "name": "Security",
          "path": "/security",
          "hidden": true,
          "redirect": "noRedirect",
          "component": "Layout",
          "alwaysShow": true,
          "meta": {
            "title": "智慧安防",
            "icon": "icon-zhihuianfang",
            "noCache": false,
            "link": null
          },
          "children": [
            {
              "name": "Fire",
              "path": "fire",
              "hidden": true,
              "component": "ParentView",
              "meta": {
                "title": "智慧消防",
                "icon": "size",
                "noCache": false,
                "link": null
              }
            }
          ]
        },
        {
          "name": "Property",
          "path": "/property",
          "hidden": false,
          "redirect": "noRedirect",
          "component": "Layout",
          "alwaysShow": true,
          "meta": {
            "title": "智慧物业",
            "icon": "icon-zhihuiwuye",
            "noCache": false,
            "link": null
          },
          "children": [
            {
              "name": "Repair",
              "path": "repair",
              "hidden": false,
              "redirect": "noRedirect",
              "component": "ParentView",
              "alwaysShow": true,
              "meta": {
                "title": "报修管理",
                "icon": "icon-baoshibaoxiu",
                "noCache": false,
                "link": null
              },
              "children": [
                {
                  "name": "Repair/configuration",
                  "path": "repair/configuration",
                  "hidden": false,
                  "component": "wisdom/repair/configuration/repair-config-tab",
                  "meta": {
                    "title": "报修配置",
                    "icon": "icon-jichuguanli",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "ApplyBasicInfo",
                  "path": "applyBasicInfo",
                  "hidden": false,
                  "component": "wisdom/repair/applyBasicInfo/index",
                  "meta": {
                    "title": "报修申请",
                    "icon": "build",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "Repair-workorder",
                  "path": "repair-workorder",
                  "hidden": false,
                  "component": "wisdom/repair/workorder/index.vue",
                  "meta": {
                    "title": "工单管理",
                    "icon": "chart",
                    "noCache": true,
                    "link": null
                  }
                }
              ]
            },
            {
              "name": "Transfer-patrol",
              "path": "transfer-patrol",
              "hidden": true,
              "redirect": "noRedirect",
              "component": "ParentView",
              "alwaysShow": true,
              "meta": {
                "title": "智能巡更",
                "icon": "24gl-fileText",
                "noCache": false,
                "link": null
              },
              "children": [
                {
                  "name": "InspectionItemManage/index",
                  "path": "inspectionItemManage/index",
                  "hidden": false,
                  "component": "wisdom/transfer-patrol/inspectionItemManage/index",
                  "meta": {
                    "title": "巡更项目",
                    "icon": "clipboard",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "InspectionPointManage/index",
                  "path": "inspectionPointManage/index",
                  "hidden": false,
                  "component": "wisdom/transfer-patrol/inspectionPointManage/index",
                  "meta": {
                    "title": "巡更点",
                    "icon": "color",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "InspectionRouteManage/index",
                  "path": "inspectionRouteManage/index",
                  "hidden": false,
                  "component": "wisdom/transfer-patrol/inspectionRouteManage/index",
                  "meta": {
                    "title": "巡更路线",
                    "icon": "component",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "InspectionPlanManage/index",
                  "path": "inspectionPlanManage/index",
                  "hidden": false,
                  "component": "wisdom/transfer-patrol/inspectionPlanManage/index",
                  "meta": {
                    "title": "巡更计划",
                    "icon": "date-range",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "InspectionTaskManage/index",
                  "path": "inspectionTaskManage/index",
                  "hidden": false,
                  "component": "wisdom/transfer-patrol/inspectionTaskManage/index",
                  "meta": {
                    "title": "巡更任务",
                    "icon": "checkbox",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "InspectionTaskDetails/index",
                  "path": "inspectionTaskDetails/index",
                  "hidden": false,
                  "component": "wisdom/transfer-patrol/inspectionTaskDetails/index",
                  "meta": {
                    "title": "巡更查询",
                    "icon": "documentation",
                    "noCache": false,
                    "link": null
                  }
                }
              ]
            },
            {
              "name": "Transfer-questionnaire",
              "path": "transfer-questionnaire",
              "hidden": true,
              "redirect": "noRedirect",
              "component": "ParentView",
              "alwaysShow": true,
              "meta": {
                "title": "问卷投票",
                "icon": "button",
                "noCache": false,
                "link": null
              },
              "children": [
                {
                  "name": "MyQuestionAnswerManage/index",
                  "path": "myQuestionAnswerManage/index",
                  "hidden": false,
                  "component": "wisdom/transfer-questionnaire/myQuestionAnswerManage/index",
                  "meta": {
                    "title": "我的问卷投票",
                    "icon": "dict",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "QuestionAnswerManage/index",
                  "path": "questionAnswerManage/index",
                  "hidden": false,
                  "component": "wisdom/transfer-questionnaire/questionAnswerManage/index",
                  "meta": {
                    "title": "发起问卷投票",
                    "icon": "edit",
                    "noCache": false,
                    "link": null
                  }
                }
              ]
            },
            {
              "name": "Transfer-maintenance",
              "path": "transfer-maintenance",
              "hidden": true,
              "redirect": "noRedirect",
              "component": "ParentView",
              "alwaysShow": true,
              "meta": {
                "title": "设备保养",
                "icon": "drag",
                "noCache": false,
                "link": null
              },
              "children": [
                {
                  "name": "MaintainanceItem/index",
                  "path": "maintainanceItem/index",
                  "hidden": false,
                  "component": "wisdom/transfer-maintenance/maintainanceItem/index",
                  "meta": {
                    "title": "检查项",
                    "icon": "24gl-fileText",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "MaintainanceStandardManage/index",
                  "path": "maintainanceStandardManage/index",
                  "hidden": false,
                  "component": "wisdom/transfer-maintenance/maintainanceStandardManage/index",
                  "meta": {
                    "title": "保养标准",
                    "icon": "button",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "MaintainancePlanManage/index",
                  "path": "maintainancePlanManage/index",
                  "hidden": false,
                  "component": "wisdom/transfer-maintenance/maintainancePlanManage/index",
                  "meta": {
                    "title": "保养计划",
                    "icon": "component",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "MaintainanceTaskManage/index",
                  "path": "maintainanceTaskManage/index",
                  "hidden": false,
                  "component": "wisdom/transfer-maintenance/maintainanceTaskManage/index",
                  "meta": {
                    "title": "保养任务",
                    "icon": "code",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "MaintainanceTaskDetails/index",
                  "path": "maintainanceTaskDetails/index",
                  "hidden": false,
                  "component": "wisdom/transfer-maintenance/maintainanceTaskDetails/index",
                  "meta": {
                    "title": "保养明细",
                    "icon": "date-range",
                    "noCache": false,
                    "link": null
                  }
                }
              ]
            },
            {
              "name": "EquipmentManage",
              "path": "equipmentManage",
              "hidden": true,
              "redirect": "noRedirect",
              "component": "ParentView",
              "alwaysShow": true,
              "meta": {
                "title": "设备管理",
                "icon": "tool",
                "noCache": false,
                "link": null
              },
              "children": [
                {
                  "name": "EquipmentLocation",
                  "path": "equipmentLocation",
                  "hidden": false,
                  "component": "wisdom/equipmentManage/equipmentLocation/index",
                  "meta": {
                    "title": "设备位置",
                    "icon": "nested",
                    "noCache": false,
                    "link": null
                  }
                }
              ]
            },
            {
              "name": "Questionnaire",
              "path": "questionnaire",
              "hidden": true,
              "component": "wisdom/questionnaire/index",
              "meta": {
                "title": "调查问卷",
                "icon": "24gl-fileText",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "EneryConsumption",
              "path": "eneryConsumption",
              "hidden": false,
              "redirect": "noRedirect",
              "component": "ParentView",
              "alwaysShow": true,
              "meta": {
                "title": "能耗管理",
                "icon": "example",
                "noCache": false,
                "link": null
              },
              "children": [
                {
                  "name": "EnergyConfig",
                  "path": "energyConfig",
                  "hidden": false,
                  "component": "wisdom/energy/configuration",
                  "meta": {
                    "title": "配置管理",
                    "icon": "dict",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "Record",
                  "path": "record",
                  "hidden": false,
                  "component": "wisdom/energy/record",
                  "meta": {
                    "title": "能耗填报",
                    "icon": "form",
                    "noCache": false,
                    "link": null
                  }
                }
              ]
            }
          ]
        },
        {
          "name": "BaseStationManage",
          "path": "/baseStationManage",
          "hidden": false,
          "redirect": "noRedirect",
          "component": "Layout",
          "alwaysShow": true,
          "meta": {
            "title": "基站管理",
            "icon": "icon-jizhanguanli",
            "noCache": false,
            "link": null
          },
          "children": [
            {
              "name": "LockUserManage",
              "path": "lockUserManage",
              "hidden": false,
              "component": "wisdom/base-station-manage/lock-user",
              "meta": {
                "title": "开锁人管理",
                "icon": "user",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "BaseStationManage",
              "path": "baseStationManage",
              "hidden": false,
              "component": "wisdom/base-station-manage/station",
              "meta": {
                "title": "基站管理",
                "icon": "build",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "StationApply",
              "path": "stationApply",
              "hidden": false,
              "component": "wisdom/base-station-manage/station-apply/index.vue",
              "meta": {
                "title": "开锁申请",
                "icon": "cascader",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "UnlockReport",
              "path": "unlockReport",
              "hidden": false,
              "component": "wisdom/base-station-manage/unlock-report",
              "meta": {
                "title": "开锁记录",
                "icon": "icon-caozuorizhi",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "BaseStation/key",
              "path": "baseStation/key",
              "hidden": false,
              "component": "wisdom/base-station-manage/key-manage",
              "meta": {
                "title": "钥匙查询",
                "icon": "lock",
                "noCache": false,
                "link": null
              }
            }
          ]
        },
        {
          "name": "Interace",
          "path": "/interace",
          "hidden": false,
          "redirect": "noRedirect",
          "component": "Layout",
          "alwaysShow": true,
          "meta": {
            "title": "交互中心",
            "icon": "wechat",
            "noCache": false,
            "link": null
          },
          "children": [
            {
              "name": "Manage",
              "path": "Manage",
              "hidden": false,
              "component": "system/notice/index",
              "meta": {
                "title": "通知公告",
                "icon": "education",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "Rotation",
              "path": "rotation",
              "hidden": false,
              "component": "system/rotation/index",
              "meta": {
                "title": "轮播图管理",
                "icon": "tab",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "Propose",
              "path": "propose",
              "hidden": false,
              "redirect": "noRedirect",
              "component": "ParentView",
              "alwaysShow": true,
              "meta": {
                "title": "意见反馈",
                "icon": "checkbox",
                "noCache": false,
                "link": null
              },
              "children": [
                {
                  "name": "Manage",
                  "path": "manage",
                  "hidden": false,
                  "component": "system/propose/manage/index",
                  "meta": {
                    "title": "模块配置",
                    "icon": "edit",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "Propose",
                  "path": "Propose",
                  "hidden": false,
                  "component": "system/propose/propose/index.vue",
                  "meta": {
                    "title": "意见反馈",
                    "icon": "email",
                    "noCache": false,
                    "link": null
                  }
                }
              ]
            },
            {
              "name": "Questionnaire",
              "path": "questionnaire",
              "hidden": false,
              "redirect": "noRedirect",
              "component": "ParentView",
              "alwaysShow": true,
              "meta": {
                "title": "调查问卷",
                "icon": "documentation",
                "noCache": false,
                "link": null
              },
              "children": [
                {
                  "name": "Questionnaire",
                  "path": "questionnaire",
                  "hidden": false,
                  "component": "wisdom/questionnaire/index",
                  "meta": {
                    "title": "问卷管理",
                    "icon": "documentation",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "Questionnaire/my-answer",
                  "path": "questionnaire/my-answer",
                  "hidden": false,
                  "component": "wisdom/questionnaire/my-answer/index",
                  "meta": {
                    "title": "我的问卷",
                    "icon": "dict",
                    "noCache": false,
                    "link": null
                  }
                }
              ]
            },
            {
              "name": "News",
              "path": "news",
              "hidden": false,
              "component": "system/news/index",
              "meta": {
                "title": "新闻咨询",
                "icon": "international",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "AddressList",
              "path": "addressList",
              "hidden": false,
              "component": "system/addressList/index",
              "meta": {
                "title": "通讯录",
                "icon": "list",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "Crowd",
              "path": "crowd",
              "hidden": false,
              "component": "system/crowd/management",
              "meta": {
                "title": "钉钉群管理",
                "icon": "tree",
                "noCache": false,
                "link": null
              }
            }
          ]
        },
        {
          "name": "PersonalCenter",
          "path": "/personalCenter",
          "hidden": false,
          "redirect": "noRedirect",
          "component": "Layout",
          "alwaysShow": true,
          "meta": {
            "title": "个人中心",
            "icon": "peoples",
            "noCache": false,
            "link": null
          },
          "children": [
            {
              "name": "PersonalCenter/userInfo",
              "path": "personalCenter/userInfo",
              "hidden": false,
              "component": "system/user/profile/index",
              "meta": {
                "title": "个人信息",
                "icon": "user",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "PersonalCenter/inAndoutRecord/index",
              "path": "personalCenter/inAndoutRecord/index",
              "hidden": false,
              "component": "wisdom/personal-center/access-record/index",
              "meta": {
                "title": "出入记录",
                "icon": "list",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "PersonalCenter/video-record",
              "path": "personalCenter/video-record",
              "hidden": false,
              "component": "wisdom/personal-center/video-record/index",
              "meta": {
                "title": "视频记录",
                "icon": "component",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "PersonalCenter/sign",
              "path": "personalCenter/sign",
              "hidden": false,
              "component": "wisdom/personal-center/sign/index",
              "meta": {
                "title": "签到记录",
                "icon": "build",
                "noCache": false,
                "link": null
              }
            }
          ]
        },
        {
          "name": "Pay",
          "path": "/pay",
          "hidden": false,
          "redirect": "noRedirect",
          "component": "Layout",
          "alwaysShow": true,
          "meta": {
            "title": "支付中心",
            "icon": "money",
            "noCache": false,
            "link": null
          },
          "children": [
            {
              "name": "Wisdom/pointsManagement",
              "path": "wisdom/pointsManagement",
              "hidden": false,
              "redirect": "noRedirect",
              "component": "ParentView",
              "alwaysShow": true,
              "meta": {
                "title": "积分管理",
                "icon": "number",
                "noCache": false,
                "link": null
              },
              "children": [
                {
                  "name": "Personal-points",
                  "path": "personal-points",
                  "hidden": false,
                  "component": "wisdom/points-management/personal-points/index",
                  "meta": {
                    "title": "个人积分",
                    "icon": "people",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "Recharge-points",
                  "path": "recharge-points",
                  "hidden": false,
                  "component": "wisdom/points-management/recharge-points/index",
                  "meta": {
                    "title": "积分充值",
                    "icon": "build",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "Point-application",
                  "path": "point-application",
                  "hidden": false,
                  "component": "wisdom/points-management/point-application/index",
                  "meta": {
                    "title": "积分申请",
                    "icon": "education",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "Integral-adjustment",
                  "path": "integral-adjustment",
                  "hidden": false,
                  "component": "wisdom/points-management/integral-adjustment/index",
                  "meta": {
                    "title": "积分调整",
                    "icon": "example",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "Account-adjust",
                  "path": "account-adjust",
                  "hidden": false,
                  "component": "wisdom/accountAdjust/index",
                  "meta": {
                    "title": "账号调整",
                    "icon": "user",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "Points-details",
                  "path": "points-details",
                  "hidden": false,
                  "component": "wisdom/points-management/points-details/index",
                  "meta": {
                    "title": "积分明细",
                    "icon": "documentation",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "Summary-points",
                  "path": "summary-points",
                  "hidden": false,
                  "component": "wisdom/points-management/summary-points/index",
                  "meta": {
                    "title": "积分汇总",
                    "icon": "chart",
                    "noCache": false,
                    "link": null
                  }
                }
              ]
            },
            {
              "name": "Ticket",
              "path": "ticket",
              "hidden": false,
              "redirect": "noRedirect",
              "component": "ParentView",
              "alwaysShow": true,
              "meta": {
                "title": "餐券管理",
                "icon": "row",
                "noCache": false,
                "link": null
              },
              "children": [
                {
                  "name": "Meal-voucher-management",
                  "path": "meal-voucher-management",
                  "hidden": false,
                  "component": "wisdom/points-management/meal-voucher-management/index",
                  "meta": {
                    "title": "餐券申请",
                    "icon": "icon-youhuiquan",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "Meal-voucher-distribution",
                  "path": "meal-voucher-distribution",
                  "hidden": false,
                  "component": "wisdom/points-management/meal-voucher-distribution/index",
                  "meta": {
                    "title": "餐券分发",
                    "icon": "guide",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "Meal-voucher-statistic/index",
                  "path": "meal-voucher-statistic/index",
                  "hidden": false,
                  "component": "wisdom/points-management/meal-voucher-statistic/index",
                  "meta": {
                    "title": "使用查询",
                    "icon": "table",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "Meal-voucher-statistic/deptIndex",
                  "path": "meal-voucher-statistic/deptIndex",
                  "hidden": false,
                  "component": "wisdom/points-management/meal-voucher-statistic/deptIndex",
                  "meta": {
                    "title": "部门汇总",
                    "icon": "tree-table",
                    "noCache": false,
                    "link": null
                  }
                }
              ]
            }
          ]
        },
        {
          "name": "Reconciliation",
          "path": "/reconciliation",
          "hidden": false,
          "redirect": "noRedirect",
          "component": "Layout",
          "alwaysShow": true,
          "meta": {
            "title": "对账模块",
            "icon": "icon-duizhangdan",
            "noCache": false,
            "link": null
          },
          "children": [
            {
              "name": "Reconciliation",
              "path": "reconciliation",
              "hidden": false,
              "component": "wisdom/reconciliation/index",
              "meta": {
                "title": "对账任务",
                "icon": "documentation",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "Reconciliation-statistics",
              "path": "reconciliation-statistics",
              "hidden": false,
              "component": "wisdom/reconciliation/reconciliation-statistics",
              "meta": {
                "title": "对账统计",
                "icon": "icon-navicon-cprkd",
                "noCache": false,
                "link": null
              }
            }
          ]
        },
        {
          "name": "Order",
          "path": "/order",
          "hidden": true,
          "redirect": "noRedirect",
          "component": "Layout",
          "alwaysShow": true,
          "meta": {
            "title": "订单中心",
            "icon": "list",
            "noCache": false,
            "link": null
          },
          "children": [
            {
              "name": "Consumption",
              "path": "consumption",
              "hidden": false,
              "component": "supplier/consumption/index",
              "meta": {
                "title": "消费订单查询",
                "icon": "chart",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "Summary",
              "path": "Summary",
              "hidden": false,
              "component": "supplier/consumption/orderSummary",
              "meta": {
                "title": "消费订单汇总查询",
                "icon": "chart",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "Http://172.16.11.166:30833/mall_manager/#/ssoJump?menu_code=102224001",
              "path": "http://172.16.11.166:30833/mall_manager/#/ssoJump?menu_code=102224001",
              "hidden": false,
              "component": "OuterLink",
              "meta": {
                "title": "商城订单查询",
                "icon": "chart",
                "noCache": false,
                "link": "http://172.16.11.166:30833/mall_manager/#/ssoJump?menu_code=102224001"
              }
            },
            {
              "name": "Dining",
              "path": "dining",
              "hidden": false,
              "component": "supplier/dining/index",
              "meta": {
                "title": "食堂消费订单",
                "icon": "chart",
                "noCache": false,
                "link": null
              }
            }
          ]
        },
        {
          "path": "/",
          "hidden": true,
          "component": "Layout",
          "children": [
            {
              "name": "/wisdom/appCenter",
              "path": "/wisdom/appCenter",
              "hidden": false,
              "meta": {
                "title": "应用中心",
                "icon": "#",
                "noCache": false,
                "link": null
              }
            }
          ]
        },
        {
          "name": " inventoryManagement",
          "path": "/ inventoryManagement",
          "hidden": true,
          "redirect": "noRedirect",
          "component": "Layout",
          "alwaysShow": true,
          "meta": {
            "title": "报表管理",
            "icon": "clipboard",
            "noCache": false,
            "link": null
          },
          "children": [
            {
              "name": "Https://***************:5678/lqyuzhong/loginsso?menu_code=1",
              "path": "https://***************:5678/lqyuzhong/loginsso?menu_code=1",
              "hidden": false,
              "component": "Layout",
              "meta": {
                "title": "食堂经营状况统计",
                "icon": "#",
                "noCache": false,
                "link": "https://***************:5678/lqyuzhong/loginsso?menu_code=1"
              }
            },
            {
              "name": "Https://***************:5678/lqyuzhong/loginsso?menu_code=2",
              "path": "https://***************:5678/lqyuzhong/loginsso?menu_code=2",
              "hidden": false,
              "component": "Layout",
              "meta": {
                "title": "财务统计",
                "icon": "#",
                "noCache": false,
                "link": "https://***************:5678/lqyuzhong/loginsso?menu_code=2"
              }
            },
            {
              "name": "Https://***************:5678/lqyuzhong/loginsso?menu_code=3",
              "path": "https://***************:5678/lqyuzhong/loginsso?menu_code=3",
              "hidden": false,
              "component": "Layout",
              "meta": {
                "title": "商城消费统计",
                "icon": "#",
                "noCache": false,
                "link": "https://***************:5678/lqyuzhong/loginsso?menu_code=3"
              }
            },
            {
              "name": "Https://***************:5678/lqyuzhong/loginsso?menu_code=4",
              "path": "https://***************:5678/lqyuzhong/loginsso?menu_code=4",
              "hidden": false,
              "component": "Layout",
              "meta": {
                "title": "食堂消费统计",
                "icon": "#",
                "noCache": false,
                "link": "https://***************:5678/lqyuzhong/loginsso?menu_code=4"
              }
            },
            {
              "name": "Https://***************:5678/lqyuzhong/loginsso?menu_code=5",
              "path": "https://***************:5678/lqyuzhong/loginsso?menu_code=5",
              "hidden": false,
              "component": "Layout",
              "meta": {
                "title": "消费次数统计",
                "icon": "#",
                "noCache": false,
                "link": "https://***************:5678/lqyuzhong/loginsso?menu_code=5"
              }
            }
          ]
        },
        {
          "name": "Management",
          "path": "/management",
          "hidden": false,
          "redirect": "noRedirect",
          "component": "Layout",
          "alwaysShow": true,
          "meta": {
            "title": "综合管理",
            "icon": "documentation",
            "noCache": false,
            "link": null
          },
          "children": [
            {
              "name": "AreaAadmin",
              "path": "areaAadmin",
              "hidden": false,
              "component": "wisdom/admittance/area-admin/index",
              "meta": {
                "title": "区域管理员",
                "icon": "user",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "AreaManagement",
              "path": "areaManagement",
              "hidden": false,
              "component": "system/area/index",
              "meta": {
                "title": "区域管理",
                "icon": "international",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "DeviceManage",
              "path": "deviceManage",
              "hidden": false,
              "component": "wisdom/admittance/device/management",
              "meta": {
                "title": "设备管理",
                "icon": "icon-rizhiguanli",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "UserIdentify/index",
              "path": "userIdentify/index",
              "hidden": false,
              "component": "wisdom/admittance/userIdentify/index",
              "meta": {
                "title": "人员标识",
                "icon": "peoples",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "UserConfig/index",
              "path": "userConfig/index",
              "hidden": false,
              "component": "wisdom/admittance/userConfig/index",
              "meta": {
                "title": "人员配置",
                "icon": "documentation",
                "noCache": false,
                "link": null
              }
            }
          ]
        },
        {
          "name": "System",
          "path": "/system",
          "hidden": false,
          "redirect": "noRedirect",
          "component": "Layout",
          "alwaysShow": true,
          "meta": {
            "title": "系统管理",
            "icon": "system",
            "noCache": false,
            "link": null
          },
          "children": [
            {
              "name": "Appversion",
              "path": "appversion",
              "hidden": false,
              "component": "wisdom/appversion/index",
              "meta": {
                "title": "App版本管理",
                "icon": "icon-qiyedingcan",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "System/dingtalk/dingtalk-sync.vue",
              "path": "system/dingtalk/dingtalk-sync.vue",
              "hidden": false,
              "component": "system/dingtalk/dingtalk-sync.vue",
              "meta": {
                "title": "钉钉同步",
                "icon": "international",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "User",
              "path": "user",
              "hidden": false,
              "component": "system/user/index",
              "meta": {
                "title": "用户管理",
                "icon": "user",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "UserBlackList",
              "path": "userBlackList",
              "hidden": false,
              "component": "system/userBlackList/index",
              "meta": {
                "title": "黑名单库",
                "icon": "user",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "Role",
              "path": "role",
              "hidden": false,
              "component": "system/role/index",
              "meta": {
                "title": "角色管理",
                "icon": "peoples",
                "noCache": true,
                "link": null
              }
            },
            {
              "name": "Menu",
              "path": "menu",
              "hidden": false,
              "component": "system/menu/index",
              "meta": {
                "title": "菜单管理",
                "icon": "tree-table",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "Dept",
              "path": "dept",
              "hidden": false,
              "component": "system/dept/index",
              "meta": {
                "title": "部门管理",
                "icon": "tree",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "Post",
              "path": "post",
              "hidden": false,
              "component": "system/post/index",
              "meta": {
                "title": "岗位管理",
                "icon": "post",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "Dict",
              "path": "dict",
              "hidden": false,
              "component": "system/dict/index",
              "meta": {
                "title": "字典管理",
                "icon": "dict",
                "noCache": true,
                "link": null
              }
            },
            {
              "name": "Config",
              "path": "config",
              "hidden": false,
              "component": "system/config/index",
              "meta": {
                "title": "参数设置",
                "icon": "edit",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "Notice",
              "path": "notice",
              "hidden": false,
              "component": "system/notice/index",
              "meta": {
                "title": "通知公告",
                "icon": "message",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "Log",
              "path": "log",
              "hidden": false,
              "redirect": "noRedirect",
              "component": "ParentView",
              "alwaysShow": true,
              "meta": {
                "title": "日志管理",
                "icon": "log",
                "noCache": false,
                "link": null
              },
              "children": [
                {
                  "name": "Operlog",
                  "path": "operlog",
                  "hidden": false,
                  "component": "monitor/operlog/index",
                  "meta": {
                    "title": "操作日志",
                    "icon": "form",
                    "noCache": false,
                    "link": null
                  }
                },
                {
                  "name": "Logininfor",
                  "path": "logininfor",
                  "hidden": false,
                  "component": "monitor/logininfor/index",
                  "meta": {
                    "title": "登录日志",
                    "icon": "logininfor",
                    "noCache": false,
                    "link": null
                  }
                }
              ]
            },
            {
              "name": "File",
              "path": "file",
              "hidden": false,
              "component": "system/attachmentMigration/index.vue",
              "meta": {
                "title": "附件迁移",
                "icon": "24gl-fileText",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "System/document/index",
              "path": "system/document/index",
              "hidden": false,
              "component": "system/document/index",
              "meta": {
                "title": "模板管理",
                "icon": "bug",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "HelpFile",
              "path": "helpFile",
              "hidden": false,
              "component": "wisdom/helpFile/index",
              "meta": {
                "title": "帮助文档",
                "icon": "24gl-fileText",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "Consume",
              "path": "consume",
              "hidden": false,
              "component": "system/consume/index",
              "meta": {
                "title": "消费管理",
                "icon": "list",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "Logodeploy",
              "path": "logodeploy",
              "hidden": false,
              "component": "system/logodeploy/index",
              "meta": {
                "title": "应用配置",
                "icon": "component",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "Leaderconfiguration",
              "path": "leaderconfiguration",
              "hidden": false,
              "component": "system/leaderconfiguration/index",
              "meta": {
                "title": "分管领导配置",
                "icon": "peoples",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "Watermark",
              "path": "watermark",
              "hidden": false,
              "component": "wisdom/watermark/index",
              "meta": {
                "title": "水印配置",
                "icon": "chart",
                "noCache": true,
                "link": null
              }
            }
          ]
        },
        {
          "name": "Tool",
          "path": "/tool",
          "hidden": false,
          "redirect": "noRedirect",
          "component": "Layout",
          "alwaysShow": true,
          "meta": {
            "title": "系统工具",
            "icon": "tool",
            "noCache": false,
            "link": null
          },
          "children": [
            {
              "name": "/sendMsg",
              "path": "/sendMsg",
              "hidden": false,
              "component": "wisdom/send-msg-record/index",
              "meta": {
                "title": "发送短信",
                "icon": "message",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "Build",
              "path": "build",
              "hidden": false,
              "component": "tool/build/index",
              "meta": {
                "title": "表单构建",
                "icon": "build",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "Swagger",
              "path": "swagger",
              "hidden": false,
              "component": "tool/swagger/index",
              "meta": {
                "title": "系统接口",
                "icon": "swagger",
                "noCache": false,
                "link": null
              }
            }
          ]
        },
        {
          "name": "Monitor",
          "path": "/monitor",
          "hidden": false,
          "redirect": "noRedirect",
          "component": "Layout",
          "alwaysShow": true,
          "meta": {
            "title": "系统监控",
            "icon": "monitor",
            "noCache": false,
            "link": null
          },
          "children": [
            {
              "name": "Online",
              "path": "online",
              "hidden": false,
              "component": "monitor/online/index",
              "meta": {
                "title": "在线用户",
                "icon": "online",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "Job",
              "path": "job",
              "hidden": false,
              "component": "monitor/job/index",
              "meta": {
                "title": "定时任务",
                "icon": "job",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "Druid",
              "path": "druid",
              "hidden": false,
              "component": "monitor/druid/index",
              "meta": {
                "title": "数据监控",
                "icon": "druid",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "Server",
              "path": "server",
              "hidden": false,
              "component": "monitor/server/index",
              "meta": {
                "title": "服务监控",
                "icon": "server",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "Cache",
              "path": "cache",
              "hidden": false,
              "component": "monitor/cache/index",
              "meta": {
                "title": "缓存监控",
                "icon": "redis",
                "noCache": false,
                "link": null
              }
            }
          ]
        },
        {
          "name": "Flowable",
          "path": "/flowable",
          "hidden": false,
          "redirect": "noRedirect",
          "component": "Layout",
          "alwaysShow": true,
          "meta": {
            "title": "流程管理",
            "icon": "drag",
            "noCache": false,
            "link": null
          },
          "children": [
            {
              "name": "Definition",
              "path": "definition",
              "hidden": false,
              "component": "flowable/definition/index",
              "meta": {
                "title": "流程定义",
                "icon": "job",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "Form",
              "path": "form",
              "hidden": false,
              "component": "flowable/task/form/index",
              "meta": {
                "title": "表单配置",
                "icon": "form",
                "noCache": false,
                "link": null
              }
            }
          ]
        },
        {
          "path": "/",
          "hidden": false,
          "component": "Layout",
          "children": [
            {
              "name": "Bigscreen",
              "path": "bigscreen",
              "hidden": false,
              "component": "wisdom/bigscreen/index",
              "meta": {
                "title": "数据大屏",
                "icon": "build",
                "noCache": false,
                "link": null
              }
            }
          ]
        },
        {
          "name": "Demo",
          "path": "/demo",
          "hidden": false,
          "redirect": "noRedirect",
          "component": "Layout",
          "alwaysShow": true,
          "meta": {
            "title": "样式例子",
            "icon": "icon-lizitubiao",
            "noCache": false,
            "link": null
          },
          "children": [
            {
              "name": "Demo",
              "path": "demo",
              "hidden": false,
              "component": "demo/index1",
              "meta": {
                "title": "普通表单页面",
                "icon": "edit",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "Index2",
              "path": "index2",
              "hidden": false,
              "component": "demo/index2",
              "meta": {
                "title": "树结构可修改",
                "icon": "date-range",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "Index3",
              "path": "index3",
              "hidden": false,
              "component": "demo/index3",
              "meta": {
                "title": "tab页签",
                "icon": "dict",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "Index5",
              "path": "index5",
              "hidden": false,
              "component": "demo/index5",
              "meta": {
                "title": "左右区域",
                "icon": "download",
                "noCache": false,
                "link": null
              }
            }
          ]
        }
      ]
    }
    let menus = response.data;
    menus.forEach((item) => {
      if (item.name == "ShortcutMenu") {
        let menuLists = [];
        for (var i = 0; i < item.children.length; i += 4) {
          menuLists.push(item.children.slice(i, i + 4));
        }
        menuList.value = menuLists
      }
    });

  } catch (error) {
    console.error(error);
  }
};


// 组件挂载时加载数据
onMounted(() => {
  menuLists();
});
</script>

<style scoped lang="scss">
.big-common-box-one {
  background: #ffffff;
  border-radius: 4px;
  width: 100%;
  padding:0 10px 10px;
 
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); // 添加阴影效果
  transition: box-shadow 0.3s ease;
  box-sizing: border-box;

  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2); // 悬停时加深阴影
  }
}

.common-header {
  height: 45px;
  line-height: 45px;
  border-bottom: 1px solid #e8eaeb;
  box-sizing: border-box;
  padding-bottom: 0px;

  .left-header-text {
    font-size: 15px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #333333;
  }

  .header-right-btn {
    font-size: 13px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #9ea4aa;
    cursor: pointer;
  }
}

.common-content {
  height: calc(100% - 45px);
}


.quick-box-link {
  height: 100%;

  .el-row {
  }

  .quick-box-link-one {
    margin-top: 36px;
    display: flex;
    align-items: center;
    flex-direction: column;
    cursor: pointer;

    img {
      width: 50px;
      height: 50px;
      margin-bottom: 8px;
    }

    .quick-box-link-text {
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #333333;
    }
  }
}


</style>
