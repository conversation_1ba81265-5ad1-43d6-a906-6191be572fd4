import request from "@/utils/request";


// 查询用户标识列表
export function listIdentify(data) {
  return request({
    url: "/user/staffsIdentify/list",
    method: "post",
    data: data
  });
}


// 查看人脸
export function getFaceImage(data) {
  return request({
    url: "/user/staffsIdentify/getInfo",
    method: "post",
    data: data
  });
}

// 根据登录名称和租户id查看人脸
export function getFaceImageInfo(data) {
  return request({
    url: "/user/staffsIdentify/getDetail",
    method: "post",
    data: data
  });
}

// 删除人脸数据
export function deleteFaceImage(data) {
  return request({
    url: "/user/staffsIdentify/delFace",
    method: "post",
    data: data
  });
}


// 修改用户标识列表
export function updateStaffIdentify(data) {
  return request({
    url: "/user/staffsIdentify/edit",
    method: "post",
    data: data
  });
}

// 查询uuid
export function getUUId() {
  return request({
    url: "/user/staffFace/getUUid",
    method: "get",
    //params: data
  });
}


// 查询用户人脸列表
export function listStaffFace(data) {
  return request({
    url: "/user/staffFace/list",
    method: "post",
    data: data
  });
}


// 新增用户人脸
export function addStaffFace(data) {
  return request({
    url: "/user/staffFace/add",
    method: "post",
    data: data
  });
}
