import request from '@/utils/request'
import { apiUrl } from '@/utils/config';
export class screenIndex {
    // 查询
    static dateManagementList(data, params) {
        return request({
            url: `user${apiUrl}/dateManagement/list`,
            method: "post",
            data: {...data,...params},
            params: params
        })
    }


    // 修改
    static dateManagementUpdate(data) {
        return request({
            url: `user${apiUrl}/dateManagement/update`,
            method: 'post',
            data: data
        })
    }




}
