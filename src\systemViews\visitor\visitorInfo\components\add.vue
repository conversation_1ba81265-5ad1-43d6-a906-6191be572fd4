<!-- 访客信息新增修改查看 -->
<template>
  <div
    class="dialog-box"
    :class="popupType === 'view' ? 'dialog-box-view' : 'dialog-box-edit'"
  >
    <el-form
      ref="formRef"
      :model="formData"
      label-width="100px"
      :rules="rules"
    >
      <el-row>
        <el-col :span="12">
          <el-form-item label="访客姓名" prop="name">
              <el-input
               v-if="popupType!='view'"
                :disabled="true"
                placeholder="请输入访客姓名"
                v-model="formData.name"
                clearable
              ></el-input>
            <div v-else>{{formData.name}}</div> 
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="访客电话" prop="telephone">
              <el-input
              v-if="popupType!='view'"
                :disabled="true"
                placeholder="请输入访客电话"
                v-model="formData.telephone"
                clearable
              ></el-input>
              <div v-else>{{formData.telephone}}</div> 
          </el-form-item>
        </el-col>
      </el-row>

      <el-row v-if="isIdCard">
        <el-col :span="12">
          <el-form-item label="证件号码" prop="idNumber">
              <el-input
               v-if="popupType!='view'"
                :disabled="true"
                placeholder="请输入证件号码"
                v-model="formData.idNumber"
                clearable
              ></el-input>
              <div v-else>{{formData.idNumber}}</div> 
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="访客单位" prop="visitorUnit">
              <el-input
              v-if="popupType!='view'"
                :disabled="true"
                placeholder="请输入访客单位"
                v-model="formData.visitorUnit"
                clearable
              ></el-input>
              <div v-else>{{formData.visitorUnit}}</div> 
          </el-form-item>
        </el-col>
      </el-row>

      <el-row v-if="isIdCard">
        <el-col :span="12">
          <el-form-item label="注册时间" prop="registTime">
            <el-date-picker
              :disabled="true"
              v-model="formData.registTime"
              type="datetime"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
              placeholder="选择注册时间"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row v-if="isIdCard">
        <el-col :span="24">
          <el-form-item label="备注" prop="remark" v-if="formData.type === '1'">
            <el-input
              v-if="popupType!='view'"
              :disabled="popupType == 'view'"
              type="textarea"
              placeholder="请填写黑名单原因"
              v-model="formData.remark"
              clearable
            ></el-input>
            <div v-else>{{formData.remark}}</div> 
          </el-form-item>
        </el-col>
      </el-row>

      <el-row v-if="!isIdCard">
        <el-col :span="12">
          <el-form-item label="访客单位" prop="visitorUnit">
              <el-input
               v-if="popupType!='view'"
                :disabled="true"
                placeholder="请输入访客单位"
                v-model="formData.visitorUnit"
                clearable
              ></el-input>
              <div v-else>{{formData.visitorUnit}}</div> 
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="注册时间" prop="registTime">
            <el-date-picker
            v-if="popupType!='view'"
              :disabled="true"
              v-model="formData.registTime"
              type="datetime"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              placeholder="选择注册时间"
            >
            </el-date-picker>
            <div v-else>{{formData.registTime}}</div> 
          </el-form-item>
        </el-col>
      </el-row>

      <el-row v-if="!isIdCard">
        <el-col :span="12">
          <el-form-item label="访客类型" prop="type">
            <el-select
             v-if="popupType!='view'"
              :disabled="popupType == 'view'"
              v-model="formData.type"
              placeholder="请选择访客类型"
            >
              <el-option
                v-for="(item, index) of VisitorType"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <div v-else>{{ $formatDictLabel(formData.type,VisitorType) }}</div> 
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="备注" prop="remark" v-if="formData.type === '1'">
            <el-input
            v-if="popupType!='view'"
              :disabled="popupType == 'view'"
              type="textarea"
              placeholder="请填写黑名单原因"
              v-model="formData.remark"
              clearable
            ></el-input>
            <div v-else>{{ formData.remark }}</div> 
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="照片" prop="pictures">
        

            <ImageUpload
                v-if="popupType !== 'view'"
                v-model="fileList"
                limit="1"
                fileSize="10"
                :paramsData="imageExtraData"
                :uploadImgUrl="
                '/park' +  apiUrls + '/visitor/visitorInfo/uploadFaceImg'
                "
              />
              <!-- 查看模式显示图片 -->
              <PreviewImage style="width: 150px;"  v-if="popupType === 'view' && formData.faceImg" :photo-id="formData.faceImg"></PreviewImage>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { appCode } from "@/utils/config";
import { apiUrl } from "@/utils/config";
import { updateVisitorInfo } from "@/api/visitor/visitorInfo/components/add";
import { getConfigInfo } from '@/api/visitor/visitorParameterConfig/index'
import { selectVisitorInfo } from '@/api/visitor/visitorInfo/index'


const apiUrls = ref(apiUrl);
const props = defineProps({
  closeBtn: Function,
  popupType: {
    type: String,
    default: "",
  },
  rowData: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(["update:modelValue"]);

// 响应式状态
const VisitorType = reactive ([{label: "白名单", value: "0"}, {label: "黑名单", value: "1"}])
const formRef = ref(null);
const isIdCard = ref(false);
const formData = reactive({ ...props.rowData });
const fileList = ref([]);
const imageExtraData = reactive({ businessId: "", appCode: appCode });

// 验证规则
const rules = reactive({
  name: [{ required: true, message: "请选择访客姓名", trigger: "blur" }],
  telephone: [{ required: true, message: "请选择访客电话", trigger: "blur" }],
  idNumber: [{ required: true, message: "请选择证件号码", trigger: "blur" }],
  visitorUnit: [{ required: true, message: "请选择访客单位", trigger: "blur" }],
  registTime: [{ required: true, message: "请选择注册时间", trigger: "blur" }],
  type: [{ required: true, message: "请选择访客类型", trigger: "change" }],
  remark: [{ required: true, message: "请填写黑名单原因", trigger: "blur" }],
});

// 生命周期
onMounted(() => {
  initializeData();
  getConfig();
});

// 初始化数据
const initializeData = async () => {
  if (["edit", "view"].includes(props.popupType)) {
    try {
      const res = await selectVisitorInfo(formData.id);
      if (res.code === 200 && res.data?.encodeToBase64) {
        const base64Image = `data:image/png;base64,${res.data.encodeToBase64}`;
        fileList.value = [{ name: "", url: base64Image }];
      }
      imageExtraData.businessId = formData.visitorTelephone;
    } catch (error) {
      console.error("数据初始化失败:", error);
    }
  }
};

// 获取配置
const getConfig = async () => {
  try {
    const res = await getConfigInfo();
    isIdCard.value = res.rows[0]?.isIdcard === "0";
  } catch (error) {
    console.error("获取配置失败:", error);
  }
};



// 保存操作
const saveBtn = async () => {
  try {
    // 1. 执行表单验证
    await formRef.value.validate()

    // 2. 处理文件上传字段
    formData.value.faceImg = fileList.value[0]?.fileId || ""

    // 3. 根据操作类型选择API方法
    const apiMethod = props.popupType === "add" 
      ? updateVisitorInfo
      : updateVisitorInfo

    // 4. 调用接口
    const { code, message } = await apiMethod(formData.value)

    // 5. 处理响应
    if (code === "1") {
      ElMessage.success({
        message: props.popupType === "add" ? "新增成功" : "修改成功",
        duration: 2000
      })
      emit("submitClose") // 统一使用事件触发关闭
    }
  } catch (error) {
    console.error("保存操作失败:", error)
    // 显示可读性错误提示
    ElMessage.error({
      message: error.message || "请求失败，请检查网络后重试",
      duration: 3000
    })
  }
}

defineExpose({
  saveBtn
})

</script>

<style scoped lang="scss">
</style>