<template>
  <div class="app-container">
    <el-card shadow="never">
    <!--    查询-->
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
      <el-form-item label="单位名称" prop="companyName">
        <el-input v-model="queryParams.companyName" placeholder="请输入单位名称" clearable style="width: 180px"
                  @keyup.enter="handleQuery"/>
      </el-form-item>

      <el-form-item label="联系人" prop="contactPerson">
        <el-input v-model="queryParams.contactPerson" placeholder="请输入联系人" clearable style="width: 180px"
                  @keyup.enter="handleQuery">
        </el-input>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <!--  刷新按钮-->
    <el-row :gutter="10" class="mb8">
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <!--  展示-->
    <el-table v-loading="loading" :data="dataList">
      <el-table-column prop="tenantName" align="left" label="租户名称" :show-overflow-tooltip="true" ></el-table-column>
      <el-table-column prop="companyName" align="left"  label="单位名称" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column prop="contactPerson" align="left"  label="联系人" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column prop="contactTelephone" align="left"  label="联系方式" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column prop="agentNum" align="left"  label="坐席数量" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column prop="description" align="left"  label="备注" :show-overflow-tooltip="true"></el-table-column>

      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                     :loading=" reloadId === scope.row.tenantExtendId && reloadType === 'edit'"
                     v-hasPermi="['sys:tenant:tenant-ext:edit']">修改
          </el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
                     :loading="reloadId === scope.row.tenantExtendId && reloadType === 'remove'"
                     v-hasPermi="['sys:tenant:tenant-info:remove']">删除
          </el-button>

        </template>
      </el-table-column>
    </el-table>

    <!--    分页器-->
    <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
    />
    <!-- 修改参数配置对话框 -->
    <el-dialog
        title="修改租户"
        v-model="open"
        width="1000px"
        append-to-body
        @close="cancel"
        :close-on-press-escape="false"
    >
      <el-form ref="formRef" :model="form" :rules="rules" label-width="150px">
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="租户名称" prop="tenantName">
              <el-input
                  v-model="form.tenantName"
                  placeholder="请输入租户名称"
                  maxlength="64"
                  disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单位名称" prop="companyName">
              <el-input
                  v-model="form.companyName"
                  placeholder="请输入单位名称"
                  maxlength="64"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系人" prop="contactPerson">
              <el-input
                  v-model="form.contactPerson"
                  placeholder="请输入联系人"
                  maxlength="64"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系方式" prop="contactTelephone">
              <el-input
                  v-model="form.contactTelephone"
                  placeholder="请输入联系方式"
                  maxlength="64"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="坐席数量" prop="agentNum">
              <el-input
                  v-model="form.agentNum"
                  placeholder="请输入坐席数量"
                  maxlength="64"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="备注" prop="description">
              <el-input
                  v-model="form.description"
                  placeholder="请输入备注"
                  maxlength="64"
              />
            </el-form-item>
          </el-col>

        </el-row>

      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm" :loading="saveLoading">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>

    </el-dialog>
    </el-card>
  </div>
</template>

<script setup name="TenantExt">
import { page, getById, update, del } from "@/api/tenant/tenantExt";
import {ElMessage, ElMessageBox} from "element-plus";
const {proxy} = getCurrentInstance();

//---------------------查询列表
// <editor-fold desc="查询列表">
const total = ref(0)

const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  companyName: undefined,
  contactPerson: undefined
})
const showSearch = ref(true);
const dataList = ref([])
const loading = ref(true)

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 查询列表 */
function getList() {
  loading.value=true
  page({
    ...queryParams.value,
  }).then(response => {
    if (response.success) {
      total.value = response.data.total
      dataList.value = response.data.records
    }
    loading.value = false;
  }).catch(() => {
    loading.value = false;
  })
}

getList()
// </editor-fold>

//修改相关
const open=ref(false)
const formRef = ref();
const form = ref({});
const rules = ref({
  companyName: [
    { required: true, message: "单位名称不能为空", trigger: "blur" },
  ],
  contactPerson: [
    { required: true, message: "联系人不能为空", trigger: "blur" },
  ],
  contactTelephone: [
    { required: true, message: "联系方式不能为空", trigger: "blur" },
    {
      pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
      message: "请输入正确的联系方式",
      trigger: "blur"
    }
  ]
})
const saveLoading = ref(false)
const reloadId = ref()
const reloadType = ref()
function reset(){
  form.value={
    tenantName: undefined,
    companyName: undefined,
    contactPerson: undefined,
    contactTelephone: undefined,
    agentNum: undefined,
    description: undefined
  }
  proxy.resetForm("formRef")
}
function handleUpdate(row) {
  reset();
  let tenantExtendId=row.tenantExtendId;
  reloadId.value=tenantExtendId;
  reloadType.value = "edit"
  getById(tenantExtendId).then((response)=>{
    if (response.data){
      form.value = response.data;
      open.value = true;
    }else {
      ElMessage.error("数据异常")
    }
  })
}
function submitForm(){
  formRef.value.validate((valid)=>{
    if (valid) {
      saveLoading.value=true;
      update(form.value).then((response)=>{
        if (!response.success) {
          ElMessage.error(response.message);
          saveLoading.value = false;
        } else {
          ElMessage.success("修改成功")
          open.value = false;
          saveLoading.value = false;
          getList();
        }
      })
    }
  })
}
function cancel(){
  open.value=false;
  reloadId.value = undefined;
  reloadType.value = undefined;
  reset();

}
//删除
function handleDelete(row) {
  let tenantExtendId = row.tenantExtendId;
  reloadType.value = "remove"
  reloadId.value = tenantExtendId;
  ElMessageBox.confirm(
      '是否确认删除"' + row.tenantName + '"的数据项?',
      "警告",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }
  ).then(()=>del(tenantExtendId)).then(() => {
    reloadId.value = undefined;
    reloadType.value = undefined;
    getList();
    ElMessage.success("删除成功")
  }).catch(() => {
    reloadId.value = undefined;
    reloadType.value = undefined;
  })
}
</script>

<style scoped>

</style>
