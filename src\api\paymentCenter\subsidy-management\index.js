import request from '@/utils/request'
import { apiUrl } from '@/utils/config'; 



export class screenIndex {

    static pageList(data,params) {
      return request({
        url: `pay${apiUrl}/pay/subsidy/list`,
        method: 'post',
        data: {...data,...params},
      }) 
    }
    
  //   供应商列表

  static supplierPageList(data) {
    return request({
      url: `pay${apiUrl}/pay/payProvider/pageList`,
      method: 'post',
      data:data
    })
  }

  //账户设置列表
    static payAccountTypePageList(data,params) {
      return request({
        url: `pay${apiUrl}/pay/payAccountType/pageList`,
        method: 'post',
        data: {...data,...params},
      })
    }

    //获取补贴管理详细信息
    static getInfo(data) {
      return request({
        url: `pay${apiUrl}/pay/subsidy/getInfo`,
        method: 'post',
        data
      })
    }
    
    //查询供应商树
    static providerTree(data) {
      return request({
        url: `pay${apiUrl}/pay/subsidy/providerTree`,
        method: 'post',
        data
      })
    }

    //查询支付类型树
    static payTypeTree(data) {
      return request({
        url: `pay${apiUrl}/pay/subsidy/payTypeTree`,
        method: 'post',
        data
      })
    }
    static paySceneTree(data) {
      return request({
        url: `pay${apiUrl}/pay/subsidy/paySceneTree`,
        method: 'post',
        data
      })
    }
    //查询账户树
    static accountTree(data) {
      return request({
        url: `pay${apiUrl}/pay/subsidy/accountTree`,
        method: 'post',
        data
      })
    }
    //新增补贴管理
    static add(data) {
      return request({
        url: `pay${apiUrl}/pay/subsidy/add`,
        method: 'post',
        data
      })
    }

    //修改补贴管理

    static edit(data) {
      return request({
        url: `pay${apiUrl}/pay/subsidy/edit`,
        method: 'post',
         data
      })
    }

    //修改补贴管理状态
    static editStatus(data) {
      return request({
        url: `pay${apiUrl}/pay/subsidy/editStatus`,
        method: 'post',
        data
      })
    }

    //删除补贴管理
    static delete(data) {
      return request({
        url: `pay${apiUrl}/pay/subsidy/delete`,
        method: 'post',
        data
      })
    }




}