<template>
  <div class="app-container">
    <Splitpanes class="default-theme">
      <Pane :size="15" :min-size="15">
        <el-card class="dep-card" style="height: 100%">
          <template #header>
            <div class="card-header">
              <span>用户组</span>
              <div style="float: right; padding: 3px">
                <el-button
                    v-if="selectNode && selectNode.orgType !== 'group'"
                    style="padding: 0; margin-left: 3px" link icon="Plus" type="primary"
                    v-hasPermi="['sys:base:user-group:add']"
                    @click="handleGroupAdd">新增
                </el-button>
                <el-button
                    v-if="selectNode && selectNode.orgType === 'group'"
                    style="padding: 0px; margin-left: 3px" link icon="Edit" type="primary"
                    v-hasPermi="['sys:base:user-group:edit']"
                    @click="handleGroupEdit">编辑
                </el-button>
                <el-button
                    v-if="selectNode && selectNode.orgType === 'group'"
                    style="padding: 0px; margin-left: 3px" link icon="Delete" type="primary"
                    v-hasPermi="['sys:base:user-group:remove']"
                    @click="handleGroupDelete">删除
                </el-button>
                <el-button
                    v-if="selectNode" type="primary"
                    style="padding: 0px; margin-left: 3px"
                    link icon="Refresh"
                    @click="reloadTree">刷新
                </el-button>
              </div>
              <el-form style="margin-top: 40px;margin-bottom: -20px;" v-if="userType === 'admin'">
                <el-form-item label="租户：">
                  <el-select
                      v-model="queryParams.tenantId"
                      style="width: 100px;"
                      remote
                      :remote-method="getTenantList"
                      :loading="getTenantLoading"
                      @change="tenantChange"
                  >
                    <el-option
                        v-for="item in tenantList"
                        :key="item.tenantId"
                        :label="item.tenantName"
                        :value="item.tenantId"
                    />
                  </el-select>
                </el-form-item>
              </el-form>
            </div>
          </template>
          <!-- 组织树 -->
          <el-tree
              :props="{label:'orgName',children:'children',isLeaf:(data) => !data.isParent} "
              :load="loadNode"
              lazy
              :expand-on-click-node="false"
              ref="asyncTree"
              @node-click="handleNodeClick"
              :default-expanded-keys="[defaultOrgId]"
              node-key="orgId"
              highlight-current
          >
          </el-tree>
        </el-card>
      </Pane>
      <Pane :size="85" :min-size="65">
        <el-card class="dep-card" shadow="never" v-loading="treeLoading">
          <template #header>
            <div class="clearfix">
              <span>{{ selectNodeName }}</span>
              <el-button
                  style="float: right; padding: 3px 0"
                  link icon="Refresh"
                  @click="allUser">全部人员
              </el-button>
            </div>
          </template>
          <el-form
              :model="queryParams"
              ref="queryForm"
              :inline="true"
              v-show="showSearch"
              label-width="68px"
          >
            <el-form-item label="用户名称" prop="staffName">
              <el-input
                  v-model="queryParams.staffName"
                  placeholder="请输入用户名称"
                  clearable
                  style="width: 240px"
                  @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item label="手机号" prop="cellphone">
              <el-input
                  v-model="queryParams.cellphone"
                  placeholder="请输入手机号"
                  clearable
                  style="width: 240px"
                  @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item label="邮箱" prop="email">
              <el-input
                  v-model="queryParams.email"
                  placeholder="请输入邮箱"
                  clearable
                  style="width: 240px"
                  @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>

          <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
              <el-button :disabled="!(selectNode && selectNode.orgType === 'group')"
                         type="primary" plain icon="User" @click="handleAdd"
                         v-hasPermi="['sys:base:user-group:add-user']">新增用户
              </el-button>
            </el-col>
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
          </el-row>

          <el-table v-loading="loading" :data="userList">
            <el-table-column
                label="用户编号"
                align="left"
                key="staffId"
                prop="staffId"
                v-if="columns[0].visible"
            />
            <el-table-column
                label="登录名称"
                align="left"
                key="loginName"
                prop="loginName"
                v-if="columns[1].visible"
            />
            <el-table-column
                label="用户名称"
                align="left"
                key="staffName"
                prop="staffName"
                v-if="columns[2].visible"
                :show-overflow-tooltip="true"
            />
            <el-table-column
                label="工作组名称"
                align="left"
                key="orgName"
                prop="orgName"
                v-if="columns[3].visible"
                :show-overflow-tooltip="true"
            />
            <el-table-column
                label="手机号码"
                align="left"
                key="cellphone"
                prop="cellphone"
                v-if="columns[4].visible"
            />
            <el-table-column
                label="邮箱"
                align="left"
                key="email"
                prop="email"
                v-if="columns[5].visible"
            />
            <el-table-column
                label="操作"
                align="center"
                width="160"
                class-name="small-padding fixed-width"
            >
              <template #default="scope">
                <el-button
                    v-if="scope.row.staffId !== 1"
                    link icon="Delete" type="primary"
                    @click="handleDelete(scope.row)"
                    v-hasPermi="['sys:base:user-group:remove-user']">删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination
              v-show="total > 0"
              :total="total"
              v-model:page="queryParams.pageNum"
              v-model:limit="queryParams.pageSize"
              @pagination="getList"
          />
        </el-card>
      </Pane>
    </Splitpanes>
    <!-- 添加或修改用户 -->
    <el-drawer
        ref="orgUserFormDialogRef"
        :title="`当前用户组：${selectNode && selectNode.orgName}`"
        v-model="open"
        size="70%"
        @close="cancel">
      <div class="drawer-content">
        <Splitpanes class="default-theme">
          <Pane :size="15" :min-size="15">
            <el-card class="drawer-card" shadow="never" v-loading="treeLoading">
              <template #header>
                <div class="clearfix">
                  <span>组织</span>
                  <el-button
                      v-if="orgUserFormDialog.selectNode"
                      style="float: right; padding: 3px 0"
                      link icon="Refresh" type="primary"
                      @click="reloadTree">刷新
                  </el-button>
                </div>
              </template>
              <el-tree
                  :props="{label:'orgName',children:'children',isLeaf:(data) => !data.isParent}"
                  :load="loadNodeOrg"
                  lazy
                  :expand-on-click-node="false"
                  :default-expanded-keys="[defaultOrgId]"
                  ref="asyncTreeAddUser"
                  @node-click="handleNodeClickAddUser"
                  node-key="orgId"
                  highlight-current
              >
              </el-tree>
            </el-card>
          </Pane>
          <Pane :size="85" :min-size="65">
            <el-card class="drawer-card" shadow="never" v-loading="treeLoading">
              <template #header>
                <div class="clearfix">
                  <span>{{ orgUserFormDialog.selectNodeName }}</span>
                </div>
              </template>
              <el-row :gutter="10" class="mb8">
                <right-toolbar v-model:showSearch="orgUserFormDialog.showSearch" @queryTable="getList"
                               :columns="columns2"></right-toolbar>
              </el-row>
              <el-table
                  v-loading="orgUserFormDialog.loading"
                  :data="orgUserFormDialog.userList"
                  @selection-change="handleSelectionChangeAddUser"
                  max-height="650">
                <el-table-column type="selection" width="50" align="center"/>
                <el-table-column
                    label="用户编号"
                    align="left"
                    key="staffId"
                    prop="staffId"
                    v-if="columns2[0].visible"
                />
                <el-table-column
                    label="登录名称"
                    align="left"
                    key="loginName"
                    prop="loginName"
                    v-if="columns2[1].visible"
                />
                <el-table-column
                    label="用户名称"
                    align="left"
                    key="staffName"
                    prop="staffName"
                    v-if="columns2[2].visible"
                    :show-overflow-tooltip="true"
                />
                <el-table-column
                    label="组织名称"
                    align="left"
                    key="orgName"
                    prop="orgName"
                    v-if="columns2[3].visible"
                    :show-overflow-tooltip="true"
                />
                <el-table-column
                    label="手机号码"
                    align="left"
                    key="cellphone"
                    prop="cellphone"
                    v-if="columns2[4].visible"
                />
                <el-table-column
                    label="邮箱"
                    align="left"
                    key="email"
                    prop="email"
                    v-if="columns2[5].visible"
                />
              </el-table>
              <pagination
                  v-show="orgUserFormDialog.total > 0"
                  :total="orgUserFormDialog.total"
                  v-model:page="orgUserFormDialog.queryParams.pageNum"
                  v-model:limit="orgUserFormDialog.queryParams.pageSize"
                  @pagination="getList"
              />
            </el-card>
          </Pane>
        </Splitpanes>
        <div class="demo-drawer__footer">
          <el-button
              type="primary"
              @click="submitSelectUserForm"
              :loading="selectSaveLoading">确 定
          </el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </div>
    </el-drawer>
    <!-- 添加或修改部门对话框 -->
    <el-dialog
        :title="orgFormDialog.title"
        v-model="orgFormDialog.open"
        width="600px"
        append-to-body
        @close="cancel">
      <el-form
          ref="orgFormRef"
          :model="orgForm"
          :rules="orgFormRules"
          label-width="80px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="归属组织" prop="parentName">
              <el-input
                  v-model="orgForm.parentName"
                  placeholder="请选择归属组织"
                  disabled>
                <template #append v-if="orgFormDialog.title === '编辑用户组'">
                  <el-link @click="proxy.$refs.treeSelect.open()">选择</el-link>
                </template>
              </el-input>
            </el-form-item>
            <dept-select
                ref="treeSelect"
                name=""
                value=""
                @selected="selected"/>
            <el-form-item v-show="false">
              <el-input v-model="orgForm.parentId"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用户组" prop="orgName">
              <el-input v-model="orgForm.orgName" placeholder="请输入用户组名称"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="显示排序" prop="orgSort">
              <el-input-number
                  v-model="orgForm.orgSort"
                  controls-position="right"
                  :min="0"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="租户">
              <el-input
                  v-model="orgForm.tenantName"
                  placeholder="租户"
                  maxlength="50"
                  disabled/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="菜单权限">
              <el-checkbox
                  v-model="menuExpand"
                  @change="handleCheckedTreeExpand($event, 'menu')">展开/折叠
              </el-checkbox>
              <el-checkbox
                  v-model="menuNodeAll"
                  @change="handleCheckedTreeNodeAll($event, 'menu')">全选/全不选
              </el-checkbox>
              <el-checkbox
                  v-model="menuCheckStrictly"
                  @change="handleCheckedTreeConnect($event, 'menu')">父子联动
              </el-checkbox>
              <el-tree
                  class="tree-border"
                  :data="permissionOptions"
                  show-checkbox
                  ref="menu"
                  node-key="permissionId"
                  :check-strictly="!menuCheckStrictly"
                  empty-text="加载中，请稍后"
                  :props="defaultMenuProps"
                  :default-checked-keys="orgFormDialog.checkedMenuIds">
              </el-tree>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button
              type="primary"
              @click="submitOrgForm"
              :loading="saveGroupLoading">确 定
          </el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="userGroup">
import {ref} from "vue";
import {listUser, delUserForGroup, addUserForGroup} from "@/api/system/user";
import {getToken} from "@/utils/auth";
import {treeselect, addDept, getDept, updateDept, delDept} from "@/api/system/dept";
import {treeselect as menuTreeselect, getOrgMenu} from "@/api/system/menu";
import {getTenants} from "@/api/tenant/tenant";
import DeptSelect from "@/systemViews/system/dept/components/deptSelect";
import useUserStore from "@/store/modules/user";
import {arrayToTree} from "@/utils";

const {proxy} = getCurrentInstance();
const userStore = useUserStore();
const userType = userStore.userInfo.customParam.userType;

const selectSaveLoading = ref(false);
const saveGroupLoading = ref(false);
const getTenantLoading = ref(false);
// 遮罩层
const loading = ref(true);
// 非单个禁用
const single = ref(true);
// 非多个禁用
const multiple = ref(true);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 用户表格数据
const userList = ref([]);
// 用户表格数据
const userListAddUser = ref([]);
const tenantList = ref([]);
// 弹出层标题
const title = ref("");
// 部门树选项
const deptOptions = ref([]);
const deptOptionsAll = ref([]);
// 是否显示弹出层
const open = ref(false);
// 部门名称
const deptName = ref(undefined);
// 状态数据字典
const statusOptions = ref([]);
// 性别状态字典
const sexOptions = ref([]);
// 岗位选项
const postOptions = ref([]);
// 角色选项
const roleOptions = ref([]);
// 菜单列表
const permissionOptions = ref([]);
// 表单参数
const form = ref({});
const orgFormDialog = ref({
  title: "",
  open: false,
  checkedMenuIds: [],
});
//为添加组织 所用form
const orgForm = ref({
  orgType: "group",
});
//为组织添加用户 所用form
const orgUserForm = ref({});
const orgUserFormDialog = ref({
  showSearch: false,
  selectNodeName: "全部人员",
  selectNode: undefined,
  total: 0,
  loading: true,
  treeLoading: true,
  // 查询参数
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    staffOrgType: "F",
    orgId: userStore.userInfo.orgId,
    tenantId: userStore.userInfo.customParam.tenantId,
    tenantName: userStore.userInfo.customParam.tenantName
  },
  userList: [],
});
const defaultProps = ref({
  children: "children",
  label: "name",
});
// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  staffName: undefined,
  email: undefined,
  cellphone: undefined,
  status: undefined,
  deptId: undefined,
  orgType: 'group',
  orgId: userStore.userInfo.orgId,
  tenantId: userStore.userInfo.customParam.tenantId,
  tenantName: userStore.userInfo.customParam.tenantName
});

// 列信息
const columns = ref([
  {key: 0, label: `用户编号`, visible: false},
  {key: 1, label: `登陆名称`, visible: false},
  {key: 2, label: `用户名称`, visible: true},
  {key: 3, label: `工作组名称`, visible: true},
  {key: 4, label: `手机号码`, visible: true},
  {key: 5, label: `邮箱`, visible: true},
]);
const columns2 = ref([
  {key: 0, label: `用户编号`, visible: false},
  {key: 1, label: `登陆名称`, visible: false},
  {key: 2, label: `用户名称`, visible: true},
  {key: 3, label: `组织名称`, visible: true},
  {key: 4, label: `手机号码`, visible: true},
  {key: 5, label: `邮箱`, visible: true},
]);
// 表单校验
const orgFormRules = {
  parentName: [
    {required: true, message: "上级组织不能为空", trigger: "blur"},
  ],
  orgName: [
    {required: true, message: "组织名称不能为空", trigger: "blur"},
  ],
  orgSort: [
    {required: true, message: "显示排序不能为空", trigger: "blur"},
  ],
}
const treeLoading = ref(false);
const selectNodeName = ref("全部人员");
const selectNode = ref(undefined);
const menuExpand = ref(false);
const menuNodeAll = ref(false);
const menuCheckStrictly = ref(true);
const defaultMenuProps = ref({
  children: "children",
  label: "permissionName",
});
const isAddUser = ref(false);
const selectUserIds = ref([]);
const defaultOrgId = userStore.userInfo.orgId;

function getTenantList(tenantName) {
  getTenantLoading.value = true;
  let query = {}
  if (tenantName !== undefined && tenantName !== '') {
    query.tenantName = tenantName
    query.tenantId = undefined
  } else {
    query.tenantId = queryParams.value.tenantId
  }
  getTenants(query).then((response) => {
    tenantList.value = response.data;
  }).finally(() => {
    getTenantLoading.value = false;
  });
}

// 所有菜单节点数据
function getMenuAllCheckedKeys() {
  // 目前被选中的菜单节点
  let checkedKeys = proxy.$refs.menu.getCheckedKeys();
  // 半选中的菜单节点
  let halfCheckedKeys = proxy.$refs.menu.getHalfCheckedKeys();
  checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys);
  return checkedKeys;
}

/** 查询菜单树结构 */
function getMenuTreeselect() {
  menuTreeselect(queryParams.value).then((response) => {
    permissionOptions.value = arrayToTree(
        response.data,
        "children",
        "permissionId",
        "parentId"
    );
  });
}

// 树权限（展开/折叠）
function handleCheckedTreeExpand(value, type) {
  if (type === "menu") {
    let treeList = permissionOptions.value;
    for (let i = 0; i < treeList.length; i++) {
      proxy.$refs.menu.store.nodesMap[treeList[i].permissionId].expanded = value;
    }
  } else if (type === "dept") {
    let treeList = deptOptions.value;
    for (let i = 0; i < treeList.length; i++) {
      proxy.$refs.dept.store.nodesMap[treeList[i].id].expanded = value;
    }
  }
}

// 树权限（全选/全不选）
function handleCheckedTreeNodeAll(value, type) {
  if (type === "menu") {
    proxy.$refs.menu.setCheckedNodes(value ? permissionOptions.value : []);
  } else if (type === "dept") {
    proxy.$refs.dept.setCheckedNodes(value ? deptOptions.value : []);
  }
}

// 树权限（父子联动）
function handleCheckedTreeConnect(value, type) {
  if (type === "menu") {
    form.value.menuCheckStrictly = value ? true : false;
  } else if (type === "dept") {
    form.value.deptCheckStrictly = value ? true : false;
  }
}

/** 新增用户组按钮操作 */
function handleGroupAdd() {
  reset();
  if (selectNode.value) {
    getMenuTreeselect();
    orgFormDialog.value.open = true;
    orgFormDialog.value.title = "添加用户组";
    orgForm.value.parentId = selectNode.value.orgId;
    orgForm.value.parentName = selectNode.value.orgName;
    orgForm.value.tenantName = queryParams.value.tenantName;
    orgForm.value.tenantId = queryParams.value.tenantId;
  } else {
    proxy.$modal.msgError("请选择上级组织");
  }
}

/** 编辑用户组按钮操作 */
function handleGroupEdit() {
  reset();
  if (selectNode) {
    getMenuTreeselect();
    orgFormDialog.value.open = true;
    orgFormDialog.value.title = "编辑用户组";
    getDept(selectNode.value.orgId).then((res) => {
      orgForm.value = res.data;
    });
    orgFormDialog.value.checkedMenuIds = [];
    menuCheckStrictly.value = false;
    getOrgMenu({orgId: selectNode.value.orgId}).then((res) => {
      orgFormDialog.value.checkedMenuIds = res.data.permissions.map(v => v.permissionId);
      orgForm.value.permissionIds = getMenuAllCheckedKeys();
    });
  } else {
    proxy.$modal.msgError("请选择上级组织");
  }
}

function handleGroupDelete(row) {
  const orgName = selectNode.value.orgName;
  const orgId = selectNode.value.orgId;
  proxy.$modal.confirm('是否确认删除用户组名称为"' + orgName + '"的数据项?')
      .then(function () {
        return delDept({orgId: orgId});
      })
      .then((res) => {
        if (res.success === false) {
          proxy.$modal.msgError(res.message);
        } else {
          proxy.$modal.msgSuccess("删除成功");
          reloadTree();
        }
        // 更新父节点
        selectNode.value = proxy.$refs.asyncTree.getNode(selectNode.value.orgId);
        reloadTree();
        queryParams.value.orgId = selectNode.value.orgId;
        getList();
      });
}

/** 查询用户列表 */
function getList() {
  if (isAddUser.value) {
    orgUserFormDialog.value.loading = true;
    listUser(orgUserFormDialog.value.queryParams).then((response) => {
      orgUserFormDialog.value.userList = response.data.records;
      orgUserFormDialog.value.total = response.data.total;
      orgUserFormDialog.value.loading = false;
    });
  } else {
    loading.value = true;
    listUser({
      ...queryParams.value,
    }).then((response) => {
      userList.value = response.data.records;
      total.value = response.data.total;
      loading.value = false;
    });
  }
}

/** 查询部门下拉树结构 */
function getTreeselect() {
  treeselect({
    orgId: defaultOrgId,
    // type: "current",
    tenantId: queryParams.value.tenantId
  }).then((response) => {
    deptOptions.value = response.data;
  });
}

// 节点单击事件
function handleNodeClick(data) {
  queryParams.value.orgId = data.orgId;
  selectNode.value = data;
  selectNodeName.value = data.orgName;
  isAddUser.value = false;
  orgForm.value.parentId = data.parentId;
  getList();
}

//添加用户 节点单击事件
function handleNodeClickAddUser(data) {
  orgUserFormDialog.value.queryParams.orgId = data.orgId;
  orgUserFormDialog.value.selectNode = data;
  orgUserFormDialog.value.selectNodeName = data.orgName;
  getList();
}

function allUser() {
  const node = proxy.$refs.asyncTree.root;
  node.loaded = false;
  node.expand();
  queryParams.value.orgId = "";
  selectNodeName.value = "全部人员";
  selectNode.value = undefined;
  getList();
}

// 取消按钮
function cancel() {
  open.value = false;
  orgFormDialog.value.open = false;
  orgFormDialog.value.checkedMenuIds = [];
  isAddUser.value = false;
  reset();
}

// 表单重置
function reset() {
  orgForm.value = {
    orgType: "group",
    orgName: undefined,
    orgId: undefined,
    parentName: undefined,
    parentId: undefined,
    orgSort: 0
  };
  orgUserForm.value = {};
  proxy.resetForm("orgFormRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryForm");
  handleQuery();
}

// 添加用户时 多选框选中数据
function handleSelectionChangeAddUser(selection) {
  selectUserIds.value = selection.map((item) => item.staffId);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  isAddUser.value = true;
  orgUserForm.value.orgId = queryParams.value.orgId ? queryParams.value.orgId : defaultOrgId;
  open.value = true;
  getTreeselect();
  getList();
}

/** 提交按钮 */
function submitOrgForm() {
  orgForm.value.permissionIds = getMenuAllCheckedKeys();
  proxy.$refs["orgFormRef"].validate((valid) => {
    if (valid) {
      saveGroupLoading.value = true;
      if (orgFormDialog.value.title === "编辑用户组") {
        updateDept(orgForm.value)
            .then((response) => {
              saveGroupLoading.value = false;
              if (response.success) {
                proxy.$modal.msgSuccess("修改成功");
                orgFormDialog.value.open = false;
                selectNode.value.orgName = orgForm.value.orgName;
              } else {
                proxy.$modal.msgError("修改失败");
              }
            })
            .catch((err) => {
              saveGroupLoading.value = false;
            });
      } else {
        addDept(orgForm.value)
            .then((response) => {
              saveGroupLoading.value = false;
              if (response.success) {
                proxy.$modal.msgSuccess("新增成功");
                orgFormDialog.value.open = false;
                reloadTree();
              } else {
                proxy.$modal.msgError("新增失败");
              }
            })
            .catch((err) => {
              saveGroupLoading.value = false;
            });
      }
    }
  });
}

/** 用户添加提交按钮 */
function submitSelectUserForm() {
  if (selectUserIds.value.length <= 0) {
    proxy.$modal.msgWarning("请选择一个用户进行添加");
  } else {
    selectSaveLoading.value = true;
    let user = {
      staffId: selectNode.value.staffId,
      orgId: selectNode.value.orgId,
    };
    addUserForGroup({
      staffIds: selectUserIds.value,
      ...user,
    })
        .then((response) => {
          selectSaveLoading.value = false;
          if (response.success) {
            proxy.$modal.msgSuccess("新增成功");
            open.value = false;
            isAddUser.value = false;
            getList();
          } else {
            proxy.$modal.msgError("新增失败");
          }
        })
        .catch((err) => {
          selectSaveLoading.value = false;
        });
  }
}

/** 删除按钮操作 */
function handleDelete(row) {
  const staffOrgId = row.staffOrgId;
  proxy.$modal.confirm('是否确认删除用户名称为"' + row.staffName + '"的数据项?')
      .then(function () {
        return delUserForGroup({staffOrgId: staffOrgId});
      })
      .then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
      });
}

function loadNode(node, resolve) {
  treeLoading.value = true;
  if (node.level === 0) {
    treeselect({
      orgId: defaultOrgId,
      queryType: 'current',
      orgType: 'all',
      tenantId: queryParams.value.tenantId,
    }).then((response) => {
      resolve(response.data);
      treeLoading.value = false;
    });
  } else {
    treeselect({
      orgId: node.data.orgId,
      queryType: 'down',
      orgType: 'all',
      tenantId: queryParams.value.tenantId,
    }).then((response) => {
      resolve(response.data);
      treeLoading.value = false;
    });
  }
}

function loadNodeOrg(node, resolve) {
  treeLoading.value = true;
  if (node.level === 0) {
    treeselect({
      orgId: defaultOrgId,
      queryType: 'current',
      tenantId: queryParams.value.tenantId,
    }).then((response) => {
      resolve(response.data);
      treeLoading.value = false;
    });
  } else {
    treeselect({
      orgId: node.data.orgId,
      queryType: 'down',
      tenantId: queryParams.value.tenantId,
    }).then((response) => {
      resolve(response.data);
      treeLoading.value = false;
    });
  }
}

function reloadTree() {
  if (selectNode.value && !isAddUser.value) {
    const node = proxy.$refs.asyncTree.getNode(selectNode.value);
    node.childNodes = [];
    node.loaded = false;
    node.expand();
  } else if (selectNode.value && isAddUser.value) {
    const node = proxy.$refs.asyncTreeAddUser.getNode(orgUserFormDialog.value.selectNode);
    node.childNodes = [];
    node.loaded = false;
    node.expand();
  } else {
    proxy.$refs.asyncTree.root.loaded = false;
    proxy.$refs.asyncTree.root.expand();
  }
  // 主动调用展开节点方法，重新查询该节点下的所有子节点
}

// 组织选择的回调
function selected(data) {
  orgForm.value.parentId = data.orgId;
  orgForm.value.parentName = data.orgName;
  proxy.$refs.treeSelect.close();
}

function tenantChange(tenantId) {
  if (tenantId !== '') {
    orgUserFormDialog.value.queryParams.tenantId = tenantId
    queryParams.value.tenantName = tenantList.value.find(item => item.tenantId === tenantId).tenantName;
  }
  selectNode.value = undefined
  queryParams.value.orgId = undefined
  proxy.$refs.asyncTree.root.loaded = false;
  proxy.$refs.asyncTree.root.expand();
  handleQuery()
}

getTenantList();
getList();


</script>

<style scoped>
.dep-card {
  min-height: calc(100vh - 120px);
}

.drawer-card {
  border: 0px;
  height: 100%;
}

.drawer-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.demo-drawer__footer {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
}
</style>
