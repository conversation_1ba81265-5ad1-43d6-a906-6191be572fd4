<!-- 类型授权 -->

<template>
  <div class="app-container-other">
    <Splitpanes class="default-theme">
      <Pane :size="18" :min-size="18">
        <el-card class="dep-card" >
    
          <treeLoad
            ref="myTree"
            :isShowSearch="true"
            :defaultProps="defaultProps"
            :treeData="treeData"
            :treeBtnEdit="false"
            :treeBtnDelete="false"
            treeName="label"
            :isLazy="false"
            @checkedKeys="handleNodeClick"
    />
        </el-card>
      </Pane>
      <Pane :size="82" :min-size="65">
        <el-card class="dep-card" >
          <dialog-search
            @getList="getList"
            formRowNumber="4"
            :columns="tabelForm.columns"
             :isShowRightBtn="$checkPermi(['parking:accredit:list'])"
          >
            <template v-slot:formList>
              <el-form
                :model="queryParams"
                ref="queryForm"
                :inline="true"
                label-width="75px"
              >
              <el-form-item label="租户">
                  <TenantSelect
                    v-model="queryParams.tenantId"
                  ></TenantSelect>
                </el-form-item>
              <el-form-item label="人员姓名">
                <el-input  v-model="queryParams.nickName" placeholder="请输入人员姓名" clearable ></el-input>
              </el-form-item>
              <el-form-item label="车牌号码">
                <el-input  v-model="queryParams.plateNo" placeholder="请输入车牌号码" clearable ></el-input>
              </el-form-item>
              <el-form-item label="有效状态" prop="accreditStatus">
                <el-select v-model="queryParams.accreditStatus" placeholder="请选择有效状态" clearable>
                  <el-option v-for="(item, index) of accredit_status"
                             :key="index" :label="item.label" :value="item.value"/>
                </el-select>
              </el-form-item>
              </el-form>
            </template>
            <template v-slot:searchList>
              <el-button type="primary" icon="Search" @click="handleQuery" v-hasPermi="['parking:accredit:list']"
                >搜索</el-button
              >
              <el-button icon="Refresh" @click="resetQuery" v-hasPermi="['parking:accredit:list']">重置</el-button>
            </template>

            <template v-slot:searchBtnList>
              <el-button
                type="primary"
                icon="Plus"
                size="mini"
                @click="handleAdd"
                v-hasPermi="['parking:accredit:add']"
                >新增</el-button
              >
              <!-- <el-button
                icon="Download"
                @click="handleExport"
                >导出
              </el-button> -->
            </template>
          </dialog-search>

          <public-table
            ref="publictable"
            :rowKey="tabelForm.tableKey"
            :tableData="list"
            :columns="tabelForm.columns"
            :configFlag="tabelForm.tableConfig"
            :pageValue="pageParams"
            :total="total"
            :getList="getList"
          >
            <template #operation="{ scope }">
              <el-button
              v-hasPermi="['parking:accredit:list']"
                size="mini"
                type="text"
                title="查看"
                icon="View"
                @click="handleView(scope.row)"
              >
              </el-button>
              <el-button
              v-hasPermi="['parking:accredit:edit']"
                size="mini"
                type="text"
                title="修改"
                icon="Edit"
                @click="handleEdit(scope.row)"
              >
              </el-button>

              <el-button
               v-hasPermi="['parking:accredit:delete']"
                size="mini"
                type="text"
                title="删除"
                icon="Delete"
                @click="handleDelete(scope.row)"
              >
              </el-button>
            </template>
          </public-table>
        </el-card>
      </Pane>
    </Splitpanes>

    <DialogBox
      :visible="open1"
      :dialogWidth="diaWindow.dialogWidth"
      :dialogFooterBtn="diaWindow.dialogFooterBtn"
      @save="save"
         :custom-class="diaWindow.customClass"
      @cancellation="cancellation"
      @close="close"
      :dialogTitle="diaWindow.headerTitle"
    >
      <template #content>
        <accreditView
        ref="accreditViewRef"
          v-if="diaWindow.popupType == 'view' || diaWindow.popupType == 'edit'"
          :rowData="diaWindow.rowData"
          :popupType="diaWindow.popupType"
           @close="cancellationRefsh"
        ></accreditView>
        <accreditAdd
        ref="accreditAddRef"
          v-if="diaWindow.popupType == 'add'"
          :rowData="diaWindow.rowData"
          :popupType="diaWindow.popupType"
          @close="cancellationRefsh"
        ></accreditAdd>
      </template>
    </DialogBox>
  </div>
</template>

<script setup name="User">
import { ElMessage,ElMessageBox } from "element-plus";
import useUserStore from "@/store/modules/user";
import { ref, reactive, getCurrentInstance } from "vue";
import {numberAccreditList,findparkinglotTree,deleteNumberAccreditById} from '@/api/majorParking/accredit/accredit'
import { apiUrl } from '@/utils/config'; 
import { formatMinuteTime } from "@/utils";
import accreditView from "./components/accreditView.vue";
import accreditAdd from "./components/accreditAdd.vue";
import treeLoad from "@/components/Tree/treeLoad";
const { proxy } = getCurrentInstance();
const { parking_rule_vehicle_type, parking_accredit_type, accredit_status } =
  proxy.useDict(
    "parking_rule_vehicle_type",
    "parking_accredit_type",
    "accredit_status"
  );

const userStore = useUserStore();
const accreditAddRef = ref(null)
const accreditViewRef = ref(null)
const open1 = ref(false);
const diaWindow = reactive({
  headerTitle: "",
  popupType: "",
  rowData: "",
  dialogWidth: "30%",
  dialogFooterBtn: false,
});

const pageParams = ref({
  pageNum: 1,
  pageSize: 10,
});
const treeData = ref([]);
const queryParams = ref({
  tenantId:userStore.userInfo.tenantId,
  parentId:'',
  nickName: "",
  plateNo: "",
  accreditStatus:""
});

const list = ref([]);

const total = ref(0);
const tabelForm = reactive({
  tableKey: "1", //表格key值
  isShowRightToolbar: true, //是否显示右侧显示和隐藏
  showSearch: true,
  // 表格表格数据
  columns: [
  {
            fieldIndex: "parkingName", // 对应列内容的字段名
            label: "停车场", // 显示的标题
            resizable: true, // 对应列是否可以通过拖动改变宽度
            visible: true, // 展示与隐藏
            sortable: true, // 对应列是否可以排序
            fixed: "", //固定
            minWidth: "160", //最小宽度%
            width: "", //宽度
            align: "left", //表格对齐方式
            
          },

          
    {
      fieldIndex: "enterName", // 对应列内容的字段名
      label: "出入口", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "140", //最小宽度%
      width: "", //宽度
      align: "left", //表格对齐方式
    },
          {
            fieldIndex: "plateNo", // 对应列内容的字段名
            label: "车牌号码", // 显示的标题
            resizable: true, // 对应列是否可以通过拖动改变宽度
            visible: true, // 展示与隐藏
            sortable: true, // 对应列是否可以排序
            fixed: "", //固定
            minWidth: "110", //最小宽度%
            width: "", //宽度
            align: "", //表格对齐方式
            
          },
          {
            fieldIndex: "vehicleType", // 对应列内容的字段名
            label: "车辆类型", // 显示的标题
            resizable: true, // 对应列是否可以通过拖动改变宽度
            visible: true, // 展示与隐藏
            sortable: true, // 对应列是否可以排序
            fixed: "", //固定
            minWidth: "160", //最小宽度%
            width: "", //宽度
            align: "left", //表格对齐方式
            type: "dict",
            dictList: parking_rule_vehicle_type,
            
          },
          {
            fieldIndex: "nickName", // 对应列内容的字段名
            label: "人员姓名", // 显示的标题
            resizable: true, // 对应列是否可以通过拖动改变宽度
            visible: true, // 展示与隐藏
            sortable: true, // 对应列是否可以排序
            fixed: "", //固定
            minWidth: "110", //最小宽度%
            width: "", //宽度
            align: "", //表格对齐方式
            
          },
          // {
          //   fieldIndex: "deptName", // 对应列内容的字段名
          //   label: "人员部门", // 显示的标题
          //   resizable: true, // 对应列是否可以通过拖动改变宽度
          //   visible: true, // 展示与隐藏
          //   sortable: true, // 对应列是否可以排序
          //   fixed: "", //固定
          //   minWidth: "140", //最小宽度%
          //   width: "", //宽度
          //   align: "", //表格对齐方式
            
          // },
          {
            fieldIndex: "accreditType", // 对应列内容的字段名
            label: "授权类型", // 显示的标题
            resizable: true, // 对应列是否可以通过拖动改变宽度
            visible: true, // 展示与隐藏
            sortable: true, // 对应列是否可以排序
            fixed: "", //固定
            minWidth: "140", //最小宽度%
            width: "", //宽度
            align: "", //表格对齐方式
            type: "dict",
            dictList: parking_accredit_type,
            
          },
          {
            label: "有效期限",
            fieldIndex: "expirationTime",
            minWidth: "150",
            sortable: true, // 对应列是否可以排序
            visible: true,
             defaultLabel:'长期有效'
          },
          {
            fieldIndex: "accreditStatus", // 对应列内容的字段名
            label: "有效状态", // 显示的标题
            resizable: true, // 对应列是否可以通过拖动改变宽度
            visible: true, // 展示与隐藏
            sortable: true, // 对应列是否可以排序
            fixed: "", //固定
            minWidth: "110", //最小宽度%
            width: "", //宽度
            align: "", //表格对齐方式
            type: "dict",
            dictList: accredit_status,
            
          },
          {
            fieldIndex: "createDate", // 对应列内容的字段名
            label: "创建时间", // 显示的标题
            resizable: true, // 对应列是否可以通过拖动改变宽度
            visible: true, // 展示与隐藏
            sortable: true, // 对应列是否可以排序
            fixed: "", //固定
            minWidth: "180", //最小宽度%
            width: "", //宽度
            align: "", //表格对齐方式
            
          },
          {
            fieldIndex: "updateName", // 对应列内容的字段名
            label: "操作人", // 显示的标题
            resizable: true, // 对应列是否可以通过拖动改变宽度
            visible: true, // 展示与隐藏
            sortable: true, // 对应列是否可以排序
            fixed: "", //固定
            minWidth: "110", //最小宽度%
            width: "", //宽度
            align: "", //表格对齐方式
            
          },

    {
      label: "操作",
      slotname: "operation",
      width: "100",
      fixed: "right", //固定
      visible: true,
    },
  ],
  // 表格基础配置项，看个人需求添加
  tableConfig: {
    needPage: true, // 是否需要分页
    index: true, // 是否需要序号
    selection: false, // 是否需要多选
    reserveSelection: false, // 是否需要支持跨页选择
    indexFixed: false, // 序号列固定 left true right
    selectionFixed: false, //多选列固定left true right
    indexWidth: "50", //序号列宽度
    loading: false, //表格loading
    showSummary: false, //是否开启合计
    height: null, //表格固定高度 比如：300px
  },
});

/** 查询用户列表 */
const getList = () => {
  tabelForm.tableConfig.loading = true;
  numberAccreditList(queryParams.value, pageParams.value).then((response) => {
    list.value = response.data.records;
    total.value = response.data.total;
    tabelForm.tableConfig.loading = false;
  });
};

//获取停车场区域
const selectParkingTree = () => {
  findparkinglotTree({}).then((res) => {
    treeData.value = res.data;
  });
};

/** 搜索按钮操作 */
const handleQuery = () => {
  pageParams.value.pageNum = 1;
  getList();
};
/** 重置按钮操作 */
const resetQuery = () => {
  proxy.resetForm("queryForm");
  queryParams.value =  {
  tenantId:userStore.userInfo.tenantId,
  parentId:'',
  nickName: "",
  plateNo: "",
  accreditStatus:""
};
  handleQuery();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy.download(
    `park${apiUrl}/parking/accredit/exportNumber`,
    {
      ...queryParams.value
    },
    `车牌授权_${formatMinuteTime(new Date())}.xlsx`
  );
};
/** 删除 */
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm("确认要删除吗？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });

    const res = await deleteNumberAccreditById(row.id);
    if (res.code == '1') {
      ElMessage.success("删除成功");
      await getList();
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    console.error("删除失败:", error);
    // 用户取消删除或其他错误
  }
};

/** 查看 */
const handleView = (data) => {
  diaWindow.headerTitle = "查看授权";
  diaWindow.popupType = "view";
  diaWindow.rowData = data;
    diaWindow.customClass = 'my_height_1'
      diaWindow.dialogWidth = '35%'
  diaWindow.dialogFooterBtn = false;
  open1.value = true;
};

// 新增
const handleAdd = (row) => {
  diaWindow.headerTitle = "新增授权";
  diaWindow.popupType = "add";
      diaWindow.customClass = 'my_height_1'
  diaWindow.rowData = {}; // 如果需要传递数据到弹窗
  diaWindow.dialogFooterBtn = true;
  diaWindow.dialogWidth = '58%'
  open1.value = true;
};

// 修改
const handleEdit = (row) => {
  diaWindow.headerTitle = "修改授权";
  diaWindow.popupType = "edit";
      diaWindow.customClass = 'my_height_1'
  diaWindow.rowData = row; // 如果需要传递数据到弹窗
  diaWindow.dialogFooterBtn = true;
    diaWindow.dialogWidth = '35%'
  open1.value = true;
};
/** 点击确定保存 */
const save = ()=>{
  if( diaWindow.popupType == 'add' ){
    accreditAddRef.value.saveBtn()
  }

  if( diaWindow.popupType == 'edit' ){
    accreditViewRef.value.saveBtn()
  }
 
}
/** 点击确定后刷新 */
const cancellationRefsh = ()=>{
  close(false);
  getList();
}
/** 点击取消保存 */
const cancellation = (val) => {
  close(false);
};

/** 关闭弹窗 */
const close = (val) => {
  open1.value = val;
};




/** 树结构数据默认 */
const defaultProps = reactive({
  children: "children",
  label: "label",
})


/** 树结构点击 */
const handleNodeClick = (data)=>{
  queryParams.value.parentId = data.id;
  getList();
}


selectParkingTree()
getList();
</script>

<style scoped>
.dep-card {
  min-height: calc(100vh - 160px);
}

.error-msg {
  color: #f56c6c;
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
}
</style>