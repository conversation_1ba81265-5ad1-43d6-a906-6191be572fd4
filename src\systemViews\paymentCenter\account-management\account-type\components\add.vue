<!-- 新增账户设置 -->

<template>
  <div
    class="dialog-box"
    :class="popupType === 'view' ? 'dialog-box-view' : 'dialog-box-edit'"
  >
    <el-form
      ref="formRef"
      :model="formData"
      label-width="90px"
      :rules="popupType !== 'view' ? rules : ''"
    >
      <el-row>
        <el-col :span="24">
          <el-form-item label="类型名称" prop="typeName">
            <!-- 添加或编辑时显示输入框 -->

            <el-input
              v-model="formData.typeName"
              :disabled="popupType === 'view'"
              placeholder="请输入类型名称"
              clearable
              v-if="popupType !== 'view'"
            />

            <!-- 查看时显示文本 -->

            <div v-else>
              {{ formData.typeName || "" }}
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="类型编码" prop="typeCode">
            <el-input
              v-model="formData.typeCode"
              :disabled="popupType === 'view'"
              placeholder="请输入类型编码"
              clearable
              v-if="popupType !== 'view'"
            />

            <div v-else>
              {{ formData.typeCode || "" }}
            </div>
          </el-form-item>
        </el-col>

   
        <el-col :span="24">
          <el-form-item label="类型状态" prop="status">
            <!-- 添加或编辑时显示单选按钮 -->

            <el-radio-group
              v-model="formData.status"
              v-if="popupType !== 'view'"
            >
            <el-radio-button :label="item.value" v-for="(item,index) of account_type_status" :key="index">{{item.label}}</el-radio-button>
            </el-radio-group>

            <!-- 查看时显示文本 -->

            <span class="dialog-text" v-else>
              {{ $formatDictLabel(formData.status, account_type_status) || "" }}
            </span>
          </el-form-item>
        </el-col>



      </el-row>


    </el-form>

  
  </div>
</template>
  
    
  
  <script setup>
import { ref, reactive, watch, getCurrentInstance, defineProps } from "vue";

import { ElMessage } from "element-plus";

import {
 screenIndex
} from "@/api/paymentCenter/account-management/account-type/index";
import { pinyin } from 'pinyin-pro'

// 定义组件 props
const props = defineProps({
  closeBtn: {
    type: Function,

    default: null,
  },

  popupType: {
    type: String,
    default: "",
  },

  rowData: {
    type: Object,
    default: () => ({}),
  },
});

// 字典
const { proxy } = getCurrentInstance();
const {
  account_type_status,
} = proxy.useDict(
  "account_type_status",
);


// 定义 emits
const emit = defineEmits(["closeBtn"]);

// 定义状态

let formData = ref({
  status:'1'
});

// 定义校验规则

const rules = reactive({
  typeName: [
    { required: true, message: '请输入类型名称', trigger: ['blur', 'change'] }
  ],
  typeCode: [
    { required: true, message: '请输入类型编码', trigger: ['blur', 'change'] },
    {
      pattern: /^[\x21-\x7E]+$/, // 允许所有可打印的 ASCII 字符（不包含空格）
      message: "类型编码只能包含字母、数字和特殊符号",
      trigger: ["blur", "change"]
    }
  ],
  status: [
    { required: true, message: '请选择类型状态', trigger: 'change' }
  ],
  
})

// 表单引用

const formRef = ref(null);



// 定义方法


const initData = async () => {
  if (props.popupType === "add") {
  } else {
    formData.value = JSON.parse(JSON.stringify(props.rowData)) 

  }

};



const saveForm = async () => {
  try {
    await formRef.value.validate();
 
    if (props.popupType === "edit") {
      const res = await screenIndex.payAccountTypeUpdate( formData.value);

      if (res.code == "1") {
        ElMessage.success("修改成功");

        emit("closeBtn");
      }
    } else if (props.popupType === "add") {
      const res = await screenIndex.payAccountTypeAdd( formData.value);

      if (res.code == "1") {
        ElMessage.success("新增成功");

        emit("closeBtn");
      }
    }
  } catch (error) {
    console.error("表单校验失败:", error);
  }
};
// 监听类型名称变化生成类型编码
let isAutoGenerated = true; // 标记是否自动生成的编码

watch(
  () =>  formData.value.typeName,
  (newVal) => {
    if (props.popupType === 'view') return;

    // 生成拼音首字母（兼容中英文）
    const code = pinyin(newVal, {
      pattern: 'first',    // 只要首字母
      toneType: 'none',    // 不要声调
      nonZh: 'consecutive' // 非中文连续显示
    })
    .replace(/ /g, '')     // 移除空格
    .toUpperCase();        // 转为大写

    formData.value.typeCode = code;
  }
);


// 初始化数据

onMounted(() => {
  initData();
});

defineExpose({
  saveForm,
});
</script>
  
    
  
    <style scoped lang="scss">
</style>