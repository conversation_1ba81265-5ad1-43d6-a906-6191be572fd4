<template>
  <div
    class="dialog-box"
    :class="popupType === 'view' ? 'dialog-box-view' : 'dialog-box-edit'"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="popupType !== 'view' ? rules : {}"
      label-width="110px"
    >
      <el-row>
        <el-col :span="24">
          <el-form-item label="停车场入口" prop="parkingEnterId">
            <el-select
              v-if="popupType !== 'view'"
              filterable
              class="main-select-tree"
              v-model="formData.parkingEnterId"
              placeholder="请选择停车场入口"
            >
              <el-option
                v-for="(item, index) of parkingList"
                :key="index"
                :label="item.parkingName"
                :value="item.id"
              >
                <div>{{ item.parkingName }}</div>
              </el-option>
            </el-select>
            <div v-if="popupType === 'view'">
              {{ formData.parkingEnterName || "" }}
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="停车场出口" prop="parkingOutId">
            <el-select
              v-if="popupType !== 'view'"
              filterable
              class="main-select-tree"
              v-model="formData.parkingOutId"
              placeholder="请选择停车场出口"
            >
              <el-option
                v-for="(item, index) of parkingList"
                :key="index"
                :label="item.parkingName"
                :value="item.id"
              >
                <div>{{ item.parkingName }}</div>
              </el-option>
            </el-select>
            <div v-if="popupType === 'view'">
              {{ formData.parkingOutName || "" }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="入场时间" prop="enterTime">
            <el-date-picker
              v-if="popupType !== 'view'"
              v-model="formData.enterTime"
              value-format="YYYY-MM-DD HH:mm:ss"
              type="datetime"
              placeholder="选择入场时间"
            />
            <div v-else>{{ formData.enterTime || "" }}</div>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="出场时间" prop="outTime">
            <el-date-picker
              v-if="popupType !== 'view'"
              v-model="formData.outTime"
              value-format="YYYY-MM-DD HH:mm:ss"
              type="datetime"
              placeholder="选择出场时间"
            />
            <div v-else>{{ formData.outTime || "" }}</div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";

import { findparkinglots,updateParkRecords } from "@/api/park/record";

const props = defineProps({
  popupType: {
    type: String,
    default: "",
  },

  rowData: {
    type: Object,
    default: () => {},
  },
});
// 响应式数据
const formData = ref({});
const parkingList = ref([]);

const emits = defineEmits(["cancellationRefresh"]);
const formRef = ref(null)

// 先定义校验函数
const validateOutTime = (rule, value, callback) => {
  if (!formData.value.enterTime || !value) {
    callback();
    return;
  }
  const enter = new Date(formData.value.enterTime);
  const out = new Date(value);
  if (enter >= out) {
    callback(new Error("出场时间必须晚于入场时间"));
  } else {
    callback();
  }
};
// 新增完整的校验规则
const rules = reactive({
  parkingEnterId: [
    { required: true, message: '请选择停车场入口', trigger: 'change' }
  ],
  parkingOutId: [
    { required: true, message: '请选择停车场出口', trigger: 'change' }
  ],
  enterTime: [
    { required: true, message: '请选择入场时间', trigger: 'change' }
  ],
  outTime: [
    { required: true, message: '请选择出场时间', trigger: 'change' },
    { validator: validateOutTime, trigger: 'change' }
  ]
});
const selectAreaTree = () => {
  formData.value = props.rowData
  findparkinglots({ parentId: formData.value.parkingId }).then((res) => {
    parkingList.value = res.data;
    
  });
};


// 处理停车场入口选择
const handleEnterChange = (val) => {
  const selected = parkingList.value.find(item => item.id === val);
  if (selected) {
    formData.value.parkingEnterName = selected.parkingName;
    // 如果不需要保留原始数据，可以删除不需要的参数
    delete formData.value.draw; // 删除draw参数
  }
};

// 处理停车场出口选择
const handleOutChange = (val) => {
  const selected = parkingList.value.find(item => item.id === val);
  if (selected) {
    formData.value.parkingOutName = selected.parkingName;
    delete formData.value.draw; // 删除draw参数
  }
};

// 修改后的提交方法（添加表单验证）
const submitForm = () => {
  formRef.value.validate((valid) => {
    if (valid) {
      // 删除不需要的参数
      ElMessageBox.confirm('数据修改后，或影响停车订单，是否确认修改？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      const submitData = {
        parkingEnterId: formData.value.parkingEnterId,
        parkingOutId: formData.value.parkingOutId,
        enterTime: formData.value.enterTime,
        outTime: formData.value.outTime,
        id: formData.value.id // 假设id来自props.rowData
      };




      updateParkRecords(submitData).then(res => {
        if (res.code == "1") {
          ElMessage.success('修改成功')
          emits("cancellationRefresh")
        }
      })
    })
    .catch(() => {
      // 用户点击了取消
    });
     
    } else {
      ElMessage.warning("请完善必填信息")
      return false
    }
  })
}
// 生命周期钩子
onMounted(() => {
  selectAreaTree();
});
defineExpose({
  submitForm
})
</script>