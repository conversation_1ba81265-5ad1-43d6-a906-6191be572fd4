import request from '@/utils/request'



// 停车申请分页列表
export function pageList(query, params) {
  return request({
    url: '/parking/apply/pageList',
    method: 'post',
    data: {...query,...params},
    params: params
  })
}

export function findParkingLotList(data) {
  return request({
    url: '/parking/apply/findParkingLotList',
    method: 'post',
    data:data
  })
}
export function saveApplyInfo(data) {
  return request({
    url: '/parking/apply/saveApplyInfo',
    method: 'post',
    data:data
  })
}
export function submitApplyInfo(data) {
  return request({
    url: '/parking/apply/submitApplyInfo',
    method: 'post',
    data:data
  })
}

export function findById(id) {
  return request({
    url: '/parking/apply/findById/'+id,
    method: 'post',
  })
}

export function delById(id) {
  return request({
    url: '/parking/apply/delById/'+id,
    method: 'post',
  })
}

export function check(data) {
  return request({
    url: '/parking/apply/check',
    method: 'post',
    data:data
  })
}

