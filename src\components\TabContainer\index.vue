<template>
    <div class="tab-container" >
      <el-tabs v-model="activeNameCom" :type="type" @tab-click="handleClick">
        <el-tab-pane
          v-for="(item, index) in tabList"
          :key="index"
          :label="item.label"
          :name="item.value"
        >
          <!-- 动态组件渲染 -->
          <component :is="item.component" v-if="activeNameCom === item.value" />
        </el-tab-pane>
      </el-tabs>
    </div>
  </template>
  
  <script setup>
  import { ref } from 'vue'
  
  // 定义 props
  const props = defineProps({
    tabList: {
      type: Array,
      required: true,
      default: () => [
       
      ]
    },
    activeName: {
      type: String,
      default: '01' // 默认激活的 tab
    },
    type: {
      type: String,
      default: '' // tab 类型，支持 card/border-card
    },
    containerHeight: {
      type: String,
      default: 'calc(100vh - 120px)' // 容器高度
    }
  })
  
  // 响应式数据
  const activeNameCom = ref(props.activeName)
  
  // 事件处理
  const handleClick = (tab) => {
    console.log('当前激活的 tab:', tab.props.name)
  }
  </script>
  
  <style scoped>
  .tab-container {
    background: #fff;
    border-radius: 4px;
    /* box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); */
  }

  :deep(.el-tabs__nav-wrap::after){
    height: 1px;
    background: #e4e7ed;
  }
  
  :deep(.el-tabs__item){
    font-size: 15px !important;
    font-weight: 500 !important;
    color: #606266 !important;
    padding: 0 24px !important;
    height: 40px !important;
    line-height: 40px !important;
    transition: all 0.3s ease !important;
    border-radius: 8px 8px 0 0 !important;
    margin-right: 6px !important;
    position: relative !important;
  }
  
  :deep(.el-tabs__item:hover) {
    color: #c20000 !important;
    background: rgba(194, 0, 0, 0.06) !important;
    transform: translateY(-1px) !important;
  }
  
  :deep(.el-tabs__item.is-active) {
    color: #c20000 !important;
    font-weight: 600 !important;
    /* background: rgba(194, 0, 0, 0.1) !important; */
  }
  
  :deep(.el-tabs__active-bar) {
    background-color: #c20000 !important;
    height: 3px !important;
    border-radius: 2px !important;
    box-shadow: 0 1px 3px rgba(194, 0, 0, 0.3) !important;
  }
  
  :deep(.el-tabs__header) {
    margin-bottom: 0 !important;
    border-bottom: 1px solid #f0f0f0 !important;
    background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%) !important;
    padding: 0 20px !important;
  }
  
  :deep(.el-tabs__content) {
    padding: 10px !important;
    background: #ffffff !important;
  }

  /* 响应式媒体查询 */
  @media (max-width: 1680px) and (min-width: 1442px) {
  
    :deep(.el-tabs__item) {
      padding: 0 20px!important;
      font-size: 13px!important;
    }
  }
  
  @media (max-width: 1442px) {
    :deep(.el-tabs__item) { 
      padding: 0 20px!important;
      font-size: 13px!important;
    }
  }
  </style>