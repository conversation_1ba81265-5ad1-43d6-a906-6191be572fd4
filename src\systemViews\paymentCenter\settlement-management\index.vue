<!-- 支付中心-结算管理 -->
 

<template>
    <div>
        <TabContainers :tabList="tabList" :activeName="activeName"></TabContainers>
    </div> 

</template>

<script setup name="settlementManagement">
// tab切换
import TabContainers from '@/components/TabContainer/index';
import settlementOfficer from '@/systemViews/paymentCenter/settlement-management/components/settlement-officer.vue'
import settlementReviewer from '@/systemViews/paymentCenter/settlement-management/components/settlement-reviewer.vue'
import { ref } from "vue";
// 定义 tab 列表
const tabList = ref([
  {
    label: '结算员管理',
    value: '01',
    component: settlementOfficer
  },

  {
    label: '结算审核员管理',
    value: '02',
    component: settlementReviewer
  },
])
// 默认激活的 tab
const activeName = ref('01')
</script>

<style scoped lang="scss">
:deep(.el-tabs .el-tabs__content .content) {
    height: calc(100vh - 250px);
}
</style>