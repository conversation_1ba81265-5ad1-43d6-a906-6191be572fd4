import request from '@/utils/request'

//添加人员
export function save(data,params) {
  return request({
    url: '/visitorinvite/personnel/save',
    method: 'post',
    params: params,
    data:data
  })
}

//获取主键
export function getUUid() {
  return request({
    url: "/meetingroom/getUUid",
    method: 'get',
  })
}

export function findById(id) {
  return request({
    url: '/visitorinvite/personnel/findById/'+id,
    method: 'get'
  })
}

export function delAttachmentByBusinessId(id){
  return request({
    url: '/sys/attachment/delAttachmentByBusinessId/' + id,
    method: 'post',
  })
}
