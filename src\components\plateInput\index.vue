<template>
    <div class="car-box">
        <span :class="`car-input ${chooseClass(index)}`" @click="handleCar(index)"
            v-for="(item, index) in (isNewEnergy ? value : value.slice(0, 7))" :key="index">
            {{ item }}
        </span>
        <span v-if="!isNewEnergy" @click="newEnergy" class="car-input car-new"></span>
    </div>
    <el-drawer v-model="show" direction="btt" :show-close="false" :with-header="false">
        <div class="car-group">
            <div class="car-row" v-for="(item, index) in keys" :key="index">
                <span @click="handleKey(item2)" :class="['car-cell', { 'key-delete': item2 === '删除' }]"
                    v-for="(item2, index2) in item" :key="index2">
                    {{ item2 === '删除' ? '' : item2 }}
                    <el-icon v-if="item2 === '删除'">
                        <Close />
                    </el-icon>
                </span>
            </div>
        </div>
    </el-drawer>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { Close } from '@element-plus/icons-vue';

const props = defineProps({
    modelValue: {
        type: String,
        default: ''
    }
});

const emit = defineEmits(['update:modelValue']);

const value = ref(['', '', '', '', '', '', '']);  // 车牌值，8位
const show = ref(false);
// 各省的简称
const keyJC = [
    ['鲁', '京', '粤', '津', '晋', '冀', '蒙', '辽', '吉', '黑', '渝'],
    ['苏', '浙', '皖', '闽', '赣', '鄂', '沪', '宁'],
    ['桂', '琼', '川', '贵', '云', '藏', '陕', '新'],
    ['豫', '湘', '青', '甘']
];
// 一般正常的键盘
const normalkey = [
    ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0'],
    ['Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P'],
    ['A', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L',],
    ['Z', 'X', 'C', 'V', 'B', 'N', 'M', '删除'],
];
// 油车最后一位键盘
const lastKey = [
    ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0'],
    ['Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P'],
    ['A', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L', '警'],
    ['Z', 'X', 'C', 'V', 'B', 'N', 'M', '学', '删除'],
];
const index = ref(0); // 被选中的车牌位置
const isNewEnergy = ref(false);

watch(() => props.modelValue, (newVal) => {
    const internalVal = value.value.join('');
    if (newVal === internalVal) {
        return;
    }

    if (newVal) {
        const chars = newVal.split('').slice(0, 8);
        console.log(chars.length)
        if (chars.length === 8) {
            isNewEnergy.value = true;
            value.value = chars;
        } else if (chars.length === 7) {
            isNewEnergy.value = false;
            value.value = chars;
        } else {
            const padded = [...chars];
            while (padded.length < 8) {
                padded.push('');
            }
            value.value = padded.slice(0, 8);
            isNewEnergy.value = false;
        }
    } else {
        isNewEnergy.value = false;
        value.value = ['', '', '', '', '', '', '', ''];
    }

    console.log(value.value)
}, { immediate: true });


watch(value, (newValue) => {
    emit('update:modelValue', newValue.join(''));
}, { deep: true });

watch(show, (val) => {
  if (!val && isNewEnergy.value && !value.value[7]) {
    isNewEnergy.value = false;
    value.value = value.value.slice(0, 7);
    value.value = [...value.value];
    if (index.value > 6) index.value = 6;
  }
});
const keys = computed(() => {
    let flag = index.value;
    if (flag === 0) {
        return keyJC;
    }
    else if (flag === 6) {
        return lastKey;
    }
    else {
        return normalkey;
    }
});

function chooseClass(idx) {
    if (show.value) {
        if (idx === index.value) {
            return 'choose-class';
        }
    }
    return '';
}

// 点击车牌号码
function handleCar(idx) {
    index.value = idx;
    show.value = true;
}

// 点击键盘内容
function handleKey(key) {
    if (key === '删除') {
        if (index.value > 0) {
            if (!value.value[index.value]) {
                index.value--;
            }
        }
        value.value[index.value] = '';
        value.value = value.value.slice(0, 8);
        value.value = [...value.value];

        // 如果当前是新能源且删除了第8位，自动切回普通车牌
        if (isNewEnergy.value && index.value === 7 && !value.value[7]) {
            isNewEnergy.value = false;
            value.value = value.value.slice(0, 7);
            value.value = [...value.value];
            if (index.value > 6) index.value = 6;
        }
        return;
    }

    // 普通车牌输入到第7位后，自动切换新能源
    if (!isNewEnergy.value && index.value === 7) {
        isNewEnergy.value = true;
    }
    // 限制最大只能输入8位
    if (index.value > 7) return;
    value.value[index.value] = key;
    value.value = value.value.slice(0, 8);
    value.value = [...value.value];
    if (index.value < 7) {
        index.value++;
    }
}

// 点击新能源
function newEnergy() {
    isNewEnergy.value = true;
    if (value.value.length < 8) {
        value.value.push('');
    }
    index.value = 7;
    show.value = true;
}
</script>

<style lang="less" scoped>
.car-box {
    display: flex;
}

.car-input {
    display: inline-block;
    position: relative;
    border: 1px solid #CCC;
    width: 35px;
    text-align: center;
    margin-right: 5px;
    height: 35px;
    line-height: 35px;
    border-radius: 5px;
}

.car-input:nth-child(2):after {
    display: inline-block;
    position: absolute;
    top: 50%;
    left: 100% + 20px;
    transform: translateY(-50%);
    height: 8px;
    width: 8px;
    border-radius: 4px;
    background-color: #00FF00;
    content: '',
}

.car-input:nth-child(2) {
    margin-right: 16px;
}

.choose-class {
    border: 2px solid #52cb9a !important;
}

.car-new {
    background: url(@/assets/images/plate-input.png) no-repeat center;
    background-size: contain;
}

.car-group {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    height: 100%;
    background-color: #e3e9ec;
}

.car-row {
    display: flex;
    justify-content: center;

}

.car-cell {
    margin: 0 5px;
    width: 48px;
    height: 48px;
    text-align: center;
    border-radius: 8px;
    border: 1px solid #CCC;
    font-size: 24px;
    background-color: #FFF;
    line-height: 48px;
    cursor: pointer;
    transition: background 0.2s;
}

.key-delete {
    background-color: #e3e9ec;

    img {
        height: 20px;
        position: relative;
        top: 4px;
    }
}
</style>