<template>
  <div class="app-container">
    <el-card>
      <dialog-search
        @getList="getList"
        formRowNumber="3"
        :columns="tabelForm.columns"
      >
        <template v-slot:formList>
          <el-form
            :model="queryParams"
            ref="queryForm"
            :inline="true"
            label-width="80px"
          >
            <el-form-item label="邀约人" prop="visitorName">
              <el-input
                v-model="queryParams.inviter"
                placeholder="请输入邀约人"
                clearable
              />
            </el-form-item>

            <el-form-item label="邀约部门" prop="visitorTelephone">
              <el-input
                v-model="queryParams.inviteUnit"
                placeholder="请输入邀约部门"
                clearable
              />
            </el-form-item>
            <el-form-item label="来访事由" prop="unit">
              <el-select
                v-model="queryParams.reason"
                placeholder="请选择来访事由"
                clearable
              >
                <el-option
                  v-for="dict in visit_reason"
                  :key="dict.id"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="创建时间" prop="selectCreateTime">
              <el-date-picker
                v-model="queryParams.selectCreateTime"
                type="datetimerange"
                value-format="YYYY-MM-DD HH:mm:ss"
                value="YYYY-MM-DD HH:mm:ss"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              >
              </el-date-picker>
            </el-form-item>
          </el-form>
        </template>
        <template v-slot:searchList>
          <el-button type="primary" icon="Search" @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </template>

        <template v-slot:searchBtnList>
          <el-button
            type="primary"
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['sys:base:user:add']"
            >新增
          </el-button>
        </template>
      </dialog-search>

      <public-table
        ref="publictable"
        :rowKey="tabelForm.tableKey"
        :tableData="userList"
        :columns="tabelForm.columns"
        :configFlag="tabelForm.tableConfig"
        :pageValue="queryParams"
        :total="total"
        :getList="getList"
      >
        <template #operation="{ scope }">
          <el-button
            link
            icon="View"
            type="primary"
            v-show="new Date(scope.row.visitorEndTime) > new Date()"
            title="查看邀约码"
            @click="handleView(scope.row)"
          >
          </el-button>
          <el-button
            link
            icon="Delete"
            type="primary"
            title="删除"
            @click="handleDelete(scope.row)"
          >
          </el-button>
        </template>
      </public-table>

      <DialogBox
        :visible="diaWindow.open1"
        :dialogWidth="diaWindow.dialogWidth"
        :dialogFooterBtn="diaWindow.dialogFooterBtn"
        @save="save"
        @cancellation="cancellation"
        @close="close"
        :dialogTitle="diaWindow.headerTitle"
      >
        <template #content>

          <add
            v-if="diaWindow.popupType=='add'"
            ref="addRef"
            :popupType="diaWindow.popupType"
            :rowData="diaWindow.rowData"
            :dialogFooterBtn="diaWindow.dialogFooterBtn"
            @submit="cancellationRefresh"
          ></add>
          <iewm  
            v-if="diaWindow.popupType=='view'"
            :rowData="diaWindow.rowData"
            :dialogFooterBtn="diaWindow.dialogFooterBtn"></iewm>
        </template>
      </DialogBox>
    </el-card>
  </div>
</template>

<script setup>
import { queryList, delDeviceById } from "@/api/equipmentManagement/index";
import { ref, reactive } from "vue";
import { ElMessageBox, ElMessage } from "element-plus";
import add from "./components/addorupdate";
import iewm from "./components/iewm";
const userList = ref([]);
const publictable = ref(null);
const addRef = ref(null);
import { apiUrl } from "@/utils/config";
// 弹窗
const diaWindow = reactive({
  open1: false,
  headerTitle: "",
  popupType: "",
  rowData: "",
  dialogWidth: "50%",
  dialogFooterBtn: false,
});
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
});
const total = ref(0);

// 字典
const { proxy } = getCurrentInstance();
const { visit_reason } = proxy.useDict("visit_reason");

const tabelForm = reactive({
  tableKey: "1", //表格key值
  isShowRightToolbar: true, //是否显示右侧显示和隐藏
  showSearch: true,
  // 表格表格数据
  columns: [
    {
      fieldIndex: "inviter", // 对应列内容的字段名
      label: "邀约人", // 显示的标题
      visible: true, // 展示与隐藏
      minWidth: "110px", //最小宽度%
      width: "", //宽度
      align: "center", //表格对齐方式
    },

    {
      fieldIndex: "inviteUnit", // 对应列内容的字段名
      label: "邀约部门", // 显示的标题
      visible: true, // 展示与隐藏
      minWidth: "140px", //最小宽度%
      width: "", //宽度
      align: "center", //表格对齐方式
    },
    {
      fieldIndex: "reason", // 对应列内容的字段名
      label: "来访事由", // 显示的标题
      visible: true, // 展示与隐藏
      minWidth: "120px", //最小宽度%
      align: "center", //表格对齐方式
      type: "dict",
      dictList: visit_reason,
    },
    {
      fieldIndex: "areaName", // 对应列内容的字段名
      label: "访问区域", // 显示的标题
      visible: true, // 展示与隐藏
      minWidth: "140px", //最小宽度%
      width: "", //宽度
      align: "left", //表格对齐方式
    },
    {
      fieldIndex: "areaNameList", // 对应列内容的字段名
      label: "通行区域", // 显示的标题
      visible: true, // 展示与隐藏
      minWidth: "160px", //最小宽度%
      align: "left", //表格对齐方式
    },
    {
      fieldIndex: "visitorTime", // 对应列内容的字段名
      label: "到访时间", // 显示的标题
      visible: true, // 展示与隐藏
      minWidth: "160px", //最小宽度%
      align: "center", //表格对齐方式
    },
    {
      fieldIndex: "visitorEndTime", // 对应列内容的字段名
      label: "离开时间", // 显示的标题
      visible: true, // 展示与隐藏
      minWidth: "160px", //最小宽度%
      align: "center", //表格对齐方式
    },
    {
      fieldIndex: "followerNum", // 对应列内容的字段名
      label: "随行人数", // 显示的标题
      visible: true, // 展示与隐藏
      minWidth: "110px", //最小宽度%
      align: "center", //表格对齐方式
    },
    {
      fieldIndex: "remark", // 对应列内容的字段名
      label: "邀约说明", // 显示的标题
      visible: true, // 展示与隐藏
      minWidth: "150px", //最小宽度%
      align: "left", //表格对齐方式
    },
    {
      fieldIndex: "createTime", // 对应列内容的字段名
      label: "创建时间", // 显示的标题
      visible: true, // 展示与隐藏
      minWidth: "160px", //最小宽度%
      align: "center", //表格对齐方式
    },
    {
      label: "操作",
      slotname: "operation",
      width: "100px",
      fixed: "right", //固定
      visible: true,
    },
  ],
  // 表格基础配置项，看个人需求添加
  tableConfig: {
    needPage: true, // 是否需要分页
    index: true, // 是否需要序号
    selection: false, // 是否需要多选
    reserveSelection: false, // 是否需要支持跨页选择
    indexFixed: false, // 序号列固定 left true right
    selectionFixed: false, //多选列固定left true right
    indexWidth: "50", //序号列宽度
    loading: false, //表格loading
    showSummary: false, //是否开启合计
    height: null, //表格固定高度 比如：300px
  },
});

/** 查询用户列表 */
const getList = () => {
  tabelForm.tableConfig.loading = true;
  queryList(queryParams.value, queryParams.value).then((response) => {
    userList.value = response.data.records;
    total.value = response.data.total;
    tabelForm.tableConfig.loading = false;
  });
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};
/** 重置按钮操作 */
const resetQuery = () => {
  queryParams.value = {};
  proxy.resetForm("queryForm");
  handleQuery();
};

/** 提交保存 */
const save = () => {
  addRef.value.handleSubmit();
};

/** 点击取消保存并刷新 */
const cancellationRefresh = (val) => {
  close(false);
  getList();
};

/** 点击取消保存 */
const cancellation = (val) => {
  close(false);
};

/** 关闭弹窗 */
const close = (val) => {
  diaWindow.open1 = val;
};

const handleDelete = (row, callback) => {
  ElMessageBox.confirm("确认删除吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      delDeviceById(row.id).then((res) => {
        if (res.code == "1") {
          ElMessage.success("删除成功");
          getList();
          callback(true);
        }
      });
    })
    .catch(() => {});
};

const handleAdd = () => {
  diaWindow.dialogWidth = "45%";
  diaWindow.headerTitle = "新增团体邀约";
  diaWindow.rowData = {};
  diaWindow.dialogFooterBtn = true;
  diaWindow.popupType = "add";
  diaWindow.open1 = true;
};


const handleView = (row) => {
  diaWindow.dialogWidth = "45%";
  diaWindow.headerTitle = "查看邀约码";
  diaWindow.rowData = row;
  diaWindow.dialogFooterBtn = false;
  diaWindow.popupType = "view";
  diaWindow.open1 = true;
};


onMounted(() => {
 getList();
  });
</script>

<style lang="scss" scoped>
.el-card {
  min-height: calc(100vh - 110px);
}
</style>