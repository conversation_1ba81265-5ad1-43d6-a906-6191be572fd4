<template>
  <el-breadcrumb class="app-breadcrumb" separator="/">
    <transition-group name="breadcrumb">
      <el-breadcrumb-item v-for="item in levelList" :key="item.path">
        <a v-if="item.feedback" @click.prevent="handleLink(item)">{{ item.title }}</a>
        <span v-else class="no-redirect">{{ item.title }}</span>
      </el-breadcrumb-item>
    </transition-group>
  </el-breadcrumb>
</template>

<script setup>
import usePermissionStore from '@/store/modules/permission';
import { systemHome } from "@/router";

const permissionStore = usePermissionStore()
const route = useRoute();
const router = useRouter();
const levelList = ref([])

watch(() => route.path, () => {
  updateLevelList()
})

updateLevelList()

function updateLevelList() {
  const { indexPath, title } = route.meta
  const newLevelList = [{ title: '首页', path: systemHome.path, feedback: "view" }]
  if (indexPath && indexPath.length) {
    let tempMenus = permissionStore.systemMenus
    for (let layer = 0; layer < indexPath.length; layer++) {
      const layerIndex = indexPath[layer];
      if (tempMenus && tempMenus[layerIndex]) {
        newLevelList.push({
          title: tempMenus[layerIndex].permissionName,
          path: tempMenus[layerIndex].path,
          feedback: tempMenus[layerIndex].feedback
        })
      }
      tempMenus = tempMenus[layerIndex].children
    }
  } else if (route.path !== systemHome.path) {
    newLevelList.push({
      title,
      path: route.path,
      feedback: "view",
    })
  }
  levelList.value = newLevelList
}

function handleLink(item) {
  const { feedback, path } = item

  if (feedback === 'view') {
    router.push({ path })
  } else if (feedback === 'blank') {
    window.open(path)
  }
}

</script>

<style lang='scss' scoped>
.app-breadcrumb.el-breadcrumb {
  display: inline-block;
  font-size: 14px;
  line-height: 50px;
  margin-left: 8px;

  .no-redirect {
    color: #97a8be;
    cursor: text;
  }
}
</style>