<template>
  <div class="app-container">
    <Splitpanes class="default-theme">
      <Pane :size="15" :min-size="15">
        <el-card class="dep-card" style="height: 100%">
          <template #header>
            <div class="card-header">
              <span>组织</span>
              <el-button
                type="primary"
                style="float: right; padding: 3px 0"
                link
                icon="Refresh"
                @click="reloadTree"
                >刷新
              </el-button>
              <el-form
                style="margin-top: 20px; margin-bottom: -20px"
                v-if="userType === 'admin'"
              >
                <el-form-item label="租户：">
                  <el-select
                    v-model="queryParams.tenantId"
                    style="width: 120px"
                    remote
                    :remote-method="initTenantList"
                    :loading="getTenantLoading"
                    @change="handleTenantChange"
                  >
                    <el-option
                      v-for="item in tenantList"
                      :key="item.tenantId"
                      :label="item.tenantName"
                      :value="item.tenantId"
                    />
                  </el-select>
                </el-form-item>
              </el-form>
            </div>
          </template>
          <el-input
            placeholder="输入名称进行查询"
            v-model="filterText"
            clearable
            style="margin-bottom: 8px"
          >
            <template #append>
              <el-button
                icon="search"
                @click="searchFiler(filterText)"
              ></el-button>
            </template>
          </el-input>
          <treeLoad
            ref="asyncTree"
            :key="treeFatherIndex"
            :isShowSearch="false"
            :idDefaultExpandAll="false"
            :defaultProps="defaultProps"
            :treeBtnEdit="false"
            @loadFirstNodeFather="loadNodeRooter"
            @loadChildNodeFather="loadNode"
            :treeBtnDelete="false"
            tree-name="orgName"
            nodeKey="orgId"
            :treeData="treeData"
            :defaultSelectedKey="defaultSelectedKey"
            :defaultExpandedKeys="[defaultOrgId]"
            :isLazy="isLazy"
            :treeindex="treeindex"
            @checkedKeys="handleNodeClick"
            @editNodes="editNodesEdit"
          />
        </el-card>
      </Pane>
      <Pane :size="85" :min-size="65">
        <el-card class="dep-card" style="height: 100%">
          <template #header>
            <div class="card-header">
              <span>{{ selectNodeName }}</span>
              <el-button
                type="primary"
                style="float: right; padding: 3px 0"
                link
                icon="Refresh"
                @click="allUser"
              >
                全部人员
              </el-button>
            </div>
          </template>

          <!-- 搜索表单 -->

          <dialog-search @getList="getList" formRowNumber="4" :columns="tabelForm.columns">
            <template v-slot:formList>
              <el-form
            :model="queryParams"
            ref="queryForm"
            :inline="true"
            v-show="showSearch"
            label-width="68px"
          >
            <el-form-item label="模糊搜索" prop="params">
              <el-input
                v-model="queryParams.params"
                placeholder="请输入搜索条件"
                clearable
                style="width: 240px"
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item label="人员姓名" prop="staffName">
              <el-input
                v-model="queryParams.staffName"
                placeholder="请输入人员姓名"
                clearable
                style="width: 240px"
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item label="手机号" prop="cellphone">
              <el-input
                v-model="queryParams.cellphone"
                placeholder="请输入手机号"
                clearable
                style="width: 240px"
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item label="邮箱" prop="email">
              <el-input
                v-model="queryParams.email"
                placeholder="请输入邮箱"
                clearable
                style="width: 240px"
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>

            <el-form-item label="组织名称" prop="orgName">
              <el-input
                v-model="queryParams.orgName"
                placeholder="请输入组织名称"
                clearable
                style="width: 240px"
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            </el-form>
            </template>
            <template v-slot:searchList>
              <el-button type="primary" icon="Search" @click="handleQuery"
                >搜索</el-button
              >
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </template>

            <template v-slot:searchBtnList>

              <el-button
                type="primary"
                icon="Plus"
                @click="handleAdd"
                v-hasPermi="['sys:base:user:add']"
                >新增
              </el-button>
            
            </template>
          </dialog-search>

          <!-- 用户信息表格 -->
          <public-table
            ref="publictable"
            :rowKey="tabelForm.tableKey"
            :tableData="userList"
            :columns="tabelForm.columns"
            :configFlag="tabelForm.tableConfig"
            :pageValue="queryParams"
            :total="total"
            :getList="getList"
          >
            <template #operation="{ scope }">
                <div v-if="scope.row.staffStatus === 'valid'">
                  <el-button
                    link
                    icon="Edit"
                    type="primary"
                    :loading="
                      getUserLoading && scope.row.staffId === loadUserId
                    "
                    @click="handleUpdate(scope.row)"
                    v-hasPermi="['sys:base:user:edit']"
                    title="修改"
                  ></el-button>
                  <el-tooltip
                    v-if="scope.row.staffId !== 1"
                    content="租户管理员不允许删除"
                    :disabled="scope.row.staffId !== queryParams.tenantAdminId"
                    placement="top"
                  >
                    <el-button
                      link
                      icon="Delete"
                      type="primary"
                      @click="handleDelete(scope.row)"
                      v-hasPermi="['sys:base:user:remove']"
                      :disabled="
                        scope.row.staffId === queryParams.tenantAdminId
                      "
                      title="删除"
                    ></el-button>
                  </el-tooltip>
                  <el-button
                    link
                    icon="Refresh"
                    type="primary"
                    @click="handleResetPwd(scope.row)"
                    v-hasPermi="['sys:base:user:resetPwd']"
                    title="重置"
                  ></el-button>
                  <el-tooltip
                    v-if="scope.row.staffId !== 1"
                    content="租户管理员不允许冻结"
                    :disabled="scope.row.staffId !== queryParams.tenantAdminId"
                    placement="top"
                  >
                    <el-button
                      link
                      icon="Lock"
                      type="primary"
                      v-hasPermi="['sys:base:user:freeze']"
                      :disabled="
                        scope.row.staffId === queryParams.tenantAdminId
                      "
                      @click="handleStatusChange(scope.row)"
                      title="冻结"
                    ></el-button>
                  </el-tooltip>
                  <el-button
                    link
                    icon="UserFilled"
                    type="primary"
                    v-hasPermi="['sys:base:user:post']"
                    @click="handlePosition(scope.row)"
                    title="岗位"
                  ></el-button>
                  <el-button
                    link
                    icon="Avatar"
                    type="primary"
                    v-hasPermi="['sys:base:user:empower']"
                    @click="openAddRole(scope.row)"
                    title="赋权"
                  ></el-button>
                </div>
                <div v-if="scope.row.staffStatus === 'invalid'">
                  <el-button
                    link
                    icon="Unlock"
                    type="primary"
                    v-hasPermi="['sys:base:user:enable']"
                    @click="handleStatusChange(scope.row)"
                    title = '启用'>
                  </el-button>
                </div>
              </template>
          </public-table>
        </el-card>
      </Pane>
    </Splitpanes>

    <!-- 添加或修改用户信息对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <div class="dialog-box dialog-box-edit">
        <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="人员姓名" prop="staffName">
                <el-input
                  v-model="form.staffName"
                  placeholder="请输入人员姓名"
                  maxlength="64"/>
              </el-form-item>
              <el-form-item v-show="false">
                <el-input v-model="form.staffId"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="归属组织" prop="orgName">
                <el-input
                  v-model="form.orgName"
                  placeholder="请选择归属组织"
                  disabled
                >
                  <template #append>
                    <el-link @click="$refs.treeSelect.open()">选择</el-link>
                  </template>
                </el-input>
              </el-form-item>
              <el-form-item v-show="false">
                <el-input v-model="form.orgId"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="登录名称" prop="loginName">
                <el-input
                  v-model="form.loginName"
                  placeholder="请输入登录名称"
                  :disabled="operateFlag"
                  maxlength="64"
                  />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="用户类型" prop="staffType">
                <el-select v-model="form.staffType" disabled>
                  <el-option
                    v-for="item in staff_type"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="手机号码" prop="cellphone">
                <el-input
                  v-model="form.cellphone"
                  placeholder="请输入手机号码"
                  maxlength="11"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="邮箱" prop="email">
                <el-input
                  v-model="form.email"
                  placeholder="请输入邮箱"
                  maxlength="50"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="身份证号" prop="employeeCode">
                <el-input
                  v-model="form.employeeCode"
                  placeholder="请输入身份证号"
                  maxlength="50"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="性别" prop="sex">
                <el-radio-group v-model="form.sex">
                  <el-radio
                    v-for="dict in sys_user_sex"
                    :key="dict.value"
                    :label="dict.value"
                    >{{ dict.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <!-- <el-col :span="12">
            <el-form-item label="生效日期" prop="effectStartDate">
              <el-date-picker
                v-model="form.effectStartDate"
                type="date"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                placeholder="默认永久有效"
              />
            </el-form-item>
          </el-col> -->
          <el-col :span="12">
            <el-form-item label="失效日期" prop="effectEndDate">
              <el-date-picker
                v-model="form.effectEndDate"
                type="date"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                placeholder="默认永久有效"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="申请人" prop="applicant">
              <el-select
                collapse-tags
                placeholder="请输入人员进行选择"
                clearable
                @change="changeStaff"
                filterable
                remote
                v-model="form.applicant"
                reserve-keyword
                :remote-method="getUserList"
              >
                <el-option
                  v-for="(item, index) in applyUserList"
                  :key="index"
                  :label="item.staffName"
                  :value="item.staffId">
                  <div>
                    {{
                      item.staffName +
                      "(" +
                      item.orgName +
                      "/" +
                      item.loginName +
                      ")"
                    }}
                  </div>
                </el-option>
              </el-select>
              <div v-if="popupType == 'view'">
                {{ form.applicantName || "-" }}
              </div>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="12">
            <el-form-item label="申请时间" prop="applicationTime">
              <el-date-picker
                v-model="form.applicationTime"
                disabled
                type="datetime"
                placeholder="默认当前时间"
              />
            </el-form-item>
          </el-col> -->
          <el-col :span="24">
            <el-form-item label="申请说明" prop="description">
              <el-input
                type="textarea"
                placeholder="请输入申请说明"
                v-model="form.description"
                maxlength="200"
                show-word-limit
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
              <el-form-item label="人员状态" prop="staffStatus">
                <el-radio-group v-model="form.staffStatus">
                  <el-radio
                    v-for="dict in user_status"
                    :key="dict.value"
                    :label="dict.value"
                    >{{ dict.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="岗位状态" prop="staffOrgStatus">
                <el-radio-group v-model="form.staffOrgStatus">
                  <el-radio
                    v-for="dict in user_status"
                    :key="dict.value"
                    :label="dict.value"
                    >{{ dict.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          <el-col :span="12">
              <el-form-item label="租户">
                <el-input
                  v-model="form.tenantName"
                  placeholder="租户"
                  maxlength="50"
                  disabled
                />
              </el-form-item>
            </el-col>
          
        
            <el-col :span="12" v-if="form.staffStatus === 'invalid'">
              <el-form-item label="冻结原因" prop="attra">
                <el-input
                  v-model="form.attra"
                  placeholder="请输入冻结原因"
                  maxlength="200"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="12" v-if="staffList.length > 0">
            <div class="common-box-one" style="margin-bottom: 12px">
              <div class="common-header">
                <div class="common-header-line"></div>
                <div class="common-header-text">相似用户</div>
              </div>
            </div>
            <!-- 用户信息表格 -->
          <el-table v-loading="loading" :data="staffList">
            <el-table-column
              label="用户编号"
              align="left"
              key="staffId"
              prop="staffId"
                width="180"
                 :show-overflow-tooltip="true"
              v-if="staffColumns[0].visible"
            />
            <!-- <el-table-column
              label="组织"
              align="left"
              key="orgName"
              prop="orgName"
              v-if="staffColumns[1].visible"
              :show-overflow-tooltip="true"
              width="150"
            /> -->
            <el-table-column
              label="人员姓名"
              align="left"
              key="staffName"
              prop="staffName"
              width="100"
              v-if="staffColumns[2].visible"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              label="登录名称"
              align="left"
              key="loginName"
                width="100"
              prop="loginName"
              v-if="staffColumns[3].visible"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              label="用户类型"
              align="center"
              key="staffType"
              prop="staffType"
              v-if="staffColumns[4].visible"
            >
              <template #default="scope">
                <dict-tag :options="staff_type" :value="scope.row.staffType" />
              </template>
            </el-table-column>
            <el-table-column
              label="手机号码"
              align="center"
              key="cellphone"
              prop="cellphone"
              v-if="staffColumns[6].visible"
              width="100"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              label="身份证号"
              align="center"
              key="employeeCode"
              prop="employeeCode"
              v-if="staffColumns[7].visible"
              width="130"
              :show-overflow-tooltip="true"
            />
            <el-table-column label="人脸图像" width="180" align="center">
              <template #default="scope">
                <el-button icon="Picture" @click="showFace(scope.row)">查看人脸</el-button>
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              align="center"
              width="80"
              class-name="small-padding fixed-width"
            >
              <template #default="scope">
                <div>
                  <el-tooltip
                    v-if="scope.row.staffType !== '3'"
                    content="外协不能添加正式或借调人员"
                    placement="top"
                  >
                    <el-button
                      link
                      icon="Tickets"
                      type="primary"
                      @click="handleCopy(scope.row)"
                      :disabled="scope.row.staffType !== '3'"
                      title="填充数据"
                    ></el-button>
                  </el-tooltip>
                  <el-tooltip
                    v-else-if="scope.row.staffType === '3' && scope.row.tenantId === queryParams.tenantId"
                    content="外协不能添加同租户下外协人员"
                    placement="top"
                  >
                    <el-button
                      link
                      icon="Tickets"
                      type="primary"
                      @click="handleCopy(scope.row)"
                      :disabled="scope.row.staffType === '3' && scope.row.tenantId === queryParams.tenantId"
                      title="填充数据"
                    ></el-button>
                  </el-tooltip>
                  <el-button v-else 
                      link
                      icon="Tickets"
                      type="primary"
                      @click="handleCopy(scope.row)"
                      title="填充数据"
                    ></el-button>
                  <!-- <el-tooltip 
                  content="外协可添加其他租户下外协人员"
                  placement="top">
                    
                  </el-tooltip> -->
                </div>
              </template>
            </el-table-column>
          </el-table>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer" style="text-align: right;">
          <el-button @click="cancel('edit')">取 消</el-button>
          <el-button type="primary" @click="submitForm" :loading="saveLoading">确 定</el-button>
      </div>
      <dept-select
        ref="treeSelect"
        name
        value
        :tenant-id="queryParams.tenantId"
        @selected="selected"
      />
      </div>
    </el-dialog>

    <!-- 岗位调整对话框 -->
    <el-dialog
      :title="title"
      v-model="openPositionDialog"
      width="600px"
      append-to-body
    >
      <el-form
        ref="positionFormRef"
        :model="positionForm"
        :rules="positionRules"
        label-width="80px"
      >
        <el-row :gutter="12">
          <el-col :span="24">
            <el-form-item label="用户">
              <el-input
                v-model="positionForm.staffName"
                placeholder="用户"
                disabled
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="归属组织" prop="orgName">
              <el-input
                v-model="positionForm.orgName"
                placeholder="请选择归属组织"
                disabled
              >
                <template #append>
                  <el-link @click="$refs.treeSelect.open()">选择</el-link>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item v-show="false">
              <el-input v-model="positionForm.orgId"></el-input>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="24">
            <el-form-item label="岗位类别" prop="staffOrgType">
              <el-radio-group v-model="positionForm.staffOrgType">
                <el-radio label="T">兼职</el-radio>
                <el-radio label="J">借调</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col> -->
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          @click="submitPositionForm"
          :loading="saveLoading"
          >确 定</el-button
        >
        <el-button @click="cancel('position')">取 消</el-button>
      </div>
      <dept-select
        ref="treeSelect"
        name
        value
        :tenant-id="queryParams.tenantId"
        @selected="selected"
      />
    </el-dialog>

    <!-- 角色赋权对话框 -->
    <transfer-table
      ref="transferTable"
      modelName="角色赋权"
      @loadData="loadData"
      :listRole="listRole"
      :listProp="{
        key: 'roleName',
        name: '角色名称',
      }"
      :selectedProp="{
        key: 'roleName',
        name: '角色名称',
      }"
      @add="addUserRole"
      @remove="delUserRole"
    />
    <!-- 岗位赋权对话框 -->
    <transfer-table
      ref="transferPositionTable"
      modelName="角色赋权"
      @loadData="loadPositionRoleData"
      :listRole="listRole"
      :listProp="{
        key: 'roleName',
        name: '角色名称',
      }"
      :selectedProp="{
        key: 'roleName',
        name: '角色名称',
      }"
      @add="addPositionRole"
      @remove="delPositionRole"
    />

    <!--查看人脸图片-->
    <el-dialog
          title="人脸图片"
          v-model="faceImageDialog"
          width="30%">
        <PreviewImage style="width: 100%;height: 300px;"
                v-if="faceImage"
                :photo-id="faceImage"
              ></PreviewImage>
        <el-empty v-else description="暂无上传人脸信息"></el-empty>
    </el-dialog>
  </div>
</template>

<script setup name="UserOutSource">
import treeLoad from "@/components/Tree/treeLoad";
import useUserStore from "@/store/modules/user";
import { ref,watch } from "vue";
import { getTenants } from "@/api/tenant/tenant";
import {
  addStaffOrgAndRole,
  addUserRoles,
  delStaff,
  delUserRoleInfo,
  enableOrDisablePortaluser,
  findUserRoles,
  getUser,
  getUserInfo,
  resetUserPwd,
  upload,
  queryUserList,
  listOutsourceStaff,
  listOutsourceSysUser,
  updateSysUser,
  addOutsourceStaff,
} from "@/api/system/user";
import {getFaceImageInfo} from "@/api/system/userIdentify";
import { getDept, treeselect,getOrgSidebar } from "@/api/system/dept";
import { ElMessageBox } from "element-plus";
import DeptSelect from "@/systemViews/system/dept/components/deptSelect";
import TransferTable from "@/components/TransferTable";
import { findRoleListByScope, findByOrgName } from "@/api/system/role";
import { getDictInfo } from "@/api/system/dict/data";
const isLazy = ref(true);
const treeData = ref([]);
const treeindex = ref(0);
const treeFatherIndex = ref(0);
/** 树结构数据默认 */
const defaultProps = reactive({
  children: "children",
  label: "orgName",
  isLeaf: (data) => !data.isParent,
});

const { proxy } = getCurrentInstance();
const { staff_kind, staff_type, user_status, sys_user_sex,staff_org_type } = proxy.useDict(
  "staff_kind",
  "staff_type",
  "user_status",
  "sys_user_sex",
  "staff_org_type"
);

const userStore = useUserStore();
const userType = userStore.userInfo.customParam.userType;
let defaultOrgId = userStore.userInfo.orgId;
const defaultSelectedKey = ref()
const getUserLoading = ref(false);
const getTenantLoading = ref(false);
const queryParams = ref({
  tenantId: userStore.userInfo.tenantId,
  pageNum: 1,
  pageSize: 10,
  email: undefined,
  cellphone: undefined,
  // orgId: userStore.userInfo.orgId,
  orgId: "",
  staffOrgType: "F",
  tenantAdminId: userStore.userInfo.customParam.tenantAdminId,
  tenantName: userStore.userInfo.customParam.tenantName,
});
const isShowFile = ref(true);
const treeLoading = ref(false);
const selectNode = ref(undefined);
const loadUserId = ref(undefined);
const loadStaffOrgId = ref(undefined);
const selectNodeName = ref("全部人员");
const tenantList = ref([]);
const userList = ref([]);
const listRole = ref([]);
const form = ref({
  staffStatus: "valid",
  staffOrgStatus: "valid",
  staffType: "3",
  sex: "male",
});
const openPositionDialog = ref(false);
const positionForm = ref({
  orgId: "",
  orgName: "",
  staffId: "",
  staffName: "",
  staffOrgType: "T",
});
const queryStaffParams = ref({
  tenantId: userStore.userInfo.tenantId,
  //pageNum: 1,
  //pageSize: 10,
  //params: "",
  staffOrgType: "F",
  // tenantAdminId: userStore.userInfo.customParam.tenantAdminId,
  // tenantName: userStore.userInfo.customParam.tenantName,
});
const staffList = ref([]);
const staffColumns = ref([
  { key: 0, label: `用户编号`, visible: false },
  { key: 1, label: `组织`, visible: true },
  { key: 2, label: `人员姓名`, visible: true },
  { key: 3, label: `登录名称`, visible: true },
  { key: 4, label: `用户类型`, visible: true },
  { key: 5, label: `用户状态`, visible: true },
  { key: 6, label: `手机号码`, visible: true },
  { key: 7, label: `身份证号`, visible: true },
  // { key: 8, label: `邮箱`, visible: true },
]);
const applicant = ref("");
// 遮罩层
const loading = ref(true);
const total = ref(0);
// 显示搜索条件
const showSearch = ref(true);
const open = ref(false);
const title = ref("");
const operateType = ref("");
const operateFlag = ref(false);
const userTypeOptions = ref([]);
const positionRoleList = ref([]);
const positionRoleErrorMsg = ref(true);
const tabelForm = reactive({
  tableKey: "1", //表格key值
  isShowRightToolbar: true, //是否显示右侧显示和隐藏
  showSearch: true,
  // 表格表格数据
  columns: [
         {
            fieldIndex: "staffId", // 对应列内容的字段名
            label: "用户编号", // 显示的标题
            resizable: true, // 对应列是否可以通过拖动改变宽度
            visible: false, // 展示与隐藏
            sortable: true, // 对应列是否可以排序
            fixed: "", //固定
            minWidth: "180", //最小宽度%
            width: "", //宽度
            align: "left", //表格对齐方式
          },
          {
            fieldIndex: "orgName", // 对应列内容的字段名
            label: "组织", // 显示的标题
            resizable: true, // 对应列是否可以通过拖动改变宽度
            visible: true, // 展示与隐藏
            sortable: true, // 对应列是否可以排序
            fixed: "", //固定
            minWidth: "140", //最小宽度%
            width: "", //宽度
            align: "left", //表格对齐方式
          },
          {
            fieldIndex: "staffOrgType", // 对应列内容的字段名
            label: "岗位类型", // 显示的标题
            resizable: true, // 对应列是否可以通过拖动改变宽度
            visible: true, // 展示与隐藏
            sortable: true, // 对应列是否可以排序
            fixed: "", //固定
            minWidth: "110", //最小宽度%
            width: "", //宽度
            align: "center", //表格对齐方式
            type: "dict",
            dictList: staff_org_type,
          },
          {
            fieldIndex: "staffName", // 对应列内容的字段名
            label: "人员姓名", // 显示的标题
            resizable: true, // 对应列是否可以通过拖动改变宽度
            visible: true, // 展示与隐藏
            sortable: true, // 对应列是否可以排序
            fixed: "", //固定
            minWidth: "120", //最小宽度%
            width: "left", //宽度
            align: "", //表格对齐方式
          },
          {
            fieldIndex: "loginName", // 对应列内容的字段名
            label: "登录名称", // 显示的标题
            resizable: true, // 对应列是否可以通过拖动改变宽度
            visible: true, // 展示与隐藏
            sortable: true, // 对应列是否可以排序
            fixed: "", //固定
            minWidth: "120", //最小宽度%
            width: "left", //宽度
            align: "", //表格对齐方式
          },
          {
            fieldIndex: "staffType", // 对应列内容的字段名
            label: "用户类型", // 显示的标题
            resizable: true, // 对应列是否可以通过拖动改变宽度
            visible: true, // 展示与隐藏
            sortable: true, // 对应列是否可以排序
            fixed: "", //固定
            minWidth: "110", //最小宽度%
            width: "", //宽度
            align: "center", //表格对齐方式
            type: "dict",
            dictList: staff_type,
          },
          {
            fieldIndex: "staffStatus", // 对应列内容的字段名
            label: "用户状态", // 显示的标题
            resizable: true, // 对应列是否可以通过拖动改变宽度
            visible: true, // 展示与隐藏
            sortable: true, // 对应列是否可以排序
            fixed: "", //固定
            minWidth: "110", //最小宽度%
            width: "", //宽度
            align: "center", //表格对齐方式
            type: "dict",
            dictList: user_status,
          },
          {
            fieldIndex: "cellphone", // 对应列内容的字段名
            label: "手机号码", // 显示的标题
            resizable: true, // 对应列是否可以通过拖动改变宽度
            visible: true, // 展示与隐藏
            sortable: true, // 对应列是否可以排序
            fixed: "", //固定
            minWidth: "120", //最小宽度%
            width: "", //宽度
            align: "left", //表格对齐方式
          },
          {
            fieldIndex: "employeeCode", // 对应列内容的字段名
            label: "身份证号", // 显示的标题
            resizable: true, // 对应列是否可以通过拖动改变宽度
            visible: true, // 展示与隐藏
            sortable: true, // 对应列是否可以排序
            fixed: "", //固定
            minWidth: "160", //最小宽度%
            width: "", //宽度
            align: "left", //表格对齐方式
          },
          {
            fieldIndex: "email", // 对应列内容的字段名
            label: "邮箱", // 显示的标题
            resizable: true, // 对应列是否可以通过拖动改变宽度
            visible: true, // 展示与隐藏
            sortable: true, // 对应列是否可以排序
            fixed: "", //固定
            minWidth: "210", //最小宽度%
            width: "", //宽度
            align: "left", //表格对齐方式
          },
          {
            fieldIndex: "applicantName", // 对应列内容的字段名
            label: "申请人", // 显示的标题
            resizable: true, // 对应列是否可以通过拖动改变宽度
            visible: true, // 展示与隐藏
            sortable: true, // 对应列是否可以排序
            fixed: "", //固定
            minWidth: "120", //最小宽度%
            width: "", //宽度
            align: "left", //表格对齐方式
            type:"tag"
          },
          {
            fieldIndex: "applicationTime", // 对应列内容的字段名
            label: "申请时间", // 显示的标题
            resizable: true, // 对应列是否可以通过拖动改变宽度
            visible: true, // 展示与隐藏
            sortable: true, // 对应列是否可以排序
            fixed: "", //固定
            minWidth: "200", //最小宽度%
            width: "", //宽度
            align: "center", //表格对齐方式
          },
          {
            label: "操作",
            slotname: "operation",
            width: "210",
            fixed: "right", //固定
            visible: true,
          },
  ],
  // 表格基础配置项，看个人需求添加
  tableConfig: {
    needPage: true, // 是否需要分页
    index: false, // 是否需要序号
    selection: false, // 是否需要多选
    reserveSelection: false, // 是否需要支持跨页选择
    indexFixed: false, // 序号列固定 left true right
    selectionFixed: false, //多选列固定left true right
    indexWidth: "50", //序号列宽度
    loading: false, //表格loading
    showSummary: false, //是否开启合计
    height: null, //表格固定高度 比如：300px
  },
});
const rules = {
  staffName: [{ required: true, message: "请输入用户名", trigger: "blur" }],
  loginName: [{ required: true, message: "请输入登录名", trigger: "blur" }],
  orgName: [{ required: true, message: "请选择组织", trigger: "blur" }],
  staffStatus: [{ required: true, message: "请选择用户状态", trigger: "blur" }],
  staffOrgStatus: [{ required: true, message: "请选择岗位状态", trigger: "blur" }],
  //staffKind: [{ required: true, message: "请选择用户类型", trigger: "blur" }],
  staffType: [{ required: true, message: "请选择用户类型", trigger: "blur" }],
  sex: [{ required: true, message: "请选择用户性别", trigger: "blur" }],
  attra: [{ required: true, message: "请输入冻结原因", trigger: "blur" }],
  effectStartDate: [{ required: true, message: "请选择生效时间", trigger: "blur" }],
  effectEndDate: [{ required: true, message: "请选择失效时间", trigger: "blur" }],
  applicant: [{ required: true, message: "请选择申请人", trigger: "blur" }],
  description: [{ required: true, message: "请输入申请说明", trigger: "blur" }],
  email: [
    {
      type: "email",
      message: "请输入正确的邮箱地址",
      trigger: ["blur", "change"],
    },
  ],
  cellphone: [
    {
      required: true,
      pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
      message: "请输入正确的手机号码",
      trigger: "blur",
    },
  ],
  employeeCode: [
    {
      required: true,
      pattern:
        /^([1-6][1-9]|50)\d{4}(18|19|20)\d{2}((0[1-9])|10|11|12)(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
      message: "请输入正确的身份证号码",
      trigger: "blur",
    },
  ],
};
const faceImageDialog = ref(false);
const faceImage = ref("");
const applyUserList = ref([]);
// 监听人员姓名、登录名称、手机号变化
watch(() => [form.value.loginName,form.value.cellphone,form.value.employeeCode],([loginVal,phoneVal,codeVal]) => {
  // 判断监听参数是否都有值
  if(operateType.value === "add" && loginVal !== undefined && phoneVal !== undefined && codeVal !== undefined){
    queryStaffParams.value.loginName = loginVal;
    queryStaffParams.value.cellphone = phoneVal;
    queryStaffParams.value.employeeCode = codeVal;
    listOutsourceStaff(queryStaffParams.value).then((response) => {
    staffList.value = response.data;
    loading.value = false;
  });
  }
});

// 刷新加载树形结构的组织
function reloadTree() {
  if (filterText.value) {
    isLazy.value = false;
    findByOrgNames();
  } else {
    isLazy.value = true
    if (selectNode.value) {
    proxy.$refs.asyncTree.reloadTree();
  } else {
    treeindex.value++;
  }
  }

}

const filterText = ref("");
const isSearchFirst = ref(false);
const searchFiler = () => {
  isSearchFirst.value = true;

  if (filterText.value) {
    isLazy.value = false;
    findByOrgNames();
  } else {
    treeData.value = [];
    isLazy.value = true;

    if (selectNode.value) {
      proxy.$refs.asyncTree.reloadTree();
    } else {
      treeindex.value++;
    }
  }
};

// 查询用户数据
const getUserList = async (name) => {
  if (!name || name.trim().length < 2) {
    applyUserList.value = []; // 立即清空列表
    return;
  }
  try {
    queryUserList({
      staffName: name,
    }).then((response)=>{
       applyUserList.value = response.data;
    });
  } catch (error) {
    console.error("获取用户列表失败:", error);
  }
};

// 选择申请人回调
const changeStaff = () => {
  const user = applyUserList.value.find(
    (o) => o.staffId === formData.value.applicant
  );
  if (user) {
    formData.value.applicant = user.staffId;
  }
};

//懒加载树形结构的组织
function loadNode(node, resolve) {
  treeLoading.value = true;
  if (node.level === 0) {
    getOrgSidebar({
      orgId: defaultOrgId,
      queryType: "current",
      tenantId: queryParams.value.tenantId,
      orgName: filterText.value,
    }).then((response) => {
      resolve(response.data);
      treeLoading.value = false;
    });
  } else {
    getOrgSidebar({
      orgId: node.data.orgId,
      queryType: "down",
      tenantId: queryParams.value.tenantId,
      orgName: isSearchFirst.value ? filterText.value : "",
    }).then((response) => {
      resolve(response.data);
      treeLoading.value = false;
      isSearchFirst.value = false;
    });
  }
}

//懒加载树形结构的组织
function loadNodeRooter(resolve) {
  treeLoading.value = true;
  getOrgSidebar({
    orgId: defaultOrgId,
    queryType: "current",
    tenantId: queryParams.value.tenantId,
    orgName: filterText.value,
  }).then((response) => {
    resolve(response.data);
    treeLoading.value = false;
  });
}
function findByOrgNames() {
  treeLoading.value = true;
  findByOrgName({
    tenantId: queryParams.value.tenantId,
    orgName: filterText.value,
  }).then((response) => {
    treeLoading.value = false;
    treeData.value = response.data;
  });
}

// 节点单击事件
function handleNodeClick(data) {
  queryParams.value.orgId = data.orgId;
  selectNode.value = data;
  selectNodeName.value = data.orgName;
  queryParams.value.orgName = data.orgName;
  getList();
}

// 获取租户列表
function initTenantList(tenantName) {
  getTenantLoading.value = true;
  let query = {};
  if (tenantName !== undefined && tenantName !== "") {
    query.tenantName = tenantName;
    query.tenantId = undefined;
  } else {
    query.tenantId = queryParams.value.tenantId;
  }
  getTenants(query)
    .then((response) => {
      tenantList.value = response.data;
    })
    .finally(() => {
      getTenantLoading.value = false;
    });
}

// 下拉框切换租户回调
function handleTenantChange(tenantId) {
  if (tenantId !== "") {
    const tenantObj = tenantList.value.find(
      (item) => item.tenantId === tenantId
    );
    queryParams.value.tenantName = tenantObj.tenantName;
    queryParams.value.tenantAdminId = tenantObj.tenantAdminId;
  }
  proxy.$refs.asyncTree.root.loaded = false;
  proxy.$refs.asyncTree.root.expand();
  selectNode.value = undefined;
  queryParams.value.orgId = undefined;
  handleQuery();
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList("noFirst");
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryForm");
  queryParams.value.pageNum = 1;
  selectNodeName.value = "";
  filterText.value = "";
  selectNode.value = "";
  queryParams.value.orgId = "";
  selectNodeName.value = "全部人员";
  treeData.value = [];
  isLazy.value = true;
  treeFatherIndex.value++;
  getList();
}

/** 查询用户列表 */
function getList(type) {
  tabelForm.tableConfig.loading = true;
  listOutsourceSysUser(queryParams.value).then((response) => {
    userList.value = response.data.records;
    total.value = response.data.total;
    tabelForm.tableConfig.loading = false;

    if (userList.value.length > 0 && type) {
      selectNodeName.value = userList.value[0].orgName;
    }
  });
}

// 获取全部人员
function allUser() {
  resetQuery()
}

// 设置当前时间为 YYYY-MM-DD HH:mm:ss 格式
function setCurrentTime(){
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');
  // 设置申请时间为当前时间
  form.value.applicationTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

// 新增按钮操作
function handleAdd() {
  reset();
  operateType.value = "add";
  if (selectNode.value) {
    form.value = {
      ...form.value,
      orgId: selectNode.value.orgId,
      orgName: selectNode.value.orgName,
    };
    open.value = true;
    title.value = "添加外协人员";
  } else {
    userTypeOptions.value = [];
    open.value = true;
    title.value = "添加外协人员";
  }
  setCurrentTime();
  form.value.tenantName = queryParams.value.tenantName;
  form.value.tenantId = queryParams.value.tenantId;
  form.value.staffType = "3";
}

// 修改按钮操作
function handleUpdate(row) {
  reset();
  operateType.value = "update";
  const userid = row.staffId;
  const staffOrgId = row.staffOrgId;
  loadUserId.value = userid;
  getUserLoading.value = true;
  getUserInfo(userid,staffOrgId)
    .then((response) => {
      getDept(response.data.orgId).then((r) => {
        getUserLoading.value = false;
        loadUserId.value = undefined;
        if (response.data) {
          form.value = {
            ...form.value,
            ...response.data,
            orgName: r.data.orgName,
          };
          setCurrentTime();
          applicant.value = response.data.applicant;
          form.value.applicant = response.data.applicantName;
          open.value = true;
          title.value = "修改外协人员";
        } else {
          proxy.$modal.msgError("数据异常！");
        }
      });
    })
    .catch((e) => {
      operateType.value = "";
      getUserLoading.value = false;
      loadUserId.value = undefined;
      proxy.$modal.msgError("数据异常！");
    });
}

// 删除按钮操作
function handleDelete(row) {
  const staffId = row.staffId;
  const staffOrgId = row.staffOrgId;
  const staffOrgType = row.staffOrgType;
  proxy.$modal
    .confirm('是否确认删除用户"' + row.staffName + '"的数据项?')
    .then(function () {
      let params = {
        staffId: staffId,
        staffOrgId: staffOrgId,
        staffOrgType: staffOrgType
      }
      return delStaff(params);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    });
}

// 重置密码按钮操作
function handleResetPwd(row) {
  proxy.$modal
    .prompt("请输入 " + row.loginName + " 的新密码")
    .then(({ value }) => {
      resetUserPwd(row.staffId, value).then((response) => {
        if (response.code === "1") {
          proxy.$modal.msgSuccess("密码重置成功！");
        } else {
          proxy.$modal.msgError(response.message);
        }
      });
    })
    .catch(() => {});
}

// 用户状态修改
function handleStatusChange(row) {
  let text = row.staffStatus === "valid" ? "冻结" : "启用";
  if (row.staffStatus === "valid") {
    ElMessageBox.prompt(
      '确认要"' + text + '""' + row.staffName + '"用户吗？请输入冻结原因！',
      "警告",
      {
        confirmButtonText: text,
        cancelButtonText: "取消",
        type: "warning",
        inputValidator: (value) => {
          if (!value) {
            return false;
          }
          return true;
        },
        inputErrorMessage: "请输入冻结原因!",
        beforeClose: (action, instance, done) => {
          if (action === "confirm") {
            instance.confirmButtonLoading = true;
            instance.confirmButtonText = text + "中...";
            enableOrDisablePortaluser({
              staffId: row.staffId,
              staffStatus: row.staffStatus === "valid" ? "invalid" : "valid",
              attra: instance.inputValue,
            })
              .then((response) => {
                done();
                if (response.success) {
                  proxy.$modal.msgSuccess(text + "成功");
                  getList();
                } else {
                  proxy.$modal.msgError(response.message);
                }
                instance.confirmButtonLoading = false;
              })
              .catch((e) => {
                done();
                instance.confirmButtonLoading = false;
              });
          } else {
            done();
          }
        },
      }
    );
  } else {
    ElMessageBox.confirm(
      '确认要"' + text + '""' + row.staffName + '"用户吗?',
      "警告",
      {
        confirmButtonText: text,
        cancelButtonText: "取消",
        type: "warning",
        beforeClose: (action, instance, done) => {
          if (action === "confirm") {
            instance.confirmButtonLoading = true;
            instance.confirmButtonText = text + "中...";
            enableOrDisablePortaluser({
              staffId: row.staffId,
              staffStatus: row.staffStatus === "vaild" ? "invalid" : "valid",
            })
              .then((response) => {
                done();
                if (response.success) {
                  proxy.$modal.msgSuccess(text + "成功");
                  getList();
                } else {
                  proxy.$modal.msgError(response.message);
                }
                instance.confirmButtonLoading = false;
              })
              .catch((e) => {
                done();
                instance.confirmButtonLoading = false;
              });
          } else {
            done();
          }
        },
      }
    );
  }
}

// 岗位分配
function handlePosition(row) {
  reset();
  positionForm.value.staffOrgType = "T";
  const staffId = row.staffId;
  loadUserId.value = staffId;
  getUser(staffId)
    .then((response) => {
      getUserLoading.value = false;
      loadUserId.value = undefined;
      if (response.data) {
        positionForm.value = {
          staffId: response.data.staffId,
          staffName: response.data.staffName,
          staffOrgType: "T",
        };
        openPositionDialog.value = true;
        title.value = "新增岗位";
      } else {
        proxy.$modal.msgError("数据异常！");
      }
    })
    .catch((e) => {
      loadUserId.value = undefined;
      proxy.$modal.msgError("数据异常！");
    });
}

// 赋权对话框
function openAddRole(row) {
  loadUserId.value = row.staffId;
  loadStaffOrgId.value = row.staffOrgId;
  proxy.$refs.transferTable.open();
}

// 表单重置
function reset() {
  form.value = {
    staffId: undefined,
    orgId: undefined,
    staffName: undefined,
    cellphone: undefined,
    email: undefined,
    staffStatus: "valid",
    staffOrgStatus: "valid",
    orgName: "",
    tenantName: undefined,
    sex: "male",
  };
  proxy.resetForm("formRef");
  proxy.resetForm("positionForm");
  positionRoleList.value = [];
  staffList.value = [];
  operateType.value = "";
  applicant.value = "";
  operateFlag.value = false;
}

const saveLoading = ref(false);

// 提交人员信息
function submitForm() {
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      saveLoading.value = true;
      if (operateType.value === "update") {
        form.value.passwd = undefined;
        let newForm = { ...form.value };
        newForm.companyId = undefined;
        newForm.displayName = undefined;
        newForm.menus = undefined;
        newForm.permissionsStr = undefined;
        newForm.permissions = undefined;
        newForm.roles = undefined;
        newForm.staffOrgs = undefined;
        if(newForm.applicant === newForm.applicantName && applicant.value !== ""){
          newForm.applicant = applicant.value;
        }
        updateSysUser(newForm).then((response) => {
          saveLoading.value = false;
          if (response.success) {
            proxy.$modal.msgSuccess("修改成功");
            open.value = false;
            getList();
          } else {
           // proxy.$modal.msgError(response.message);
          }
        });
      } else if(operateType.value === "add"){
        addOutsourceStaff(form.value).then((response) => {
          saveLoading.value = false;
          if (response.success) {
            proxy.$modal.msgSuccess("新增成功");
            open.value = false;
            getList();
          } else {
           // proxy.$modal.msgError(response.message);
          }
        });
      }
    }
  });
}

// 新增人员取消按钮
function cancel(type) {
  if (type === "edit") {
    open.value = false;
  } else if (type === "position") {
    openPositionDialog.value = false;
  } 
  reset();
}

// 组织选择的回调
function selected(data) {
  form.value = {
    ...form.value,
    orgId: data.orgId,
    orgName: data.orgName,
  };
  positionForm.value = {
    ...positionForm.value,
    orgId: data.orgId,
    orgName: data.orgName,
  };
  proxy.$refs.treeSelect.close();
}

const positionRules = ref({
  orgName: [{ required: true, message: "请选择组织", trigger: "blur" }],
  staffOrgType: [
    { required: true, message: "请选择岗位类别", trigger: "change" },
  ],
});

// 岗位选择对话框
function openPositionRole() {
  if (
    positionForm.value.orgId === "" ||
    typeof positionForm.value.orgId === "undefined"
  ) {
    proxy.$modal.msgError("请先选择对应的归属组织！");
  } else {
    loadUserId.value = positionForm.value.staffId;
    loadStaffOrgId.value = positionForm.value.orgId;
    proxy.$refs.transferPositionTable.open();
  }
}

// 提交岗位
function submitPositionForm() {
  // positionForm.value.roleIds = positionRoleList.value.map(
  //   (item) => item.roleId
  // );
  positionRoleErrorMsg.value = true;
  positionRoleErrorMsg.value = positionRoleList.value.length <= 0;
  proxy.$refs["positionFormRef"].validate((valid) => {
    //if (valid && !positionRoleErrorMsg.value) {
    if (valid) {
      saveLoading.value = true;
      if (positionForm.value.staffId !== undefined) {
        // positionForm.value.roleIds = positionRoleList.value.map(
        //   (item) => item.roleId
        // );
        let newForm = { ...positionForm.value };
        newForm.staffName = undefined;
        newForm.orgName = undefined;
        newForm.staffOrgType = "J";
        addStaffOrgAndRole(newForm).then((response) => {
          saveLoading.value = false;
          if (response.success) {
            proxy.$modal.msgSuccess("设置成功");
            openPositionDialog.value = false;
            getList();
          } else {
            proxy.$modal.msgError(response.message);
          }
        });
      }
    }
  });
}

// 加载角色列表
function loadData() {
  findRoleListByScope(queryParams.value).then((r) => {
    getDictInfo("scope").then((response) => {
      const newData = response.data.map((v) => {
        v["children"] = r.data.filter((i) => i.roleScope === v.dictValue);
        v["roleName"] = v.dictLabel;
        v["roleId"] = v.dictDataId;
        v["isOp"] = true;
        return v;
      });
      proxy.$refs.transferTable.setData(newData);
    });
  });
  findUserRoles(loadStaffOrgId.value).then((r) => {
    proxy.$refs.transferTable.setRightData({ data: r.data.havelist });
  });
}

// 用户赋权
function addUserRole(row) {
  addUserRoles(loadStaffOrgId.value, row.roleId).then((r) => {
    if (r.code == "0") {
      proxy.$modal.msgError(r.message);
      proxy.$refs.transferTable.reload();
    } else {
      proxy.$modal.msgSuccess("操作成功！");
      proxy.$refs.transferTable.reload();
    }
  });
}

// 删除用户角色
function delUserRole(row) {
  delUserRoleInfo(loadStaffOrgId.value, row.roleId).then((r) => {
    proxy.$refs.transferTable.reload();
  });
}

// 加载岗位角色
function loadPositionRoleData() {
  findRoleListByScope(queryParams.value).then((r) => {
    getDictInfo("scope").then((response) => {
      const newData = response.data.map((v) => {
        v["children"] = r.data.filter((i) => i.roleScope === v.dictValue);
        v["roleName"] = v.dictLabel;
        v["roleId"] = v.dictDataId;
        v["isOp"] = true;
        return v;
      });
      proxy.$refs.transferPositionTable.setData(newData);
    });
  });
  proxy.$refs.transferPositionTable.setRightData({
    data: positionRoleList.value,
  });
}

// 添加岗位角色
function addPositionRole(row) {
  const result = positionRoleList.value.find(
    (item) => item.roleId === row.roleId
  );
  if (typeof result !== "undefined") {
    proxy.$modal.msgError(`请勿重复添加角色：${row.roleName}`);
    proxy.$refs.transferPositionTable.reload();
  } else {
    positionRoleList.value.push(row);
    proxy.$refs.transferPositionTable.reload();
  }
  positionRoleErrorMsg.value = positionRoleList.value.length <= 0;
}

// 删除岗位角色
function delPositionRole(row) {
  positionRoleList.value.splice(
    positionRoleList.value.findIndex((item) => item.roleId === row.roleId),
    1
  );
  proxy.$refs.transferPositionTable.reload();
  positionRoleErrorMsg.value = positionRoleList.value.length <= 0;
}

// 处理文件改变
function handleFileChange(e) {
  isShowFile.value = false;
  loading.value = true;
  // 触发input选择文件事件
  let inputDOM = proxy.$refs.inputer;
  var file = inputDOM.files[0]; // 通过DOM取文件数据
  var fileName = file.name;

  var formData = new FormData(); // new一个formData事件
  formData.append("multipartFile", file); // 将file属性添加到formData里
  formData.append("orgId", queryParams.value.orgId);

  // 使用axios提交到后台
  upload(formData).then((reponse) => {
    isShowFile.value = true;
    loading.value = false;
    if (reponse.success === false) {
      proxy.$modal.msgError(reponse.message);
    } else {
      proxy.$modal.msgSuccess("导入成功");
      getList();
    }
  });
}

// 导入按钮点击事件
function handleUpload() {
  proxy.$refs.inputer.dispatchEvent(new MouseEvent("click")); // 触发input框的click事件
}


// 自动填充数据
function handleCopy(row){
  if(row !== null){
    form.value.staffId = row.staffId;
    form.value.staffName = row.staffName;
    form.value.loginName = row.loginName;
    form.value.cellphone = row.cellphone;
    form.value.employeeCode = row.employeeCode;
    form.value.email = row.email; 
    form.value.unionId = row.unionId;
    operateFlag.value = true;
  }
}

// 显示人脸图片
function showFace(row) {
  let params = {
    loginName: row.loginName,
    tenantId: row.tenantId,
  }
  getFaceImageInfo(params).then((response) => {
    if(response.success){
      faceImage.value = response.data.faceImage;
      faceImageDialog.value = true;
    }
    }).catch((e) => {
      proxy.$modal.msgError("数据异常！");
    });
}

initTenantList();
getList();
</script>

<style scoped>
.dep-card {
  min-height: calc(100vh - 120px);
}
.error-msg {
  color: #f56c6c;
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
}
</style>
