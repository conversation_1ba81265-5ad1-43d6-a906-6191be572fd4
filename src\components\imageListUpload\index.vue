<template>
  <div class="images-list" :class="{ hide: hideUploadEdit }">
    <!-- 文件上传组件 -->
    <el-upload
      class="upload-box"
      :action="uploadUrl"
      :headers="myHeaders"
      :before-upload="handleBeforeUpload"
      :on-success="handleSuccess"
      :on-error="handleUploadError"
      :on-remove="handleRemove"
      :before-remove="beforeRemove"
      :on-exceed="handleExceed"
      :on-preview="handlePictureCardPreview"
      :file-list="fileList"
      :multiple="fileLimit > 1"
      :data="paramsData"
      :limit="fileLimit"
      :list-type="listType"
    >
      <!-- 上传按钮 -->
      <i v-if="listType === 'picture-card'" class="el-icon-plus"></i>
      <el-button v-else size="small" type="primary">点击上传</el-button>
      <!-- 提示信息 -->
      <div v-if="showTip" class="el-upload__tip">
        只能上传{{ fileTypeName || "jpg/png" }}文件，且不超过 {{ fileSize }}MB
      </div>
    </el-upload>

    <!-- 图片预览弹窗 -->
    <el-dialog v-model="dialogVisible" :modal="false" :append-to-body="true">
      <img width="100%" :src="dialogImageUrl" alt="" />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getToken } from '@/utils/auth'

// ===================== 属性定义 =====================
const props = defineProps({
  modelValue: [String, Object, Array], // 双向绑定的值
  fileSize: {                         // 文件大小限制（MB）
    type: Number,
    default: 2
  },
  fileType: {                         // 允许的文件类型
    type: Array,
    default: () => ['png', 'jpg', 'jpeg']
  },
  listType: {                         // 文件列表类型
    type: String,
    default: 'picture'
  },
  uploadUrlProp: {                    // 上传地址
    type: String,
    default: ''
  },
  isShowTip: {                        // 是否显示提示
    type: Boolean,
    default: true
  },
  fileLimit: {                        // 最大上传数量
    type: Number,
    default: 99
  },
  paramsData: {                       // 上传额外参数
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'removeImage']) // 定义事件

// ===================== 响应式数据 =====================
const uploadUrl = ref(import.meta.env.VITE_APP_BASE_API + props.uploadUrlProp) // 上传地址
const myHeaders = ref({ Authorization: 'Bearer ' + getToken() })               // 请求头
const fileList = ref([])                                                      // 文件列表
const dialogImageUrl = ref('')                                                // 预览图片地址
const dialogVisible = ref(false)                                              // 预览弹窗状态
const hideUploadEdit = ref(false)                                             // 是否隐藏上传按钮

// ===================== 计算属性 =====================
// 是否显示提示
const showTip = computed(() => props.isShowTip && (props.fileType || props.fileSize))

// 文件类型名称
const fileTypeName = computed(() => props.fileType.join('，'))

// 文件类型接受格式
const fileAccept = computed(() => props.fileType.map(type => `.${type}`).join(','))

// ===================== 监听器 =====================
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal) {
      fileList.value = JSON.parse(JSON.stringify(newVal))
      hideUploadEdit.value = fileList.value.length >= props.fileLimit
    }
  },
  { immediate: true, deep: true }
)

// ===================== 方法定义 =====================
// 上传前校验
const handleBeforeUpload = (file) => {
  // 校验文件类型
  const fileExtension = file.name.slice(file.name.lastIndexOf('.') + 1)
  const isTypeOk = props.fileType.some(type => file.type.includes(type) || fileExtension.includes(type))
  if (!isTypeOk) {
    ElMessage.error(`文件格式不正确, 请上传${props.fileType.join('/')}格式文件!`)
    return false
  }

  // 校验文件大小
  const isLt = file.size / 1024 / 1024 < props.fileSize
  if (!isLt) {
    ElMessage.error(`上传文件大小不能超过 ${props.fileSize} MB!`)
    return false
  }

  return true
}

// 上传成功回调
const handleSuccess = (res, file, fileList) => {
  if (res.code === 200) {
    ElMessage.success('上传成功')
    changeFileList(fileList)
  } else {
    ElMessage.error(res.msg)
    handleRemove(file, fileList)
  }
}

// 上传失败回调
const handleUploadError = () => {
  ElMessage.error('上传失败, 请重试')
}

// 文件超出限制回调
const handleExceed = () => {
  ElMessage.error(`超出上传文件个数,请删除以后再上传！`)
}

// 删除文件前确认
const beforeRemove = (file, fileList) => {
  if (file.status === 'success') {
    return ElMessageBox.confirm('此操作将永久删除该图片, 是否继续?', '提示', {
      confirmButtonText: '是',
      cancelButtonText: '否',
      type: 'warning'
    })
      .then(() => {
        changeFileList(fileList)
        emit('removeImage', fileList, file)
        return true
      })
      .catch(() => false)
  }
  return true
}

// 删除文件回调
const handleRemove = (file, fileList) => {
  if (file.status === 'success') {
    changeFileList(fileList)
    emit('removeImage', fileList, file)
  }
}

// 更新文件列表
const changeFileList = (fileList) => {
  const tempFileList = fileList.map(item => ({
    name: item.name,
    url: item.response?.url || item.url,
    res: item.response
  }))
  hideUploadEdit.value = fileList.length >= props.fileLimit
  emit('update:modelValue', tempFileList)
}

// 图片预览
const handlePictureCardPreview = (file) => {
  dialogImageUrl.value = file.url
  dialogVisible.value = true
}
</script>

<style lang="scss" scoped>
.images-list {
  border: 1px dashed #d5d5d5;
  padding: 10px;
  border-radius: 4px;
  background: #fff;
}

// 添加/删除文件时去掉动画过渡
::v-deep .el-upload-list__item {
  transition: none !important;
}
</style>