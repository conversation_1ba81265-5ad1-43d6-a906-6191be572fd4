import request from '@/utils/request'
import { apiUrl } from '@/utils/config'; 



export class screenIndex {
//选择明细列表

    static pageList(data,params) {
      return request({
        url: `pay${apiUrl}/pay/payPaymentOrder/pageList`,
        method: 'post',
        data: {...data,...params},
      })
    }

    // 支付场景树
    static paySceneTree(data) {
      return request({
        url: `pay${apiUrl}/pay/payPaymentOrder/paySceneTree`,
        method: 'post',
        data
      })
    }

    // 支付类型树
    static payTypeTree(data) {
      return request({
        url: `pay${apiUrl}/pay/payPaymentOrder/payTypeTree`,
        method: 'post',
        data
      })
    }
    


}