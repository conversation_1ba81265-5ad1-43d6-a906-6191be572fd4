<template>
    <div class="main-panel">
        <grid-layout v-if="layout" ref="mainDom" :style="gridStyle()" v-model:layout="layout" :col-num="12"
            :row-height="size.height" :is-draggable="true" :is-resizable="true" :is-mirrored="false"
            :vertical-compact="true" :margin="[size.margin, size.margin]" :use-css-transforms="true"
            @layout-updated="layoutUpdatedEvent">
            <grid-item class="back-item" v-for="item in backLayout" :x="item.x" :y="item.y" :w="item.w" :h="item.h"
                :i="item.i" :key="item.i" :static="true">
            </grid-item>
            <grid-item class="grid-item" v-for="(item, index) in layout" :x="item.x" :y="item.y" :w="item.w" :h="item.h"
                :minH="item.minH" :minW="item.minW" :i="item.i" :key="item.i">
                <component v-if="item.comp" :is="Components[item.comp]" v-bind="item.props" />
                <el-button class="edit-button" type="primary" size="small" :icon="Edit" circle
                    @click="editItem(index)" />
                <el-button class="delete-button" type="danger" size="small" :icon="Delete" circle
                    @click="deleteItem(index)" />
            </grid-item>
        </grid-layout>
        <el-button-group class="button-group">
            <el-button :disabled="!layout" type="primary" :icon="Edit" @click="editSize">页面配置</el-button>
            <el-button :disabled="!layout" type="primary" :icon="Plus" @click="addComp">增加组件</el-button>
            <el-button :disabled="!layout" type="primary" :icon="UploadFilled" @click="saveConfig">保存页面</el-button>
        </el-button-group>
    </div>
    <el-dialog v-model="editSizeVisible" title="页面配置">
        <el-form label-width="120px">
            <el-form-item label="页面宽度">
                <el-radio-group v-model="editSizeData.width">
                    <el-radio label="100%">100%</el-radio>
                    <el-radio label="1366px">1366px</el-radio>
                    <el-radio label="1920px">1920px</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="栅格列数">
                12
            </el-form-item>
            <el-form-item label="栅格高度(px)" prop="height">
                <el-input-number v-model="editSizeData.height" :min="10" :max="100" />
            </el-form-item>
            <el-form-item label="栅格间距(px)" prop="margin">
                <el-input-number v-model="editSizeData.margin" :min="0" :max="20" />
            </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="cancelEditSize">取消</el-button>
                <el-button type="primary" @click="saveEditSize">
                    确定
                </el-button>
            </span>
        </template>
    </el-dialog>
    <el-dialog v-model="addCompVisible" title="组件选择" style="width: 90vw;">
        <div class="comp-action">
            <el-input v-model="compName" placeholder="输入组件名称" clearable>
                <template #prepend>
                    <el-icon>
                        <Search />
                    </el-icon>
                </template>
            </el-input>
            <el-select v-model="compType" placeholder="组件类别" style="width: 200px;margin-left: 50px;" clearable>
                <el-option label="案例组件" value="case" />
                <el-option label="功能组件" value="use" />
                <el-option label="广告组件" value="ad" />
                <el-option label="监控组件" value="monitor" />
                <el-option label="静态组件" value="static" />
            </el-select>
        </div>
        <div class="comp-content">
            <template v-for="(item, key) in CompsConfig">
                <div class="component" v-if="showComp(item)">
                    <div class="header">
                        <span class="name">{{ item.name }}</span>
                        <span class="type" v-if="item.type === 'case'"
                            style="color: #009f5d;border-color: #009f5d;">案例组件</span>
                        <span class="type" v-else-if="item.type === 'use'"
                            style="color: #019fde;border-color: #019fde;">功能组件</span>
                        <span class="type" v-else-if="item.type === 'ad'"
                            style="color: #887ddd;border-color: #887ddd;">广告组件</span>
                        <span class="type" v-else-if="item.type === 'monitor'"
                            style="color: #ff5675;border-color: #ff5675;">监控组件</span>
                        <span class="type" v-else-if="item.type === 'static'"
                            style="color: #f8bd0b;border-color: #f8bd0b;">静态组件</span>
                        <el-button type="primary" :icon="CirclePlusFilled" @click="selectComp(item, key)">添加</el-button>
                    </div>
                    <component :is="Components[key]" />
                </div>
            </template>
        </div>
    </el-dialog>
    <el-dialog v-model="editItemVisible" title="组件配置" destroy-on-close>
        <el-form label-width="120px" v-if="layout[editItemIndex].comp && CompsConfig[layout[editItemIndex].comp]">
            <template v-for="(item, key) in CompsConfig[layout[editItemIndex].comp].props">
                <el-form-item :label="item.label">
                    <el-input-number v-if="item.type === Number" v-model="editItemData[key]" />
                    <el-input v-else-if="item.type === String" v-model="editItemData[key]" />
                    <el-switch v-else-if="item.type === Boolean" v-model="editItemData[key]" />

                </el-form-item>
            </template>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="cancelEditItem">取消</el-button>
                <el-button type="primary" @click="saveEditItem">
                    确定
                </el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup>
import { h, ref, reactive, computed, watch, onMounted, onUnmounted, defineAsyncComponent, nextTick } from "vue";
import { useRouter, useRoute } from "vue-router";
import { GridLayout, GridItem } from 'vue3-grid-layout'
import { ElMessageBox, ElMessage, ElRadioGroup, ElRadio } from "element-plus";
import { Delete, CirclePlusFilled, UploadFilled, Plus, Edit, Search } from '@element-plus/icons-vue'
import { CompsConfig, Modules } from "@/gridViews/config"
import { updateConfigInfo, getAllLayoutList } from "@/api/system/config";
import { getPageDesigner, updatePageDesigner } from "@/api/extend/pageDesigner";
import useUserStore from "@/store/modules/user";
import { defaultSize } from "./config"

const router = useRouter();
const route = useRoute();

const userStore = useUserStore();

const mainDom = ref(null)

const Components = {}

for (const key in CompsConfig) {
    if (Object.hasOwnProperty.call(CompsConfig, key)) {
        if (Modules[key]) {
            Components[key] = defineAsyncComponent(() =>
                Modules[key]()
            )
        }
    }
}

console.log(CompsConfig)

const rowNum = ref(0)

const backLayout = computed(() => {
    const list = []
    for (let yIndex = 0; yIndex < rowNum.value + 2; yIndex++) {
        for (let xIndex = 0; xIndex < 12; xIndex++) {
            list.push({
                "x": xIndex,
                "y": yIndex,
                "w": 1,
                "h": 1,
                "i": `backitem-${xIndex}-${yIndex}`
            })
        }
    }
    return list
})

const layout = ref(false)

const size = ref(defaultSize)

function gridStyle() {
    if (size.value.width === '100%') {
        return {
            width: '100%'
        }
    }
    return {
        width: '100%',
        maxWidth: size.value.width
    }
}

const editSizeVisible = ref(false)

const editSizeData = ref({})

const editSize = () => {
    editSizeData.value = { ...size.value }
    editSizeVisible.value = true
}

const cancelEditSize = () => {
    editSizeVisible.value = false
}

const saveEditSize = () => {
    size.value = { ...editSizeData.value }
    editSizeVisible.value = false
    checkItemSize(true)
}

function checkItemSize(checkH) {
    const itemWidth = Math.ceil((mainDom.value.$el.clientWidth - size.value.margin) / 12 - size.value.margin)
    layout.value.forEach(item => {
        item.minW = countMinW(item.minWidth, itemWidth)
        if (checkH) {
            item.minH = countMinH(item.minHeight)
        }
    })
}


const compName = ref('')

const compType = ref('')

const addCompVisible = ref(false)

const addComp = () => {
    addCompVisible.value = true
}

function showComp(comp) {
    if (compName.value && !comp.name.includes(compName.value)) {
        return false
    }
    if (compType.value && compType.value !== comp.type) {
        return false
    }
    return true
}

const selectComp = (comp, compKey) => {
    const { minHeight, minWidth, props } = comp
    const itemWidth = Math.ceil((mainDom.value.$el.clientWidth - size.value.margin) / 12 - size.value.margin)
    const minH = countMinH(minHeight)
    const minW = countMinW(minWidth, itemWidth)
    const newItem = {
        x: 0,
        y: rowNum.value,
        w: 12,
        h: minH,
        minH,
        minW,
        minWidth,
        minHeight,
        i: Date.now(),
        comp: compKey,
        props: getCompProps(props)
    }
    layout.value.push(newItem)
    layoutUpdatedEvent(layout.value)
}

function countMinW(minWidth, itemWidth) {
    if (!minWidth) {
        return 1
    }
    const minW = Math.floor((minWidth + size.value.margin) / (itemWidth + size.value.margin))
    const remainder = (minWidth + size.value.margin) % (itemWidth + size.value.margin)
    if (remainder === 0) {
        return minW
    } else if (remainder <= itemWidth) {
        return minW + 1
    } else {
        return minW + 2
    }
}

function countMinH(minHeight) {
    if (!minHeight) {
        return 1
    }
    const minH = Math.floor((minHeight + size.value.margin) / (size.value.height + size.value.margin))
    const remainder = (minHeight + size.value.margin) % (size.value.height + size.value.margin)
    if (remainder === 0) {
        return minH
    } else if (remainder <= size.value.height) {
        return minH + 1
    } else {
        return minH + 2
    }
}

function getCompProps(configProps) {
    const props = {}
    for (const key in configProps) {
        props[key] = configProps[key].default
    }
    return props
}

const deleteItem = (index) => {
    layout.value.splice(index, 1)
    layoutUpdatedEvent(layout.value)
}

const saveConfig = () => {
    try {
        const simplifyLayout = layout.value.map(item => ({ ...item, moved: undefined }))
        const configStr = JSON.stringify({ size: size.value, layout: simplifyLayout })
        saveLayout(configStr)
        console.log(route)
    } catch (error) {
        console.log(error)
    }
}

const layoutUpdatedEvent = (newLayout) => {
    console.log('layoutUpdatedEvent--------------')
    console.log(newLayout)
    let max = 0
    for (let index = 0; index < newLayout.length; index++) {
        const item = newLayout[index];
        if ((item.y + item.h) > max) {
            max = item.y + item.h
        }
    }
    rowNum.value = max
}

const editItemVisible = ref(false)

let editItemIndex

const editItemData = ref({})

const editItem = (index) => {
    editItemIndex = index
    editItemData.value = { ...layout.value[index].props }
    editItemVisible.value = true
}

const cancelEditItem = () => {
    editItemVisible.value = false
}

const saveEditItem = () => {
    layout.value[editItemIndex].props = { ...editItemData.value }
    editItemVisible.value = false
}

function parseConfig(configStr) {
    try {
        const config = JSON.parse(configStr)
        layout.value = config.layout || []
        size.value = config.size || defaultSize
    } catch (error) {
        layout.value = []
        size.value = defaultSize
    }
}

let requireParams = {}

function initLayout() {
    const { id, tenantId } = route.query
    if (!id) {
        return
    }
    if (route.path === '/page/grid') {
        getPageDesigner(id).then((res) => {
            if (res.success && res.data) {
                parseConfig(res.data.pageDefine)
                requireParams = {
                    pageCode: res.data.pageCode
                }
            }
        })
        return
    }
    let params
    if (route.path === '/home/<USER>/custom') {
        params = {
            configScope: "custom",
            personalId: id
        }
    } else if (route.path === '/home/<USER>/org') {
        params = {
            configScope: "org_config",
            personalId: id
        }
    } else if (route.path === '/home/<USER>/tenant' && tenantId) {
        params = {
            configScope: "tenant_config",
            personalId: id,
            tenantId
        }
    }
    if (params) {
        getAllLayoutList(params).then((res) => {
            if (res.success && res.data && res.data[0]) {
                parseConfig(res.data[0].personalValue)
                requireParams = {
                    configCode: res.data[0].configCode,
                    configId: res.data[0].configId
                }
            }
        })
        return
    }
}

function saveLayout(configStr) {
    const { id, tenantId } = route.query
    if (!id) {
        return
    }
    if (route.path === '/page/grid') {
        const params = {
            id: id,
            pageDefine: configStr,
            ...requireParams
        }
        updatePageDesigner(params).then((res) => {
            console.log('updatePageDesigner')
            console.log(res)
            if (res.success) {
                ElMessage.success('保存页面成功');
            } else {
                ElMessage.error('保存页面失败')
            }
        }).catch(() => {
            ElMessage.error('保存页面失败')
        })
    } else {
        const params = {
            personalId: id,
            personalValue: configStr,
            staffOrgId: userStore.userInfo.staffOrgId,
            ...requireParams
        }
        updateConfigInfo(params).then((res) => {
            console.log('updateConfigInfo')
            console.log(res)
            if (res.success) {
                ElMessage.success('保存页面成功');
            } else {
                ElMessage.error('保存页面失败')
            }
        }).catch(() => {
            ElMessage.error('保存页面失败')
        })
    }
}

initLayout()

setTimeout(()=>{
    console.log(layout.value)
    console.log(editItemIndex)
},8000)
</script>

<style scoped lang="scss">
.main-panel {
    position: relative;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    min-height: calc(100vh - 94px);
}

.back-item {
    border: dashed #8d8d8d 1px;
}

.grid-item {
    border: 1px solid #247fff;
    background-color: #ffffff;

    .edit-button {
        position: absolute;
        right: 36px;
        top: 3px
    }

    .delete-button {
        position: absolute;
        right: 3px;
        top: 3px
    }
}

.button-group {
    position: fixed;
    bottom: 20px;
}

.comp-action {
    width: 100%;
    height: 40px;
    padding: 0px 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.comp-content {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;

    .component {
        width: 100%;
        margin: 10px 0px;
        display: flex;
        flex-direction: column;
        border: 1px solid #a8a8a8;

        .header {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 5px 10px;
            border-bottom: 1px solid #a8a8a8;
            margin-bottom: 5px;

            .name {
                white-space: nowrap;
                font-weight: bold;
            }

            .type {
                white-space: nowrap;
                padding: 4px 10px;
                border: 1px solid #000000;
                border-radius: 5px;
            }
        }
    }

}
</style>
