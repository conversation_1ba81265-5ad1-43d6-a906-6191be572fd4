<template>
  <el-dialog
    v-if="dialog"
    class="commons_popup"
    :class="[{ 'fullscreen-dialog': isFullscreen }, customClass]"
    v-model="dialog"
    append-to-body
    :modal="props.dialogModal"
    :close-on-click-modal="props.dialogClickModalClose"
    :close-on-press-escape="props.dialogESCModalClose"
    :draggable="props.dialogDraggable"
    :show-close="props.dialogShowClose"
    :title="props.dialogTitle"
    :width="props.dialogWidth"
    :align-center="props.dialogAlignCenter"
    :center="props.dialogContentCenter"
    @open="open"
    @close="close"
    :style="{ top: isFullscreen ? '' : props.dialogTop || '' }"
  >
    <template #title>
      <div class="el-popup-header-title">
        <svg-icon :icon-class="iconClass" />
        <span class="el-dialog-header-name">{{ props.dialogTitle }}</span>
        <div class="flex-1"></div>
        <el-icon @click="toggleFullscreen"><FullScreen /></el-icon>
        <!-- 全屏按钮 -->
      </div>
    </template>

    <slot name="content"> </slot>
    <template #footer v-if="props.dialogFooterBtn || props.dialogFooterSubmitBtn">
      <div class="dialog-footer">
        <el-button
          type="default"
          @click="CloseSubmit"
          v-if="props.dialogFooterBtn"
          >{{ CloseSubmitText }}</el-button
        >
        <el-button
          type="primary"
          @click="SaveSubmit"
          v-if="props.dialogFooterBtn"
          >{{ SaveSubmitText }}</el-button
        >
        <el-button
          type="primary"
          @click="SubmitLast"
          v-if="props.dialogFooterSubmitBtn"
          >{{ SubmitLastText }}</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>

  <script  setup>
import { fa } from "element-plus/es/locale/index.mjs";
import { defineProps, ref, defineEmits, watch } from "vue";
const emits = defineEmits(["save", "cancellation", "open", "close"]);
const isFullscreen = ref(false);
const props = defineProps({
  visible: {
    //模态框显示隐藏
    type: Boolean,
    default: false,
  },
  customClass: {
    type: String,
    default: "",
  },
  CloseSubmitText: {
    type: String,
    default: "取消",
  },
  SaveSubmitText: {
    type: String,
    default: "保存",
  },
  SubmitLastText: {
    type: String,
    default: "提交",
  },
  popupType: {
    type: String,
    default: "type",
  },
  dialogTitle: {
    //模态框标题名称
    type: String,
    default: "默认标题",
  },
  dialogWidth: {
    //模态框弹窗宽度
    type: String,
    default: "40%",
  },
  dialogShowClose: {
    //是否显示关闭按钮
    type: Boolean,
    default: true,
  },
  dialogModal: {
    //是否需要模态框(遮罩层)
    type: Boolean,
    default: true,
  },
  dialogAlignCenter: {
    //是否水平垂直对齐模态框
    type: Boolean,
    default: false,
  },
  dialogContentCenter: {
    //模态框header和footer内容是否居中对齐
    type: Boolean,
    default: false,
  },
  dialogFullscreen: {
    //模态框是否为全屏
    type: Boolean,
    default: true,
  },
  dialogClickModalClose: {
    //是否可以通过点击遮罩层关闭Dialog
    type: Boolean,
    default: false,
  },
  dialogESCModalClose: {
    //是否可以通过按下ESC关闭Dialog
    type: Boolean,
    default: false,
  },
  dialogDraggable: {
    //是否开启模态框拖拽功能
    type: Boolean,
    default: false,
  },
  dialogFooterBtn: {
    //是否开启底部操作按钮
    type: Boolean,
    default: true,
  },
  dialogFooterSubmitBtn: {
    //是否开启底部提交操作按钮
    type: Boolean,
    default: false,
  },

  iconClass: {
    //弹窗图标
    type: String,
    default: "edit_file",
  },
  dialogTop: {
    // 控制弹窗顶部距离
    type: String,
    default: "", // 默认15%视口高度
  },
});
const dialog = ref(props.visible);

// watch监听
watch(
  () => props.visible,
  (newValue, oldValue) => {
    dialog.value = newValue;
  },
  { deep: true, immediate: true }
);

// 监听 dialog 状态变化
watch(
  () => dialog.value,
  (newVal) => {
    if (newVal) {
      window.addEventListener("keydown", handleKeyDown);
    } else {
      window.removeEventListener("keydown", handleKeyDown);
    }
  }
);

// 新增键盘事件处理
const handleKeyDown = (event) => {
  if (event.keyCode === 27) {
    // ESC 键
    CloseSubmit();
  }
};

// 组件卸载时移除监听器
onUnmounted(() => {
  window.removeEventListener("keydown", handleKeyDown);
});
// 保存提交回调函数
const SaveSubmit = () => {
  emits("save", false); //emit方法供父级组件调用
};
// 提交按钮
const SubmitLast = () => {
  emits("submit", false); //emit方法供父级组件调用
};
// 取消保存回调函数
const CloseSubmit = () => {
  emits("cancellation", false); //emit方法供父级组件调用
};

// 打开事件回调函数
const open = () => {
  emits("open", true); //emit方法供父级组件调用
};

// 关闭事件回调函数(当显示头部关闭按钮时需调用该回调函数方法 -> dialogShowClose = true 反之)
const close = () => {
  emits("close", false); //emit方法供父级组件调用
};

const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value;
};
</script>

   <style scoped lang="scss">
@import "../../assets/styles/variables.module.scss";

.commons_popup .el-dialog__header {
  padding: 0px !important;
  border-bottom: 1px solid #eee;
}
.el-popup-header-title {
  position: relative;
  top: -4px;
  color: $--color-black;
  display: flex;
  align-items: center;
  width: 100%;
}

.el-popup-header-title svg {
  margin-right: 8px;
  // color: $--color-black;
  color: #000;
  font-size: 18px;
  vertical-align: -4px;
  font-weight: 500;
}

.el-dialog-header-name {
  font-size: 16px;
  // font-family: PingFang SC;
  color: #000;
  font-weight: 500;
}

.bottom-btn {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-right: 10px;
}

.left-empty {
  flex: 1;
}

.bottom-btn .one-cancenl-btn {
  height: 28px;
  background: #fff;
  border: 1px solid #376dee;
  opacity: 1;
  border-radius: 2px;
  padding: 0px 10px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  cursor: pointer;
}

.bottom-btn .one-sure-btn {
  height: 28px;
  // background: $base-home-top-header-color;
  // border: 1px solid $base-home-top-header-color;
  opacity: 1;
  border-radius: 2px;
  padding: 0px 10px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  margin-left: 10px;
  cursor: pointer;
}

.bottom-btn .el-icon-circle-close {
  font-size: 18px;
  margin-right: 5px;
  // color: $base-home-top-header-color;
}

.bottom-btn .one-cancenl-btn-name {
  font-size: 12px;
  font-family: PingFang SC;
  font-weight: 400;
  // color: $base-home-top-header-color;
}

.bottom-btn .el-icon-s-order {
  font-size: 18px;
  margin-right: 5px;
  color: #fff;
}

.bottom-btn .el-icon-success {
  font-size: 18px;
  margin-right: 5px;
  color: #fff;
}

.bottom-btn .one-sure-btn-name {
  color: #fff;
  font-size: 12px;
  font-family: PingFang SC;
  font-weight: 400;
}

.commons_popup .el-dialog__body {
  padding: 15px 20px !important;
  padding-top: 10px !important;
}
.el-popup-header-title .el-icon svg {
  color: $--color-black;
  cursor: pointer;
}
.el-icon {
  margin-right: 25px;
  font-size: 24px;
  margin-top: 4px;
}
</style>
