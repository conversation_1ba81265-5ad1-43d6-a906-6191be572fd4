<template>
  <el-card :class="bgColor === true ? 'line-card':'unshow-line-card'" v-loading="loading" shadow="never">
    <template #header v-if="showTitle">
      <div class="card-title">
        <div class="title">
          <span>
            <el-icon class="icon iconfont icon-gonggao"></el-icon>云监控-折线面积图
          </span>
        </div>
        <div class="top">
          <el-button icon="DArrowRight" class="el-button" circle @click="cancel"></el-button>
          <transition name="forms">
            <el-form :inline="true" class="demo-form-inline" :data="usefulVal" v-if="show" style="line-height:40px;margin-left:10px;width:100%">
              <el-form-item label="监控类型">
                <el-select placeholder="请选择">
                  <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"/>
                </el-select>
              </el-form-item>
            </el-form>
          </transition>
        </div>
      </div>
    </template>
    <div class="home-card-body">
      <div class="borkenLine" v-if="borkenLineVal">
        <div class="borkenLines"></div>
      </div>
    </div>
  </el-card>
</template>

<script setup name="BrokenLine">
import { Chart } from "@antv/g2";

const props = defineProps({
  showTitle: Boolean,
  bgColor: Boolean
})

const { proxy } = getCurrentInstance();
const loading = ref(false)
const show = ref(true)
const usefulVal = ref({})
const borkenLineVal = ref(true)

const options = {

}
const cancel = () => {
  show.value = !show.value;
}

const brokenLine = () => {
  const data = [
    { country: "Asia", year: "1750", value: 502 },
    { country: "Asia", year: "1800", value: 635 },
    { country: "Asia", year: "1850", value: 809 },
    { country: "Asia", year: "1900", value: 5268 },
    { country: "Asia", year: "1950", value: 4400 },
    { country: "Asia", year: "1999", value: 3634 },
    { country: "Asia", year: "2050", value: 947 },
    { country: "Africa", year: "1750", value: 106 },
    { country: "Africa", year: "1800", value: 107 },
    { country: "Africa", year: "1850", value: 111 },
    { country: "Africa", year: "1900", value: 1766 },
    { country: "Africa", year: "1950", value: 221 },
    { country: "Africa", year: "1999", value: 767 },
    { country: "Africa", year: "2050", value: 133 },
    { country: "Europe", year: "1750", value: 163 },
    { country: "Europe", year: "1800", value: 203 },
    { country: "Europe", year: "1850", value: 276 },
    { country: "Europe", year: "1900", value: 628 },
    { country: "Europe", year: "1950", value: 547 },
    { country: "Europe", year: "1999", value: 729 },
    { country: "Europe", year: "2050", value: 408 },
    { country: "Oceania", year: "1750", value: 200 },
    { country: "Oceania", year: "1800", value: 200 },
    { country: "Oceania", year: "1850", value: 200 },
    { country: "Oceania", year: "1900", value: 460 },
    { country: "Oceania", year: "1950", value: 230 },
    { country: "Oceania", year: "1999", value: 300 },
    { country: "Oceania", year: "2050", value: 300 },
  ];

  const chart = new Chart({
    container: proxy.$el.querySelector(".borkenLines"),
    autoFit: true,
    height: 400,
    width: 1000
  });

  chart
      .data(data)
      .encode('x', 'year')
      .encode('y', 'value')
      .encode('color', 'country')
      .axis('x', { title: false })
      .axis('y', { title: false });

  chart.area().style('fillOpacity', 0.3);

  chart.line().style('strokeWidth', 2).tooltip(false);

  chart.render();
}

onMounted(() => {
  brokenLine();
})
</script>

<style scoped lang="scss">
.line-card {
  height: 100%;
  background: #fff;
  overflow: auto;
  .center {
    position: relative;
  }
  .card-title {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  #titleTop {
    position: absolute;
    top: 50%;
    right: 50%;
    transform: translate(50%, -50%);
    margin: 0 auto;
    font-size: 16px;
    text-align: center;
  }

  .veRing {
    margin: 0 auto;
  }
  .top {
    display: flex;
    margin-left: 20px;
  }
  .el-button {
    display: inline-block;
    height: 40px;
    width: 40px;
    margin-right: 20px;
  }
  .borkenLine {
    width: 100%;
    height: 300px;
  }
  .borkenLines {
    width: 100%;
    height: 100%;
  }
  .demo-form-inline {
    width: 85%;
    margin: 0 auto;
    height: 40px;
  }
  .el-form-item__label {
    color: black;
    font-size: 12px;
  }
  .Box-enter-active,
  .Box-leave-active {
    transition: all 2s;
  }
  .Box-enter,
  .Box-leave-to {
    width: 0px;
    height: 0px;
  }
}
.unshow-line-card {
  border: 0px;
  height: 100%;
  background: #f5f9fa;
  .center {
    position: relative;
  }
  .card-title {
    width: 100%;
    display: flex;
    align-items: center;
  }
  #titleTop {
    position: absolute;
    top: 50%;
    right: 50%;
    transform: translate(50%, -50%);
    margin: 0 auto;
    font-size: 16px;
    text-align: center;
  }

  .veRing {
    margin: 0 auto;
  }
  .top {
    margin-left: 20px;
    width: 60%;
    display: flex;
    justify-content: flex-start;
  }
  .el-button {
    display: inline-block;
    height: 40px;
    width: 40px;
    margin-right: 20px;
  }
  .borkenLine {
    width: 100%;
    height: 300px;
  }
  .borkenLines {
    width: 100%;
    height: 100%;
  }
  .demo-form-inline {
    width: 85%;
    margin: 0 auto;
    height: 40px;
  }
  .el-form-item__label {
    color: black;
    font-size: 12px;
  }
  .Box-enter-active,
  .Box-leave-active {
    transition: all 2s;
  }
  .Box-enter,
  .Box-leave-to {
    width: 0px;
    height: 0px;
  }
}
</style>
