import request from '@/utils/request'


// 获取配置信息
export function getConfigInfo() {
  return request({
    url: '/visitor/visitorParameterConfig/list',
    method: 'get'
  })
}
// 获取配置信息--不含图片
export function queryConfig() {
  return request({
    url: '/visitor/visitorParameterConfig/queryConfig',
    method: 'get'
  })
}
//保存配置信息
export function saveConfigInfo(data) {
  return request({
    url: '/visitor/visitorParameterConfig/save',
    method: 'post',
    data: data
  })

}

export function getUUid() {
  return request({
    url: "/visitor/visitorParameterConfig/getUUid",
    method: 'get',
  })
}

export function removeImage(id) {
  return request({
    url: "/visitor/visitorParameterConfig/removeImage/" + id,
    method: 'post',
  })
}
