<template>
  <div class="app-container">
    <el-card shadow="never">
      <!--    查询-->
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
        <el-form-item label="系统入驻名" prop="clientApp">
          <el-input v-model="queryParams.clientApp" placeholder="请输入系统入驻名" clearable style="width: 180px"
                    @keyup.enter="handleQuery"/>
        </el-form-item>

        <el-form-item label="系统负责单位" prop="clientUnit">
          <el-input v-model="queryParams.clientUnit" placeholder="请输入系统负责单位" clearable style="width: 180px"
                    @keyup.enter="handleQuery">
          </el-input>
        </el-form-item>
        <el-form-item label="联系人" prop="clientContact">
          <el-input v-model="queryParams.clientContact" placeholder="请输入联系人" clearable style="width: 180px"
                    @keyup.enter="handleQuery">
          </el-input>
        </el-form-item>

        <el-form-item label="联系人电话" prop="clientTel">
          <el-input v-model="queryParams.clientTel" placeholder="请输入联系人电话" clearable style="width: 180px"
                    @keyup.enter="handleQuery">
          </el-input>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!--  新增/刷新按钮-->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="Plus" @click="handleAdd"
                     v-hasPermi="['sys:tenant:oauth-client-info:add']">新增
          </el-button>
        </el-col>
        <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>
      <!--  展示-->
      <el-table v-loading="loading" :data="dataList">
        <el-table-column prop="clientApp" align="left" label="系统入驻名" :show-overflow-tooltip="true" ></el-table-column>
        <el-table-column prop="clientKey" align="left"  label="系统入驻Key" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column prop="clientUnit" align="left"  label="系统负责单位" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column prop="clientContact" align="left"  label="联系人" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column prop="clientTel" align="left"  label="联系人电话" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column prop="clientRemark" align="left"  label="备注" :show-overflow-tooltip="true"></el-table-column>

        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button link type="primary" icon="View" @click="handleGetSecret(scope.row)"
                       :loading="reloadId === scope.row.clientId && reloadType === 'getSecret'"
                       v-hasPermi="['sys:tenant:oauth-client-info:view']">查看密钥
            </el-button>
            <el-button link type="primary" icon="Edit" @click="handleDebug(scope.row)"
                       :loading="reloadId === scope.row.clientId && reloadType === 'debug'"
                       v-hasPermi="['sys:tenant:oauth-client-info:debug']">调试
            </el-button>
            <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
                       :loading="reloadId === scope.row.clientId && reloadType === 'delete'"
                       v-hasPermi="['sys:tenant:oauth-client-info:remove']">删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!--    分页器-->
      <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
      />

      <!-- 新增或编辑对话框 -->
      <el-dialog
          :title="title"
          v-model="open"
          width="1000px"
          append-to-body
          @close="cancel"
          :close-on-press-escape="false"
      >
        <el-form ref="formRef" :model="form" :rules="rules" label-width="150px">
          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="系统入驻名" prop="clientApp">
                <el-input
                    v-model="form.clientApp"
                    placeholder="系统入驻名"
                    maxlength="64"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="系统负责单位" prop="clientUnit">
                <el-input
                    v-model="form.clientUnit"
                    placeholder="请输入系统负责单位"
                    maxlength="64"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="联系人" prop="clientContact">
                <el-input
                    v-model="form.clientContact"
                    placeholder="请输入联系人"
                    maxlength="64"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="联系人电话" prop="clientTel">
                <el-input
                    v-model="form.clientTel"
                    placeholder="请输入联系人电话"
                    maxlength="64"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="系统回调地址" prop="clientRedirect">
                <el-input
                    v-model="form.clientRedirect"
                    placeholder="请输入系统回调地址"
                    maxlength="64"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="联系人邮箱" prop="clientEmail">
                <el-input
                    v-model="form.clientEmail"
                    placeholder="联系人邮箱将作为查看密钥的凭证,请妥善输入"
                    maxlength="64"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="权限标识" prop="clientRemark">
                <el-input
                    v-model="form.clientRemark"
                    placeholder="请输入权限标识"
                    maxlength="64"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="userType==='admin'">
              <el-form-item label="租户" >
                <el-select
                    v-model="form.tenantId"
                    placeholder="请选择租户"
                    remote
                    style="width: 240px"
                    :remote-method="initTenantList"
                    :loading="getTenantLoading"
                >
                  <el-option
                      v-for="item in tenantList"
                      :key="item.tenantId"
                      :label="item.tenantName"
                      :value="item.tenantId"
                  />

                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForm" :loading="saveLoading">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </div>
        </template>

      </el-dialog>

<!--      查看密钥对话框-->
      <el-dialog
          title="查看密钥"
          v-model="openGetSecret"
          width="600px"
          append-to-body
          @close="cancelGetSecret"
          :close-on-press-escape="false"
      >
        <el-form ref="secretFormRef" :model="secretForm" :rules="rulesGetSecret" label-width="150px">
          <el-row :gutter="24">
            <el-col :span="22">
              <el-form-item label="联系人邮箱" prop="clientEmail">
                <el-input
                    v-model="secretForm.clientEmail"
                    placeholder="请输入当前入驻系统的联系人邮箱"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitFormGetSecret" :loading="saveLoading">确 定</el-button>
            <el-button @click="cancelGetSecret">取 消</el-button>
          </div>
        </template>
      </el-dialog>

      <!-- 单点登录调试对话框 -->
      <el-dialog
          title="单点登录调试"
          v-model="openDebug"
          width="700px"
          append-to-body
          @close="cancelDebug"
          :close-on-press-escape="false"
      >
        <el-form ref="debugFormRef" :model="debugForm"  label-width="150px">
<!--          第一步-->
          <el-row :gutter="24">
            <el-col :span="24">
              <p style="font-weight: bold;color: red;">一、根据当前登录用户动态生成 mmy 参数;</p>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="18">
              <el-form-item label="跳转参数 [mmy]" prop="mmy">
                <el-input
                    v-model="debugForm.mmy"
                    type="textarea"
                    width="800px"
                    placeholder="点击右侧按钮,系统会根据当前登录用户的登录账号,动态生成跳转参数 [mmy]"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-button type="primary" plain @click="createMmy" :loading="createMmyLoading">点我生成参数</el-button>
            </el-col>
          </el-row>
          <!-- 第二步 -->
          <el-row :gutter="24">
            <el-col :span="24">
              <p style="font-weight: bold;color: red;">二、输入接入系统的跳转地址;</p>
              <p style="color: darkgray;margin-left: 28px;">门户应用打开接入系统的跳转地址时,会默认携带 [mmy] 参数进行跳转</p>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="18">
              <el-form-item label="跳转地址" prop="clientUrl">
                <el-input
                    v-model="debugForm.clientUrl"
                    type="textarea"
                    width="800px"
                    placeholder="输入目标系统的跳转地址"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-button type="primary" plain @click="createClientUrl">拼接跳转参数</el-button>
            </el-col>
          </el-row>
          <!-- 第三步 -->
          <!-- 第三步 - (1) -->
          <el-row :gutter="24">
            <el-col :span="24">
              <p style="font-weight: bold;color: red;">三、此时接入目标系统会解析 [mmy] ,并请求门户认证中心接口,获取授权码 [code];</p>
              <p style="color: darkgray;margin-left: 28px;">(1) 此处我们模拟接入系统,向门户认证中心 [获取授权码接口] 发起请求,首先填入接入系统对应参数</p>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="10">
              <el-form-item label="系统入驻Key" prop="clientKey">
                <el-input
                    v-model="debugForm.clientKey"
                    placeholder="请输入系统入驻Key"
                    style="width: 180px;"
                />
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item label="重定向地址" prop="clientRedirect">
                <el-input
                    v-model="debugForm.clientRedirect"
                    placeholder="请输入重定向地址"
                    style="width: 180px;"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="18">
              <el-form-item label="跳转参数 [mmy]" prop="mmySource">
                <el-input
                    v-model="debugForm.mmySource"
                    type="textarea"
                    width="800px"
                    placeholder="可使用 [第一步] 操作生成"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <!-- 第三步 - (2) -->
          <el-row :gutter="24">
            <el-col :span="24">
              <p style="color: darkgray;margin-left: 28px;">(2) 根据上方的配置,生成 [获取授权码接口] 的接口所需要的参数</p>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="18">
              <el-form-item label="接口所需参数" prop="decideParams">
                <el-input
                    v-model="debugForm.decideParams"
                    type="textarea"
                    width="800px"
                    placeholder="点击右侧按钮,系统会根据上面输入的系统参数,生成 [获取授权码接口] 所需要的请求参数"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-button type="primary" plain @click="createDecideParams" :loading="createDecideParamsLoading">生成请求参数</el-button>
            </el-col>
          </el-row>
          <!-- 第三步 - (3) -->
          <el-row :gutter="24">
            <el-col :span="24">
              <p style="color: darkgray;margin-left: 28px;">(3) 拼接上第(2)步生成的请求参数,调用 [获取授权码] 接口</p>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="18">
              <el-form-item label="[获取授权码] URL" prop="decideApiUrl">
                <el-input
                    v-model="debugForm.decideApiUrl"
                    type="textarea"
                    width="800px"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="18">
              <el-form-item label="[获取授权码] 响应" prop="code">
                <el-input
                    v-model="debugForm.code"
                    type="textarea"
                    width="800px"
                    placeholder="点击右侧按钮,调用 [获取授权码] 接口,获取响应结果,解析 [code] 授权码参数"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-button type="primary" plain @click="getDecideCode" :loading="getDecideCodeLoading">调用接口</el-button>
            </el-col>
          </el-row>
          <!-- 第三步 - (4) -->
          <el-row :gutter="24">
            <el-col :span="24">
              <p style="color: darkgray;margin-left: 28px;">(4) 使用第(3)步获取的授权码,调用 [获取令牌] 接口</p>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="10">
              <el-form-item label="授权码 [code]" prop="code">
                <el-input
                    v-model="debugForm.code"
                    placeholder="请输入授权码"
                    style="width: 180px;"
                />
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item label="[client_id]" prop="clientKey">
                <el-input
                    v-model="debugForm.clientKey"
                    placeholder="请输入系统入驻Key"
                    style="width: 180px;"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="10">
              <el-form-item label="[client_secret]" prop="clientSecret">
                <el-input
                    v-model="debugForm.clientSecret"
                    placeholder="请输入系统入驻密钥"
                    style="width: 180px;"
                />
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item label="[redirect_uri]" prop="clientRedirect">
                <el-input
                    v-model="debugForm.clientRedirect"
                    placeholder="请输入重定向地址"
                    style="width: 180px;"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="18">
              <el-form-item label="[获取令牌] URL" prop="checkTokenApiUrl">
                <el-input
                    v-model="debugForm.checkTokenApiUrl"
                    type="textarea"
                    width="800px"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="18">
              <el-form-item label="[获取令牌] 响应" prop="checkTokenResponse">
                <el-input
                    v-model="debugForm.checkTokenResponse"
                    type="textarea"
                    width="800px"
                    placeholder="点击右侧按钮,调用 [获取令牌] 接口,获取响应结果,解析 [token] 获取令牌"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-button type="primary" plain @click="getCheckToken" :loading="getCheckTokenLoading">调用接口</el-button>
            </el-col>
          </el-row>
          <!-- 第四步 -->
          <el-row :gutter="24">
            <el-col :span="24">
              <p style="font-weight: bold;color: red;">四、至此,我们便获取到了门户认证中心统一令牌 [token] ;</p>
              <p style="color: darkgray;margin-left: 28px;">当请求携带统一令牌 [token] 时,即可调用门户接口;</p>
              <p style="color: darkgray;margin-left: 28px;">建议将请求头 Cookie 中新增 unifast_token 的Key (value为统一令牌) 作为标识尝试其他接口的调用;</p>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="10">
              <el-form-item label="统一令牌 [token]" prop="token">
                <el-input
                    v-model="debugForm.token"
                    placeholder="请输入系统入驻Key"
                    style="width: 180px;"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="resetDebug" >重 置</el-button>
            <el-button @click="cancelDebug">取 消</el-button>
          </div>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup name="SystemEntry">
import {add, del, findList, getParam, getSecret} from "@/api/tenant/systemEntry";
import {getToken} from '@/utils/auth'
import {getInfo} from "@/api/login";
import ssoCrypto from "@/utils/ssoCrypto";
// import {list as tenantList} from "@/api/tenant/tenant";
import axios from "axios";
import useUserStore from "@/store/modules/user";
import {ref} from "vue";
const {proxy} = getCurrentInstance();
const userStore = useUserStore();
const customParam = userStore.userInfo.customParam
const userType = customParam.userType
// <editor-fold desc="查询列表相关">
const total = ref(0)
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  clientApp: undefined,
  clientUnit: undefined,
  clientContact: undefined,
  clientTel: undefined,
})
const showSearch = ref(true);
const dataList = ref([])
const loading = ref(true)
/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList();
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}
/** 查询列表 */
function getList() {
  loading.value = true;
  findList({
    ...queryParams.value,
  }).then(response => {
    if (response.success) {
      total.value = response.data.total
      dataList.value = response.data.records
    }
    loading.value = false;
  }).catch(() => {
    loading.value = false;
  })
}

getList()
// </editor-fold>
// <editor-fold desc="修改、查看密钥相关">
//获得租户列表
import {getTenants} from "@/api/tenant/tenant";
import {ElMessage, ElMessageBox} from "element-plus";
const tenantList = ref([]);
const getTenantLoading = ref(false);
function initTenantList(tenantName) {
  getTenantLoading.value = true;
  let query = {};
  if (tenantName !== undefined && tenantName !== "") {
    query.tenantName = tenantName;
    query.tenantId = undefined;
  } else {
    query.tenantId = queryParams.value.tenantId;
  }
  getTenants(query)
      .then((response) => {
        tenantList.value = response.data;
      })
      .finally(() => {
        getTenantLoading.value = false;
      });
}
initTenantList();

//新增/修改窗口
const saveLoading = ref(false)
const open = ref(false)
const reloadId = ref()
const reloadType = ref()
const title = ref("");
const formRef = ref();
const form = ref({});
const rules = ref({
  clientApp: [
    {required: true, message: "系统入驻名不能为空", trigger: "blur"},
    {max: 30, message: "长度需要小于 30 个字符", trigger: "blur"},
  ],
  clientUnit: [
    {required: true, message: "系统负责单位不能为空", trigger: "blur"},
    {max: 50, message: "长度需要小于 50 个字符", trigger: "blur"},
  ],
  clientContact: [
    {required: true, message: "联系人不能为空", trigger: "blur"},
    {max: 20, message: "长度需要小于 50 个字符", trigger: "blur"},
  ],
  clientRedirect: [
    {required: true, message: "系统回调地址不能为空", trigger: "blur"},
  ],
  clientTel: [
    {required: true, message: "联系人电话不能为空", trigger: "blur"},
    {pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: "请输入正确的手机号码", trigger: "blur"},
  ],
  clientEmail: [
    {required: true, message: "联系人邮箱不能为空", trigger: "blur"},
  ]
})
function handleAdd() {
  proxy.resetForm("formRef");
  open.value = true;
  title.value = "新增"
  reloadType.value = "add"
}
function submitForm(){
  formRef.value.validate((valid)=>{
    if (valid) {
      saveLoading.value=true;
      add(form.value).then((res)=>{
        if (!res.success){
          ElMessage.error(res.message);
          saveLoading.value=false;
        }else {
          ElMessage.success("保存成功")
          open.value=false;
          saveLoading.value=false;
          getList();
          ElMessageBox.confirm(
              '请妥善保管好您的系统密钥 [' + res.data + ']',
              "系统密钥",
              {
                confirmButtonText: "确定",
                type: "warning",
              }

          ).catch(()=>{

          })
        }
      })
    }
  })
}
// 数据行上锁
function setLoad(id, type) {
  reloadId.value= id;
  reloadType.value = type;
}
// 数据行锁重置
function resetLoad() {
  reloadId.value = undefined;
  reloadType.value = undefined;
}
function cancel() {
  proxy.resetForm("formRef");
  resetLoad();
  open.value = false;
}
//查看密钥
const openGetSecret=ref(false)
const secretForm=ref({})
const secretFormRef=ref()
const rulesGetSecret=ref({
  clientEmail: [
    {required: true, message: "联系人邮箱不能为空", trigger: "blur"},
  ],
})
function handleGetSecret(row) {
  proxy.resetForm("secretFormRef");
  openGetSecret.value=true;
  reloadType.value = "getSecret"
  secretForm.value.clientId = row.clientId;
}
// 取消查看密钥
function cancelGetSecret() {
  proxy.resetForm("secretFormRef");
  resetLoad();
  openGetSecret.value = false;
}

//验证查看密钥
function submitFormGetSecret() {
  secretFormRef.value.validate((valid)=>{
    if (valid){
      saveLoading.value=true;
      getSecret({
        clientId: secretForm.value.clientId,
        clientEmail: secretForm.value.clientEmail,
      }).then((res)=>{

        if (!res.success){
          ElMessage.error(res.message)
        }else{
          openGetSecret.value=false;
          getList();
          ElMessageBox.confirm(
              '请妥善保管好您的系统密钥 [' + res.data + ']',
              "系统密钥",
              {
                confirmButtonText: "确定",
                type: "warning",
              }

          ).catch(()=>{

          })
        }
        saveLoading.value=false
      })
    }
  })
}
//删除
function handleDelete(row) {
  setLoad(row.clientId,"delete")
  ElMessageBox.confirm(
      '是否确认删除"' + row.clientApp + '"的数据项?',
      "警告",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }
  ).then(function () {
    return del({"clientId": row.clientId});
  }).then(() => {
    resetLoad();
    getList();
    ElMessage.success('删除成功');
  }).catch(() => {
    resetLoad();
  });
}

// </editor-fold>

// <editor-fold desc="单点登录调试">
const createMmyLoading=ref(false)
const createDecideParamsLoading=ref(false)
const getDecideCodeLoading=ref(false)
const getCheckTokenLoading=ref(false)
const decideApiUrlRaw=ref("")
const openDebug=ref(false)
const debugForm=ref({})
const debugFormRef=ref()


// [USUALLY]
// @param variable : 期望获取的路径参数Key;
// @param url : 进行操作的url地址,不传递时默认为当前页面路径;
// @describe 获取当前页面路径上传入的路径参数值.
function getQueryVariable (variable, url) {
  let query="";
  if (!isNull(url)){
    if (url.split("?")[1]) {
      query = url.split('?')[1];
    } else {
      return false;
    }
  }else {
    query = window.location.search.substring(1);
  }
  let vars = query.split("&");
  for (let i = 0; i < vars.length; i++) {
    let pair = vars[i].split("=");
    if (pair[0] === variable) {
      return pair[1];
    }
  }
  return false;
}

// [OFTEN]
// @param key : 期望新增或更新的路径参数Key;
// @param value : 期望新增或更新的路径参数Value;
// @param url : 进行操作的url地址,不传递时默认为当前页面路径;
// @describe 将入参 url 中路径参数 key 对应的值替换为 value,没有参数则添加此参数,返回替换后的url字符串.
function addOrUpdateQueryVariable(key, value, url){
  let query="";
  if (!isNull(url)) {
    if (url.split("?")[1]) {
      query = url.split('?')[1];
    }
  } else {
    url = window.location.href;
    query = window.location.search.substring(1);
  }
  let vars = query.split("&");
  for (let i = 0; i < vars.length; i++) {
    if (vars[i] !== "") {
      let pair = vars[i].split("=");
      if (pair[0] === key) {
        let target = pair[0] + "=" + pair[1];
        return url.replace(target, pair[0] + "=" + value);
      }
    }
  }
  let param = key + "=" + value;
  return url.indexOf("?") !== -1 ? url + "&" + param : url + "?" + param;
}

// [OFTEN]
// @param val : 判断对象;
// @describe 根据入参对象的类型判断是否为逻辑意义上的空值.
function isNull(val){
  if (typeof(val) == "undefined" || !val) {
    return true;
  }
  switch (typeof(val)) {
    case "string":
      return val.trim() === "";
    case "number":
      return false;
    case "object":
      return val.length === 0;
    default:
      return false;
  }
}
// ┏ ━ ━ ━ ━ 单点登录调试 ━ ━ ━ ━ ┓
// 重置调试
function resetDebug() {
  proxy.resetForm("debugFormRef");
}
// 取消调试
function cancelDebug() {
  proxy.resetForm("debugFormRef");
 resetLoad();
  openDebug.value = false;
}
// 开始调试
function handleDebug(row) {
  console.log("到这里啦")
  debugForm.value = row;
  let gateway = window.location.origin + import.meta.env.VUE_APP_BASE_API;
  decideApiUrlRaw.value =  gateway + "/auth/decide";
  console.log("gateway",gateway)
  console.log("dec",decideApiUrlRaw.value)
  debugForm.value.decideApiUrl=decideApiUrlRaw.value;
  debugForm.value.checkTokenApiUrl=gateway + "/auth/oauth/token"

  openDebug.value = true;
}
// 生成 mmy 参数
function createMmy() {
  createMmyLoading.value= true;
  getInfo({token: getToken()}).then((res) => {
    createMmyLoading.value = false;
    let mmy = ssoCrypto(`${res.loginName}:${res.tenantId}`);
    delete debugForm.value.mmy
    debugForm.value.mmy=mmy
    delete debugForm.value.mmySource
    debugForm.value.mmySource=mmy

  }).catch(() => {
    createMmyLoading.value = false;
    ElMessage.error('当前登录用户信息查询失败');
  });
}
// 拼接跳转参数
function createClientUrl() {
  if (debugForm.value.clientUrl) {
    let clientUrl = addOrUpdateQueryVariable("mmy", debugForm.value.mmy, debugForm.value.clientUrl);
    delete debugForm.value.clientUrl;
    debugForm.value.clientUrl=clientUrl

  } else {
    ElMessage.error("请先输入跳转地址");
  }
}
// 生成 decide 接口请求参数
function createDecideParams() {
  createDecideParamsLoading.value = true;
  getParam({
    "clientKey": debugForm.value.clientKey,
    "clientRedirect": debugForm.value.clientRedirect,
    "mmySource": debugForm.value.mmySource
  }).then((res) => {
    createDecideParamsLoading.value = false;
    delete debugForm.value.decideParams;
    debugForm.value.decideParams=res.data;

    // 将参数拼接至接口地址后面
    delete debugForm.value.decideParams;
    debugForm.value.decideApiUrl=decideApiUrlRaw.value + "?" + res.data

  }).catch(() => {
    createDecideParamsLoading.value = false;
    ElMessage.error('当前登录用户信息查询失败');
  });
}

// 调用 decide 接口
function getDecideCode() {
  getDecideCodeLoading.value = true;
  axios.get(debugForm.value.decideApiUrl,{
  }).then(function (res) {
    getDecideCodeLoading.value = false;
    if(res.request.responseURL) {
      let url =  res.request.responseURL;
      let code = getQueryVariable("code", url);
      delete debugForm.value.code;
      debugForm.value.code=code

    }
  }).catch(err => {
    getDecideCodeLoading.value = false;
    // 如果跳转地址无法访问或者跨域,将进入 catch
    ElMessageBox.confirm(
        '请确保重定向地址能正常访问!<p>若重定向地址有网络问题或跨域问题无法访问,可将生成的<b> [获取授权码] URL</b>复制到浏览器中进行查看;<p>或使用开发者工具F12查看携带<b> [code] </b>参数的重定向地址;</p></p>',
        "请求失败",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
          dangerouslyUseHTMLString: true,
        }
    ).catch(() => {});
  });
}
// 调用 check_token 接口
function getCheckToken() {
  getCheckTokenLoading.value = true;
  axios.post(debugForm.value.checkTokenApiUrl, {}, {
    params: {
      "grant_type": "authorization_code",
      "client_id": debugForm.value.clientKey,
      "client_secret": debugForm.value.clientSecret,
      "redirect_uri": debugForm.value.clientRedirect,
      "code": debugForm.value.code,
    }
  }).then(function (res) {
    getCheckTokenLoading.value = false;
    delete debugForm.value.checkTokenResponse;
    delete debugForm.value.token;
    debugForm.value.checkTokenResponse=res.data.data.value
    debugForm.value.token=res.data.data.value

  }).catch(err => {
    // 异常
    console.log(err)
    getCheckTokenLoading.value = false;
    ElMessage.error('请求失败,请确认参数是否正确');
  });
}
// </editor-fold>
</script>

<style scoped>

</style>
