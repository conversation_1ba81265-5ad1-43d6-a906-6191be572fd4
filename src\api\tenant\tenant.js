import request from "@/utils/request";

// 查询列表
export function getTenants(params) {
    return request({
        url: "/user/tenants/list",
        method: "get",
        params: params,
    });
}

// 查询新-列表
export function juniorList(params) {
    return request({
        url: "/user/tenants/juniorList",
        method: "get",
        params: params,
    });
}

// 查询分页
export function page(params) {
    return request({
        url: "/user/tenants/page",
        method: "get",
        params: params
    });
}

// 查询列表
export function list(params) {
    return request({
        url: "/user/tenants/list",
        method: "get",
        params: params
    });
}

// 根据ID查询详细
export function getById(id) {
    return request({
        url: "/user/tenants/detail/" + id,
        method: "get"
    });
}

// 根据业务ID查询详细
export function getByBusinessId(businessId) {
    return request({
        url: "/user/tenants/business/" + businessId,
        method: "get"
    });
}

// 验证租户登录名唯一性
export function checkTenantLoginName(tenantLoginName) {
    return request({
        url: "/user/tenants/checkTenantLoginName/" + tenantLoginName,
        method: "get"
    });
}

// 验证租户名唯一性
export function checkTenantName(tenantName) {
    return request({
        url: "/user/tenants/checkTenantName/" + tenantName,
        method: "get"
    });
}

// 新增
export function add(data) {
    return request({
        url: "/user/tenants/add",
        method: "post",
        data: data
    });
}

// 修改用户
export function update(data) {
    return request({
        url: "/user/tenants/update",
        method: "post",
        data: data
    });
}

// 删除
export function del(id) {
    return request({
        url: "/user/tenants/delete/" + id,
        method: "post"
    });
}

export function getHadRolesAndNoRoles(params) {
    return request({
        url: "/user/tenantRole/get",
        method: "get",
        params
    });
}

export function updateTenantRole(tenantId, roleId) {
    return request({
        url: "/user/tenantRole/update/" + tenantId + "/" + roleId,
        method: "post"
    });
}
export function delTenantRole(tenantId, roleId) {
    return request({
        url: "/user/tenantRole/delete/" + tenantId + "/" + roleId,
        method: "post"
    });
}
