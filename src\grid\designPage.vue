<template>
    <GridContainer :layout="layout" :size="size" />
</template>

<script setup>
import { ref } from 'vue'
import { useRoute } from "vue-router";
import GridContainer from "./container"
import { defaultSize } from "./config"
import { getPageDesigner } from "@/api/extend/pageDesigner";

const layout = ref([])
const size = ref(defaultSize)

const route = useRoute();

function initPage() {
    const id = route.query.id || route.meta.designId
    if (id) {
        getPageDesigner(id).then((res) => {
            if (res.success && res.data) {
                parseConfig(res.data.pageDefine)
            }
        })
    }
}

function parseConfig(configStr) {
    try {
        const config = JSON.parse(configStr)
        layout.value = config.layout || []
        size.value = config.size || defaultSize
    } catch (error) {
        layout.value = []
        size.value = defaultSize
    }
}

initPage()

</script>
<style lang="scss" scoped></style>