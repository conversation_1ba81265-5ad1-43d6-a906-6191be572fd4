import request from '@/utils/request'

export function findByIdAndAttribute(id, attribute) {
    return request({
        url: '/visitorinvite/visitorItem/find/' + id + '/' + attribute,
        method: 'get'
    })
}

export function delVisitorItemById(id) {
    return request({
        url: '/visitorinvite/visitorItem/del/' + id,
        method: 'post'
    })
}

export function insertVisitorItem(data) {
    return request({
        url: '/visitorinvite/visitorItem/insert',
        method: 'post',
        data: data
    })
}

export function updateVisitorItem(data) {
    return request({
        url: '/visitorinvite/visitorItem/update',
        method: 'post',
        data: data
    })
}