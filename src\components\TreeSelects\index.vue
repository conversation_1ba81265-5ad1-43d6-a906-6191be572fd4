<template>
  <el-select
    filterable
    :filter-method="dataFilter"
    popper-class="opperView"
    :popper-append-to-body="true"
    class="main-select-tree"
    ref="selectTreeRef"
    clearable
   v-model="selectedParkingIdsComputed"
    multiple
    collapse-tags
    placeholder="请选择停车场"
  >
    <!-- 为每个停车场生成隐藏的选项 -->
       <span   style="display: none;">
    <el-option
      v-for="parking in allParkingNodes"
      :key="parking.id"
      :label="parking.label"
      :value="parking.id"
    />
</span>
    <el-tree
      :check-strictly="true"
      :data="areaTreeData"
      :filter-node-method="filterNode"
      show-checkbox
      @check="handleNodeCheck"
      default-expand-all
      node-key="id"
      ref="areaTreeRef"
      highlight-current
      :props="{
        ...treeProps,
        disabled: (data) => data.type !== 'parking',
      }"
    />
  </el-select>
</template>

<script setup>
import { ref, watch, computed, onMounted,nextTick } from "vue";
import { ElMessage } from "element-plus";
// 接口方法
import { screenIndex } from "@/api/majorParking/Income-rules";
import { findparkingTree } from "@/api/park/record";

// 定义 props
const props = defineProps({
  popupType: {
    type: String,
    default: "edit",
  },
  parkingId: {
    type: [String, Number, Array],
    required: true,
  },
  parkingName: {
    type: String,
    required: false,
  },
  isRecord: {
    type: Boolean,
    default: false,
  },
});
console.log(props.parkingId)
console.log(props.parkingName)
// 定义 emits
const emit = defineEmits(["update:parkingId", "update:parkingName"]);

// 树形数据
const areaTreeData = ref([]);

// 组件引用
const selectTreeRef = ref(null);
const areaTreeRef = ref(null);

// 当前选中的停车场ID
const selectedParkingIds = ref(
  props.parkingId
    ? Array.isArray(props.parkingId)
      ? props.parkingId
      : props.parkingId.split(",")
    : []
);

// 存储所有停车场节点（用于生成选项）
const allParkingNodes = ref([]);

// 计算属性：用于 v-model 绑定 parkingId
const selectedParkingIdsComputed = computed({
  get: () => selectedParkingIds.value,
  set: (value) => {
    selectedParkingIds.value = value;
    emit("update:parkingId", value.join(","));
  },
});

// 初始化加载树数据
watchEffect(async () => {
  try {
    const res = await (props.isRecord
      ? findparkingTree()
      : screenIndex.findparkinglotTree());
    areaTreeData.value = res.data;

    // 提取所有停车场节点
    extractParkingNodes(res.data);
  const checkSelected = () => {
      if (selectedParkingIds.value.length > 0 && areaTreeRef.value) {
        areaTreeRef.value.setCheckedKeys(selectedParkingIds.value);
      } else {
        // 如果还未准备好，延迟检查
        setTimeout(checkSelected, 50);
      }
    };
    
    checkSelected();
  } catch (error) {
    console.error("加载树数据失败:", error);
    ElMessage.error("加载树数据失败");
  }
});
// 添加 watch 监控 props 变化
watch(() => props.parkingId, (newVal) => {
  console.log('parkingId changed:', newVal);
  selectedParkingIds.value = newVal 
    ? Array.isArray(newVal) 
      ? newVal 
      : newVal.split(",") 
    : [];
}, { immediate: true });

watch(() => props.parkingName, (newVal) => {
  console.log('parkingName changed:', newVal);
}, { immediate: true });
// 递归提取所有停车场节点
const extractParkingNodes = (nodes) => {
  allParkingNodes.value = [];
  const traverse = (nodes) => {
    nodes.forEach((node) => {
      if (node.type === "parking") {
        allParkingNodes.value.push(node);
      }
      if (node.children && node.children.length > 0) {
        traverse(node.children);
      }
    });
  };
  traverse(nodes);
};

// 过滤方法
const dataFilter = (val) => {
  if (val && areaTreeRef.value) {
    areaTreeRef.value.filter(val);
  }
};

const filterNode = (value, data) => {
  if (!value) return true;
  return data.label.indexOf(value) !== -1;
};

// 节点选中处理
const handleNodeCheck = (data, checked) => {
  // 获取当前所有选中的节点
  const checkedNodes = areaTreeRef.value.getCheckedNodes();

  // 过滤出停车场节点
  const parkingNodes = checkedNodes.filter((node) => node.type === "parking");

  // 更新选中的停车场ID
  selectedParkingIds.value = parkingNodes.map((node) => node.id);

  // 触发更新事件
  emit("update:parkingId", selectedParkingIds.value.join(","));
  emit("update:parkingName", parkingNodes.map((node) => node.label).join("，"));

  // 关闭下拉框
  // selectTreeRef.value?.blur();
};
</script>

<style scoped>
.main-select-tree {
  width: 100%;
}
</style>