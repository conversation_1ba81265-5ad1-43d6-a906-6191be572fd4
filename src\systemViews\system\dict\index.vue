<template>
  <div class="app-container">
    <el-card>
      <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          v-show="showSearch"
          label-width="68px">
        <el-form-item label="字典名称" prop="dictName">
          <el-input v-model="queryParams.dictName"
                    placeholder="请输入字典名称"
                    clearable style="width: 240px"
                    @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="字典类型" prop="dictCode">
          <el-input v-model="queryParams.dictCode"
                    placeholder="请输入字典类型" clearable style="width: 240px"
                    @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="10">
          <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['sys:base:dict:add']">新增
          </el-button>
        </el-col>
        <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table @row-click="goRow" v-loading="loading" :data="typeList">
        <el-table-column label="字典名称" align="left" prop="dictName" :show-overflow-tooltip="true"/>
        <el-table-column label="字典类型" align="left" :show-overflow-tooltip="true">
          <template #default="scope">
            <router-link :to="{path:'/system/base/dictData',query:{dictId:scope.row.dictId}}" class="link-type">
              <span>{{ scope.row.dictCode }}</span>
            </router-link>
          </template>
        </el-table-column>
        <el-table-column label="备注" align="center" prop="dictDesc" :show-overflow-tooltip="true"/>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                       v-hasPermi="['sys:base:dict:edit']">修改
            </el-button>
            <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
                       v-hasPermi="['sys:base:dict:remove']">删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
      />
    </el-card>

    <!-- 添加或修改字典类型对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="字典名称" prop="dictName">
          <el-input v-model="form.dictName" placeholder="请输入字典名称"/>
        </el-form-item>
        <el-form-item label="字典类型" prop="dictCode">
          <el-input v-model="form.dictCode" placeholder="请输入字典类型"/>
        </el-form-item>
        <el-form-item label="备注" prop="dictDesc">
          <el-input v-model="form.dictDesc" type="textarea" placeholder="请输入内容"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm" :loading="saveLoading">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Dict">
import {
  listType,
  getType,
  delType,
  addType,
  updateType,
  exportType,
  refreshCache,
} from "@/api/system/dict/type";
import {ref} from "vue";
import {useRouter} from "vue-router";


const {proxy} = getCurrentInstance();
const router = useRouter()
// 遮罩层
const loading = ref(true);
const saveLoading = ref(false);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 字典表格数据
const typeList = ref([]);
// 弹出层标题
const title = ref("");
// 是否显示弹出层
const open = ref(false);
// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  dictName: undefined,
  dictCode: undefined,
  status: undefined,
});
// 表单参数
const form = ref({});
// 表单校验
const rules = {
  dictName: [
    {required: true, message: "字典名称不能为空", trigger: "blur"},
  ],
  dictCode: [
    {required: true, message: "字典类型不能为空", trigger: "blur"},
  ],
};

function goRow(row, column) {
  if (column.label !== "操作") {
    router.push({
      path: "/system/base/dictData", query: {
        dictId: row.dictId
      }
    });
  }
}

/** 查询字典类型列表 */
function getList() {
  loading.value = true;
  listType(queryParams.value).then(
      (response) => {
        typeList.value = response.data.records;
        total.value = response.data.total;
        loading.value = false;
      }
  );
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    dictId: undefined,
    dictName: undefined,
    dictCode: undefined,
    remark: undefined,
  };
  proxy.resetForm("formRef");
  saveLoading.value = false;
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryForm");
  handleQuery();
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加字典类型";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const dictId = row.dictId;
  getType(dictId).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "修改字典类型";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      saveLoading.value = true;
      if (form.value.dictId != undefined) {
        updateType(form.value).then((response) => {
          saveLoading.value = false;
          if (response.success) {
            proxy.$modal.msgSuccess("修改成功");
            open.value = false;
            getList();
          } else {
            proxy.$modal.msgError(response.message);
          }
        });
      } else {
        addType(form.value).then((response) => {
          saveLoading.value = false;
          if (response.success) {
            proxy.$modal.msgSuccess("新增成功");
            open.value = false;
            getList();
          } else {
            proxy.$modal.msgError(response.message);
          }
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$modal.confirm(
      '是否确认删除字典名称为"' + row.dictName + '"的数据项?').then(() => {
    return delType({
      dictId: row.dictId,
    }).then((res) => {
      if (res.code == 1) {
        getList();
        proxy.$modal.msgSuccess("删除成功");
      } else {
        proxy.$modal.msgError(res.message);
      }
    });
  })
}

getList();
</script>

<style scoped>

</style>
