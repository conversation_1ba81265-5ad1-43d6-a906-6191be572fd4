<template>
  <div class="container-table-box">
    <el-card>
        <el-row :gutter="24">
          <el-col :span="24" :xs="24">
            <dialog-search @getList="loadData" formRowNumber="4">
              <template v-slot:formList>
                <el-form
                  :model="formData"
                  ref="queryForm"
                  :inline="true"
                  label-width="80px"
                >
                  <el-form-item label="部门名称" prop="orgName">
                    <el-input
                      v-model="formData.orgName"
                      placeholder="请输入部门名称"
                      clearable
                      @keyup.enter.native="loadData"
                    />
                  </el-form-item>
                  <el-form-item label="人员姓名" prop="staffName">
                    <el-input
                      v-model="formData.staffName"
                      placeholder="请输入人员姓名"
                      clearable
                      @keyup.enter.native="loadData"
                    />
                  </el-form-item>
                </el-form>
              </template>
              <template v-slot:searchList>
                <el-button type="primary" icon="Search" @click="loadData"
                  >搜索</el-button
                >
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              </template>
            </dialog-search>
            <el-table
              class="myTreeLazyTable"
              ref="publictable"
              v-loading="loading"
              :data="dataList"
              row-key="dataId"
              :key="tableIndex"
              lazy
              :load="loadChildren"
              :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
            >
              <el-table-column prop="dataName" label="名称" min-width="180">
                <template #default="scope">
                  <i
                    class="icon iconfont icon-ic_org"
                    v-if="scope.row.dataType == '0'"
                  ></i>
                  <i
                    class="icon iconfont icon-fl-renyuan"
                    v-if="scope.row.dataType == '1'"
                  ></i>
                  <span class="left-label" v-if="scope.row.ancestorsName">
                      <template v-for="(segment, index) in formatAncestors(scope.row.ancestorsName)">
                  <strong
                  v-if="index > 0"
                   style="color: black; font-weight: 900;  font-size: 1.2em;"
                  >/</strong>{{ segment }}
                  </template>
                    <span v-if="scope.row.dataType == '1'"><strong style="color: black; font-weight: 900; font-size: 1.2em;">/</strong>
                  <span style="color: darkgreen;font-weight: bold;">{{ scope.row.dataName }}</span>
                      <strong style="color: black; font-weight: 900; ">（</strong><span style="color: darkgreen;font-weight: bold;">{{ scope.row.mobile }}</span><strong style="color: black; font-weight: 900; ">）</strong></span>
                  </span>
                </template>
              </el-table-column>
              <el-table-column
                label="操作"
                align="left"
              width="280px"
                header-align="center"
              >
                <template #default="scope">
                  <div v-if="scope.row.dataId">
                    <el-button
                      plain
                      size="mini"
                      type="primary"
                      @click="handleStatusUpate(scope.row)"
                      >更新</el-button
                    >

                    <el-button
                      plain
                      size="mini"
                      type="primary"
                      @click="handleStatusChange(scope.row)"
                      v-if="scope.row.dataType == '0'"
                    >
                      批量同步
                    </el-button>
                    <el-button
                      plain
                      size="mini"
                      type="primary"
                      @click="syncDataToSystemFun(scope.row)"
                    >
                      同步
                    </el-button>
                  </div>
                </template>
              </el-table-column>

              <el-table-column class="lazy-line" minWidth="10">
                <template #default="scope">
                  <div class="test"></div>
                </template>
              </el-table-column>

              <el-table-column
                prop="dataSysName"
                label="名称"
                min-width="180"
                align="left"
              >
                <template #default="scope">
                  <i
                    class="icon iconfont icon-ic_org"
                    v-if="scope.row.dataType == '0'"
                  ></i>
                  <i
                    class="icon iconfont icon-fl-renyuan"
                    v-if="scope.row.dataType == '1'"
                  ></i>
                  <span class="left-label" v-if="scope.row.sysAncestorsName">
                      <template v-for="(segment, index) in formatAncestors(scope.row.sysAncestorsName)">
                  <strong
                      v-if="index > 0"
                      style="color: black; font-weight: 900; font-size: 1.2em;"
                  >/</strong>{{ segment }}
                  </template>
                    <span v-if="scope.row.dataType == '1'"><strong style="color: black; font-weight: 900; font-size: 1.2em;">/</strong>
                  <span style="color: darkgreen;font-weight: bold;">{{ scope.row.dataSysName }}</span>
                      <strong style="color: black; font-weight: 900; ">（</strong><span style="color: darkgreen;font-weight: bold;">{{ scope.row.dataSysMobile }}</span><strong style="color: black; font-weight: 900; ">）</strong></span>
                  </span>


                </template>
              </el-table-column>

              <el-table-column
                label="操作"
                width="240px"
                align="center"
                header-align="center"
              >
                <template #default="scope">
                  <el-button
                    v-if="
                      scope.row.dataStatus != '0' && scope.row.dataStatus != ''
                    "
                    size="mini"
                    type="primary"
                  >
                    {{ $formatDictLabel(scope.row.dataStatus, sync_data_status) }}
                  </el-button>

                  <el-button v-if="scope.row.dataStatus == '0'" size="mini">
                    {{ $formatDictLabel(scope.row.dataStatus, sync_data_status) }}
                  </el-button>
                  <el-button
                    plain
                    v-if="scope.row.sysId && scope.row.dataType == '1'"
                    size="mini"
                    type="primary"
                    @click="syncDataToSystemFun(scope.row)"
                  >
                    修改
                  </el-button>
                  <el-button
                    plain
                    v-if="scope.row.sysId"
                    size="mini"
                    type="primary"
                    @click="deleteDataToSystemFun(scope.row)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
      </el-row>
    </el-card>

    <DialogBox
      :visible="diaWindow.open1"
      :dialogWidth="diaWindow.dialogWidth"
      :dialogFooterBtn="diaWindow.dialogFooterBtn"
      @save="save"
      :custom-class="diaWindow.customClass"
      @cancellation="cancellation"
      @close="close"
      :dialogTitle="diaWindow.headerTitle"
    >
      <template #content>
        <!-- <userUpateAdd
          ref="userUpateAddRef"
          :rowData="diaWindow.rowData"
          :popupType="diaWindow.popupType"
          @closeBtn="cancellationRefsh"
        ></userUpateAdd> -->
        <peopleUpplate
          v-if="diaWindow.popupType == 'peopleUpplate'"
          ref="userUpateAddRef"
          :rowData="diaWindow.rowData"
          :popupType="diaWindow.popupType"
          @closeBtn="cancellationRefsh"
        >
        </peopleUpplate>

        <batch
          v-if="diaWindow.popupType == 'batch'"
          ref="batchRef"
          :rowData="diaWindow.rowData"
          :popupType="diaWindow.popupType"
          @closeBtn="cancellationRefsh2"
        >
        </batch>
      </template>
    </DialogBox>
  </div>
</template>

<script setup name="UserUpate">
import { ref, reactive, onMounted, getCurrentInstance, nextTick } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  findDeptUserTree,
  syncDeptAndUserToSystem,
  syncDataToSystem,
  deleteDataToSystem,
  syncBatchSystem,
  pullLatestData,
} from "@/api/system/config";
import userUpateAdd from "./components/add";
import peopleUpplate from "./components/peopleUpplate";
import batch from "./components/batch";
const { proxy } = getCurrentInstance();
const { sync_data_status } = proxy.useDict("sync_data_status");
const loading = ref(false);
const dataList = ref([]);
// 表单数据
const formData = reactive({
  dataType: "0",
  staffName: "",
  orgName: "",
});
const isChangeTable = ref(false)
const publictable = ref();
const userUpateAddRef = ref();
const batchRef = ref();
const tableIndex = ref(0)
const diaWindow = reactive({
  headerTitle: "",
  popupType: "edit",
  rowData: "",
  dialogWidth: "70%",
  open1: false,
  customClass: "",
  dialogFooterBtn: true,
});
// 接口调用方法
const findDeptUserTrees = async (params) => {
  try {
    loading.value = true; // 开启加载状态
    const response = await findDeptUserTree(params); // 调用接口
    if (response.data && !response.data.children) {
      response.data.children = []; // 初始化 children 字段
    }
    return response.data; // 返回处理后的数据
  } catch (error) {
    console.error("获取部门用户树失败:", error);
    // ElMessage.error("获取部门用户树失败，请稍后重试");
    return []; // 返回空数组，避免页面崩溃
  } finally {
    loading.value = false; // 关闭加载状态
  }
};
const formatAncestors = (str) => {
  return str.split('/') // 将字符串按斜杠分割成数组
}
// 优化后的加载逻辑
const loadData = async (type) => {
  const data = await findDeptUserTrees(formData);

  // 初始化第一级节点结构
  dataList.value = data.map((item) => ({
    ...item,
    children: item.hasChildren ? [] : undefined, // 关键初始化
  }));

  if (type === "refresh") {
    expandDef();
  }
};

// 优化后的子节点加载方法
const loadChildren = async (row, treeNode, resolve) => {
  try {
    const data = await findDeptUserTrees({
      dataId: row.dataId,
      dataType: "0",
    });

    // 处理数据确保结构正确
    const processedData = data.map((item) => ({
      ...item,
      children: item.hasChildren ? [] : undefined, // 保持懒加载结构
    }));

    resolve(processedData);
  } catch (error) {
    console.error("加载失败:", error);
    resolve([]);
  }
};

// 编写触发点击函数
const expandDef = () => {
  const els = document.getElementsByClassName("el-table__expand-icon");
  nextTick(() => {
    // console.log('els[0]', els[0])
    els[0].click();
  });
};
const resetQuery = () => {
  formData.dataType = "0";
  formData.staffName = "";
  formData.orgName = "";
  loadData();
};

// 处理状态变更
const handleStatusChange = async (row) => {
  diaWindow.rowData = row;
  diaWindow.popupType = "batch";
  const page = {
    pageNum: 1,
    pageSize: 10,
  };
  delete row.pageSize;
  delete row.length;
  syncBatchSystem(page, row).then((response) => {
    if (response.success) {
      diaWindow.open1 = true;
      diaWindow.headerTitle = "批量同步";
      diaWindow.dialogFooterBtn = false;
      diaWindow.dialogWidth = '70%';
    }
  });

  // try {
  //   // 1. 显示确认对话框
  //   await ElMessageBox.confirm(
  //     `确认要将【${row.dataName}】及其所有子部门和人员同步至系统吗？`,
  //     "提示",
  //     { type: "warning" }
  //   );

  //   // 2. 调用实际接口
  //   const result = await syncDeptAndUserToSystem(row);
  //   if (result.success) {
  //     // 3. 提示成功
  //     ElMessage.success(result.message || "同步成功");
  //     // 4. 刷新数据
  //     await loadData();
  //   }
  // } catch (error) {
  //   // 5. 错误处理
  //   if (error !== "cancel") {
  //     // 排除用户取消的情况
  //     // ElMessage.error(error.message || "同步失败，请稍后重试");
  //   }
  // }
};

const handleStatusUpate = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确认要更新【${row.dataName}】的系统数据吗？`,
      "提示",
      { type: "warning" }
    );

    const result = await pullLatestData(row);
    if (result?.success) {
      ElMessage.success(result.message || "删除成功");

      // ✅ 核心修改点：直接更新传入的 row 对象（需确保 row 是响应式的）
      Object.assign(row, result.data); // 合并新数据到当前行
    }
  } catch (error) {
    if (error !== "cancel") {
      const errMsg =
        error.response?.data?.message || error.message || "删除失败";
      // ElMessage.error(errMsg);
    }
  }
};
const deleteDataToSystemFun = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确认要删除【${row.dataName}】的系统数据吗？`,
      "提示",
      { type: "warning" }
    );

    const result = await deleteDataToSystem(row);
    if (result?.success) {
      ElMessage.success(result.message || "删除成功");

      // ✅ 核心修改点：直接更新传入的 row 对象（需确保 row 是响应式的）
      Object.assign(row, result.data); // 合并新数据到当前行
    }
  } catch (error) {
    if (error !== "cancel") {
      const errMsg =
        error.response?.data?.message || error.message || "删除失败";
      ElMessage.error(errMsg);
    }
  }
};

const syncDataToSystemFun = async (row) => {
  if (row.dataType == "1") {
    diaWindow.rowData = row;
    diaWindow.popupType = "peopleUpplate";
    diaWindow.open1 = true;
    diaWindow.headerTitle = "同步";
    diaWindow.dialogFooterBtn = true;
    diaWindow.dialogWidth = '40%'
    return;
  }
  try {
    await ElMessageBox.confirm(`确认要同步【${row.dataName}】吗？`, "提示", {
      type: "warning",
    });

    const result = await syncDataToSystem(row);
    if (result?.success) {
      ElMessage.success(result.message || "同步成功");

      // ✅ 核心修改点：直接更新传入的 row 对象（需确保 row 是响应式的）
      Object.assign(row, result.data); // 合并新数据到当前行
    }
  } catch (error) {
    if (error !== "cancel") {
      const errMsg =
        error.response?.data?.message || error.message || "同步失败";
      ElMessage.error(errMsg);
    }
  }
};
/** 点击确定保存 */
const save = () => {
  userUpateAddRef.value.saveForm();
};
/** 点击确定后刷新 */
const cancellationRefsh = async (data) => {
  try {
    await ElMessageBox.confirm(
      `确认要同步【${diaWindow.rowData.dataName}】吗？`,
      "提示",
      {
        type: "warning",
      }
    );

    // 合并数据
    const mergedData = data;

    const result = await syncDataToSystem(mergedData.value);
    if (result?.success) {
      ElMessage.success(result.message || "同步成功");
      // ✅ 核心修改点：直接更新传入的 row 对象（需确保 row 是响应式的）
      Object.assign(diaWindow.rowData, result.data); // 合并新数据到当前行
      close(false);
    }
  } catch (error) {
    if (error !== "cancel") {
      const errMsg =
        error.response?.data?.message || error.message || "同步失败";
      ElMessage.error(errMsg);
    }
  }
};

// 保持其他方法不变
const cancellationRefsh2 = async (data) => {
  isChangeTable.value = true
};
/** 点击取消保存 */
const cancellation = (val) => {
  close(false);
};

/** 关闭弹窗 */
const close = async (val) => {
  if (diaWindow.popupType == "batch" && isChangeTable.value) {
    tableIndex.value++
    isChangeTable.value = false
    await loadData("refresh");
  }
  diaWindow.open1 = val;
};

onMounted(() => {
  loadData();
});
</script>

<style scoped lang="scss">
:deep(.myTreeLazyTable) {
  box-shadow: 0 10px 12px rgba(0, 0, 0, 0.1);
}
:deep(.myTreeLazyTable tr .el-table__cell:nth-child(3)) {
  position: relative;
 border: none;
}

:deep(.myTreeLazyTable tr .el-table__cell:nth-child(3)::after) {
  content: "";
  display: block;
  height: 100%;
  width: 15px;
  background: #fff;
  position: absolute;
  left: 0px;
  top: 50%;
  transform: translate(0, -50%);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

:deep(.myTreeLazyTable tr .el-table__cell:nth-child(3)) {
  padding-left: 24px;
}
// .test{
//   display: block;
//   height: 100%;
//   width: 20px;
//   background: #fff;
//   position: absolute;
//   left: 0px;
//   top: 50%;
//   transform: translate(0, -50%);
//   box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
// }
.icon-ic_org {
  // color: #c20000;
  padding: 2px;
  font-size: 14px;
}
.left-label {
  word-break: break-all;
}
</style>
