<!-- 开通账号 -->
<template>
  <div class="dialog-box dialog-box-edit" style="height: 600px">
    <el-row style="height: 100%">
      <Splitpanes class="default-theme">
        <Pane :size="30" :min-size="10">
          <el-card class="dep-card" style="height: 100%">
            <el-scrollbar style="height: 100%">
              <div class="tenant-section">
                <div class="section-title">请选择账户:</div>
                <el-form-item label="租户" prop="tenantId" class="tenant-select-item">
                  <TenantSelect v-model="queryTreeParams.tenantId" @change="handleChange"></TenantSelect>
                </el-form-item>
              </div>
              <el-form ref="form" label-width="0px">
                <el-form-item>
                  <el-tree
                    style="margin:20px 0px;width:100%"
                    :check-strictly="true"
                    :data="areaOptions"
                    show-checkbox
                    default-expand-all
                    node-key="id"
                    ref="areaTree"
                    highlight-current
                    :props="treeProps"
                    @check="handleTreeCheck"
                  />
                </el-form-item>
              </el-form>
            </el-scrollbar>
          </el-card>
        </Pane>

        <Pane :size="70" :min-size="62">
          <el-card class="dep-card" style="height: 100%">
            <el-form ref="ruleform" :model="formData">
              <el-row>
                <el-col :span="12">
                  <el-form-item label="人员" prop="staffId">
                    <el-select
                      style="width: 300px"
                      collapse-tags
                      placeholder="请输入人员进行选择"
                      clearable
                      @change="changeUser"
                      filterable
                      remote
                      reserve-keyword
                      :remote-method="getUserList"
                    >
                      <el-option
                        v-for="(item, index) in applyUserList"
                        :key="index"
                        :label="item.staffName"
                        :value="item.staffId"
                      >
                        <div>
                          {{
                            item.staffName +
                            "(" +
                            item.orgName +
                            "/" +
                            item.loginName +
                            ")"
                          }}
                        </div>
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>

              </el-row>
            </el-form>
            <public-table
              ref="publictable"
              :rowKey="tabelForm.tableKey"
              :tableData="userInfo"
              :columns="tabelForm.columns"
              :configFlag="tabelForm.tableConfig"
            >
              <template #operation="{ scope }">
                <el-button
                  size="mini"
                  type="text"
                  title="删除"
                  icon="Delete"
                  @click="handleDelete(scope.row)"
                >
                </el-button>
              </template>
            </public-table>
          </el-card>
        </Pane>
      </Splitpanes>
    </el-row>

    <DialogBox
      :visible="diaWindow.open1"
      :dialogWidth="diaWindow.dialogWidth"
      :dialogFooterBtn="diaWindow.dialogFooterBtn"
      @save="save"
      @cancellation="cancellation"
      @close="close"
      :dialogTitle="diaWindow.headerTitle"
    >
      <template #content>
        <UploadFile
          ref="uploadRef"
          :uploadData="extraUploadFileData"
          :action="upload.url"
          :limit="1"
          :accept="'.xlsx, .xls'"
          :disabled="upload.isUploading"
          :auto-upload="false"
          tip="提示：仅允许导入“xls”或“xlsx”格式文件！<br>"
          @file-success="handleFileSuccess"
          @file-error="handleFileError"
        />
      </template>
    </DialogBox>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessageBox, ElMessage } from "element-plus";
import { screenIndex } from "@/api/paymentCenter/account-list/index";
import UploadFile from "@/components/UploadFile/index";

import { apiUrl } from "@/utils/config";
import useUserStore from "@/store/modules/user";
const userStore = useUserStore();

const emit = defineEmits(["closeBtn"]);
const props = defineProps({
  closeBtn: {
    type: Function,
    default: () => {},
  },
  popupType: {
    type: String,
    default: "",
  },
  rowData: {
    type: Object,
    default: () => ({}),
  },
});
const { proxy } = getCurrentInstance();
const {  staff_type,  } = proxy.useDict(
  "staff_type",
);
// 响应式数据
const applyUserList = ref([]);
let userInfo = ref([]);

const areaOptions = ref([]);
const areaTree = ref(null);
const treeProps = reactive({
  multiple: false,
  emitPath: false,
  value: "id",
  label: "accountName",
});

const extraUploadFileData = ref({
  validDate: "",
});
// 用户导入参数
const upload = reactive({
  title: "",
  isUploading: false,
  updateSupport: 0,
  url: `${
    import.meta.env.VITE_APP_BASE_API
  }/park${apiUrl}/majorParking/userCar/importData`,
});
const diaWindow = reactive({
  open1: false,
  headerTitle: "",
  popupType: "",
  rowData: "",
  dialogWidth: "30%",
  dialogFooterBtn: false,
  customClass: "my_height_1",
});
const queryTreeParams = ref({
  tenantId: userStore.userInfo.tenantId,
});
const formData = reactive({});

const tabelForm = reactive({
  tableKey: 1, //表格key值
  isShowRightToolbar: true, //是否显示右侧显示和隐藏
  showSearch: true,
  // 表格表格数据
  columns: [
    {
      fieldIndex: "staffName",
      label: "员工姓名",
      show: true,
      sortable: true,
      visible: true, // 展示与隐藏
      minWidth: "120px",
    },
    {
      fieldIndex: "loginName",
      label: "HR编码",
      show: true,
      sortable: true,
      visible: true, // 展示与隐藏
      minWidth: "120px",
    },
    {
      fieldIndex: "cellphone",
      label: "联系方式",
      show: true,
      sortable: true,
      visible: true, // 展示与隐藏
      minWidth: "120px",
    },

    {
      fieldIndex: "orgName",
      label: "员工部门",
      show: true,
      sortable: true,
      visible: true, // 展示与隐藏
      minWidth: "150px",
      align:'left'
    },

    {
      fieldIndex: "staffType",
      label: "人员类型",
      show: true,
      visible: true, // 展示与隐藏
      sortable: true,
      minWidth: "120px",
      type:'dict',
      dictList:staff_type
    },
    // {
    //     fieldIndex: "loginName",
    //   label: "账号名称",
    //   show: true,
    //   visible: true, // 展示与隐藏
    //   sortable: true,
    //   minWidth: "120px",
    // },

    // {
    //     fieldIndex: "accountCode",
    //   label: "账号编码",
    //   show: true,
    //   visible: true, // 展示与隐藏
    //   sortable: true,
    //   minWidth: "120px",
    // },

    // {
    //     fieldIndex: "status",
    //   label: "开通状态",
    //   show: true,
    //   visible: true, // 展示与隐藏
    //   sortable: true,
    //   minWidth: "120px",
    // },

    {
      label: "操作",
      slotname: "operation",
      width: "100",
      fixed: "right", //固定
      visible: true,
    },
  ],
  // 表格基础配置项，看个人需求添加
  tableConfig: {
    needPage: true, // 是否需要分页
    index: true, // 是否需要序号
    selection: false, // 是否需要多选
    reserveSelection: false, // 是否需要支持跨页选择
    indexFixed: false, // 序号列固定 left true right
    selectionFixed: false, //多选列固定left true right
    indexWidth: "50", //序号列宽度
    loading: false, //表格loading
    showSummary: false, //是否开启合计
    height: null, //表格固定高度 比如：300px
  },
});

// 树节点选择事件处理
const handleTreeCheck = (currentNode, checkedState) => {
  // 获取当前选中节点
  const checkedKeys = checkedState.checkedKeys;
  // 实现单选逻辑
  if (checkedKeys.length > 1) {
    const lastKey = currentNode.id;//checkedKeys[checkedKeys.length - 1];
    areaTree.value.setCheckedKeys([lastKey]);
    formData.accountManageId = lastKey;
    formData.payOrder = currentNode.paymentOrder;
  } else if (checkedKeys.length === 1) {
    formData.accountManageId = checkedKeys[0];
    formData.payOrder = currentNode.paymentOrder;
  } else {
    formData.accountManageId = null;
    formData.payOrder = currentNode.paymentOrder;
  }
};
/** 账户树 */
const PayAccountManageList = () => {
  screenIndex.PayAccountManageList(queryTreeParams.value).then((response) => {
    areaOptions.value = response.data;
    // 确保树数据加载完成后设置默认选中
    nextTick(() => {
      if (props.rowData.accountManageId) {
        areaTree.value.setCheckedKeys([props.rowData.accountManageId]);
      }
    });
  });
};
// 租户切换
const handleChange = () =>{
  //queryParams.value.tenantId = queryTreeParams.value.tenantId;
  // 立即清空列表
  applyUserList.value = []; 
  userInfo.value = [];
  PayAccountManageList();
}
const handleDelete = (row) => {
  // 1. 使用解构获取staffId（避免多次访问row.staffId）
  const { staffId } = row;
  // 2. 单次过滤操作（同时更新两个数组）
  userInfo.value = userInfo.value.filter((item) => item.staffId !== staffId);
  // 3. 检查是否已存在于applyUserList再添加（避免重复）
  if (!applyUserList.value.some((item) => item.staffId === staffId)) {
    applyUserList.value = [...applyUserList.value, row]; // 保持响应式
  }
};

const changeUser = (staffId) => {
  // 1. 根据staffId查找选中的用户
  const selectedUser = applyUserList.value.find(
    (item) => item.staffId === staffId
  );

  // 2. 如果找到用户且尚未存在于userInfo中
  if (
    selectedUser &&
    !userInfo.value.some((user) => user.staffId === staffId)
  ) {
    // 3. 使用展开运算符创建新数组（保持响应式）
    userInfo.value = [...userInfo.value, selectedUser];
  } else {
    // 可选：已存在时的提示
    ElMessage.warning("该人员已添加");
  }
};

const getUserList = async (name) => {
  if (!name || name.trim().length < 2) {
    applyUserList.value = []; // 立即清空列表
    return;
  }
  try {
    const res = await screenIndex.selectUserList({
      staffName: name,
      tenantId: queryTreeParams.value.tenantId
    });
    applyUserList.value = res.data;
  } catch (error) {
    console.error("获取用户列表失败:", error);
  }
};

// 模板导出
const importTemplate = () => {
  proxy.download(
    `park${apiUrl}/majorParking/userCar/importTemplateForUser`,
    {
      ...queryParams.value,
    },
    `开通账户导入模版_${formatMinuteTime(new Date())}.xlsx`
  );
};

// 导入
const handleImport = () => {
  diaWindow.popupType = "upload";
  diaWindow.headerTitle = "用户导入";
  diaWindow.dialogWidth = "400px";
  diaWindow.open1 = true;
};
// 文件上传成功处理
const handleFileSuccess = (response) => {
  if (response.response.code == "1") {
    ElMessage.success("导入成功");
    setTimeout(() => {
      upload.isUploading = false;
      diaWindow.open1 = false;
      getList();
    }, 800);
  } else {
    ElMessage.error(response.response.message);
  }
};

// 文件上传失败处理
const handleFileError = (res) => {
  ElMessage.error("导入失败");
};
const saveBtn = async () => {
  try {
    // 1. 数据校验
    if (!formData.accountManageId) {
        ElMessage.error("请选择关联账户！");
        return false
    }

    if (!userInfo.value?.length) {
        ElMessage.error("请至少添加一名人员！");
        return false
    }

    // 2. 准备符合接口要求的提交数据
    const submitData = userInfo.value.map((user) => ({
      accountManageId: formData.accountManageId, // 从formData获取账号ID
      staffName: user.staffName,        // 映射staffName -> name
      loginName: user.loginName,      // 映射loginName -> hrCode
      staffId: user.staffId,          // 映射staffId -> id
      cellphone: user.cellphone,       // 映射cellphone -> phone
      department: user.orgName,    // 映射orgName -> department
      type: user.staffType,        // 映射staffType -> type
      tenantId: queryTreeParams.value.tenantId,
      unionId:user.unionId,
      payOrder: formData.payOrder,
      state:'1'
    }));

    // 3. 调用接口（假设接口接受数组格式）
    const res = await screenIndex.insertBatch(submitData);

    // 4. 处理结果
    if (res.success) {
      ElMessage.success("操作成功");
      emit("closeBtn");
    } 
  } catch (error) {
    console.error("保存失败:", error);
  
  
  } finally {
  }
};

const cancelBtn = () => {
  props.closeBtn();
};

/** 点击确定后刷新 */
const cancellationRefsh = () => {
  close(false);
  // getList();
};
/** 点击取消保存 */
const cancellation = (val) => {
  close(false);
};

/** 关闭弹窗 */
const close = (val) => {
  diaWindow.open1 = val;
};

// 生命周期
onMounted(() => {
  PayAccountManageList();
});
defineExpose({
  saveBtn,
});
</script>

<style scoped lang="scss">
.limit-cell-table {
  :deep(.el-table__cell) {
    padding: 8px 0;
  }
}

.text-left {
  text-align: left;
}

.tenant-section {
  
  .section-title {
    font-size: 18px;
    font-weight: 500;
    color: #282828;
    margin-bottom: 15px;
  }
  
  .tenant-select-item {
    margin-left: 5px;
    margin-bottom: 5px!important;
    :deep(.el-form-item__label) {
      font-weight: 500;
      color: #606266;
    }
    
    :deep(.el-select) {
      width: 100%;
    }
  }
}

::v-deep .el-tree-node__content {
  color: #282828;
  font-weight: normal;
}
</style>