import request from '@/utils/request'
import { apiUrl } from '@/utils/config'; 



export class screenIndex {
//选择账户明细列表

    static pageList(data,params) {
      return request({
        url: `pay${apiUrl}/pay/payAccountList/detailsList`,
        method: 'post',
        data: {...data,...params},
      })
    }

     static payFlowpageList(data,params) {
      return request({
        url: `pay${apiUrl}/pay/payAccountFinancialFlows/pageList`,
        method: 'post',
        data: {...data,...params},
      })
    }
}