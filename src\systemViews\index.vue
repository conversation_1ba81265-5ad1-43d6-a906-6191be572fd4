<template>
  <div class="app-container">
    <el-card class="sys-home-card">
      <h1>欢迎您（{{ userInfo.staffName }}）访问系统管理页面</h1>
      <div class="imgs">
        <img src="@/assets/images/syshome.png"/>
      </div>
    </el-card>
  </div>
</template>

<script setup name="Index">
defineOptions({
  name: "systemIndex"
})
import useUserStore from "@/store/modules/user";
const userStore = useUserStore();
const userInfo = userStore.userInfo

</script>

<style scoped lang="scss">
.sys-home-card {
  width: 100%;
  height: calc(100vh - 160px);
  position: relative;

  h1 {
    text-align: center;
  }

  .imgs {
    width: 50%;
    height: 50%;
    margin: auto;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

    img {
      width: 100%;
      height: 100%;
    }
  }
}
</style>

