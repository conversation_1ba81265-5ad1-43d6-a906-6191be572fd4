<template>
  <div class="flow-content">
    <div class="dialog-box dialog-box-edit">
      <el-row type="flex">
        <!-- 待选规则 -->
        <el-col :span="11" style="padding-left: 30px; box-sizing: border-box">
          <el-row class="tran_title">
            <span class="tran_tip_text">待选规则</span>
          </el-row>
          <el-table
            :data="table1Data"
            style="width: 100%"
            ref="table1Ref"
            border
            height="420"
            stripe
            @selection-change="onTable1Select"
          >
            <el-table-column v-if="popupType !== 'viewRules'" type="selection" width="55" />
            <el-table-column prop="ruleName" label="规则名称" />
            <el-table-column
              v-if="popupType !== 'view'"
              prop="operation"
              label="操作"
              width="100"
              align="center"
            >
              <template #default="scope">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-view"
                  @click="handleView(scope.row)"
                  title="查看"
                />
              </template>
            </el-table-column>
          </el-table>
        </el-col>

        <!-- 操作按钮 -->
        <el-col :span="2">
          <div class="mid_btn" v-if="popupType !== 'view'">
            <div class="item-center flex flex-direction-column mid_btn_one">
              <el-button v-if="popupType !== 'viewRules'" type="primary" @click="onAdd">添加</el-button>
              <el-button v-if="popupType !== 'viewRules'" type="primary" @click="onDeleteArray">删除</el-button>
            </div>
          </div>
        </el-col>

        <!-- 已选规则 -->
        <el-col :span="11" style="padding-right: 30px; box-sizing: border-box">
          <el-row class="tran_title">
            <span class="tran_tip_text">已选规则</span>
          </el-row>
          <el-table
            :data="table2Data"
            style="width: 100%"
            border
            ref="table2Ref"
            height="420"
            stripe
            @selection-change="onTable2Select"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="ruleName" label="规则名称" />
            <el-table-column
              v-if="popupType !== 'viewRules'"
              prop="operation"
              label="操作"
              width="100"
              align="center"
            >
              <template #default="scope">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-view"
                  @click="handleView(scope.row)"
                  title="查看"
                />
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                  @click="onDelete(scope.row)"
                  title="删除"
                />
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>


    </div>

    <!-- 弹窗组件 -->

    <DialogBox
      :visible="diaWindow.open"
       :dialogWidth="diaWindow.width"
      @save="save"
      @cancellation="cancellation"
      @close="close"
      :dialogFooterBtn="diaWindow.dialogFooterBtn"
      :CloseSubmitText="diaWindow.CloseSubmitText"
      :SaveSubmitText="diaWindow.SaveSubmitText"
      :dialogTitle="diaWindow.titleName"
    >
      <template #content>
        <ruleView
          @closeBtn="cancellationRefresh"
          :rowData="diaWindow.rowData"
          :popupType="diaWindow.popupType"
        />
      </template>
    </DialogBox>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { findParkinRulesByParkingLotId, findParkinRules } from '@/api/majorParking/parkinglot'
import ruleView from  "@/systemViews/vehicle-management/parkingRule/components/add.vue";

// 定义 props
const props = defineProps({
  popupType: {
    type: String,
    default: ''
  },
  parkingId: {
    type: String,
    default: ''
  },
  ruleInfo: {
    type: Array,
    default: () => []
  }
})

// 定义 emits
const emit = defineEmits(['submitClose'])

// 弹窗配置
const diaWindow = ref({
  dialogFooterBtn:true,
  open: false,
  width: '50%',
  titleName: '修改',
  CloseSubmitTextL:'取消',
  SaveSubmitText:'保存',
  popupType: 'edit', // add edit view
  rowData: {} // 传值数据
})

// 表格数据
const table1Data = ref([]) // 待选规则
const table2Data = ref([]) // 已选规则
const selectedTable1Data = ref([]) // table1 已选数据
const selectedTable2Data = ref([]) // table2 已选数据

// 表格引用
const table1Ref = ref(null)
const table2Ref = ref(null)

// 初始化数据
onMounted(() => {
  findAllRule()
  table2Data.value = props.ruleInfo
})

// 提交表单
const submitForm = () => {
  emit('submitClose', table2Data.value)
}


// 查看规则详情
const handleView = (row) => {
  diaWindow.value.titleName = '查看';
  diaWindow.value.popupType = 'view';
  diaWindow.value.width = '50%';
  diaWindow.value.rowData = row;
  diaWindow.value.dialogFooterBtn = false;
  diaWindow.value.open = true
}

// 获取所有规则
const findAllRule = async () => {
  try {
    const res = await findParkinRules({ parkingId: props.parkingId })
    if (res.code === 200) {
      table1Data.value = res.data
    }
  } catch (error) {
    console.error('获取规则列表失败:', error)
  }
}

const findRule = async () => {
  try {
    const res = await findParkinRulesByParkingLotId({ parkingId: props.parkingId })
    if (res.code === 200) {
      table2Data.value = res.data
    }
  } catch (error) {
    console.error('获取规则列表失败:', error)
  }
}


// table1 选择事件
const onTable1Select = (rows) => {
  selectedTable1Data.value = [...rows]
}

// table2 选择事件
const onTable2Select = (rows) => {
  selectedTable2Data.value = [...rows]
}

// 添加规则
const onAdd = () => {
  filterAdd(selectedTable1Data.value, table2Data.value, 'id')
  selectedTable1Data.value = []
  table1Ref.value.clearSelection()
}

// 删除规则
const onDelete = (row) => {
  selectedTable2Data.value = [row]
  table2Data.value = filterDelete(selectedTable2Data.value, table2Data.value, 'id')
  selectedTable2Data.value = []
}

// 批量删除规则
const onDeleteArray = () => {
  table2Data.value = filterDelete(selectedTable2Data.value, table2Data.value, 'id')
  selectedTable2Data.value = []
}

// 添加规则去重
const filterAdd = (records = [], targetRecords = [], compareProperty, isEnd = false) => {
  const o = new Set()
  targetRecords.forEach((record) => o.add(record[compareProperty]))
  records.forEach((record) => {
    if (!o.has(record[compareProperty])) {
      isEnd ? targetRecords.push(record) : targetRecords.unshift(record)
    }
  })
}

// 删除规则
const filterDelete = (records = [], targetRecords = [], compareProperty) => {
  const o = new Set()
  records.forEach((record) => o.add(record[compareProperty]))
  return targetRecords.filter((item) => !o.has(item[compareProperty]))
}



/** 提交保存 */
const save = (val) => {
  close(false);
};



/** 点击取消保存 */
const cancellation = (val) => {
  close(false);
};

/** 关闭弹窗 */
const close = (val) => {
  diaWindow.value.open = val;
};

/** 点击取消保存并刷新 */
const cancellationRefresh = (val) => {
  close(false);
};

defineExpose({
  submitForm
})
</script>

<style lang="scss" scoped>
.flow-content {
  .table-common {
    width: 100%;
  }

  .dialog-box-edit {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  ::v-deep .dialog-box-edit > div {
    width: 100%;
    height: 100%;
  }

  ::v-deep .el-form {
    width: 100%;
  }

  .dialog-footer {
    margin-top: 20px;
    justify-content: center;
  }

  .tran_tip_text {
    color: #333;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 20px;
    display: inline;
  }

  .tran_title {
    margin-bottom: 12px;
  }

  .mid_btn {
    margin-top: 200px;
    display: flex;
    justify-content: center;
  }

  .mid_btn_one {
    padding: 0px 10px;
  }

  ::v-deep .el-button + .el-button {
    margin-left: 8px;
    margin-right: 8px;
    margin-top: 8px;
  }
}
</style>