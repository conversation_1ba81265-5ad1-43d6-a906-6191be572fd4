<template>
  <div class="app-container">
    <el-card>

      <dialog-search @getList="getList" formRowNumber="4" :columns="columns">
            <template v-slot:formList>
              <el-form
          :model="queryParams"
          ref="queryForm"
          v-show="showSearch"
          :inline="true">
        <el-form-item label="人员姓名" prop="staffName">
          <el-input
              v-model="queryParams.staffName"
              placeholder="请输入人员姓名"
              clearable
              style="width: 240px"
              @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="人员编码" prop="staffCode">
          <el-input
              v-model="queryParams.staffCode"
              placeholder="请输入人员编码"
              clearable
              style="width: 240px"
              @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="昵称" prop="agentNickname">
          <el-input
              v-model="queryParams.agentNickname"
              placeholder="请输入昵称"
              clearable
              style="width: 240px"
              @keyup.enter.native="handleQuery"
          />
        </el-form-item>
      </el-form>
            </template>
            <template v-slot:searchList>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </template>

          </dialog-search>
   

      <!-- <el-row :gutter="10" class="mb8">
        <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row> -->

      <el-table v-loading="loading" :data="dataList">
        <el-table-column label="人员姓名" align="left" prop="staffName" :show-overflow-tooltip="true"/>
        <el-table-column label="租户名称" align="left" prop="tenantName" :show-overflow-tooltip="true"/>
        <el-table-column label="人脸图像">
          <template #default="scope">
            <el-button icon="Picture" @click="showFace(scope.row)">查看人脸</el-button>
          </template>
        </el-table-column>
        <el-table-column label="用户端昵称" align="left" prop="agentNickname" :show-overflow-tooltip="true"/>
        <el-table-column label="最大接待量" align="left" prop="chatCount" :show-overflow-tooltip="true"/>
        <el-table-column label="技能组" align="left" prop="skillId" :show-overflow-tooltip="true"/>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="260">
          <template #default="scope">
            <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['sys:base:user-ext:edit']"
                       :loading="reloadId === scope.row.staffExtId && reloadType === 'edit'">修改
            </el-button>
            <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['sys:base:user-ext:remove']"
                       :loading="reloadId === scope.row.staffExtId && reloadType === 'remove'">删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
      />

      <!-- 添加或修改对话框 -->
      <el-dialog
          :title="title"
          v-model="open"
          width="1000px"
          append-to-body
          :close-on-press-escape="false"
          @close="cancel"
      >
        <el-form ref="formRef" :model="form" :rules="rules" label-width="150px">
          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="所属租户">
                <el-input v-model="form.tenantName" placeholder="所属租户" maxlength="64" disabled/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="用户端昵称" prop="agentNickname">
                <el-input v-model="form.agentNickname" placeholder="请输入用户端昵称" maxlength="64"/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="技能编码" prop="skillId">
                <el-input v-model="form.skillId" placeholder="请输入技能编码" maxlength="64"/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="最大接待量" prop="chatCount">
                <el-input-number v-model="form.chatCount" controls-position="right" :min="0"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-form-item label="人脸图像" prop="faceImage">
              <el-upload :on-change="faceImageChange"
                         :auto-upload="false"
                         :show-file-list="false"
              >
                <img v-if="form.faceImage" :src="form.faceImage" style="width: 200px;height: 200px"/>
                <el-button v-else type="primary" plain icon="Plus">新增</el-button>
                <template #tip>
                  <div class="el-upload__tip">只能上传jpg/png文件，且不超过2MB</div>
                </template>
              </el-upload>
            </el-form-item>
          </el-row>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForm" :loading="saveLoading">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </div>
        </template>
      </el-dialog>

      <!--查看人脸图片-->
      <el-dialog
          title="人脸图片"
          v-model="faceImageDialog"
          width="30%">
        <img v-if="faceImage" style="width: 100%;" :src="faceImage">
        <el-empty v-else description="暂无上传人脸信息"></el-empty>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup name="UserExt">
import {ref} from "vue";
import {page, getById, update, del} from "@/api/system/userExt";
import useUserStore from "@/store/modules/user";

const {proxy} = getCurrentInstance();
const userStore = useUserStore();

const {effective_status} = proxy.useDict("effective_status");
// 遮罩层
const loading = ref(true);
// 导出遮罩层
const exportLoading = ref(false);
// 选中数组
const ids = ref([]);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 参数表格数据
const dataList = ref([]);
// 弹出层标题
const title = ref("");
// 是否显示弹出层
const open = ref(false);
// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  staffName: undefined,
  staffCode: undefined,
  agentNickname: undefined,
  deleteFlag: undefined,
  tenantId: userStore.userInfo.tenantId
})
// 表单参数
const form = ref({})
// 表单校验
const rules = {
  agentNickname: [
    {required: true, message: "用户端昵称不能为空", trigger: "blur"},
  ],
  skillId: [
    {required: true, message: "技能编码不能为空", trigger: "blur"},
  ],
  chatCount: [
    {required: true, message: "最大接待量不能为空", trigger: "blur"},
  ]
};
const reloadId = ref(undefined);
const reloadType = ref(undefined);
const saveLoading = ref(false);
const defaultOrgId = userStore.userInfo.orgId;
const loadTenantId = ref(undefined);
const faceImageDialog = ref(false);
const faceImage = ref("");

/** 查询参数列表 */
function getList() {
  loading.value = true;
  page({...queryParams.value}).then(
      (response) => {
        dataList.value = response.data.records;
        total.value = response.data.total;
        loading.value = false;
      }
  );
}

// 取消按钮
function cancel() {
  open.value = false;
  reloadId.value = undefined;
  reloadType.value = undefined;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    tenantName: undefined,
    agentNickname: undefined,
    skillId: undefined,
    chatCount: undefined,
    faceImage: ''
  };
  proxy.resetForm("formRef");
  proxy.resetForm("queryForm");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryForm");
  handleQuery();
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const staffExtId = row.staffExtId;
  reloadId.value = staffExtId;
  reloadType.value = "edit";
  getById(staffExtId).then((response) => {
    if (response.data) {
      form.value = response.data;
      open.value = true;
      title.value = "修改租户";
    } else {
      proxy.$modal.msgError("数据异常！");
    }
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      saveLoading.value = true;
      update(form.value).then((response) => {
        if (!response.success) {
          proxy.$modal.msgError(response.message);
          saveLoading.value = false;
        } else {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          saveLoading.value = false;
          getList();
        }
      });
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const staffExtId = row.staffExtId;
  reloadId.value = staffExtId;
  reloadType.value = "remove";
  proxy.$modal.confirm('是否确认删除"' + row.staffName + '"的数据项?')
      .then(function () {
        return del(staffExtId);
      })
      .then(() => {
        reloadId.value = undefined;
        reloadType.value = undefined;
        getList();
        proxy.$modal.msgSuccess("删除成功");
      })
      .catch(() => {
        reloadId.value = undefined;
      });
}

// 显示人脸图片
function showFace(row) {
  faceImage.value = row.faceImage;
  faceImageDialog.value = true;
}

// 处理人脸上传
function faceImageChange(file, fileList) {
  const isJPG = file.raw.type === "image/jpeg";
  const isPng = file.raw.type === "image/png";
  const isLt2M = file.raw.size / 1024 / 1024 < 2;

  if (!isLt2M) {
    proxy.$modal.msgError("上传图片大小不能超过 2MB!");
    return;
  }
  if (!isJPG && !isPng) {
    proxy.$modal.msgError("上传图片只能是 JPG/Png 格式!");
    return;
  } else {
    getBase64(file.raw).then((res) => {
      form.value.faceImage = res;
    });
  }
}

// 将图片进行Base64编码
function getBase64(file) {
  return new Promise(function (resolve, reject) {
    let reader = new FileReader();
    let imgResult = "";
    reader.readAsDataURL(file);
    reader.onload = function () {
      imgResult = reader.result;
    };
    reader.onerror = function (error) {
      reject(error);
    };
    reader.onloadend = function () {
      resolve(imgResult);
    };
  });
}

getList();
</script>
<style scoped>

</style>
