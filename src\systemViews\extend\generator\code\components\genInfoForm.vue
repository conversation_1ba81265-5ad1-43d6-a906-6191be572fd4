<template>
  <div class="gen-info-form">
    <el-form ref="genInfoForm" :model="info" :rules="rules" label-width="150px">
      <el-row>
        <el-col :span="12">
          <el-form-item label="生成模板" prop="tplCategory">
            <el-select v-model="info.tplCategory" @change="tplSelectChange">
              <el-option label="单表（增删改查）" value="crud"/>
              <el-option label="树表（增删改查）" value="tree"/>
<!--              <el-option label="主子表（增删改查）" value="sub"/>-->
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="packageName">
            <template #label>
              <span>
                <el-tooltip content="生成在哪个java包下，例如 com.unifast.system" placement="top">
                  <el-icon><question-filled /></el-icon>
                </el-tooltip>
                生成包路径
              </span>
            </template>
            <el-input v-model="info.packageName"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="moduleName">
              <template #label>
              <span>
                <el-tooltip content="可理解为子系统名，例如 system" placement="top">
                  <el-icon><question-filled /></el-icon>
                </el-tooltip>
                生成模块名
              </span>
              </template>
            <el-input v-model="info.moduleName"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="businessName">
            <template #label>
              <span>
                <el-tooltip content="可理解为功能英文名，例如 user" placement="top">
                  <el-icon><question-filled /></el-icon>
                </el-tooltip>
                生成业务名
              </span>
            </template>
            <el-input v-model="info.businessName"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="functionName">
            <template #label>
              <span>
                <el-tooltip content="用作类描述，例如 用户" placement="top">
                  <el-icon><question-filled /></el-icon>
                </el-tooltip>
                生成功能名
              </span>
            </template>
            <el-input v-model="info.functionName"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item>
            <template #label>
              <span>
                <el-tooltip content="分配到指定菜单下，例如 系统管理" placement="top">
                  <el-icon><question-filled /></el-icon>
                </el-tooltip>
                上级菜单
              </span>
            </template>
            <el-tree-select v-model="info.parentMenuId" :data="menus"
                            :props="{ value: 'permissionId', label: 'permissionName', isLeaf: (d) => !d.isParent, children: 'children' }"
                            check-strictly :render-after-expand="false" placeholder="选择上级菜单"/>
          </el-form-item>
        </el-col>
<!--        <el-col :span="12">-->
<!--          <el-form-item prop="genType">-->
<!--            <template #label>-->
<!--              <span>-->
<!--                <el-tooltip content="默认为zip压缩包下载，也可以自定义生成路径" placement="top">-->
<!--                  <el-icon><question-filled /></el-icon>-->
<!--                </el-tooltip>-->
<!--                生成代码方式-->
<!--              </span>-->
<!--            </template>-->
<!--            <el-radio v-model="info.genType" label="0">zip压缩包</el-radio>-->
<!--            <el-radio v-model="info.genType" label="1">自定义路径</el-radio>-->
<!--          </el-form-item>-->
<!--        </el-col>-->
<!--        <el-col :span="24" v-if="info.genType == '1'">-->
<!--          <el-form-item prop="genPath">-->
<!--            <template #label>-->
<!--              <span>-->
<!--                <el-tooltip content="填写磁盘绝对路径，若不填写，则生成到当前Web项目下" placement="top">-->
<!--                  <el-icon><question-filled /></el-icon>-->
<!--                </el-tooltip>-->
<!--                自定义路径-->
<!--              </span>-->
<!--            </template>-->
<!--            <el-input v-model="info.genPath">-->
<!--              <el-dropdown slot="append">-->
<!--                <el-button type="primary">最近路径快速选择-->
<!--                  <i class="el-icon-arrow-down el-icon&#45;&#45;right"></i>-->
<!--                </el-button>-->
<!--                <el-dropdown-menu slot="dropdown">-->
<!--                  <el-dropdown-item @click.native="info.genPath = '/'">恢复默认的生成基础路径</el-dropdown-item>-->
<!--                </el-dropdown-menu>-->
<!--              </el-dropdown>-->
<!--            </el-input>-->
<!--          </el-form-item>-->
<!--        </el-col>-->
      </el-row>
      <!--  树表--其他信息  -->
      <el-row v-show="info.tplCategory == 'tree'">
        <el-col><h4 class="form-header">其他信息</h4></el-col>
        <el-col :span="12">
          <el-form-item>
            <template #label>
              <span>
                <el-tooltip content="树显示的编码字段名， 如：dept_id" placement="top">
                  <el-icon><question-filled /></el-icon>
                </el-tooltip>
                树编码字段
              </span>
            </template>
            <el-select v-model="info.treeCode" placeholder="请选择">
              <el-option
                  v-for="(column, index) in info.columns" :key="index"
                  :label="column.columnName + '：' + column.columnComment" :value="column.columnName">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item>
            <template #label>
              <span>
                <el-tooltip content="树显示的父编码字段名， 如：parent_Id" placement="top">
                  <el-icon><question-filled /></el-icon>
                </el-tooltip>
                树父编码字段
              </span>
            </template>
            <el-select v-model="info.treeParentCode" placeholder="请选择">
              <el-option v-for="(column, index) in info.columns" :key="index"
                         :label="column.columnName + '：' + column.columnComment" :value="column.columnName">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item>
            <template #label>
              <span>
                <el-tooltip content="树节点的显示名称字段名， 如：dept_name" placement="top">
                  <el-icon><question-filled /></el-icon>
                </el-tooltip>
                树名称字段
              </span>
            </template>
            <el-select v-model="info.treeName" placeholder="请选择">
              <el-option v-for="(column, index) in info.columns" :key="index"
                         :label="column.columnName + '：' + column.columnComment" :value="column.columnName">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <!--  主子表--关联信息  -->
      <el-row v-show="info.tplCategory == 'sub'">
        <el-col><h4 class="form-header">关联信息</h4></el-col>
        <el-col :span="12">
          <el-form-item>
            <template #label>
              <span>
                <el-tooltip content="关联子表的表名， 如：sys_user" placement="top">
                  <el-icon><question-filled /></el-icon>
                </el-tooltip>
                关联子表的表名
              </span>
            </template>
            <el-select v-model="info.subTableName" placeholder="请选择" @change="subSelectChange">
              <el-option
                  v-for="(table, index) in tables"
                  :key="index"
                  :label="table.tableName + '：' + table.tableComment"
                  :value="table.tableName"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item>
            <template #label>
              <span>
                <el-tooltip content="子表关联的外键名， 如：user_id" placement="top">
                  <el-icon><question-filled /></el-icon>
                </el-tooltip>
                子表关联的外键名
              </span>
            </template>
            <el-select v-model="info.subTableFkName" placeholder="请选择">
              <el-option
                  v-for="(column, index) in subColumns"
                  :key="index"
                  :label="column.columnName + '：' + column.columnComment"
                  :value="column.columnName"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script setup>

const genInfoForm = ref(null)
const props = defineProps({
  info: { type: Object, default: null },
  tables: { type: Array, default: null },
  menus: { type: Array, default: null },
})

const subColumns = ref([])
const rules = {
  tplCategory: [{required: true, message: "请选择生成模板", trigger: "blur"}],
  packageName: [{required: true, message: "请输入生成包路径", trigger: "blur"}],
  moduleName: [{required: true, message: "请输入生成模块名", trigger: "blur"}],
  businessName: [{required: true, message: "请输入生成业务名", trigger: "blur"}],
  functionName: [{required: true, message: "请输入生成功能名", trigger: "blur"}],
}

/** 选择子表名触发 */
const subSelectChange = (value) => {
  props.info.subTableFkName = '';
}

/** 选择生成模板触发 */
const tplSelectChange = (value) => {
  if (value !== 'sub') {
    props.info.subTableName = '';
    props.info.subTableFkName = '';
  }
}

watch(() => props.info.subTableName, (val) => {setSubTableColumns(val);})

/** 设置关联外键 */
const setSubTableColumns = (value) => {
  for (const item in props.tables) {
    const name = props.tables[item].tableName;
    if (value === name) {
      subColumns.value = props.tables[item].columns;
      break;
    }
  }
}
defineExpose({
  genInfoForm,
});

</script>

<style scoped lang="scss">

</style>
