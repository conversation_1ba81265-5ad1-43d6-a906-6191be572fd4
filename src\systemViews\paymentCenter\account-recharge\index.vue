<!-- 账户充值 -->

<template>
  <div class="container-table-box">
    <Splitpanes class="default-theme">
      <Pane :size="100" :min-size="65">
        <el-card class="dep-card">
          <dialog-search @getList="getList" formRowNumber="4" :columns="tabelForm.columns" :isShowRightBtn="$checkPermi(['pay:payRecharge:list'])">
            <template v-slot:formList>
              <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="75px">
                <el-form-item label="充值月份">
                  <el-date-picker v-model="queryParams.rechargeMonth" type="month" format="YYYYMM" value-format="YYYYMM"
                    placeholder="请选择" clearable />
                </el-form-item>

                <el-form-item label="充值名称">
                  <el-input v-model="queryParams.rechargeName" placeholder="请输入充值名称" clearable></el-input>
                </el-form-item>

                <el-form-item label="充值类型" prop="rechargeType">
                  <el-select v-model="queryParams.rechargeType" placeholder="请选择充值类型" clearable>
                    <el-option v-for="(item, index) of recharge_type" :key="index" :label="item.label"
                      :value="item.value" />
                  </el-select>
                </el-form-item>

                <el-form-item label="充值状态" prop="rechargeStatus">
                  <el-select v-model="queryParams.rechargeStatus" placeholder="请选择类型状态" clearable>
                    <el-option v-for="(item, index) of recharge_status" :key="index" :label="item.label"
                      :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-form>
            </template>
            <template v-slot:searchList>
              <el-button type="primary" icon="Search" @click="handleQuery" v-hasPermi="['pay:payRecharge:list']">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery" v-hasPermi="['pay:payRecharge:list']">重置</el-button>
            </template>

            <template v-slot:searchBtnList>
              <el-button type="primary" size="mini" @click="handleRecharge" v-hasPermi="['pay:payRechargeDetail:add']">
                <i class="icon iconfont icon-zhanghuchongzhi icon-btn"></i>
                账户充值</el-button>
              <el-button size="mini" @click="handleCancelRecharge"  v-hasPermi="['pay:payRecharge:cancel']">
                <i class="icon iconfont icon-chexiao icon-btn"></i>
                撤销充值</el-button>
              <el-button @click="handleBatchRecharge" v-hasPermi="['pay:payRechargeDetail:add']">
                <i class="icon iconfont icon-piliangchongzhi icon-btn"></i>
                批量充值
              </el-button>
              <el-button icon="Download" @click="handleExport" v-hasPermi="['pay:payRecharge:export']">导出 </el-button>
            </template>
          </dialog-search>

          <public-table ref="publictable" :rowKey="tabelForm.tableKey" :tableData="list" :columns="tabelForm.columns"
            :configFlag="tabelForm.tableConfig" :pageValue="pageParams" :total="total" :getList="getList">
            <template #statusType="{ scope }">
              <el-radio-group v-model="scope.row.status" @change="radioChange(scope.row)">
                <el-radio-button :label="item.value" v-for="(item, index) of account_type_status" :key="index">{{
                  item.label }}</el-radio-button>
              </el-radio-group>
            </template>
            <template #operation="{ scope }">
              <el-button size="mini" type="text" title="查看明细" icon="View" @click="handleView(scope.row)" v-hasPermi="['pay:payRechargeDetail:list']">
              </el-button>
              <el-button size="mini" type="text" title="撤销充值" @click="handleCancel(scope.row)" v-hasPermi="['pay:payRecharge:cancel']">
                <i class="icon iconfont icon-chexiao"></i>
              </el-button>
            </template>
          </public-table>
        </el-card>
      </Pane>
    </Splitpanes>

    <DialogBox :visible="open1" :dialogWidth="diaWindow.dialogWidth" :dialogFooterBtn="diaWindow.dialogFooterBtn"
      @save="save" @cancellation="cancellation" :custom-class="diaWindow.customClass" @close="close"
      :dialogTitle="diaWindow.headerTitle" :dialogTop="diaWindow.dialogTop">
      <template #content>
        <accountAdd v-if="diaWindow.popupType == 'recharge'" ref="accountAddRef" :rowData="diaWindow.rowData"
          :popupType="diaWindow.popupType" @closeBtn="cancellationRefsh"></accountAdd>
        <detail v-if="diaWindow.popupType == 'detail'" ref="detailRef" :rowData="diaWindow.rowData"></detail>
        <!-- <cancel v-if="diaWindow.popupType == 'cancel'" ref="cancelRef" :rowData="diaWindow.rowData" @closeBtn="cancellationRefsh"></cancel> -->
        <div class="flex" style="margin-bottom: 10px" v-if="diaWindow.popupType == 'batch'">
          <el-form style="margin-right: 20px" v-if="diaWindow.popupType == 'batch'" ref="formRef"
            :model="extraUploadFileData" label-width="80px" :rules="rules">
            <el-row>
              <el-col :span="24">
                <el-form-item label="充值月份" prop="rechargeMonth">
                  <!-- 添加或编辑时显示输入框 -->
                  <el-date-picker v-model="extraUploadFileData.rechargeMonth" type="month" format="YYYYMM"
                    value-format="YYYYMM" placeholder="请选择" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="导入模版:" prop="type">
                  <div class="flex a-link" @click="importTemplate">
                    <el-icon>
                      <UploadFilled />
                    </el-icon>
                    批量充值导入模版
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="导入文件:" prop="type">

                  <UploadFile v-if="diaWindow.popupType == 'batch'" ref="uploadRef" :isDrag="false"
                    :uploadData="extraUploadFileData" :action="upload.url" :limit="1" :loading="upload.isUploading"
                    :accept="'.xlsx, .xls'" :disabled="upload.isUploading" :auto-upload="false"  @update:file-list="handleFileListUpdate"  
                    tip="提示：仅允许导入“xls”或“xlsx”格式文件！<br>" @file-success="handleFileSuccess"
                    @file-error="handleFileError" />

                </el-form-item>
              </el-col>
            </el-row>
          </el-form>

         
        </div>


        <uploadDetail v-if="diaWindow.popupType == 'uploadSuccessdetailList'" ref="uploadDetailRef"
          :uoloadStatus="diaWindow.uoloadStatus" :redisLock="redisLock" @closeBtn="cancellationRefsh"></uploadDetail>

        <cancelRecharge v-if="diaWindow.popupType == 'cancel'" ref="cancelRef" :rowData="diaWindow.rowData" @closeBtn="cancellationRefsh"></cancelRecharge>



      </template>
    </DialogBox>
  </div>
</template>

<script setup name="accountRecharge">
import { ElMessage, ElMessageBox } from "element-plus";
import { ref, reactive, getCurrentInstance } from "vue";
import { screenIndex } from "@/api/paymentCenter/account-recharge/index";
import { apiUrl } from "@/utils/config";
import { formatMinuteTime } from "@/utils";
import accountAdd from "./components/add.vue";
import detail from "./components/detail.vue";
import uploadDetail from "./components/uploadDetail.vue";
import cancel from "./components/cancel.vue";
import cancelRecharge from "./components/cancelRecharge";
import UploadFile from "@/components/UploadFile/index";
import useUserStore from "@/store/modules/user";
const userStore = useUserStore();
const { proxy } = getCurrentInstance();
const { recharge_type, recharge_status } = proxy.useDict(
  "recharge_type",
  "recharge_status"
);
const redisLock = ref(""); // 定义 redisLock 变量
const accountAddRef = ref(null);
const batchRechargeRef = ref(null);
const detailRef = ref(null);
// 在setup中添加ref
const cancelRef = ref(null);
const open1 = ref(false);
const diaWindow = reactive({
  headerTitle: "",
  popupType: "",
  rowData: "",
  dialogWidth: "20%",
  dialogFooterBtn: false,
  customClass: "",
  dialogTop:''
});

// 用户导入参数
const uploadDetailRef = ref(null);
const uploadRef = ref(null);
const fileList = ref([])
// 添加轮询状态控制变量
const isPollingActive = ref(true);
// 用户导入参数
const upload = reactive({
  title: "",
  isUploading: false,
  updateSupport: 0,
  url: `${import.meta.env.VITE_APP_BASE_API
    }/pay${apiUrl}/pay/payRechargeDetails/batchImport`,
});
// 批量导入额外参数
const formRef = ref(null);
const extraUploadFileData = ref({
  rechargeMonth: "",
});

const providerOptions = ref([]);
const pageParams = ref({
  pageNum: 1,
  pageSize: 10,
});
const queryParams = ref({
  tenantId: userStore.userInfo.tenantId,
});
const list = ref([]);
const total = ref(0);
const tabelForm = reactive({
  tableKey: "1", //表格key值
  isShowRightToolbar: true, //是否显示右侧显示和隐藏
  showSearch: true,
  columns: [
    {
      fieldIndex: "rechargeMonth", // 对应列内容的字段名
      label: "充值月份", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "120", //最小宽度%
      width: "", //宽度
    },

    {
      fieldIndex: "rechargeNumber", // 对应列内容的字段名
      label: "充值人数", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "120", //最小宽度%
      width: "", //宽度
    },

    {
      fieldIndex: "rechargeAmount", // 对应列内容的字段名
      label: "充值金额(元)", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "120", //最小宽度%
      width: "", //宽度
      type: "dollor",
    },
    {
      fieldIndex: "rechargeName", // 对应列内容的字段名
      label: "充值名称", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "150", //最小宽度%
      width: "", //宽度
      align: "", //表格对齐方式
    },
    {
      fieldIndex: "rechargeType", // 对应列内容的字段名
      label: "充值类型", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "150", //最小宽度%
      width: "", //宽度
      align: "", //表格对齐方式
      type: "dict",
      dictList: recharge_type,
    },
    {
      fieldIndex: "rechargeStatus", // 对应列内容的字段名
      label: "充值状态", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "120", //最小宽度%
      width: "", //宽度
      align: "", //表格对齐方式
      type: "dict",
      dictList: recharge_status,
    },

    {
      fieldIndex: "createDate", // 对应列内容的字段名
      label: "创建时间", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "150", //最小宽度%
      width: "", //宽度
    },

    {
      fieldIndex: "createBy", // 对应列内容的字段名
      label: "操作人", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "120", //最小宽度%
      width: "", //宽度
    },

    {
      label: "操作",
      slotname: "operation",
      width: "120",
      fixed: "right", //固定
      headerAlign: "center",
      visible: true,
    },
  ],
  // 表格基础配置项，看个人需求添加
  tableConfig: {
    needPage: true, // 是否需要分页
    index: true, // 是否需要序号
    selection: false, // 是否需要多选
    reserveSelection: false, // 是否需要支持跨页选择
    indexFixed: false, // 序号列固定 left true right
    selectionFixed: false, //多选列固定left true right
    indexWidth: "50", //序号列宽度
    loading: false, //表格loading
    showSummary: false, //是否开启合计
    height: null, //表格固定高度 比如：300px
  },
});
// 定义校验规则

const rules = reactive({

  rechargeMonth: [
    { required: true, message: "请选择充值月份", trigger: ["blur", "change"] },

    {
      validator: (_, value, callback) => {
        if (!value) return callback();

        const currentDate = new Date();
        const currentYear = currentDate.getFullYear();
        const currentMonth = currentDate.getMonth() + 1;
        const [selectedYear, selectedMonth] = [
          value.substring(0, 4),
          value.substring(4, 6),
        ];

        if (
          selectedYear < currentYear ||
          (selectedYear == currentYear && selectedMonth < currentMonth)
        ) {
          callback(new Error("充值月份不能早于当前月份"));
        } else {
          callback();
        }
      },
      trigger: ["blur", "change"],
    },
  ],
});
/** 查询列表 */
const getList = () => {
  tabelForm.tableConfig.loading = true;
  screenIndex.pageList(queryParams.value, pageParams.value).then((response) => {
    list.value = response.data.records;
    total.value = response.data.total;
    tabelForm.tableConfig.loading = false;
  });
};

/** 搜索按钮操作 */
const handleQuery = () => {
  pageParams.value.pageNum = 1;
  getList();
};
/** 重置按钮操作 */
const resetQuery = () => {
  proxy.resetForm("queryForm");
  queryParams.value = {
    tenantId: userStore.userInfo.tenantId
  };
  handleQuery();
};

/** 导出按钮操作 */

const handleExport = () => {
  proxy.download(
    `pay${apiUrl}/pay/payRecharge/export`,
    {
      ...queryParams.value,
    },
    `账号充值列表_${formatMinuteTime(new Date())}.xlsx`
  );
};
/** 删除 */
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm("确认要删除吗？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });

    const res = await screenIndex.payAccountTypeDelete({ id: row.id });
    if (res.code == "1") {
      ElMessage.success("删除成功");
      await getList();
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    console.error("删除失败:", error);
    // 用户取消删除或其他错误
  }
};

/** 查看 */
const handleView = (data) => {
  diaWindow.headerTitle = "账户充值明细";
  diaWindow.popupType = "detail";
  diaWindow.rowData = data;
  diaWindow.dialogWidth = "75%";
  diaWindow.dialogFooterBtn = false;
      diaWindow.dialogTop = 'auto'
  open1.value = true;
};

// 账户充值
const handleRecharge = () => {
  diaWindow.headerTitle = "账户充值";
  diaWindow.popupType = "recharge";
  diaWindow.rowData = {}; // 如果需要传递数据到弹窗
  diaWindow.dialogFooterBtn = true;
  diaWindow.dialogWidth = "28%";
  diaWindow.dialogTop = '12vh'
  open1.value = true;
};

// 撤销充值按钮
// const handleCancelRecharge = () => {
//   diaWindow.headerTitle = "撤销充值";
//   diaWindow.popupType = "cancel";
//   diaWindow.rowData = {}; // 传递空数据，在组件内处理
//   diaWindow.dialogWidth = "28%";
//   diaWindow.dialogFooterBtn = true;
//   open1.value = true;
// };

// 获取供应商树
const getProviderTreeData = () => {
  screenIndex.getProviderTree().then(res => {
    if (res.code === "1" && res.data) {
      providerOptions.value = res.data.map(item => ({
        id: item.id,
        label: item.label
      }));
    }
  });
};

// 批量充值
const handleBatchRecharge = () => {
  diaWindow.headerTitle = "批量充值";
  diaWindow.popupType = "batch";
  diaWindow.rowData = {}; // 如果需要传递数据到弹窗
  diaWindow.dialogFooterBtn = true;
  diaWindow.dialogWidth = "30%";
  diaWindow.dialogTop = '12vh'
  // 开启新的批量导入前，重置轮询状态
  isPollingActive.value = true;
  // 获取供应商数据
  open1.value = true;
};
// 批量开通成功列表
const uploadSuccessdetailListFun = () => {
  diaWindow.headerTitle = "批量充值成功列表";
  diaWindow.popupType = "uploadSuccessdetailList";
  diaWindow.dialogFooterBtn = true;
  diaWindow.uoloadStatus = "successed";
  diaWindow.dialogWidth = "75%";
    diaWindow.dialogTop = 'auto'
  open1.value = true;
};
const uploadSuccessdetailListFun2 = () => {
  diaWindow.headerTitle = "批量充值失败列表";
  diaWindow.popupType = "uploadSuccessdetailList";
  diaWindow.uoloadStatus = "failed";
  diaWindow.dialogFooterBtn = false;
  diaWindow.dialogWidth = "75%";
      diaWindow.dialogTop = 'auto'
  open1.value = true;
};
// 模板导出
const importTemplate = () => {
  proxy.download(
    `pay${apiUrl}/pay/payRechargeDetails/templateDownload`,
    {},
    `批量充值导入模版_${formatMinuteTime(new Date())}.xlsx`
  );
};

// 文件上传成功处理
const handleFileSuccess = (response) => {
  upload.isUploading = false;
  if (response.response.code == "1") {
    setTimeout(() => {
      upload.isUploading = false;
      // 确保在开始新的导入时，轮询状态是激活的
      isPollingActive.value = true;
      getImportResult(response.response.data);
    }, 800);
  } else {
    ElMessage.error(response.response.message);
  }
};

// 导入校验 getImportResult
const getImportResult = async (data) => {
  // 如果轮询已停止，则不继续请求
  if (!isPollingActive.value) return;

  const res = await screenIndex.getImportResult({ redisLock: data });
  redisLock.value = data;
  if (res.code === "1") {
    // 0:导入执行中、1:导入失败、2:导入成功
    switch (res.data) {
      case "0":
        // 导入中，轮询检查状态，但仅在轮询激活时继续
        setTimeout(() => {
          if (isPollingActive.value) {
            getImportResult(data);
          }
        }, 2000);
        break;
      case "1":
        // 修改为确认框只留下确定按钮
        // ElMessageBox.confirm("导入失败，是否下载失败数据？", "提示", {
        //   confirmButtonText: "下载",
        //   cancelButtonText: "取消",
        //   type: "error",
        //   closeOnClickModal: false, // 点击遮罩层不关闭
        // })
        //   .then(() => {
        //     proxy.download(
        //       `pay${apiUrl}/pay/payRechargeDetails/exportErrorData`,
        //       {
        //         redisLock: data,
        //       },
        //       `批量充值导入失败列表_${formatMinuteTime(new Date())}.xlsx`
        //     );
        //     // 清空导入文件和状态
        //     upload.isUploading = false;
        //     uploadRef.value.clearFiles();
        //   })
        //   .catch(() => {
        //     // 清空导入文件和状态
        //     upload.isUploading = false;
        //     uploadRef.value.clearFiles();
        //   });

        // 处理失败数据...

        const successData1 = []
        ElMessage.error(`导入失败`);
        // 清空导入文件和状态
        setTimeout(() => {
          upload.isUploading = false;
          uploadRef.value.clearFiles();
          uploadSuccessdetailListFun2();
          detailList.value = successData1.data;
          getList(); // 刷新列表
        }, 1000);
        break;
      case "2":
        // 导入成功，获取成功数据
        const successData = await screenIndex.importDataList({
          redisLock: data,
        });
        ElMessage.success(`导入成功，成功${successData.data.total}条`);
        // 清空导入文件和状态
        setTimeout(() => {
          // 存储下data
          redisLock.value = data;
          upload.isUploading = false;
          uploadRef.value.clearFiles();
          uploadSuccessdetailListFun();
          getList(); // 刷新列表
        }, 1000);

        break;
    }
  }
  return res;
};
// 文件上传失败处理
const handleFileError = () => {
  upload.isUploading = false;
  ElMessage.error("导入失败");
};

// 修改handleCancel方法
const handleCancelRecharge = (row) => {
  diaWindow.headerTitle = "撤销充值";
  diaWindow.popupType = "cancel"; // 新增类型
  diaWindow.rowData = row; // 传递行数据
  diaWindow.dialogWidth = "28%"; // 与充值表单相同宽度
  diaWindow.dialogFooterBtn = true; // 显示确定按钮
      diaWindow.dialogTop = '10vh'
  open1.value = true;
};

 const handleCancel = (row) => {
  ElMessageBox.confirm("确认要撤销充值吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    screenIndex.payAccountTypeCancel({ id: row.id }).then((res) => {
      if (res.code == "1") {
        ElMessage.success("撤销成功");
        getList();
      } else {
        ElMessage.error(res.msg);
      }
    });
  });
}; 

const radioChange = async (row) => {
  const res = await screenIndex.payAccountTypeUpdate(row);
  if (res.success) {
    ElMessage.success("修改成功");
    getList();
  }
};

const handleFileListUpdate = (files) => {
  fileList.value = files; // 存储文件列表
};
/** 点击确定保存 */
const save = () => {
  if (diaWindow.popupType == "recharge") {
    accountAddRef.value.saveForm();
  }

  if (diaWindow.popupType == "edit") {
    accountAddRef.value.saveForm();
  }

  if (diaWindow.popupType == "batch") {
    // 先去校验月份选没有
    formRef.value.validate((valid) => {
      if (valid) {

        if(fileList.value.length === 0){
          ElMessage.error("请选择文件");
          return false;
        }
        upload.isUploading = true;
        uploadRef.value.submitFileForm();
      } else {
        return false;
      }
    });
  }

  if (diaWindow.popupType == "uploadSuccessdetailList") {
    uploadDetailRef.value.saveBtn(redisLock.value);
  }
  if(diaWindow.popupType=="cancel"){
    cancelRef.value.saveForm();

  }
};
/** 点击确定后刷新 */
const cancellationRefsh = () => {
  close(false);
  getList();
};
/** 点击取消保存 */
const cancellation = (val) => {
  close(false);
};

/** 关闭弹窗 */
const close = (val) => {
  open1.value = val;
  // 关闭弹窗时停止轮询
  isPollingActive.value = false;
};

getList();
</script>

<style scoped>
.icon-btn {
  font-size: 16px;
  margin-right: 5px;
}

.a-link {
  color: #c20000;
  align-items: center;
  cursor: pointer;
  :deep(.el-icon) {
    margin-right: 8px;
  }
}
</style>
