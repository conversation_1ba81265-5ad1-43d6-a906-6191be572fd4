<template>
  <div class="app-container">
    <el-card style="height: 100%" shadow="never">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
        <el-form-item label="租户" prop="tenantId" v-if="userStore.userInfo.customParam.userType === 'admin'">
          <el-select v-model="queryParams.tenantId" placeholder="请选择租户" style="width: 180px" @change="handleQuery">
            <el-option v-for="item in tenantList" :key="item.tenantId" :label="item.tenantName" :value="item.tenantId"/>
          </el-select>
        </el-form-item>
        <el-form-item label="员工名称" prop="staffName">
          <el-input v-model="queryParams.staffName" placeholder="请输入员工名称" clearable style="width: 180px;"
                    @keyup.enter="handleQuery"/>
        </el-form-item>
        <el-form-item label="业务类型" prop="busiType">
          <el-input v-model="queryParams.busiType" placeholder="请输入业务类型" clearable style="width: 180px;"
                    @keyup.enter="handleQuery"/>
        </el-form-item>
        <el-form-item label="操作类型" prop="operType">
          <el-select v-model="queryParams.operType" clearable style="width: 150px" @change="handleQuery">
            <el-option v-for="item in operation_type" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="操作日期">
          <el-date-picker v-model="dateRange" style="width: 320px" format="YYYY-MM-DD HH:mm:ss" type="datetimerange"
                          start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions"
                          date-format="YYYY/MM/DD ddd" time-format="A hh:mm:ss"/>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search"  @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table v-loading="loading" :data="logList">
        <el-table-column type="expand">
          <template #default="props">
            <el-form label-position="left">
              <el-form-item label="列名描述:">
                <span>{{ props.row.columnDesc }}</span>
              </el-form-item>
              <el-form-item label="改动前值:">
                <span>{{ props.row.oldValue }}</span>
              </el-form-item>
              <el-form-item label="改动后值:">
                <span>{{ props.row.newValue }}</span>
              </el-form-item>
            </el-form>
          </template>
        </el-table-column>
        <el-table-column label="序号" type="index" width="50" align="center"/>
        <el-table-column label="操作人" align="center" prop="staffName" :show-overflow-tooltip="true"  />
        <el-table-column label="业务类型" align="center" prop="busiType" :show-overflow-tooltip="true"  />
        <el-table-column label="操作类型" align="center" prop="operType" width="130" >
          <template #default="scope">
            <dict-tag :options="operation_type" :value="scope.row.operType"/>
          </template>
        </el-table-column>
        <el-table-column label="操作时间" align="center" prop="createTime" :show-overflow-tooltip="true" />
        <el-table-column label="操作表" align="center" prop="tableName" :show-overflow-tooltip="true" />
        <el-table-column label="操作列" align="center" prop="columnName" :show-overflow-tooltip="true" />
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize"
                  @pagination="getList"/>
    </el-card>
  </div>
</template>

<script setup name="Log">
import useUserStore from '@/store/modules/user';
import {getTenants} from "@/api/tenant/tenant";
import {addDateRange, selectDictLabel} from "@/utils/common";
import {listLog} from "@/api/operation/log";

const { proxy } = getCurrentInstance();
const {operation_type} = proxy.useDict("operation_type");
const dateRange = ref([]);
const showSearch = ref(true);

/** 初始化租户数据 */
const userStore = useUserStore();
const tenantList = ref([]);
function getTenantList() {
  getTenants().then(res => {
    tenantList.value = res.data;
  }).catch(() => {
    tenantList.value = [];
  })
}

/** 表格数据显示 */
const logList = ref([]);
const loading = ref(false);
const total = ref(0);
const queryRef = ref(null)
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  staffName: "",
  busiType: "",
  operType: "",
  beginTime: "",
  endTime: "",
  tenantId: userStore.userInfo.tenantId,
})
const pickerOptions = {
  disabledDate: (time) => {
    const one = 30 * 24 * 3600 * 1000;
    return time.getTime() > Date.now() || time.getTime() <= (Date.now() - one);
  }
}

function getList() {
  if (null !== dateRange.value && "" !== dateRange.value) {
    queryParams.value.beginTime = dateRange.value[0];
    queryParams.value.endTime = dateRange.value[1];
  }
  loading.value = true;
  listLog(queryParams.value).then(res => {
    if (res.data) {
      logList.value = res.data.records;
      total.value = res.data.total;
    }
    loading.value = false;
  }).catch(() => {
    loading.value = false;
  })
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  dateRange.value = [];
  proxy.resetForm("queryRef");
  handleQuery();
}
getTenantList();
</script>

