
const useInquiryCenterStore = defineStore("inquiryCenter", {
    state:()=>({
        tabs: [],
        activeTab: 'inquiry-center'
    }),
    actions:{
        addTab(view) {
            if (this.tabs.some(v=>v.id===view.id)){
                this.activeTab=view.id
            }else {
                this.tabs.push(view)
                this.activeTab=view.id
            }
        },
        delTab(view) {
            let index=this.tabs.findIndex(v=>v.id===view.id)
            if (index > -1 && this.tabs.length>1){
                this.tabs.splice(index,1)
                this.activeTab=this.tabs[index-1].id
            }
        },
        setActiveTab(id) {
           this.activeTab=id
        },
    }
})

export default useInquiryCenterStore;
