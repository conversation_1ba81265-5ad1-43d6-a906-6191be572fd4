<template>
  <div class="app-container">
    <el-card v-loading="loading"  class="app-card" shadow="never">
      <template #header>
        <span class="card-title">
          <span>小工具管理</span>
          <span>
            <el-button class="exec" link icon="Refresh"  type="primary" @click="refresh">
              刷新
            </el-button>
            <el-button class="exec" link icon="DArrowRight"  type="primary"  @click="openToolsMar" v-hasPermi="['sys:extend:tools:sort-manage']">
              小工具管理
            </el-button>
          </span>
        </span>
      </template>
      <div class="tool" v-if="list[0].apps.length > 0">
        <div
            v-for="item in list[0].apps"
            :key="item.id"
            class="tool-icon"
            :style="`background-color: ${item.remark}20;`"
        >
          <svg-icon
              slot="prefix"
              :icon-class="item.logoTemp"
              class="el-input__icon"
              :style="`height: 30px;width: 30px; color: ${item.remark}`"
          />
          <span>{{ item.name }} </span>
        </div>
        <div
            style="background-color: #9fa2a520;"
            class="tool-icon"
            @click="openTool(list[0])"
            v-hasPermi="['sys:extend:tools:add']"
        >
          <i
              class="icon iconfont icon-xinzeng"
              style="color: #9fa2a5;font-size: 30px;"
          ></i>
        </div>
      </div>
      <el-empty
          v-else
          class="app-empty"
          :image-size="200" >
        <el-button
            type="primary"
            icon="Plus"
            @click="openTool(list[0])"
            v-hasPermi="['sys:extend:tools:add']"
        >添加小工具
        </el-button >
      </el-empty>
    </el-card>
    <el-drawer :with-header="false" v-model="toolsMar" size="70%">
      <el-card class="box-card" v-loading="allLoading">
        <template #header class="clearfix">
          <span>小工具管理</span>
          <el-button
              style="float: right; padding: 3px 0"
              link
              icon="Plus"
              type="primary"
              @click="openTool(list[0])"
              v-hasPermi="['sys:extend:tools:add']"
          >新增小工具
          </el-button
          >
        </template>
        <el-table :data="listAll[0].apps" style="width: 100%">
          <el-table-column label="序号" type="index" width="50">
          </el-table-column>
          <el-table-column property="logoTemp" width="100" label="图标">
            <template #default="scope">
              <span
                  class="tool-icon"
                  :style="`background-color: ${scope.row.remark}20;`"
              >
<!--                 <template #prefix>-->
                   <svg-icon
                       :icon-class="scope.row.logoTemp"
                       class="el-input__icon"
                       :style="
                    `height: 30px;width: 30px; color: ${scope.row.remark}`
                  "
                   />

<!--                </template>-->

              </span>
            </template>
          </el-table-column>
          <el-table-column
              property="name"
              label="小工具名称"
              width="150"
          ></el-table-column>
          <el-table-column
              property="sort"
              label="排序"
              width="50"
          ></el-table-column>
          <el-table-column
              property="url"
              label="url"
              width="150"
          ></el-table-column>
          <el-table-column property="status" label="状态" width="80">
            <template #default="scope">
              <el-switch
                  v-model="scope.row.status"
                  @change="handleToolStatusChange(scope.row)"
              >
              </el-switch>
            </template>
          </el-table-column>
          <el-table-column
              property="createTime"
              label="创建时间"
          ></el-table-column>
          <el-table-column property="remark" label="色值"></el-table-column>
          <el-table-column label="操作" >
            <template #default="scope">
              <el-button type="primary" link @click="editTool(scope.row)" v-hasPermi="['sys:extend:tools:edit']"
              >编辑
              </el-button
              >
              <el-button @click="delTool(scope.row)" type="primary" link v-hasPermi="['sys:extend:tools:remove']"
              >删除
              </el-button
              >
              <el-button
                  @click="authTool(scope.row)"
                  type="primary" link
                  v-hasPermi="['sys:extend:tools:auth']"
              >授权
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </el-drawer>
    <el-drawer
        :title="toolTitle"
        :before-close="closeTool"
        v-model="toolVisible"
        ref="drawerEditRef"
        size="50%"
        destroy-on-close
    >
      <div class="body">
        <el-form :model="toolForm" :rules="toolRules" ref="toolFormRef">
          <el-form-item label="名称" label-width="80px" prop="name">
            <el-input v-model="toolForm.name" autocomplete="off" placeholder="请输入名称"></el-input>
          </el-form-item>
          <el-form-item label="系统地址" label-width="80px" prop="url">
            <el-input v-model="toolForm.url" autocomplete="off" placeholder="请输入系统地址"></el-input>
          </el-form-item>
          <el-form-item label="是否单点" label-width="100px" prop="urlType">
            <el-radio-group v-model="toolForm.urlType">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="图标" label-width="80px" prop="logoTemp">
            <el-popover
                placement="bottom-start"
                :width="460"
                trigger="click"
                @show="resetIconSelect"
            >
              <template #reference>
                <el-input
                    v-model="toolForm.logoTemp"
                    placeholder="点击选择图标"
                    readonly
                >
                  <template #prefix>
                    <svg-icon
                        v-if="toolForm.logoTemp"
                        :icon-class="toolForm.logoTemp"
                        class="el-input__icon"
                        style="height: 32px;width: 16px;"
                    />
                    <i v-else class="el-icon-search el-input__icon"/>
                  </template>
                </el-input>
              </template>
              <template #default>
              <IconSelect ref="iconSelectRef" @selected="selected"/>
              </template>
            </el-popover>
          </el-form-item>
          <el-form-item label="排序" label-width="80px" prop="sort">
            <el-input-number
                :min="1"
                v-model="toolForm.sort"
            ></el-input-number>
          </el-form-item>
          <el-form-item label="颜色" label-width="80px" prop="remark">
            <el-color-picker
                v-model="toolForm.remark"
                :predefine="[
                '#409EFF',
                '#1890ff',
                '#304156',
                '#212121',
                '#11a983',
                '#13c2c2',
                '#6959CD',
                '#f5222d'
              ]"
                class="theme-picker"
                popper-class="theme-picker-dropdown"
            />
          </el-form-item>
        </el-form>
        <div class="foot">
          <el-button @click="closeTool" icon="Close">取 消</el-button>
          <el-button
              type="primary"
              icon="Check"
              @click="toolTitle == '新增小工具' ? saveTool() : updTool()"
              :loading="toolLoading"
          >{{ toolLoading ? "提交中 ..." : "确 定" }}
          </el-button
          >
        </div>
      </div>
    </el-drawer>
    <el-drawer
        title="租户中心"
        V-model="authVisible"
        size="50%"
    >
      <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          style="padding: 10px"
      >
        <el-form-item label="租户名称" prop="tenantName">
          <el-input
              v-model="queryParams.tenantName"
              placeholder="请输入租户名称"
              clearable
              style="width: 240px"
              @keyup.enter="handleTenantQuery"
          />
        </el-form-item>
        <el-form-item label="租户登录名" prop="tenantLoginName">
          <el-input
              v-model="queryParams.tenantLoginName"
              placeholder="请输入租户登录名"
              clearable
              style="width: 240px"
              @keyup.enter="handleTenantQuery"
          />
        </el-form-item>
        <el-form-item label="租户管理员" prop="displayName">
          <el-input
              v-model="queryParams.displayName"
              placeholder="请输入租户管理员"
              clearable
              style="width: 240px"
              @keyup.enter.native="handleTenantQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search"
                     @click="handleTenantQuery"
          >搜索
          </el-button
          >
          <el-button icon="Refresh"  @click="resetTenantQuery"
          >重置
          </el-button
          >
        </el-form-item>
      </el-form>
      <el-table v-loading="authLoading" :data="tenantList" style="padding: 10px"
                @selection-change="selectTenantApp" ref="multipleTableRef">
        <el-table-column type="selection" width="55"/>
        <el-table-column label="租户名称" align="left" prop="tenantName" :show-overflow-tooltip="true" width="80px"/>
        <el-table-column label="租户登录名" align="left" prop="tenantLoginName" :show-overflow-tooltip="true"/>
        <el-table-column label="租户管理员" align="left" prop="displayName" :show-overflow-tooltip="true"/>
        <el-table-column label="有效截止时间" align="left" prop="effectiveDate" :show-overflow-tooltip="true"/>
        <el-table-column label="租户状态" align="left" prop="tenantStatus" :formatter="effectiveStatusFormat"
                         :show-overflow-tooltip="true"/>
      </el-table>
      <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getTenantList"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="addTenantTool" >确 定</el-button>
          <el-button @click="cancelAuth">取 消</el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script setup name="Tool">
import {
  list as listTool,
  listAll as listAllTool,
  addCenter,
  deleteCenter,
  updCenter, authApp,
  selectTenantIds as selectTenant
} from "@/api/extend/application";
import IconSelect from "@/components/IconSelect";
import {page} from "@/api/tenant/tenant";
import {ElMessage, ElMessageBox} from "element-plus";
const {proxy} = getCurrentInstance();
//字典
const {effective_status} = proxy.useDict("effective_status")

//列表
const loading=ref(true)
const list=ref([{apps: []}])
function refresh(){
  getList();
  all();
}
function getList(){
loading.value=true;
  listTool(1).then(res=>{
    list.value=res.data
    loading.value=false
  })
}
//管理
const toolsMar=ref(false)
const listAll=ref([{apps: []}])
const toolTitle=ref("新增小工具")
const toolForm=ref({urlType: 1,
  sort: 1,
  logoTemp: ""})
const toolRules={
  name: [
    {required: true, message: "请输入名称", trigger: "blur"},
    {min: 1, max: 64, message: "最大输入64个字符", trigger: "blur"}
  ],
  url: [
    {required: true, message: "请输入访问地址", trigger: "blur"},
    {min: 1, max: 255, message: "最大输入255个字符", trigger: "blur"}
  ],
  remark: [
    {min: 1, max: 255, message: "最大输入255个字符", trigger: "blur"}
  ]
}
const imageUrl=ref("")
const allLoading=ref(true)
const toolVisible=ref(false)
const toolLoading=ref(false)
const iconSelectRef=ref()
function resetIconSelect(){
  iconSelectRef.value.reset();
}
function all(){
  allLoading.value=true

  //TODO：后台返回缺少urlType属性
  listAllTool(1).then(res=>{
    listAll.value=res.data
    allLoading.value=false
  })
}
function openTool(row){
  toolVisible.value=true
  toolLoading.value=false
  toolTitle.value = "新增小工具"
  toolForm.value={
    urlType: 1,
    sort: 1,
    categoryId: row.id
  }
}
function openToolsMar(){
  toolsMar.value=true
}
function selected(name) {
  toolForm.value={
    ...toolForm.value,
    logoTemp: name
  }
}
function closeTool(){
  toolVisible.value=false;
  toolLoading.value=false
  toolTitle.value = "新增小工具"
  imageUrl.value=""
  toolForm.value={
    urlType: 1,
    sort: 1
  };
}
const toolFormRef=ref()
function saveTool(){
  toolFormRef.value.validate(valid => {
    if (valid){
      toolLoading.value=true;
      let tool=toolForm.value
      addCenter(tool).then(r=>{
        toolLoading.value=false;
        if (r.success){
          ElMessage.success("添加成功")
          closeTool()
          all()
          getList()
          toolVisible.value=false
        }else {
          ElMessage.success("添加失败")
        }
      })
    }
  })
}
function editTool(row){
  toolForm.value=row
  imageUrl.value=row.logoTemp
  toolVisible.value=true
  toolLoading.value=false
  toolTitle.value="修改小工具"
}
function updTool(){
  toolFormRef.value.validate(valid => {
    if (valid){
      toolLoading.value=true;
      let tool=toolForm.value
      updCenter(tool).then(r=>{
        toolLoading.value=false;
        if (r.success){
          ElMessage.success("修改成功")
          closeTool()
          all()
          getList()
          toolVisible.value=false
        }else {
          ElMessage.success("修改失败")
        }
      })
    }
  })

}
function delTool(row){
  ElMessageBox.confirm(
      "此操作将永久删除该工具, 是否继续?", "提示",
      {
        confirmButtonText: "删除",
        cancelButtonText: "取消",
        type: "warning",
        center: true,
      })
      //TODO: 删除有问题
      .then(()=>deleteCenter(row.id))
      .then(()=>{
        ElMessage.success("删除成功")
        all(row.categoryId)
        getList(row.categoryId)
      }).catch(() => {
    ElMessage.info(
        "已取消删除"
    );
  });
}
function handleToolStatusChange(value){
  updCenter({
    id: value.id,
    status: value.status ? false : true
  }).then(res => {
    if (res.success) {
      all(value.categoryId);
      getList(value.categoryId);
    } else {
      ElMessage.success("修改失败")

    }
  });
}

//租户授权
const authVisible=ref(false)
const authLoading=ref(true)
const tenantList=ref([])
const total=ref(0)
const queryParams=ref({
  pageNum: 1,
  pageSize: 10,
  tenantName: undefined,
  tenantLoginName: undefined,
  displayName: undefined,
})
const selectTenantIds=ref([])
const appId=ref()
function authTool(row){
  authVisible.value=true
  appId.value=row.id
  queryTenantIds(row.id);
}
function getTenantList() {
  authLoading.value=true
  page(queryParams.value).then(
      (res)=>{
        tenantList.value=res.data.records
        total.value=res.data.total
        authLoading.value=false
      }
  )
}
function selectTenantApp(selection) {
  selectTenantIds.value = selection.map((item) => item.tenantId);
}
function addTenantTool() {
  let data = {
    appCenterId: appId.value,
    tenantIds: selectTenantIds.value,
  }
  authApp(data).then(res => {
    ElMessage.success("操作成功！")
  })
}
/** 搜索按钮操作 */
function handleTenantQuery() {
  authLoading.value = true;
  queryParams.value.pageNum = 1;
  page(queryParams.value).then(
      (response) => {
        tenantList.value= response.data.records;
        total.value = response.data.total;
        nextTick(() => {
         proxy.$refs.multipleTableRef.clearSelection();
        })
        selectTenant(appId.value).then(res => {
          if (res.data.length > 0) {
            for (let i = 0; i < tenantList.value.length; i++) {
              if (res.data.indexOf(tenantList.value[i].tenantId) !== -1) {
                proxy.$refs.multipleTableRef.toggleRowSelection(tenantList.value[i], true)
              }
            }
            authLoading.value = false;
          } else {
            authLoading.value = false;
          }
        })
      }
  );
}
/** 重置按钮操作 */
function resetTenantQuery() {
  proxy.resetForm("queryForm");
  handleTenantQuery();
}
function queryTenantIds(ids) {
  authLoading.value = true;
  nextTick(() => {
    proxy.$refs.multipleTableRef.clearSelection();
  })
  selectTenant(ids).then(res => {
    if (res.data.length > 0) {
      for (let i = 0; i < tenantList.value.length; i++) {
        if (res.data.indexOf(tenantList.value[i].tenantId) !== -1) {
         proxy.$refs.multipleTableRef.toggleRowSelection(tenantList.value[i], true)
        }
      }
   authLoading.value = false;
    } else {
    authLoading.value = false;
    }
  })
}
function cancelAuth(){
  authVisible.value=false
}
refresh()
getTenantList()
</script>

<style scoped lang="scss">
.app-card {
  margin: 0px 0px 10px 0px;
  min-height: calc(100vh - 200px);
  border: 0px solid #e6ebf5;

.card-title {
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: space-between;

> span {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.icon {
  font-size: 22px;
  color: #419eee;
  -webkit-transition: font-size 0.25s linear, width 0.25s linear;
  -moz-transition: font-size 0.25s linear, width 0.25s linear;
  transition: font-size 0.25s linear, width 0.25s linear;
}

.exec {
  padding: 3px 0;
}
}

.tool {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.app-empty {
  background-color: #f5f9fa;
}
}

.box-card {
  height: 100%;
  width: 100%;
  overflow: auto;
  margin-top: 0;
}

.body {
  margin: 40px;

.foot {
  text-align: center;
  margin-top: 50px;
}
}

.tool-icon {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin: 15px 15px 0 0;
  border-radius: 5px;
  font-size: 14px;
  width: 75px;
  height: 75px;
  font-weight: bold;

.img {
  font-size: 28px;
}

cursor: pointer;
}
</style>
