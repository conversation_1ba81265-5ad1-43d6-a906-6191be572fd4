<template>
  <el-row>
    <el-col :span="24">
      <div class="big-common-box-one" style="  height: 310px;">
        <div class="combination-common-box-one">
          <div class="combination-common-box-one-top">
            <el-row>
              <el-col :span="24">
                <div
                  class="mid-common-box-one"
                  style="display: flex; flex-direction: column;height: 290px;"
                >
                  <div class="header-switch">
                    <div
                      class="header-switch-one"
                      :class="switchIndex == '0' ? 'actived' : ''"
                      @click="changeSwitch('0')"
                    >
                      我的待办（{{ todoTotal }}）
                    </div>
                    <div
                      class="header-switch-one"
                      :class="switchIndex == '1' ? 'actived' : ''"
                      @click="changeSwitch('1')"
                    >
                      我的已办（{{ hasdoTotal }}）
                    </div>
                    <div class="flex-1" />
                    <div class="header-right-btn" @click="goUrl('more')">
                      更多
                    </div>
                  </div>
                  <div
                    v-if="switchIndex == '0' && todoList.length > 0"
                    class="switch-content"
                  >
                    <div
                      v-for="(item, index) of todoList"
                      :key="index"
                      class="switch-content-one"
                      @click="handleProcess(item)"
                    >
                      <div
                        class="switch-header"
                        :title="'【' + item.handleTypeName + '】' + item.title"
                      >
                        【{{ item.handleTypeName }}】{{ item.title }}
                      </div>
                      <!-- <div class="switch-block">{{ item.taskName }}</div> -->
                      <div class="switch-text-one">
                        <svg-icon icon-class="usertodo" />
                        上环节处理人：{{ item.fromUserName }}
                      </div>
                      <div class="switch-text-one">
                        <svg-icon icon-class="timestodo" /> 接收时间：{{
                          item.createTime
                        }}
                      </div>
                    </div>

                    <pagination
                      v-show="todoTotal > 0"
                      :total="todoTotal"
                      v-model:page="paramsTodo1.pageNum"
                      v-model:limit="paramsTodo1.pageSize"
                      @pagination="todoLists"
                    />
                  </div>
                  <div
                    v-if="switchIndex == '0' && todoList.length == 0"
                    class="switch-content empty-content"
                  >
                    <el-empty :image-size="100" />
                  </div>

                  <div
                    v-if="switchIndex == '1' && finishedList.length == 0"
                    class="switch-content empty-content"
                  >
                    <el-empty :image-size="100" />
                  </div>

                  <div
                    v-if="switchIndex == '1' && finishedList.length > 0"
                    class="switch-content"
                  >
                    <div
                      v-for="(item, index) of finishedList"
                      :key="index"
                      class="switch-content-one"
                      @click="handleFlowRecord(item)"
                    >
                      <div class="switch-header" :title="item.title">
                        {{ item.title }}
                      </div>
                      <!-- <div class="switch-block">{{ item.taskName }}</div> -->
                      <div class="switch-text-one">
                        <svg-icon icon-class="usertodo" /> 当前处理人：{{
                          item.assigneeName
                        }}
                      </div>
                      <div class="switch-text-one">
                        <svg-icon icon-class="timestodo" /> 处理时间：{{
                          item.finishTime
                        }}
                      </div>
                    </div>

                    <pagination
                      v-show="hasdoTotal > 0"
                      :total="hasdoTotal"
                      v-model:page="paramsTodo2.pageNum"
                      v-model:limit="paramsTodo2.pageSize"
                      @pagination="finishedLists"
                    />
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </div>
    </el-col>
  </el-row>
</template>




    <script setup>
import { ref } from "vue";
const todoList = ref([

]);
const finishedList = ref([]);
const todoTotal = ref(0);
const hasdoTotal = ref(0);
const switchIndex = ref("0");
const paramsTodo1 = reactive({
  pageNum: 1,
  pageSize: 6,
});

const paramsTodo2 = reactive({
  pageNum: 1,
  pageSize: 6,
});

const todoLists = async () => {
  try {
    // const response = await todoList(paramsTodo1);

    const response = {
      data: {
        total: 4,
        records: [
          {
            id: 1,
            title: "【流程标题】",
            handleTypeName: "待办",
            fromUserName: "张三",
            createTime: "2023-07-01 12:00:00",
            taskName: "任务名称",
          },
          {
            id: 1,
            title: "【流程标题】",
            handleTypeName: "待办",
            fromUserName: "张三",
            createTime: "2023-07-01 12:00:00",
            taskName: "任务名称",
          },
          {
            id: 1,
            title: "【流程标题】",
            handleTypeName: "待办",
            fromUserName: "张三",
            createTime: "2023-07-01 12:00:00",
            taskName: "任务名称",
          },
          {
            id: 2,
            title: "【流程标题】",
            handleTypeName: "待办",
            fromUserName: "张三",
          }
        ]
      }
    };
    todoList.value = response.data.records;
    todoTotal.value = response.data.total;
  } catch (error) {
    console.error(error);
  }
};

const finishedLists = async () => {
  try {
    const response = await finishedList(paramsTodo2);
    finishedList.value = response.data.records;
    hasdoTotal.value = response.data.total;
  } catch (error) {
    console.error(error);
  }
};
const changeSwitch = (index) => {
  switchIndex.value = index;
  if (index == "0") {
    todoLists();
  }
  if (index == "1") {
    finishedLists();
  }
};

// 组件挂载时加载数据
onMounted(() => {
  todoLists();
});
</script>

    <style scoped lang="scss">
    .pagination-container{
      margin-top:0;
    width: 100%;
    }
.big-common-box-one {
  background: #ffffff;
  border-radius: 4px;
  width: 100%;
  padding:0 10px 10px;

  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); // 添加阴影效果
  transition: box-shadow 0.3s ease;
  box-sizing: border-box;

  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2); // 悬停时加深阴影
  }
}

.header-switch {
  display: flex;
  height: 45px;
  line-height: 45px;
  align-items: center;
  position: relative;
  border-bottom: 1px solid #e8eaeb;

  .header-switch-one {
    font-size: 15px;
    text-align: center;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #333333;
    cursor: pointer;
    margin-right: 20px;
    position: relative;
  }

  .actived {
    &::after {
      content: "";
      position: absolute;
      left: 50%;
      bottom: 0px;
      transform: translate(-50%);
      width: 80px;
      height: 3px;
      background: #c20000;
    }
  }

  .header-right-btn {
    font-size: 13px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #9ea4aa;
    cursor: pointer;
  }
}

.switch-content {
  display: flex;
  flex-wrap: wrap;
  flex: 1;
  overflow-y: auto;
  &.empty-content {
    justify-content: center;
    align-items: center;
  }
  .switch-content-one {
    width: calc((100% / 3) - 30px);
    height: 83px;
    background: #ffffff;
    box-shadow: 0px 0px 7px 0px rgba(0, 0, 0, 0.09);
    border-radius: 2px;
    margin-top: 8px;
    margin-right: 15px;
    cursor: pointer;
    box-sizing: border-box;

    &:nth-child(3n) {
      margin-right: 0px;
    }

    padding: 6px 14px;

    .switch-header {
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #333333;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin-bottom: 5px;
    }

    .switch-block {
      height: 22px;
      line-height: 22px;
      background: #eff9ff;
      padding: 0px 10px;
      display: inline-block;
      font-size: 13px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #4f87f1;
      margin-bottom: 9px;
    }

    .switch-text-one {
      font-size: 13px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #9d9fa1;
      margin-bottom: 6px;
      .svg-icon {
        font-size: 15px;
        margin-right: 6px;
      }
      &:last-child {
        margin-bottom: 0px;
      }
    }
  }
}

.combination-common-box-one {
  .combination-common-box-one-bottom {
    height: 50px;
    background: #ffffff;
    border-radius: 4px;
    margin-top: 10px;
  }
}

.mid-common-box-one {
  
  background: #ffffff;
  border-radius: 4px;
  padding: 12px 16px 0;
  padding-top: 0px;
}

.bottom-content {
  margin-top: 10px;
}

.common-header {
  height: 45px;
  line-height: 45px;
  border-bottom: 1px solid #e8eaeb;
  box-sizing: border-box;
  padding-bottom: 0px;

  .left-header-text {
    font-size: 15px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #333333;
  }
}

.common-content {
  height: calc(100% - 45px);
}
</style>
