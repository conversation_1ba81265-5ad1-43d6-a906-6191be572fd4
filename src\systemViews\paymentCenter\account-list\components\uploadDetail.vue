<!-- 账户明细 -->
<template>
  <div class="container-table-box">


    <div class="flex" style="margin-bottom: 12px;" v-if="uoloadStatus=='failed'">
      <div class="flex-1"></div>
      <el-button type="primary" size="mini" icon="Download" @click="handleExport">下载批量开通失败列表
      </el-button>
    </div>
    <public-table ref="publictable" :rowKey="tabelForm.tableKey" :tableData="list" :columns="tabelForm.columns"
      :configFlag="tabelForm.tableConfig" :pageValue="pageParams" :total="total">
    </public-table>
  </div>
</template>

<script setup>
import { ref, reactive, watch, getCurrentInstance } from "vue";
import { ElMessageBox, ElMessage } from "element-plus";
import { screenIndex } from "@/api/paymentCenter/account-list/index";
import { apiUrl } from "@/utils/config";
import useUserStore from "@/store/modules/user";
import { formatMinuteTime } from '@/utils';
const userStore = useUserStore()
const emit = defineEmits(["closeBtn"]);
const { proxy } = getCurrentInstance();
const { activation_status, staff_type } = proxy.useDict(
  "activation_status",
  "staff_type"
);
const props = defineProps({
  detailList: {
    type: Array,
    default: () => [],
  },
  uoloadStatus: {
    type: String,
    default: "",
  },
  redisLock: {
    type: String,
    default: "",
  }
});
const list = ref([]);
const total = ref(0);
const tabelForm = reactive({
  tableKey: "1", //表格key值
  isShowRightToolbar: true, //是否显示右侧显示和隐藏
  showSearch: true,
  // 表格表格数据
  columns: [
    {
      fieldIndex: "staffName", // 对应列内容的字段名
      label: "员工姓名", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "120", //最小宽度%
      width: "", //宽度
    },
    {
      fieldIndex: "loginName", // 对应列内容的字段名
      label: "登录账号", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "120", //最小宽度%
      width: "", //宽度
    },
    {
      fieldIndex: "department", // 对应列内容的字段名
      label: "员工部门", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "120", //最小宽度%
      width: "", //宽度
      align: "left", //表格对齐方式
    },
    {
      fieldIndex: "accountName", // 对应列内容的字段名
      label: "账户名称", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "150", //最小宽度%
      align: "left",
      width: "", //宽度
    },

    {
      fieldIndex: "accountCode", // 对应列内容的字段名
      label: "账户编码", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "120", //最小宽度%
      width: "", //宽度
    },

    {
      fieldIndex: "state", // 对应列内容的字段名
      label: "开通状态", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "120", //最小宽度%
      width: "", //宽度
      type: "dict",
      dictList: activation_status,
    },
  ],
  // 表格基础配置项，看个人需求添加
  tableConfig: {
    needPage: false, // 是否需要分页
    index: true, // 是否需要序号
    selection: false, // 是否需要多选
    reserveSelection: false, // 是否需要支持跨页选择
    indexFixed: false, // 序号列固定 left true right
    selectionFixed: false, //多选列固定left true right
    indexWidth: "50", //序号列宽度
    loading: false, //表格loading
    showSummary: false, //是否开启合计
    height: '620px', //表格固定高度 比如：300px
  },
});

const handleExport = () => {
  proxy.download(
    `pay${apiUrl}/pay/payAccountList/exportErrorData`,
    {
      redisLock: props.redisLock,
    },
    `批量开通导入失败列表_${formatMinuteTime(new Date())}.xlsx`
  );
}
// 保存
const saveBtn = async () => {
  try {

    // 2. 准备符合接口要求的提交数据
    const submitData = props.detailList.map((user) => ({
      accountManageId: user.accountManageId, // 从formData获取账号ID
      staffName: user.staffName,        // 映射staffName -> name
      loginName: user.loginName,      // 映射loginName -> hrCode
      staffId: user.staffId,          // 映射staffId -> id
      cellphone: user.cellphone,       // 映射cellphone -> phone
      department: user.orgName,    // 映射orgName -> department
      type: user.staffType,        // 映射staffType -> type
      unionId: user.unionId,
      payOrder: user.payOrder,
      tenantId: user.tenantId,
      state: user.state,
    }));

    // 3. 调用接口（假设接口接受数组格式）
    const res = await screenIndex.insertBatch(submitData);

    // 4. 处理结果
    if (res.success) {
      ElMessage.success("操作成功");
      emit("closeBtn");
    }
  } catch (error) {
    console.error("保存失败:", error);


  } finally {
  }
};


// 添加 watch 监听 prop 变化
watch(
  () => props.detailList,
  (newVal) => {
    console.log("接收到 detailList:", newVal);
    list.value = newVal;
  },
  { immediate: true }
);

defineExpose({
  saveBtn,
});
</script>

<style scoped>
</style>