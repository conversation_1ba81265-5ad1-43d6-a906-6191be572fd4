<template>
  <div style="padding-bottom: 10px">
    <el-form ref="ruleformRef" :model="staffData">
      <div class="user_info">
        <div class="user_info_img">
          <PreviewImage v-if="staffData.faceImage" :photo-id="staffData.faceImage"></PreviewImage>
          <img v-else style="width: 100%;" src="@/assets/images/face-img.png" />
        </div>
        <div class="user_info_content">
          <div class="user_name">
            <div class="user_name_left">{{ staffData.staffName }}</div>
            <div class="user_ht" v-if="staffData.staffOrgType">
              {{ $formatDictLabel(staffData.staffOrgType, staff_org_type) }}
            </div>

             <div class="user_ht" v-if="staffData.staffType">
              {{ $formatDictLabel(staffData.staffType, staff_type) }}
            </div>
            <div class="user_ht" v-if="staffData.staffTypeName">{{ staffData.staffTypeName }}</div>
          </div>
          <div class="user_fg">
            <img src="@/assets/images/user_fg.png" alt="" />
          </div>
          <div class="user_p_d" v-if="staffData.staffName">
            <div class="user_dept">{{ staffData.staffName }}</div>
            <div class="user_phone">电话：{{ staffData.cellphone }}</div>
          </div>
          <div class="user_one_info" v-if="staffData.loginName">
            <div class="user_one_info_left">登录名称：</div>
            <div class="user_one_info_right">{{ staffData.loginName }}</div>
          </div>

          <div class="user_one_info" v-if="staffData.orgName">
            <div class="user_one_info_left">人员部门：</div>
            <div class="user_one_info_right">{{ staffData.orgName }}</div>
          </div>
        </div>
      </div>
    </el-form>
  </div>
</template>

<script setup>
import { onMounted, ref, getCurrentInstance, watch } from "vue";
import { queryDetail } from '@/api/index/monitor'

// 组件属性
const props = defineProps({
  rowData: {
    type: Object,
    default: () => ({}),
  }
});

const staffData = ref({});
const { proxy } = getCurrentInstance();
const { staff_org_type,staff_type} = proxy.useDict("staff_org_type","staff_type");
const emit = defineEmits(["update:staffData"]);

// 获取人员信息
const getStaffDetail = async (staffId) => {
  if (!staffId) return;
  try {
    const res = await queryDetail({ staffId });
    if (res && res.data) {
      staffData.value = res.data;
      emit("update:staffData", staffData.value);
    }
  } catch (error) {
    console.error("获取人员信息失败:", error);
  }
};

// 监听rowData.staffId变化
watch(() => props.rowData?.staffId, (newVal) => {
  if (newVal) {
    getStaffDetail(newVal);
  } else if (Object.keys(props.rowData).length > 0) {
    // 如果没有staffId但有rowData，直接使用rowData
    staffData.value = props.rowData;
  }
}, { immediate: true });

// 生命周期
onMounted(() => {
  // 如果有rowData.staffId，获取人员信息
  if (props.rowData?.staffId) {
    getStaffDetail(props.rowData.staffId);
  } else if (Object.keys(props.rowData).length > 0) {
    // 如果没有staffId但有rowData，直接使用rowData
    staffData.value = props.rowData;
  }
});
</script>

<style scoped lang="scss">
/* 保持原有样式不变 */


:deep(.user_info_img .el-image) {
  width: 100% !important;
  height: 100% !important;

}

.user_info {
  display: flex;

  .user_info_img {
    width: 170px;
    height: 200px;
    background: url("@/assets/images/user_xk.png") no-repeat center center;
    background-size: 100% 100%;
    padding: 10px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;

  }

  /* 其他样式保持不变... */
  .user_info_content {
    padding-left: 30px;
    flex: 1;

    .user_fg {
      margin-top: 15px;
    }

    .user_name {
      display: flex;
      align-items: center;

      .user_name_left {
        font-size: 20px;
        font-weight: 600;
        color: #333;
        margin-right: 25px;
      }

      .user_ht {
        padding: 0px 15px;
        height: 25px;
        line-height: 25px;
        background: #ffa349;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 20px;
      }
    }

    .user_p_d {
      display: flex;
      align-items: center;
      margin-top: 15px;

      .user_dept {
        min-height: 28px;
        line-height: 28px;
        background: #238ff0;
        padding: 0px 15px;
        color: #fff;
        font-size: 14px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
      }

      .user_phone {
        min-height: 28px;
        line-height: 28px;
        background: #238ff0;
        padding: 0px 15px;
        color: #fff;
        font-size: 14px;
        display: flex;
        align-items: center;
        min-width: 150px;
      }
    }

    .user_one_info {
      margin-top: 15px;
      display: flex;
      align-items: center;

      .user_one_info_left {
        color: #000;
        font-size: 15px;
      }

      .user_one_info_right {
        color: #000;
        font-size: 15px;
        flex: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}
</style>