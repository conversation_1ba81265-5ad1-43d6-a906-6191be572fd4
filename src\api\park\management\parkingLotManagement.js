import request from '@/utils/request'

/**
 * 停车场信息列表
 * @param data
 * @param params
 */
export function parkingLotInfoList(data, params) {
  return request({
    url: "/parking/parkingLotManagement/parkingLotInfoList",
    method: "post",
    data: data,
    params: params
  })
}

/**
 * 停车场信息
 * @param data
 */
export function parkingLotInfo(data) {
  return request({
    url: "/parking/parkingLotManagement/parkingLotInfo",
    method: "post",
    data: data
  })
}

/**
 * 通过停车系统获取停车场列表
 * @param params
 */
export function parkingLotListByParkingSystem() {
  return request({
    url: "/parking/parkingLotManagement/parkingLotListByParkingSystem",
    method: "post"
  })
}

/**
 * 保存停车场信息
 * @param data
 */
export function saveParkingLotInfo(data) {
  return request({
    url: "/parking/parkingLotManagement/saveParkingLotInfo",
    method: "post",
    data: data
  })
}

/**
 * 验证存在停车场
 * @param parkId
 */
export function validHasParkingLot(parkId) {
  return request({
    url: "/parking/parkingLotManagement/validHasParkingLot/" + parkId,
    method: "post"
  })
}

/**
 * 删除停车场信息
 * @param id
 */
export function deleteParkingLotInfo(id) {
  return request({
    url: "/parking/parkingLotManagement/deleteParkingLotInfo/" + id,
    method: "post"
  })
}
