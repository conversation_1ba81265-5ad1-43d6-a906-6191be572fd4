<template>
  <el-dialog
      width="70%"
      :title="modelName"
      v-model="visible"
      append-to-body
      destroy-on-close>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-card class="select-dep-card" shadow="never" destroy-on-close>
          <template #header>
            <span>{{ leftTitle }}</span>
            <div style="float: right; margin-top: -10px">
              <el-input
                  placeholder="请输入角色名称"
                  v-model="queryParams.roleName"
                  style="width: 240px"
                  @keyup.enter.native="reload"
                  clearable>
                <template #append>
                  <el-button
                      icon="Search"
                      @click="reload"
                      :loading="unselectLoading">
                  </el-button>
                </template>
              </el-input>
            </div>
          </template>
          <el-table
              v-if="list.length > 0"
              :data="list"
              style="width: 100%"
              height="500"
              v-loading="unselectLoading"
              row-key="roleId"
              :tree-props="{ children: 'children', hasChildren: 'hasChildren' }">
            <el-table-column :prop="listProp.key" :label="listProp.name">
            </el-table-column>
            <el-table-column fixed="right" width="100" label="操作">
              <template #default="scope">
                <el-button
                    v-if="!scope.row.isOp"
                    @click="add(scope.row)"
                    link
                    icon="ArrowRight">
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-empty
              v-if="list.length <= 0"
              description="暂无数据">
          </el-empty>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="select-dep-card" shadow="never" destroy-on-close>
          <template #header>
            <span>{{ rightTitle }}</span>
            <el-button
                style="float: right; padding: 3px 0"
                link
                icon="Refresh">刷新
            </el-button>
          </template>
          <el-table
              v-if="selected.length > 0"
              :data="selected"
              style="width: 100%"
              height="500"
              v-loading="selectLoading">
            <el-table-column :label="selectedProp.name">
              <template #default="scope">
                <div>
                  <i class="right-table-role"></i>
                  {{ scope.row.roleName }}
                </div>
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" width="100">
              <template #default="scope">
                <a @click="remove(scope.row)" link size="small" style="margin:0">删除</a>
              </template>
            </el-table-column>
          </el-table>
          <el-empty
              v-if="selected.length <= 0"
              description="暂未选择数据">
          </el-empty>
        </el-card>
      </el-col>
    </el-row>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import {Right, UserFilled} from "@element-plus/icons-vue";

const props = defineProps({
  modelName: {
    type: String,
    default: "穿梭框",
  },
  leftTitle: {
    type: String,
    default: "全部",
  },
  rightTitle: {
    type: String,
    default: "已选择",
  },
  selectedProp: {
    type: Object,
    default: {
      key: "name",
      name: "列1",
    },
  },
  selectedId: {
    type: String,
    default: "id",
  },
  listId: {
    type: String,
    default: "id",
  },
  listProp: {
    type: Object,
    default: {
      key: "name",
      name: "列1",
    },
  },
})
const emit = defineEmits()

const unselectLoading = ref(false);
const visible = ref(false);
const queryParams = ref({roleName: undefined});
const selected = ref([]);
const list = ref([]);
const selectLoading = ref(false);
defineExpose({open, reload, close, setRightData, setData, roleNameRenderHeader})

function getList() {
  unselectLoading.value = true;
  selectLoading.value = true;
  emit("loadData", queryParams.value)
}

function setData(data) {
  list.value = Array.isArray(data) ? data : [];
  unselectLoading.value = false;
}

function setRightData(response) {
  selected.value = Array.isArray(response.data) ? response.data : [];
  selectLoading.value = false;
}

function reload() {
  getList();
}

function open() {
  getList();
  visible.value = true;
}

function close() {
  visible.value = false;
}

function add(row) {
  unselectLoading.value = true;
  selectLoading.value = true;
  emit("add", row);
}

function remove(row) {
  unselectLoading.value = true;
  selectLoading.value = true;
  emit("remove", row);
}

function roleNameRenderHeader(h, {column}) {
  return h(
      'div', [
        h('span', column.name),
        h('i', {
          class: 'el-icon-location',
          style: 'color:#409eff;margin-left:5px;'
        })
      ],
  );
}
</script>

<style scoped lang="scss">
.select-dep-card {
  height: 100%;
  overflow-y: auto;
}
</style>
<style>
.el-icon-role {
  background: url('@/assets/images/transferTable/role_mini.png') no-repeat;
}

.el-icon-role:before {
  content: "\8d3a";
  font-size: 14px;
  visibility: hidden;
}
</style>
<style scoped lang="scss">
/*有子节点 且未展开*/
.el-table :deep(.el-table__expand-icon .el-icon-arrow-right:before) {
  background: url('@/assets/images/transferTable/expand.png') no-repeat;
  content: '';
  display: block;
  width: 16px;
  height: 16px;
  font-size: 16px;
  background-size: 16px;
  margin-bottom: -3px;
}

/*有子节点 且已展开*/
.el-table :deep(.el-table__expand-icon--expanded) {
  -webkit-transform: rotate(0);
  transform: rotate(0);
}

.el-table :deep(.el-table__expand-icon--expanded .el-icon-arrow-right:before) {
  background: url('@/assets/images/transferTable/putitaway.png') no-repeat;
  content: '';
  display: block;
  width: 16px;
  height: 16px;
  font-size: 16px;
  background-size: 16px;
  margin-bottom: -3px;
}

/*没有子节点*/
.el-table :deep(.el-table__placeholder)::before {
  background: url('@/assets/images/transferTable/role.png');
  content: '';
  display: block;
  width: 16px;
  height: 16px;
  font-size: 16px;
  background-size: 16px;
  margin-bottom: -2px;
}

.right-table-role {
  background: url('@/assets/images/transferTable/role.png');
  display: block;
  width: 16px;
  height: 16px;
  font-size: 16px;
  background-size: 16px;
  margin-top: 3px;
  float: left;
}
</style>
