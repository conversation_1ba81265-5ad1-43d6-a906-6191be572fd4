<!-- 离散曲
吾已入曲 七八年，枉顾一瞬，似若即若离，离哽在喉；遥想初选，再看若今，似痛若苦，不晓是非。而今已年至，心痛而弱；
遥望前，冰封千里路，心无暖流情，探古今，尔之小难，先人轻声趣谈，吾等之难算了之！
冬之来血，夏之烈日，春之绿意，秋之黄昏，纵使 千万离别，终得一曲人散。 -->

<template>
  <div class="app-container treeLoadHight">
    <Splitpanes class="default-theme">
      <Pane :size="50" :min-size="10">
        <div class="flex">
          <el-card class="dep-card" style="min-height: calc(350px);">
            <div class="common-header">
              <div class="common-header-line"></div>
              <div class="common-header-text">上游平台信息</div>
              <div class="flex-1" />
            </div>
            <div class="dialog-box dialog-box-edit">
              <el-form ref="formRef" :model="formData" label-width="80px" :rules="rules">
                <el-row>
                  <el-col :span="24">
                    <el-form-item label="人员姓名" prop="dataName">
                      <div>
                        {{ formData.dataName || "" }}
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="人员电话" prop="mobile">
                      <div>
                        {{ formData.mobile || "" }}
                      </div>
                    </el-form-item>
                  </el-col>

                  <el-col :span="24">
                    <el-form-item label="人员部门" prop="ancestorsName">
                      <div>
                        {{ formData.ancestorsName || "" }}
                      </div>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </div>

          </el-card>
          <el-icon class="my_right">
            <Right />
          </el-icon>
        </div>

      </Pane>

      <Pane :size="50" :min-size="10">
        <el-card class="dep-card" shadow="never" v-loading="treeLoading">
          <div class="common-header">
            <div class="common-header-line"></div>
            <div class="common-header-text">平台信息</div>
            <div class="flex-1" />
          </div>

          <div class="dialog-box dialog-box-edit">
            <el-form ref="formRef" :model="formData" label-width="80px" :rules="rules">
              <el-row>

                <el-col :span="24">
                  <el-form-item label="人员姓名" prop="dataSysName">
                    <div>
                      {{ formData.dataSysName || "" }}
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="人员电话" prop="dataSysMobile">
                    <div>
                      {{ formData.dataSysMobile || "" }}
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="人员部门" prop="sysAncestorsName">
                    <div>
                      {{ formData.sysAncestorsName || "" }}
                    </div>
                  </el-form-item>
                </el-col>


                <el-col :span="24">
                  <el-form-item label="人员账号" prop="loginName">
                    <!-- 添加或编辑时显示输入框 -->
                    <el-input v-model="formData.loginName" placeholder="请输入人员账号" clearable />
                  </el-form-item>
                </el-col>

                <el-col :span="24">
                  <el-form-item label="人员照片" prop="faceImage">
                    <PreviewImage style="width: 50px;height: 50px;" :photo-id="formData.faceImage"></PreviewImage>
                  </el-form-item>
                </el-col>

              </el-row>
            </el-form>
          </div>
        </el-card>
      </Pane>
    </Splitpanes>

    <!-- <div class="common-header" style="margin-top: 12px">
      <div class="common-header-line"></div>
      <div class="common-header-text">岗位操作</div>
      <div class="flex-1" />
    </div> -->
    <!-- <div class="dialog-box dialog-box-edit">
      <el-form
        ref="formRef2"
        :model="formData"
        label-width="80px"
        :rules="rules"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="岗位类型" prop="staffOrgType">
              <el-radio-group
                :readonly="userData.length > 0"
                v-model="formData.staffOrgType"
              >
                <el-radio
                  v-for="(dict,index) in sync_staff_org_type"
                  :disabled="!formData.sysId && dict.value!='F'"
                  :key="dict.value"
                  :label="dict.value"
                  >{{ dict.label }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-table
        ref="publictable"
        :data="userData"
        border
        :row-class-name="tableRowClassName"
      >
        <el-table-column label="选择" minWidth="20px" align="center">
          <template #default="{ row }">
            <el-checkbox
              :disabled="
                (formData.staffOrgType == 'F' && row.staffOrgType == 'F') ||
                (formData.staffOrgType != 'F' && row.staffOrgType == 'F')
              "
              @change="() => handleCheckboxChange(row)"
              v-model="row.selectDelStatus"
            />
          </template>
</el-table-column>

<el-table-column property="staffName" label="人员姓名" minWidth="60px" align="center"></el-table-column>
<el-table-column property="orgName" label="部门名称" minWidth="120px"></el-table-column>
<el-table-column property="loginName" label="账号名称" minWidth="80px"></el-table-column>
<el-table-column property="staffOrgType" label="岗位类型" align="center" minWidth="60px">
  <template #default="{ row }">
            {{ $formatDictLabel(row.staffOrgType, sync_staff_org_type) }}
          </template>
</el-table-column>
</el-table>
</div> -->
  </div>
</template>

<script setup name="RegionManagement">
import { getWillSyncUserData } from "@/api/system/config";
import { ElMessage } from "element-plus";
// 定义组件 props

const props = defineProps({
  closeBtn: {
    type: Function,

    default: null,
  },

  popupType: {
    type: String,

    default: "",
  },

  rowData: {
    type: Object,
    default: () => ({}),
  },
});

// 表单引用
const formRef = ref(null);
const formRef2 = ref(null);
const userData = ref([]);
// 定义 emits
const emit = defineEmits(["closeBtn"]);

let formData = ref({
  loginName: "",
  staffOrgType: "",
});
const selectAll = ref(false);
const { proxy } = getCurrentInstance();
const { sync_staff_org_type } = proxy.useDict("sync_staff_org_type");
const rules = reactive({
  loginName: [
    { required: true, message: "请输入人员账号", trigger: ["blur", "change"] },
  ],
  // staffOrgType: [
  //   { required: true, message: "请选择岗位类型", trigger: ["blur", "change"] },
  // ],
});
// 接口调用方法
const getWillSyncUserDataFun = async () => {
  try {
    const response = await getWillSyncUserData(props.rowData); // 调用接口
    formData.value = response.data;

    formData.value.loginName = formData.value.loginName
      ? response.data.loginName
      : "";

    userData.value = response.data.staffOrgs.map((item) => ({
      ...item,
      // 自动设置选中状态
      selectDelStatus:
        item.code != formData.value.dataParentId &&
        formData.value.staffOrgType === "F" &&
        item.staffOrgType == "F",
    }));
    updateSelectAll(); // 新增：数据加载后同步全选状态
    console.log(userData.value);
  } catch (error) {
    console.error("获取部门用户树失败:", error);
  } finally {
  }
};

// 修改行样式判断逻辑
const tableRowClassName = ({ row }) => {
  const shouldHighlight =
    (row.code != formData.value.dataParentId &&
      formData.value.staffOrgType === "F" &&
      row.staffOrgType == "F") ||
    row.selectDelStatus;
  return shouldHighlight ? "highlight-row" : "";
};

// 监听岗位类型变化
watch(
  () => formData.value.staffOrgType,
  (newVal) => {
    // 主岗模式自动选中所有非主部门
    if (newVal === "F") {
      userData.value = userData.value.map((item) => ({
        ...item,
        selectDelStatus: item.code != formData.value.dataParentId &&
          formData.value.staffOrgType === "F" &&
          item.staffOrgType == "F",
      }));
    } else {
      // 其他模式清空选中状态
      userData.value = userData.value.map((item) => ({
        ...item,
        selectDelStatus: false,
      }));
    }
    // 自动更新全选状态
    updateSelectAll();
  }
);

// 统一更新全选状态方法
const updateSelectAll = () => {
  selectAll.value = userData.value.every((item) => item.selectDelStatus);
};

// 复选框点击处理
const handleCheckboxChange = (row) => {
  if (formData.value.staffOrgType === "F") {
    // 主岗模式处理逻辑
    if (row.code != formData.value.dataParentId) {
      // // 非主部门行保持强制选中状态
      // row.selectDelStatus = true;
      userData.value = [...userData.value];

      return;
    }
    // 主部门行切换时实现单选逻辑
    const newState = row.selectDelStatus;
    userData.value = userData.value.map((item) => ({
      ...item,
      selectDelStatus:
        item.code === formData.value.dataParentId
          ? newState // 主部门行切换状态
          : item.selectDelStatus, // 非主部门行保持选中
    }));
  } else {
    // 普通模式直接切换状态
    row.selectDelStatus = row.selectDelStatus;
  }

  updateSelectAll();
};

// 全选处理
const handleSelectAll = (val) => {
  userData.value = userData.value.map((item) => {
    if (formData.value.staffOrgType === "F") {
      return {
        ...item,
        // 只影响主部门行，非主部门保持选中
        selectDelStatus: val,
      };
    }

    return { ...item, selectDelStatus: val };
  });

  updateSelectAll();
};
const saveForm = async () => {
  try {
    await formRef.value.validate();
    emit("closeBtn", formData);
  } catch (error) {
    console.error("表单校验失败:", error);
  }
};

onMounted(() => {
  getWillSyncUserDataFun();
});
defineExpose({
  saveForm,
});
</script>

<style scoped lang="scss">
.common-header {
  margin-bottom: 10px;
}

/* 如果 scoped 样式不生效，可以去掉 scoped 或使用深度选择器 */
:deep(.el-table .highlight-row) {
  position: relative;

  &::after {
    content: "";
    display: block;
    position: absolute;
    height: 1px;
    width: 100%;
    left: 0px;
    top: 50%;
    background: red;
  }

  :hover {
    background-color: transparent !important;
  }

  /* 或者你需要的其他样式 */
}

:deep(.el-table--enable-row-hover .el-table__body tr.highlight-row:hover > td.el-table__cell) {
  background-color: transparent !important;
}

.my_right {
  font-size: 35px;
  font-weight: 800;
  display: flex;
  align-items: center;
  min-height: 350px;
  color: #c20000;
  background: #fff;
}
</style>
