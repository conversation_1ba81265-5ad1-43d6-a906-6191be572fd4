import request from '@/utils/request'
import { apiUrl } from '@/utils/config'; 



export class screenIndex {

    // 查询结算员列表
    static settlerList(data,params) {
      return request({
        url: `pay${apiUrl}/pay/settleManage/settlerList`,
        method: 'post',
        data: {...data,...params},
      }) 
    }
    // 查询结算审核员列表
    static auditorList(data,params) {
      return request({
        url: `pay${apiUrl}/pay/settleManage/auditorList`,
        method: 'post',
        data: {...data,...params},
      })
    }
    // 新增结算员
    static addSettler(data) {
      return request({
        url: `pay${apiUrl}/pay/settleManage/addSettler`,
        method: 'post',
        data: data,
      })
    }
    // 新增结算审核员
    static addAuditor(data) {
      return request({
        url: `pay${apiUrl}/pay/settleManage/addAuditor`,
        method: 'post',
        data: data,
      })
    }
    // 编辑结算员
    static editSettler(data) {
      return request({
        url: `pay${apiUrl}/pay/settleManage/editSettler`,
        method: 'post',
        data: data,
      })
    }
    // 编辑结算审核员
    static editAuditor(data) {
      return request({
        url: `pay${apiUrl}/pay/settleManage/editAuditor`,
        method: 'post',
        data: data,
      })
    }

// 人员选择
static selectUserList(data) {
    return request({
      url: `pay${apiUrl}/pay/settleManage/selectUserList` ,
      method: 'post',
      data
    })
  }

  //   供应商列表

  static supplierPageList(data) {
    return request({
      url: `pay${apiUrl}/pay/settleManage/providerList`,
      method: 'post',
      data:data
    })
  } 
//   删除结算管理信息
static deleteSettleManage(data) {
  return request({
    url: `pay${apiUrl}/pay/settleManage/delete`,
    method: 'post',
    data:data
  })
}
}