<template>
  <el-card :class="bgColor === true ? 'notice-buisness-card' : 'unshow-notice-buisness-card'" v-loading="loading" shadow="never">
    <template #header v-if="showTitle">
      <span class="card-title">
        <div class="title">
          <div><el-icon class="icon iconfont icon-yewudongtai"></el-icon> 统计数据</div>
        </div>
      </span>
    </template>
    <div class="home-card-body">
      <div
          ref="browser"
          style="width: 500px; height: 400px; display: inline-block"
      ></div>
      <div
          ref="os"
          style="width: 500px; height: 400px; display: inline-block"
      ></div>
    </div>
  </el-card>
</template>

<script setup name="AreaChart">
import useUserStore from "@/store/modules/user";
import {getBrowserTotal, getOsTotal} from "@/api/operation/log";

const {proxy} = getCurrentInstance();
const userStore = useUserStore();
const props = defineProps({
  showTitle: Boolean,
  bgColor: Boolean
})

const tenantId = ref(userStore.userInfo.customParam.tenantId);
const loading = ref(false);
const browserList = ref([]);
const osList = ref([]);

const getBrowser = () => {
  const data = {
    tenantId: tenantId.value,
    browser: "browser",
  };
  getBrowserTotal(data).then((res) => {
    browserList.value = res.data;
    nextTick(() => {
      getBrowserEcharts();
    });
  });
}

const getOs = () => {
  const data = {
    tenantId: tenantId.value,
    os: "os",
  };
  getOsTotal(data).then((res) => {
    osList.value = res.data;
    nextTick(() => {
      getOsEcharts();
    });
  });
}

function getBrowserEcharts() {
  let browserEchart = proxy.$echarts.init(
      proxy.$refs.browser,
      "walden"
  );
  //配置图表
  let option = {
    title: {
      text: "用户浏览器占比统计",
      left: "center",
    },
    tooltip: {
      trigger: "item",
    },
    legend: {
      orient: "vertical",
      bottom: "bottom",
    },
    series: [
      {
        name: "浏览器",
        type: "pie",
        radius: "50%",
        data: browserList.value,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)",
          },
        },
      },
    ],
  }
  browserEchart.setOption(option);
}

function getOsEcharts() {
  let OsEchart = proxy.$echarts.init(
      proxy.$refs.os,
      "walden"
  );
  //配置图表
  let option = {
    title: {
      text: "用户操作系统占比统计",
      left: "center",
    },
    tooltip: {
      trigger: "item",
    },
    legend: {
      orient: "vertical",
      bottom: "bottom",
    },
    series: [
      {
        name: "操作系统",
        type: "pie",
        radius: "50%",
        data: osList.value,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)",
          },
        },
      },
    ],
  };
  OsEchart.setOption(option);
}

getBrowser();
getOs();
</script>

<style lang="scss" scoped>
.notice-buisness-card {
  height: 100%;
  background: #fff;
  .home-card-body {
    display: flex;
    justify-content:space-around;
  }
}
.unshow-notice-buisness-card {
  height: 100%;
  background: #f5f9fa;
  border: 0px;
}
</style>
