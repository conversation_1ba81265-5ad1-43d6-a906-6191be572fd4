<!-- 账户明细 -->
<template>
  <div class="container-table-box-p">
    <Splitpanes class="default-theme">
      <Pane :size="100" :min-size="65">
        <el-card class="dep-card">
          <dialog-search
            @getList="getList"
            formRowNumber="3"
            :weightChange="true"
            :columns="tabelForm.columns"
          >
            <template v-slot:formList>
              <el-form
                :model="queryParams"
                ref="queryForm"
                :inline="true"
                label-width="75px"
              >
                <el-form-item label="流水编号">
                  <el-input
                    v-model="queryParams.flowsCode"
                    placeholder="请输入流水编号"
                    clearable
                  ></el-input>
                </el-form-item>

                <el-form-item label="流水类型" prop="flowType">
                  <el-select
                    v-model="queryParams.flowType"
                    placeholder="请选择流水类型"
                    clearable
                  >
                    <el-option
                      v-for="(item, index) of flow_type"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>

                <el-form-item label="时间范围" prop="timeRange">
                  <el-date-picker
                    v-model="timeRange"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    value-format="YYYY-MM-DD"
                    clearable
                  />
                </el-form-item>
              </el-form>
            </template>
            <template v-slot:searchList>
              <el-button type="primary" icon="Search" @click="handleQuery"
                >搜索</el-button
              ><el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </template>

            <template v-slot:searchBtnList>
              <el-button
              v-hasPermi="['pay:accountList:detail:export']"
                type="primary"
                size="mini"
                icon="Download"
                @click="handleExport"
                >导出
              </el-button>
            </template>
          </dialog-search>

          <public-table
            ref="publictable"
            :rowKey="tabelForm.tableKey"
            :tableData="list"
            :columns="tabelForm.columns"
            :configFlag="tabelForm.tableConfig"
            :pageValue="pageParams"
            :total="total"
            :getList="getList"
          >
          </public-table>
        </el-card>
      </Pane>
    </Splitpanes>
  </div>
</template>
      
      <script setup>
import { ElMessage, ElMessageBox } from "element-plus";
import { ref, reactive, getCurrentInstance } from "vue";
import { screenIndex } from "@/api/paymentCenter/account-detail/index";
import { apiUrl } from "@/utils/config";
import { formatMinuteTime } from "@/utils";
const { proxy } = getCurrentInstance();
const { flow_type } = proxy.useDict("flow_type");
const props = defineProps({
  closeBtn: {
    type: Function,
    default: () => {},
  },
  popupType: {
    type: String,
    default: "",
  },
  rowData: {
    type: Object,
    default: () => ({}),
  },
});
const pageParams = ref({
  pageNum: 1,
  pageSize: 10,
});
const queryParams = ref({
  staffId: "",
  accountListId: "",
  flowsCode: "",
  flowType: "",
  startTime: "", // 新增
  endTime: "", // 新增
});
const timeRange = ref([]); // 新增
const list = ref([{}]);
const total = ref(0);
const tabelForm = reactive({
  tableKey: "1", //表格key值
  isShowRightToolbar: true, //是否显示右侧显示和隐藏
  showSearch: true,
  // 表格表格数据
  columns: [
    {
      fieldIndex: "flowsCode", // 对应列内容的字段名
      label: "流水编号", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "150", //最小宽度%
      width: "", //宽度
    },
   
    {
      fieldIndex: "flowType", // 对应列内容的字段名
      label: "流水类型", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "120", //最小宽度%
      width: "", //宽度
      type: "dict",
      dictList: flow_type,
    },

    {
      fieldIndex: "balance", // 对应列内容的字段名
      label: "消费前金额(元)", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "140", //最小宽度%
      width: "", //宽度
      type: "dollor",
    },

    {
      fieldIndex: "spendingBalance", // 对应列内容的字段名
      label: "消费金额(元)", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "130", //最小宽度%
      width: "", //宽度
      type: "dollor",
    },
    {
      fieldIndex: "amount", // 对应列内容的字段名
      label: "消费后金额(元)", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "140", //最小宽度%
      width: "", //宽度
      type: "dollor",
    },
    {
      fieldIndex: "paymentTime", // 对应列内容的字段名
      label: "支付时间", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "150", //最小宽度%
      width: "", //宽度
    },

    // {
    //   fieldIndex: "operator", // 对应列内容的字段名
    //   label: "操作人", // 显示的标题
    //   resizable: true, // 对应列是否可以通过拖动改变宽度
    //   visible: true, // 展示与隐藏
    //   sortable: true, // 对应列是否可以排序
    //   fixed: "", //固定
    //   minWidth: "120", //最小宽度%
    //   width: "", //宽度
    // },
  ],
  // 表格基础配置项，看个人需求添加
  tableConfig: {
    needPage: true, // 是否需要分页
    index: true, // 是否需要序号
    selection: false, // 是否需要多选
    reserveSelection: false, // 是否需要支持跨页选择
    indexFixed: false, // 序号列固定 left true right
    selectionFixed: false, //多选列固定left true right
    indexWidth: "50", //序号列宽度
    loading: false, //表格loading
    showSummary: false, //是否开启合计
    height: null, //表格固定高度 比如：300px
  },
});

/** 查询列表 */
const getList = () => {
  tabelForm.tableConfig.loading = true;
  const params = {
    ...queryParams.value,
    startTime: timeRange.value[0] || "",
    endTime: timeRange.value[1] || "",
  };
  screenIndex.pageList(params, pageParams.value).then((response) => {
    list.value = response.data.records;
    total.value = response.data.total;
    tabelForm.tableConfig.loading = false;
  });
};

/** 搜索按钮操作 */
const handleQuery = () => {
  pageParams.value.pageNum = 1;
  getList();
};
/** 重置按钮操作 */
const resetQuery = () => {
  proxy.resetForm("queryForm");
  timeRange.value = [];  // 添加这行重置时间范围
  queryParams.value = {
    staffId:props.rowData.staffId,
    accountListId:props.rowData.id
  };
  handleQuery();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy.download(
    `pay${apiUrl}/pay/payAccountList/detailsExport`,
    {
      ...queryParams.value,
    },
    `账号明细列表_${formatMinuteTime(new Date())}.xlsx`
  );
};

onMounted(() => {
  queryParams.value.staffId = props.rowData.staffId
  queryParams.value.accountListId = props.rowData.id
  getList();
});
</script>
      
      <style scoped>
</style>