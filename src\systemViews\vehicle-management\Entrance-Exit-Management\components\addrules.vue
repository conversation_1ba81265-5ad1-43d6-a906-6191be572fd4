<!-- 停车规则 -->
<template>
  <div class="dialog-box" :class="popupType === 'view' ? 'dialog-box-view' : 'dialog-box-edit'">
    <el-form ref="formRef" :model="form" :rules="popupType !== 'view' ? rules : {}" label-width="80px">
      <el-row>
        <!-- 规则名称 -->
        <el-col :span="24">
          <el-form-item label="规则名称" prop="ruleName">
            <el-select
              v-if="popupType !== 'view'"
              v-model="form.parkingRuleId"
              collapse-tags
              placeholder="请选择规则名称"
              clearable
              filterable
              @change="selectRuleInfo"
            >
              <el-option
                v-for="(item, index) in ruleList"
                :key="index"
                :label="item.ruleName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <!-- 适用类型 -->
        <el-col :span="24">
          <el-form-item label="适用类型" prop="applyType">
            <el-select disabled v-if="popupType !== 'view'" v-model="form.applyType" placeholder="请选择规则">
              <el-option
                v-for="dict in dict.type.parking_rule_apply_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <!-- 车辆类型 -->
        <el-col :span="24">
          <el-form-item label="车辆类型" prop="vehicleType">
            <el-select disabled v-if="popupType !== 'view'" v-model="form.vehicleType" placeholder="请选择规则">
              <el-option
                v-for="dict in dict.type.parking_rule_vehicle_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <!-- 车牌号码 -->
        <el-col :span="24">
          <el-form-item label="车牌号码" prop="plateNumbers">
            <el-input
              disabled
              v-if="popupType !== 'view'"
              v-model="form.plateNumbers"
              placeholder=""
              maxlength="30"
            />
          </el-form-item>
        </el-col>

        <!-- 进出类型 -->
        <el-col :span="24">
          <el-form-item label="进出类型" prop="entryExitType">
            <el-select disabled v-if="popupType !== 'view'" v-model="form.entryExitType" placeholder="请选择规则">
              <el-option
                v-for="dict in dict.type.entry_exit_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
            <div v-if="popupType === 'view'">
              {{ formatDict(form.entryExitType, dict.type.entry_exit_type) }}
            </div>
          </el-form-item>
        </el-col>

        <!-- 通过类型 -->
        <el-col :span="24">
          <el-form-item label="通过类型" prop="restrictionType">
            <el-select disabled v-if="popupType !== 'view'" v-model="form.restrictionType" placeholder="请选择规则">
              <el-option
                v-for="dict in dict.type.parking_rule_restriction_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
            <div v-if="popupType === 'view'">
              {{ formatDict(form.restrictionType, dict.type.parking_rule_restriction_type) }}
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <!-- 操作按钮 -->
    <div class="dialog-footer" v-if="popupType !== 'view'">
      <div class="flex-1"></div>
      <div class="dialog-footer-btn">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted,getCurrentInstance } from 'vue'
import { findParkinRules } from '@/api/majorParking/parkinglot'

// 定义 props
const props = defineProps({
  rowData: {
    type: Object,
    default: () => ({})
  },
  popupType: {
    type: String,
    default: ''
  }
})

// 字典数据
const { proxy } = getCurrentInstance();
const {
  parking_rule_apply_type,
  parking_rule_vehicle_type,
  entry_exit_type,
  parking_rule_restriction_type
} = proxy.useDict(
  "parking_rule_apply_type",
  "parking_rule_vehicle_type",
  "entry_exit_type",
  "parking_rule_restriction_type"
);

// 定义 emits
const emit = defineEmits(['submitClose', 'cancelClose'])

// 表单引用
const formRef = ref(null)

// 响应式数据
const form = ref({})
const ruleList = ref([])

// 表单校验规则
const rules = ref({
  parkingRuleId: [{ required: true, message: '请选择规则', trigger: 'blur' }]
})

// 字典格式化方法（替代过滤器）
const formatDict = (value, dictType) => {
  const item = dictType.find(item => item.value === value)
  return item ? item.label : ''
}

// 选择规则处理
const selectRuleInfo = () => {
  const selectedRule = ruleList.value.find(item => item.id === form.value.parkingRuleId)
  if (selectedRule) {
    form.value = {
      ...form.value,
      ruleName: selectedRule.ruleName,
      applyType: selectedRule.applyType,
      vehicleType: selectedRule.vehicleType,
      plateNumbers: selectedRule.plateNumbers,
      entryExitType: selectedRule.entryExitType,
      restrictionType: selectedRule.restrictionType
    }
  }
}

// 获取规则列表
const findRules = async () => {
  try {
    const res = await findParkinRules({ parkingId: props.rowData.id })
    ruleList.value = res.data
  } catch (error) {
    console.error('获取规则列表失败:', error)
  }
}

// 提交表单
const submitForm = async () => {
  try {
    const valid = await formRef.value.validate()
    if (valid) {
      emit('submitClose', { ...form.value, parkingId: props.rowData.id })
    }
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 取消操作
const cancel = () => {
  emit('cancelClose')
}

// 组件挂载时加载数据
onMounted(() => {
  findRules()
})
</script>

<style scoped>

</style>