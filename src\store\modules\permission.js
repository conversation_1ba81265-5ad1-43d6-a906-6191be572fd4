import auth from "@/plugins/auth";
import router, { dynamicRoutes } from "@/router";
import { getRouters } from "@/api/login";
import { handleTree } from "@/utils/common";
import Layout from "@/layout";
import PortalContainer from "@/portal";
import PortalIframe from "@/portal/PortalIframe";
import DesignPage from "@/grid/designPage";
import config from '@/utils/config'
// 匹配views里面所有的.vue文件
const modules = import.meta.glob(["@/portalViews/**/*.vue", "@/systemViews/**/*.vue"]);

const iframeReg = /^(https|http|inner):\/\//;

const designPageReg = /^pageView:\/\/\d+/;

const usePermissionStore = defineStore("permission", {
  state: () => ({
    portalMenus: [],
    portalName: "",
    systemMenus: [],
    systemName: "",
  }),
  actions: {
    generateDynamicRoutes() {
      dynamicRoutes.forEach((route) => {
        if (route.permissions && auth.hasPermiOr(route.permissions)) {
          router.addRoute(route);
        }
      });
    },
    generatePortalRoutes() {
      return new Promise((resolve, reject) => {
        getRouters({ permissionScope: "portal" })
          .then((res) => {
            if (res.success) {
              const menutree = getMenutree(res.data);
              if (menutree[0]) {
                this.portalMenus = generateRouteMenus("portal", menutree[0].children, "/portal");
                this.portalName = menutree[0].permissionName;
              }
            }
            resolve();
          })
          .catch((error) => {
            console.log(error);
            resolve();
          });
      });
    },
    generateSystemRoutes() {
      return new Promise((resolve, reject) => {
        getRouters({ permissionScope: "system" })
          .then((res) => {
            if (res.success) {
              const menutree = getMenutree(res.data);
              if (menutree[0]) {
                this.systemMenus = generateRouteMenus("system", menutree[0].children, "/system");
                // this.systemName = menutree[0].permissionName;
                this.systemName = config.siteName
              }
            }
            resolve();
          })
          .catch((error) => {
            console.log(error);
            resolve();
          });
      });
    },
  },
});

function getMenutree(menus) {
  if (!menus || !menus.length) {
    return [];
  }
  const routeMenus = menus.filter((menu) => menu.permissionType !== "operation");
  const menusTree = handleTree(routeMenus, "permissionId");
  return menusTree;
}

function generateRouteMenus(permissionScope, menus, routerPath, baseIndexPath = []) {
  if (!menus || !menus.length) {
    return [];
  }
  return menus.map((menu, index) => {
    //uri 路由 code 组件地址
    const { uri, code, permissionName, openType } = menu;

    const path = `${routerPath}/${uri}`;

    const indexPath = [...baseIndexPath, index];

    let feedback;

    if (designPageReg.test(code)) {
      // 处理设计页面
      feedback = "view";
      const meta = { title: permissionName, designId: code.replace(/^pageView:\/\//, ""), indexPath };
      addNestedRoute(permissionScope, path, DesignPage, meta);
    } else if (iframeReg.test(code)) {
      // ... 处理 HTTP 路径的逻辑
      feedback = openType === "blank" ? "blank" : "view";
      if (feedback === "view") {
        const meta = { title: permissionName, link: code.replace(/^inner:\/\//, ""), indexPath };
        let iframeView = null;
        if (permissionScope === "portal") {
          iframeView = PortalIframe;
        }
        addNestedRoute(permissionScope, path, iframeView, meta);
      }
    } else if (code) {
      const view = loadView(code);
      if (view) {
        feedback = "view";
        const meta = { title: permissionName, indexPath };
        addNestedRoute(permissionScope, path, view, meta);
      }
    }
    return {
      ...menu,
      children: generateRouteMenus(permissionScope, menu.children, path, indexPath),
      path,
      feedback,
    };
  });
}

function addNestedRoute(permissionScope, routePath, component, meta) {
  let container = Layout;
  if (permissionScope === "portal") {
    container = PortalContainer;
  }
  if (!router.hasRoute(routePath)) {
    let route = {
      path: routePath,
      component: container,
      name: routePath,
      meta
    }
    if (component) {
      route = {
        path: routePath,
        component: container,
        children: [
          {
            path: "",
            component,
            name: routePath,
            meta,
          },
        ],
      };
    }
    router.addRoute(route);
  }
}

function loadView(path) {
  const file = `/src${path}.vue`;
  const indexFile = `/src${path}/index.vue`;
  if (modules[file]) {
    return () => modules[file]();
  }
  if (modules[indexFile]) {
    return () => modules[indexFile]();
  }
}

export default usePermissionStore;
