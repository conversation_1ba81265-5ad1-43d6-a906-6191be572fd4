import request from "@/utils/request";

// 获取公钥
export function getPublicKey() {
  return request({
    url: "/auth/publicKey",
    headers: {
      isToken: false,
    },
    method: "get",
  });
}

// 登录方法
export function login(dataForm) {
  return request({
    url: "/auth/login",
    headers: {
      isToken: false,
      "Content-Type": "multipart/form-data",
    },
    method: "post",
    data: dataForm,
  });
}

// 获取用户详细信息
export function getInfo(params) {
  return request({
    url: "/auth/oauth/check_token",
    method: "get",
    params,
  });
}

export function getRouters(params) {
  return request({
    url: "/user/permissions/findAllMenuVoList",
    method: "get",
    params,
  });
}

// 退出方法
export function logout() {
  return request({
    url: "/auth/logout",
    method: "post",
  });
}

//获取动态配置数据
export function configData(data) {
  return request({
    url: '/user/common/clientConfig',
    method: 'post',
    data: data
  })
}
