import request from '@/utils/request'
import { apiUrl } from '@/utils/config'; 



export class screenIndex {
//账户设置列表
  static payAccountTypePageList(data,params) {
    return request({
      url: `pay${apiUrl}/pay/PayAccountManage/accountTypeTree`,
      method: 'post',
      data: {...data,...params},
    })
  }
//账户设置列表
  static findPage(data,params) {
    return request({
      url: `pay${apiUrl}/pay/PayAccountManage/pageList`,
      method: 'post',
      data: {...data,...params},
    })
  }

// 删除
static PayAccountManageDelete(data) {
    return request({
      url: `pay${apiUrl}/pay/PayAccountManage/delete`,
      method: 'post',
      data
    })
  }

// 查询

static PayAccountManageGet(data) {
    return request({
      url: `pay${apiUrl}/pay/PayAccountManage/detail`,
      method: 'post',
      data
    })
  }
// 添加
  static PayAccountManageAdd(data) {
    return request({
      url: `pay${apiUrl}/pay/PayAccountManage/add`,
      method: 'post',
      data
    })
  }

//修改
static PayAccountManageUpdate(data) {
    return request({
      url: `pay${apiUrl}/pay/PayAccountManage/update`,
      method: 'post',
      data
    })
  }

  // 管理员选择

  static selectUserList(data) {
    return request({
      url: `pay${apiUrl}/pay/payAccountList/selectUserList`,
      method: 'post',
      data
    })
  }
  // 供应商
  static payProviderPageList(param,data) {
    return request({
      url: `pay${apiUrl}/pay/PayAccountManage/providerTree`,
      method: 'post',
      data: {...param,...data}
    })
  }
  static juniorList(param) {
    return request({
      url: `/user/tenants/juniorList`,
      method: 'get',
      data:param
    })
  }

  
}