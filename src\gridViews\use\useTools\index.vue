<template>
  <el-card :class="bgColor === true ?'tool-card':'unshow-tool-card'" v-loading="loading" shadow="never" >
    <template #header v-if="showTitle">
      <span class="card-title" >
        <div class="title">
          <span > <el-icon class="icon iconfont icon-gongju"></el-icon> 小工具 </span>
        </div>
      </span>
    </template>
    <el-empty v-if="tools.length <= 0 && !loading" :image-size="100"/>
    <div v-if="tools.length > 0" class="tool home-card-body">
      <div
          v-for="item in tools"
          :key="item.id"
          :style="`background-color: ${item.remark}20;`"
          class="tool-item"
          @click="toApp(item.url)"
      >
        <svg-icon
            slot="prefix"
            :icon-class="item.logoTemp"
            class="el-input__icon"
            :style="`height: 30px;width: 30px; color: ${item.remark}`"
        />
        <span>{{ item.name }}</span>
      </div>

    </div>
  </el-card>
</template>

<script setup name="UseTools">
import {useRouter} from "vue-router";
import {selectTenantApps} from "@/api/grid/useAppCenter";

const props = defineProps({
  showTitle: Boolean,
  bgColor: Boolean
})

const loading = ref(false)
const tools = ref([])
const router = useRouter();

const toApp = (url) => {
  const usrs = url.split("?");
  console.log(`${usrs[0]}?${usrs.length > 1 ? usrs[1] + "&" : ""}`);
  router.push(
      `${usrs[0]}`
  );
}

function getTools() {
  loading.value = true;
  selectTenantApps("1").then((res) => {
    if (res.data.length > 0) {
      tools.value = res.data[0].apps;
      loading.value = false;
    } else {
      loading.value = false;
    }
  });
}

getTools();
</script>

<style scoped lang="scss">
.tool-card {
  height: 100%;
  background: #fff;
  overflow: auto;
  :deep(.el-card__body) {
    // padding: 0px 20px 35px 20px;
  }
  .home-card-body {
    padding-top: 20px;
  }
  .card-title {
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .icon {
      font-size: 18px;
      color: #419eee;
      padding: 0px 5px;
      -webkit-transition: font-size 0.25s linear, width 0.25s linear;
      -moz-transition: font-size 0.25s linear, width 0.25s linear;
      transition: font-size 0.25s linear, width 0.25s linear;
    }

    .exec {
      padding: 3px 0;
    }
  }

  .tool {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    justify-content: space-around;
    overflow-y: auto;
    height: 100%;

    .tool-item {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      margin: 10px 0 0 0;
      border-radius: 5px;
      width: 70px;
      height: 70px;
      font-size: 14px;
      font-weight: bold;
      cursor: pointer;
    }
  }
}
.unshow-tool-card {
  border: 0px;
  height: 100%;
  background: #fff;

  :deep(.el-card__body) {
    // padding: 0px 20px 35px 20px;
  }
  .home-card-body {
    padding-top: 20px;
  }
  .card-title {
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .icon {
      font-size: 18px;
      color: #419eee;
      padding: 0px 5px;
      -webkit-transition: font-size 0.25s linear, width 0.25s linear;
      -moz-transition: font-size 0.25s linear, width 0.25s linear;
      transition: font-size 0.25s linear, width 0.25s linear;
    }

    .exec {
      padding: 3px 0;
    }
  }

  .tool {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    justify-content: space-around;
    overflow-y: auto;
    height: 100%;

    .tool-item {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      margin: 10px 0 0 0;
      border-radius: 5px;
      width: 70px;
      height: 70px;
      font-size: 14px;
      font-weight: bold;
      cursor: pointer;
    }
  }
}
</style>
