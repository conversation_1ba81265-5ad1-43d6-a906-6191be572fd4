// 停车订单
import request from '@/utils/request'
import { apiUrl } from '@/utils/config'; 
export class screenIndex {
  static findParkOrdersPage(params,data) {
    return request({
      url: `park${apiUrl}/majorParking/parkOrders/findParkOrdersPage`,
      method: "post",
      data: {...data,...params},
      params: params
    })
  }

  
//停车场查询
static findparkinglot(param,data) {
    return request({
      url: `park${apiUrl}/majorParking/parkingLot/findparkinglot`,
      method: 'post',
      data:{...data,...param},
      params: param
    })
  }
  
  static getFileInfoById(id) {
    return request({
        url: `park${apiUrl}/majorParking/parkRecords/getFileInfoById/${id}`,
        method: 'get',
    })
  }
  
  

}
