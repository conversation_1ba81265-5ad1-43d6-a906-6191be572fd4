import request from "@/utils/request";

// 查询角色树列表
export function findRoleListByScope(params) {
  return request({
    url: "/user/roles/list",
    method: "get",
    params: params
  });
}

// 查询角色详细
export function getRole(roleId) {
  return request({
    url: "/user/roles/" + roleId,
    method: "get"
  });
}

// 新增角色
export function addRole(data) {
  return request({
    url: "/user/roles",
    method: "post",
    data
  });
}

// 修改角色
export function updateRole(data) {
  return request({
    url: "/user/roles/edit",
    method: "post",
    data: data
  });
}

// 角色数据权限
export function dataScope(data) {
  return request({
    url: "/system/role/dataScope",
    method: "put",
    data: data
  });
}

// 角色状态修改
export function changeRoleStatus(roleId, status) {
  const data = {
    roleId,
    status
  };
  return request({
    url: "/system/role/changeStatus",
    method: "put",
    data: data
  });
}

// 删除角色
export function delRole(data) {
  return request({
    url: "/user/roles/delete",
    method: "post",
    data
  });
}

export function queryRoleMenu(roleId) {
  return request({
    url: "/user/roles/" + roleId +"/permissions",
    method: "get"
  });
}

export function get(params) {
  return request({
    url: "/user/roles/get",
    method: "get",
    params
  });
}

export function findByOrgName(data) {
  return request({
    url: "/user/orgs/findByOrgName",
    method: "post",
    data
  });
}

