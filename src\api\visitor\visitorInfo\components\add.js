import request from '@/utils/request'

//修改访客信息
export function updateVisitorInfo(data) {
  return request({
    url: '/visitor/visitorInfo/update',
    method: 'post',
    data: data,
  })
}

//新增访客信息
export function insertVisitorInfo(data) {
  return request({
    url: '/visitor/visitorInfo/insert',
    method: 'post',
    data: data,
  })
}

//根据电话查询访客信息
export function findVisitorInfoByTel(params) {
  return request({
    url: '/visitor/visitorInfo/tel/' + params,
    method: 'get',
  })
}

