<template>
    <el-select
      v-model="selectedValue"
      remote
      :remote-method="handleRemoteSearch"
      :loading="loading"
      @change="handleChange"
      placeholder="请选择租户"
    >
    
      <el-option
        v-for="item in options"
        :key="item.tenantId"
        :label="item.tenantName"
        :value="item.tenantId"
      />
    </el-select>
  </template>
  
  <script setup>
  import { ref, watchEffect, onMounted } from 'vue'
  import { juniorList } from '@/api/tenant/tenant'
  
  const props = defineProps({
    modelValue: [String, Number]
  })
  
  const emit = defineEmits(['update:modelValue', 'change'])
  
  const selectedValue = ref(props.modelValue)
  const options = ref([])
  const loading = ref(false)
  
  // 远程搜索方法
  const handleRemoteSearch = async (query) => {
    try {
      loading.value = true
      const params = query ? { tenantName: query } : { tenantId: selectedValue.value }
      const { data } = await juniorList(params)
      options.value = data
    } finally {
      loading.value = false
    }
  }
  
  // 选项变化处理
  const handleChange = (value) => {
    const selectedTenant = options.value.find(item => item.tenantId === value)
    emit('update:modelValue', value)
    emit('change', { 
      tenantId: value,
      tenant: selectedTenant 
    })
  }
  
  // 初始化加载
  onMounted(async () => {
    await handleRemoteSearch('')
  })
  
  // // 监听外部 modelValue 变化
  // watchEffect(() => {
  //   selectedValue.value = props.modelValue
  // })
  </script>