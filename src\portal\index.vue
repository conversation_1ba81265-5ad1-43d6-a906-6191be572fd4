<template>
  <div class="header">
    <div class="uni-header uni-header-bordered inner-home">
      <div class="uni-header-logo uni-header-item xiao-logo">
        <img v-if="logo" :src="logo" class="logo" />
      </div>

      <h1 class="uni-header-title uni-header-item inner-home-header">
        {{ title }}
      </h1>
      <div class="uni-nav uni-header-nav">
        <div class="uni-nav-item tab-menu">
          <div
            id="adminbutton"
            class="right-menu-item hover-effect"
            @click="goAdmin"
          >
            <el-icon>
              <Setting></Setting>
            </el-icon>
            <span style="margin-left: 3px">用户中心</span>
          </div>
        </div>
        <div class="uni-nav-item">
          <screenfull
            id="screenfull"
            class="right-menu-item hover-effect"
          ></screenfull>
        </div>
        <div class="uni-nav-item">
          <el-dropdown
            @command="handleCommand"
            class="right-menu-item hover-effect"
            trigger="click"
          >
            <div class="avatar-wrapper">
              <img
                style="width: 24px; height: 24px"
                src="@/assets/styles/js-pro.v.1.1/images/avatar.png"
              />
              <p class="uni-nav-item-title">
                你好, <span id="userName">{{ userObj.staffName }}</span>
              </p>
              <el-icon style="align-self: flex-end"><caret-bottom /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <router-link to="/portal/userInfo/index">
                  <el-dropdown-item>个人中心</el-dropdown-item>
                </router-link>
                <el-dropdown-item
                  command="homeLayout"
                  v-if="route.path === '/portal/index'"
                >
                  <span>主页配置</span>
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <span>退出登录</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>

    <div class="uni-topnav uni-topnav-md">
      <el-menu
        class="uni-topnav-body"
        :default-active="route.path"
        mode="horizontal"
        style="border-bottom: 1px"
      >
        <menu-item
          class="uni-topnav-body-item"
          v-for="(item, index) in menusData"
          :key="`${item.permissionId}-${index}`"
          :menu-index="index + ''"
          :item="item"
        ></menu-item>
      </el-menu>
    </div>

  </div>

  <!-- 新增左侧菜单容器 -->
  <div class="main-container" :class="{ 'has-sidebar': !isOpenLeft }">
    <div class="sidebar-container" v-if="hasChildrenMenu">
      <el-scrollbar :class="sideTheme" wrap-class="scrollbar-wrapper"  v-if="isOpenLeft">
        <el-menu
          :default-active="activeMenu"
          :default-openeds="openedMenus"
          :background-color="
            sideTheme === 'theme-dark'
              ? variables.menuBackground
              : variables.menuLightBackground
          "
          :text-color="
            sideTheme === 'theme-dark'
              ? variables.menuColor
              : variables.menuLightColor
          "
          :active-text-color="theme"
          :collapse-transition="false"
          mode="vertical"
          :unique-opened="true"
        >
          <sidebar-tree
            :data="parentMenuData"
            @toggle-menu="handleToggleMenu"
          />
        </el-menu>
      </el-scrollbar>
      <hamburger
        id="hamburger-container"
        :is-active="isOpenLeft"
        class="hamburger-container hamburger-container-mh"
        @toggleClick="toggleSideBar"
      />
    </div>

    <div class="content-container">
     
     
      <div
        id="portal-container"
        :class="[{ special: hasChildrenMenu  }, 'comp-container']"
      >
      
        <router-view v-slot="{ Component, route }">
          <TagsView  v-if="hasChildrenMenu"></TagsView>
          <keep-alive :include="tagsViewStore.cachedViews">
          <component :is="Component" :key="route.path" :routeList="route" />
          </keep-alive>
        </router-view>
        <el-backtop target="#portal-container" :right="100" :bottom="100" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
import Hamburger from "@/components/Hamburger";

import TagsView from "./TagsView/index";

import useTagsViewStore from '@/store/modules/tagsViewHome'
const tagsViewStore = useTagsViewStore()
import logo from "@/assets/logo/logo.png";
import MenuItem from "./MenuItem";
import Screenfull from "@/components/Screenfull";
import usePermissionStore from "@/store/modules/permission";
import useUserStore from "@/store/modules/user";
import useAppStore from "@/store/modules/app";
import { portalHome } from "@/router";
import defaultAvatar from "@/assets/images/avatar.png";
const title = import.meta.env.VITE_APP_TITLE;
const permissionStore = usePermissionStore();
const userStore = useUserStore();
const appStore = useAppStore();
const route = useRoute();
const router = useRouter();
import { getUser } from "@/api/system/user";
import SidebarTree from "./components/SidebarTree.vue";
import variables from "@/assets/styles/variables.module.scss";
import useSettingsStore from "@/store/modules/settings";
const settingsStore = useSettingsStore();
const sideTheme = computed(() => settingsStore.sideTheme);
// 当前激活的菜单路径
const activeMenu = computed(() => route.path);

// 当前菜单的父级菜单数据
const parentMenuData = ref([]);
// 需要展开的父级菜单ID列表
const openedMenus = ref([]);
const constantMenus = [
  {
    permissionId: "1",
    permissionType: "menu",
    openType: "tab",
    permissionName: "首页",
    path: portalHome.path,
    icon: "dashboard",
    permissionSort: 0,
    children: [], // 没有子菜单
    feedback: "view",
  },
];

const userObj = ref({});
const userId = userStore.userInfo.staffId;
const isOpenLeft = ref(true);


watch(route, () => {
  console.log(tagsViewStore.cachedViews)
})
// 在 script 中添加切换方法
const toggleSideBar = () => {
  isOpenLeft.value = !isOpenLeft.value;
};
function getUserProfile() {
  getUser(userId).then((res) => {
    userObj.value = res.data;
  });
}

const menusData = computed(() => {
  return [...constantMenus, ...permissionStore.portalMenus];
});

const levelList = computed(() => {
  const { indexPath } = route.meta;
  const newLevelList = [
    { title: "首页", path: portalHome.path, feedback: "view" },
  ];
  if (indexPath && indexPath.length) {
    let tempMenus = permissionStore.portalMenus;
    for (let layer = 0; layer < indexPath.length; layer++) {
      const layerIndex = indexPath[layer];
      if (tempMenus && tempMenus[layerIndex]) {
        newLevelList.push({
          title: tempMenus[layerIndex].permissionName,
          path: tempMenus[layerIndex].path,
          feedback: tempMenus[layerIndex].feedback,
        });
      }
      tempMenus = tempMenus[layerIndex].children;
    }

    return newLevelList;
  }

  return [];
});
function handleLink(item) {
  const { feedback, path } = item;

  if (feedback === "view") {
    router.push({ path });
  } else if (feedback === "blank") {
    window.open(path);
  }
}

function handleCommand(command) {
  if (command === "homeLayout") {
    appStore.openHomeDrawer();
  } else if (command === "logout") {
    userStore.logOut();
  }
}

const goAdmin = () => {
  router.push({ path: "/system/index" });
};

// 递归查找当前路径的父级菜单
const findParentMenu = (menus, targetPath) => {
  for (const menu of menus) {
    if (menu.path === targetPath) {
      return menu;
    }
    if (menu.children) {
      const found = findParentMenu(menu.children, targetPath);
      if (found) {
        // 返回父级菜单
        return menu;
      }
    }
  }
  return null;
};

// 处理菜单展开/折叠事件
const handleToggleMenu = (menuId) => {
  const index = openedMenus.value.indexOf(menuId);
  if (index === -1) {
    // 展开菜单
    openedMenus.value.push(menuId);
    // 高亮第一个子菜单
    const menu = findMenuById(menusData.value, menuId);
    if (menu?.children?.length) {
      activeMenu.value = menu.children[0].path;
      alert(menu.children[0].path);
      router.push(menu.children[0].path);
    }
  } else {
    // 折叠菜单
    openedMenus.value.splice(index, 1);
  }
};

// 递归查找菜单项
const findMenuById = (menus, menuId) => {
  for (const menu of menus) {
    if (menu.permissionId === menuId) {
      return menu;
    }
    if (menu.children) {
      const found = findMenuById(menu.children, menuId);
      if (found) return found;
    }
  }
  return null;
};

// 判断是否有子菜单
const hasChildrenMenu = computed(() => {
  return  parentMenuData.value.some(
    (menu) => menu.children && menu.children.length > 0
  );
});

// 监听路由变化，动态更新父级菜单数据
watch(
  () => route.path,
  (newPath) => {
    const parentMenu = findParentMenu(menusData.value, newPath);
    if (parentMenu) {
      parentMenuData.value = [parentMenu];
      // 默认展开父级菜单和第一个子菜单
      openedMenus.value = [parentMenu.permissionId];
      if (parentMenu.children?.length) {
        openedMenus.value.push(parentMenu.children[0].permissionId);
      }
    } else {
      hasChildrenMenu.value = false
      parentMenuData.value = [];
      openedMenus.value = [];
    }
  },
  { immediate: true }
);

// 组件挂载时加载数据
onMounted(() => {
  getUserProfile();
});
</script>

<style lang="scss" scoped>
.uni-topnav-md {
  position: fixed;
  width: 100%;
  top: 56px;
}
.header {
  position: fixed;
  top: 0px;
  left: 0px;
  z-index: 999;
  width: 100%;
}

.box {
  width: 100%;
  height: 78px;
  display: flex;
  justify-content: center;
  background-color: #ffffff;
  border-bottom: 1px solid #e6e6e6;
  position: absolute;
  top: 100px;
}

.logo-box {
  height: 30px;
  width: 250px;
  display: flex;
  align-items: center;
  flex-shrink: 0;

  .logo {
    height: 30px;
    width: 30px;
    margin-right: 10px;
  }

  .title {
    font-size: 20px;
    font-weight: bold;
  }
}

.menu {
  width: calc(100% - 475px);
}

.right-menu {
  height: 60px;
  width: 225px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex-shrink: 0;

  .right-menu-item {
    display: flex;
    margin: 0px 8px;
    font-size: 18px;
    color: #5a5e66;
    align-items: center;

    &.hover-effect {
      cursor: pointer;

      &:hover {
        color: var(--el-menu-active-color);
      }
    }
  }

  .avatar-wrapper {
    height: 36px;
    display: flex;
    align-items: center;

    .user-avatar {
      width: 36px;
      height: 36px;
    }
  }
}

.content {
  width: 1366px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0px 20px;
}

.bread {
  width: 100%;
  height: 35px;
  display: flex;
  justify-content: center;
  background-color: #f6f6f6;
}

.comp-container {
  position: fixed;
  top: 94px;
  width: 100%;
  height: calc(100vh - 100px);
  overflow: auto;
}

.special {
  top: 100px;
  width: calc(100% - 200px);
  height: calc(100vh - 100px);
  left:200px;
  .iframe-container {
    height: calc(100vh - 100px);
  }
}
.has-sidebar .special{
  width: calc(100% - 30px);
  left:30px;
}

.users-info {
  display: flex;
  align-items: center;

  .left-user-img {
    width: 90px;
    height: 90px;
    border-radius: 50%;
    background: #3288ff;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      width: 100%;
      border-radius: 50%;
    }
  }

  .right-user-info {
    margin-left: 24px;
    flex: 1;

    .right-user-info-top {
      font-size: 20px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #333333;
      padding-bottom: 12px;
      border-bottom: 1px solid #e8eaeb;
    }

    .right-user-info-bottom {
      font-size: 15px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #8c9398;
      padding-top: 12px;
    }
  }
}
#adminbutton {
  display: flex;
  align-items: center;
  color: #606266;
}

.hamburger-container-mh {
  position: absolute;
  right: -12px;
  top: 50%;
  transform: translate(0, -50%);
  z-index: 9000;
  cursor: pointer;
}
@media screen and (max-width: 768px) {
  .logo-box {
    width: 120px;
  }

  .menu {
    width: calc(100% - 190px);
  }

  .right-menu {
    width: 70px;
  }

  #adminbutton {
    display: none;
  }

  #screenfull {
    display: none;
  }
}

.main-container {
  display: flex;
  margin-left: 200px !important;
  /* 默认无侧边栏状态 */
  margin-left:0!important;
  /* 有侧边栏时的状态 */
  &.has-sidebar {
    margin-left: 0px !important;
  }
}

.main-container .sidebar-container {
  width: 200px !important;
  height: calc(100vh - 100px) !important;
  overflow: hidden !important;
  background: #ffffff;
  transition: width 0.28s;
  flex-shrink: 0;
  top: 100px !important;
  .el-menu {
    border-right: none;
  }
}
#app .has-sidebar .sidebar-container{
  width: 30px !important;
}
.content-container {
  flex: 1;
  overflow: auto;
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .sidebar-container {
    width: 64px;
    .el-menu-item span,
    .el-sub-menu__title span {
      display: none;
    }
  }
}

/* 响应式适配 */
@media screen and (max-width: 768px) {
  .main-container.has-sidebar {
    margin-left: 0;
  }
  .special{
    left:0px;
    width: 100%;
  }
  .sidebar-container {
    transform: translateX(-100%);
    &.active {
      transform: translateX(0);
    }
  }
}

:deep(.uni-nav-item-title){
  max-width:initial!important;
}
</style>
