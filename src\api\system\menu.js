import request from "@/utils/request";

//获取权限详情
export function getMenuDetail(id) {
  return request({
    url: `/user/permissions//${id}`,
    method: "get",
  });
}

// 查询菜单列表
export function listMenu(query) {
  return request({
    url: "/user/permissions/findAllMenu",
    method: "get",
    params: query,
  });
}

// 新增菜单
export function addMenu(data) {
  return request({
    url: "/user/permissions",
    method: "post",
    data: data,
  });
}

// 修改菜单
export function updateMenu(data) {
  return request({
    url: "/user/permissions/edit",
    method: "post",
    data: data,
  });
}

// 删除菜单
export function delMenu(data) {
  return request({
    url: "/user/permissions/delete",
    method: "post",
    data: data,
  });
}

// 查询用户组下菜单
export function getOrgMenu(data) {
  return request({
    url: "/user/roles/orgId/permissions",
    method: "post",
    data: data
  });
}

// 查询菜单下拉树结构
export function treeselect(params) {
  return request({
    url: "/user/permissions/findAllMenu",
    method: "get",
    params
  });
}
