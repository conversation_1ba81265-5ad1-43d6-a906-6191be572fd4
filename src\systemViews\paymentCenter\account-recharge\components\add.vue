<!-- 账户充值 -->

<template>
  <div
    class="dialog-box"
    :class="popupType === 'view' ? 'dialog-box-view' : 'dialog-box-edit'"
  >
    <el-form
      ref="formRef"
      :model="formData"
      label-width="110px"
      :rules="popupType !== 'view' ? rules : ''"
    >
      <el-row>
        <el-col :span="24">
          <el-form-item label="充值月份" prop="rechargeMonth">
            <!-- 添加或编辑时显示输入框 -->
            <el-date-picker
              v-if="popupType !== 'view'"
              v-model="formData.rechargeMonth"
              type="month"
              format="YYYYMM"
              value-format="YYYYMM"
              placeholder="请选择"
              clearable
            />

            <!-- 查看时显示文本 -->

            <div v-else>
              {{ formData.rechargeMonth || "" }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="员工姓名" prop="staffId">
            <el-select
              collapse-tags
              placeholder="请输入人员进行选择"
              clearable
              @change="changeUser"
              filterable
              remote
              v-model="formData.staffId"
              reserve-keyword
              :remote-method="getUserList"
            >
              <el-option
                v-for="(item, index) in applyUserList"
                :key="index"
                :label="item.staffName"
                :value="item.staffId"
              >
                <div>
                  {{
                    item.staffName +
                    "(" +
                    item.orgName +
                    "/" +
                    item.loginName +
                    ")"
                  }}
                </div>
              </el-option>
            </el-select>
            <div v-if="popupType == 'view'">
              {{ formData.staffName || "-" }}
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="员工部门" prop="orgName">
            <div>
              {{ formData.orgName || "" }}
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="人员类型" prop="staffType">
            <div>
              {{ $formatDictLabel(formData.staffType, staff_type) }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="账户选择" prop="accountManageId">
            <el-select
              v-model="formData.accountManageId"
              placeholder="请选择账户"
              clearable
              filterable
                @change="handleAccountChange"
            >
              <el-option
                v-for="(item, index) of accountList"
                :key="index"
                :label="item.accountName"
                :value="item.accountManageId"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="剩余金额(元)" prop="balance">
            <div>
              {{ formData.balance || "" }}
            </div>
          </el-form-item>
        </el-col>
        
        <el-col :span="24">
          <el-form-item label="充值金额(元)" prop="rechargeAmount">
            <NumberInput
              v-if="popupType !== 'view'"
              v-model="formData.rechargeAmount"
              customPlaceholder="请输入充值金额"
              input-type="decimal"
            />
            <!-- 查看时显示文本 -->
            <div v-else>
              {{ formData.paymentOrder }}
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, watch, getCurrentInstance, defineProps } from "vue";

import { ElMessage } from "element-plus";

import { screenIndex } from "@/api/paymentCenter/account-recharge/index";
import { pinyin } from "pinyin-pro";
import useUserStore from "@/store/modules/user";
const userStore = useUserStore()

const { proxy } = getCurrentInstance();
const {  staff_type,  } = proxy.useDict(
  "staff_type",
);
// 定义组件 props
const props = defineProps({
  closeBtn: {
    type: Function,

    default: null,
  },

  popupType: {
    type: String,
    default: "",
  },

  rowData: {
    type: Object,
    default: () => ({}),
  },
});

// 字典
// const { proxy } = getCurrentInstance();
// const {} = proxy.useDict("");
// 定义状态

let formData = ref({
  tenantId: userStore.userInfo.tenantId
});

// 定义 emits
const emit = defineEmits(["closeBtn"]);
const applyUserList = ref([]);
const accountList = ref([]);
const changeUser = () => {
  const user = applyUserList.value.find(
    (o) => o.staffId === formData.value.staffId
  );
  if (user) {
 
    formData.value.loginName = user.loginName;
    formData.value.staffId = user.staffId;
    formData.value.orgName = user.orgName;
    formData.value.staffType = user.staffType;
    queryAccountList()
  }


};

const getUserList = async (name) => {
  if (!name || name.trim().length < 2) {
    applyUserList.value = []; // 立即清空列表
    return;
  }
  try {
    const res = await screenIndex.selectUserList({
      //staffName: name,
        params: name,
    });
    applyUserList.value = res.data;
  } catch (error) {
    console.error("获取用户列表失败:", error);
  }
};

// 获取账户列表
const queryAccountList = async () => {
  try {
    const res = await screenIndex.queryAccountList({
      staffId: formData.value.staffId,
    }); 
    if (res.code == "1") {
      accountList.value = res.data;
    }
  }
  catch (error) {
    console.error("获取用户列表失败:", error); 
  }
}
// 添加账户选择变化处理
const handleAccountChange = (accountId) => {
  const selectedAccount = accountList.value.find(
    (account) => account.accountManageId === accountId
  );
  if (selectedAccount) {
    formData.value.balance = selectedAccount.balance;
  }
};
// 定义校验规则

const rules = reactive({
  rechargeMonth: [
    { required: true, message: "请选择充值月份", trigger: ["blur", "change"] },

    {
      validator: (_, value, callback) => {
        if (!value) return callback();
        
        const currentDate = new Date();
        const currentYear = currentDate.getFullYear();
        const currentMonth = currentDate.getMonth() + 1;
        const [selectedYear, selectedMonth] = [value.substring(0, 4), value.substring(4, 6)];
        
        if (selectedYear < currentYear || 
            (selectedYear == currentYear && selectedMonth < currentMonth)) {
          callback(new Error("充值月份不能早于当前月份"));
        } else {
          callback();
        }
      },
      trigger: ["blur", "change"]
    }
  ],
  staffId: [
    { required: true, message: "请选择员工", trigger: ["blur", "change"] }
  ],
  accountManageId: [
    { required: true, message: "请选择充值账户", trigger: ["blur", "change"] }
  ],
  rechargeAmount: [
    { required: true, message: "请输入充值金额", trigger: ["blur", "change"] },
    {
      type: "number",
      message: "金额必须为数字",
      trigger: ["blur", "change"],
      transform: (value) => Number(value)
    },
    {
      validator: (_, value, callback) => {
        if (value <= 0) {
          callback(new Error("金额必须大于0"));
        } else {
          callback();
        }
      },
      trigger: ["blur", "change"]
    }
  ]
});

// 表单引用

const formRef = ref(null);

// 定义方法

const initData = async () => {
  if (props.popupType === "recharge") {
  } else {
    formData.value = JSON.parse(JSON.stringify(props.rowData));
  }
  
};

const saveForm = async () => {
  try {
    await formRef.value.validate();
    const submitData = {
      ...formData.value,
      loginName: undefined,
      orgName: undefined,
      balance:undefined
    };
    delete submitData.loginName;
    delete submitData.orgName;
    delete submitData.balance;
    if (props.popupType === "edit") {
      const res = await screenIndex.payAccountTypeAdd(submitData);

      if (res.code == "1") {
        ElMessage.success("修改成功");

        emit("closeBtn");
        await screenIndex.pageList();
        
      }
    } else if (props.popupType === "recharge") {
      const res = await screenIndex.payAccountTypeAdd(submitData);

      if (res.code == "1") {
        ElMessage.success("新增成功");

        emit("closeBtn");
      }
    }
  } catch (error) {
    console.error("表单校验失败:", error);
  }
};


// 初始化数据

onMounted(() => {
  initData();
});

defineExpose({
  saveForm,
});
</script>

<style scoped lang="scss"></style>
