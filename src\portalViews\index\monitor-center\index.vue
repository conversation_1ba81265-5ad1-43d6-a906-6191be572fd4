<template>
  <div style="height: calc(100vh - 94px);">
    <v-scale-screen width="2000" height="1080" :fullScreen="true" id="commandModuleFullScreen">
      <div id="mainWindow" ref="mainWindow">
        <div class="cm-page cm-flex-col">
          <div class="cm-group1 cm-flex-col">
            <div class="cm-bd1 cm-flex-col">
              <div class="cm-outer1 cm-flex-col">
                <div class="cm-mod1 cm-flex-col"></div>
                <div class="cm-mod2 cm-flex-col">
                  <div class="cm-layer18 cm-flex-col">
                    <div class="cm-main1 cm-flex-col"><span class="cm-info1">{{ homeSystemTitle }}</span></div>
                    <div class="cm-main2 cm-flex-row">
                      <div class="cm-box12 cm-flex-col">
                        <div class="cm-group3 cm-flex-col">
                          <div class="cm-section11 cm-flex-row cm-justify-between">
                            <img
                              class="cm-label1"
                              referrerpolicy="no-referrer"
                              src="./assets/img/psvigysueyh8vcj4r8j2rtmynkw8d2zen1ff9ac0b-7178-4029-8556-e6394573b739.png"
                            />
                            <span class="cm-word1">在线用户</span>
                          </div>
                          <img
                            class="cm-pic1"
                            referrerpolicy="no-referrer"
                            src="./assets/img/ps75ajqg3qcid5tk51anpn1r3uodpqjzjez73f77fd7-2271-4991-99c2-96da02b7f7f1.png"
                          />
                          <div class="cm-box2 cm-flex-col">
                            <div class="cm-box2-data">
                              <span>{{ onlineUserList.length }}</span>
                            </div>
                            <div class="cm-box2-cicle"></div>
                          </div>
                        </div>
                      </div>
                      <div class="cm-box15 cm-flex-col">
                        <div class="cm-layer2 cm-flex-row">
                          <span class="cm-word3">{{ total.appTotal }}</span>
                          <div class="cm-mod3 cm-flex-col"><span class="cm-word4">{{ total.tenantTotal }}</span></div>
                          <div class="cm-mod4 cm-flex-col"><span class="cm-word5">{{ noticeTotal }}</span></div>
                        </div>
                        <div class="cm-layer3 cm-flex-row cm-justify-between">
                          <div class="cm-section2 cm-flex-col">
                            <span class="cm-word6">应用</span>
                            <img
                              class="cm-img1"
                              referrerpolicy="no-referrer"
                              src="./assets/img/psba1hdc7f0hncp6k9byhofiwl4yu6wbh3ee060f3ce-f60b-4cc9-a36c-7dba95cab7d5.png"
                            />
                          </div>
                          <div class="cm-section3 cm-flex-col">
                            <span class="cm-word7">租户</span>
                            <img
                              class="cm-img2"
                              referrerpolicy="no-referrer"
                              src="./assets/img/ps45x19nlfkrx1ils67mw78qns5uivf6py376a68c6-175d-4796-a967-9d9d7917a7a9.png"
                            />
                            <div class="cm-wrap1 cm-flex-col">
                              <span class="cm-txt1">公告</span>
                              <img
                                class="cm-img3"
                                referrerpolicy="no-referrer"
                                src="./assets/img/psu48kkc3qvggbjxra8tu8gh21h6khvvaa0e4501c0-9c78-4d93-b81f-e77dc621eb11.png"
                              />
                            </div>
                          </div>
                        </div>
                        <div class="cm-main19 cm-flex-col">
                          <div class="cm-mod16 cm-flex-col">
                            <div class="cm-main20 cm-flex-row cm-justify-between">
                              <div class="cm-outer2 cm-flex-col">
                                <img
                                  class="cm-label5"
                                  referrerpolicy="no-referrer"
                                  src="./assets/img/ps89gdtem0do4argl2aatjoy2beecok8aee58e4c-5f7a-4bc4-9585-d08ce9007088.png"
                                />
                              </div>
                              <span class="cm-txt2">基础服务状态</span>
                            </div>
                            <div class="cm-main21 cm-flex-row">
                              <div class="cm-main22 cm-flex-col cm-justify-between">
                                <div class="cm-mod5 cm-flex-col"></div>
                                <div class="cm-mod6 cm-flex-col"><div class="cm-section4 cm-flex-col"></div></div>
                              </div>
                              <div class="cm-layer6 cm-flex-col"></div>
                              <div class="cm-layer7 cm-flex-col"></div>
                              <div class="cm-layer8 cm-flex-col"></div>
                            </div>
                            <table border="0" style="border-collapse: collapse;margin-top: 10px;">
                              <tr class="cm-bd6 cm-bd6-table-tr">
                                <td>服务</td>
                                <td>IP</td>
                                <td>端口</td>
                                <td>健康状况</td>
                                <td>启用状态</td>
                                <td>启用状态</td>
                              </tr>
                              <tr class="cm-bd7">
                                <td>gateway</td>
                                <td>{{ basicServicesData.gateway !== undefined ? basicServicesData.gateway.ip : '' }}</td>
                                <td>{{ basicServicesData.gateway !== undefined ? basicServicesData.gateway.port : '' }}</td>
                                <td>
                                <span
                                  :class="basicServicesData.gateway !== undefined ? basicServicesData.gateway.healthy
                                    ? 'cm-common-txtcolor-normal' : 'cm-common-txtcolor-unnormal' : ''">
                                  {{ basicServicesData.gateway !== undefined && basicServicesData.gateway.healthy
                                !== undefined  ?
                                  basicServicesData.gateway.healthy ? '健康' : '异常' : ''}}
                                </span>
                                </td>
                                <td>
                                <span
                                  :class="basicServicesData.gateway !== undefined && basicServicesData.gateway.enabled
                                    ? 'cm-common-txtcolor-normal' : 'cm-common-txtcolor-unnormal'">
                                  {{ basicServicesData.gateway.enabledTitle }}
                                </span>
                                </td>
                                <td>
                                  <div class="cm-table-tr-td-box10 cm-flex-col" @click="getSingleServiceHealthStatus('gateway')">
                                    <span class="cm-word12">检测</span>
                                  </div>
                                </td>
                              </tr>
                              <tr class="cm-bd8">
                                <td>auth</td>
                                <td>{{ basicServicesData.auth !== undefined ? basicServicesData.auth.ip : '' }}</td>
                                <td>{{ basicServicesData.auth !== undefined ? basicServicesData.auth.port : '' }}</td>
                                <td>
                                <span
                                  :class="basicServicesData.auth !== undefined ? basicServicesData.auth.healthy
                                    ? 'cm-common-txtcolor-normal' : 'cm-common-txtcolor-unnormal' : ''">
                                  {{ basicServicesData.auth !== undefined && basicServicesData.auth.healthy
                                !== undefined  ?
                                  basicServicesData.auth.healthy ? '健康' : '异常' : ''}}
                                </span>
                                </td>
                                <td>
                                <span
                                  :class="basicServicesData.auth !== undefined && basicServicesData.auth.enabled
                                    ? 'cm-common-txtcolor-normal' : 'cm-common-txtcolor-unnormal'">
                                  {{ basicServicesData.auth.enabledTitle }}
                                </span>
                                </td>
                                <td>
                                  <div class="cm-table-tr-td-box10 cm-flex-col" @click="getSingleServiceHealthStatus('auth')">
                                    <span class="cm-word12">检测</span>
                                  </div>
                                </td>
                              </tr>
                              <tr class="cm-bd7">
                                <td>usercenter</td>
                                <td>{{ basicServicesData.usercenter !== undefined ? basicServicesData.usercenter.ip : '' }}</td>
                                <td>{{ basicServicesData.usercenter !== undefined ? basicServicesData.usercenter.port : '' }}</td>
                                <td>
                                <span
                                  :class="basicServicesData.usercenter !== undefined ? basicServicesData.usercenter.healthy
                                    ? 'cm-common-txtcolor-normal' : 'cm-common-txtcolor-unnormal' : ''">
                                  {{ basicServicesData.usercenter !== undefined && basicServicesData.usercenter.healthy
                                !== undefined  ?
                                  basicServicesData.usercenter.healthy ? '健康' : '异常' : ''}}
                                </span>
                                </td>
                                <td>
                                <span
                                  :class="basicServicesData.usercenter !== undefined && basicServicesData.usercenter.enabled
                                    ? 'cm-common-txtcolor-normal' : 'cm-common-txtcolor-unnormal'">
                                  {{ basicServicesData.usercenter.enabledTitle }}
                                </span>
                                </td>
                                <td>
                                  <div class="cm-table-tr-td-box10 cm-flex-col"
                                       @click="getSingleServiceHealthStatus('usercenter')">
                                    <span class="cm-word12">检测</span>
                                  </div>
                                </td>
                              </tr>
                              <tr class="cm-bd8">
                                <td>cms</td>
                                <td>{{ basicServicesData.cms !== undefined ? basicServicesData.cms.ip : '' }}</td>
                                <td>{{ basicServicesData.cms !== undefined ? basicServicesData.cms.port : '' }}</td>
                                <td>
                                <span
                                  :class="basicServicesData.cms !== undefined ? basicServicesData.cms.healthy
                                    ? 'cm-common-txtcolor-normal' : 'cm-common-txtcolor-unnormal' : ''">
                                  {{ basicServicesData.cms !== undefined && basicServicesData.cms.healthy
                                !== undefined  ?
                                  basicServicesData.cms.healthy ? '健康' : '异常' : ''}}
                                </span>
                                </td>
                                <td>
                                <span
                                  :class="basicServicesData.cms !== undefined && basicServicesData.cms.enabled
                                    ? 'cm-common-txtcolor-normal' : 'cm-common-txtcolor-unnormal'">
                                  {{ basicServicesData.cms.enabledTitle }}
                                </span>
                                </td>
                                <td>
                                  <div class="cm-table-tr-td-box10 cm-flex-col" @click="getSingleServiceHealthStatus('cms')">
                                    <span class="cm-word12">检测</span>
                                  </div>
                                </td>
                              </tr>
                              <tr class="cm-bd7">
                                <td>exchange</td>
                                <td>{{ basicServicesData.exchange !== undefined ? basicServicesData.exchange.ip : '' }}</td>
                                <td>{{ basicServicesData.exchange !== undefined ? basicServicesData.exchange.port : '' }}</td>
                                <td>
                                <span
                                  :class="basicServicesData.exchange !== undefined ? basicServicesData.exchange.healthy
                                    ? 'cm-common-txtcolor-normal' : 'cm-common-txtcolor-unnormal' : ''">
                                  {{ basicServicesData.exchange !== undefined && basicServicesData.exchange.healthy
                                !== undefined ?
                                  basicServicesData.exchange.healthy ? '健康' : '异常' : ''}}
                                </span>
                                </td>
                                <td>
                                <span
                                  :class="basicServicesData.exchange !== undefined && basicServicesData.exchange.enabled
                                    ? 'cm-common-txtcolor-normal' : 'cm-common-txtcolor-unnormal'">
                                   {{ basicServicesData.exchange.enabledTitle }}
                                </span>
                                </td>
                                <td>
                                  <div class="cm-table-tr-td-box10 cm-flex-col" @click="getSingleServiceHealthStatus('exchange')">
                                    <span class="cm-word12">检测</span>
                                  </div>
                                </td>
                              </tr>
                            </table>
                          </div>
                        </div>
                      </div>
                      <div class="cm-main5 cm-flex-col">
                        <div class="cm-layer9 cm-flex-col">
                          <span class="cm-basic-service-txt">基础服务</span>
                          <div class="cm-layer9-box10 cm-flex-col" @click="getServiceHealthStatus()">
                            <span class="cm-word12">检测</span>
                          </div>
                          <span class="cm-check-tip-txt">{{ basicServiceTipText !== undefined ? basicServiceTipText : '' }}</span>
                        </div>
                        <div class="cm-layer10 cm-flex-col">
                          <span class="cm-db-txt">数据库</span>
                          <div class="cm-layer9-box10 cm-flex-col" @click="getDatabaseStatus()">
                            <span class="cm-word12">检测</span>
                          </div>
                          <span class="cm-check-tip-txt">{{ databaseTipText !== undefined ? databaseTipText : '' }}</span>
                        </div>
                        <div class="cm-layer11 cm-flex-col">
                          <span class="cm-mq-txt">消息队列</span>
                          <div class="cm-layer9-box10 cm-flex-col"  @click="getKafkaStatus()">
                            <span class="cm-word12">检测</span>
                          </div>
                          <span class="cm-check-tip-txt">{{ kafkaTipText !== undefined ? kafkaTipText : '' }}</span>
                        </div>
                      </div>
                    </div>
                    <div class="cm-outer10 cm-flex-row cm-justify-between">
                      <div class="cm-section12 cm-flex-col">
                        <div class="cm-section13 cm-flex-col">
                          <div class="cm-layer20 cm-flex-row cm-justify-between">
                            <div class="cm-main7 cm-flex-col">
                              <img
                                class="cm-icon4"
                                referrerpolicy="no-referrer"
                                src="./assets/img/psbhqmpp0557qqrky0iyw0o78hcwajbsk57809d750-a3b6-47d9-b0e9-3cc36c65d701.png"
                              />
                            </div>
                            <span class="cm-word8">活跃用户</span>
                          </div>
                          <div class="cm-layer21 cm-flex-row">
                            <div class="cm-box16 cm-flex-col cm-justify-between">
                              <div class="cm-main8 cm-flex-col"></div>
                              <div class="cm-main9 cm-flex-col"><div class="cm-mod7 cm-flex-col"></div></div>
                            </div>
                            <div class="cm-bd12 cm-flex-col"></div>
                            <div class="cm-bd13 cm-flex-col"></div>
                            <div class="cm-bd14 cm-flex-col"></div>
                          </div>
                          <div class="cm-bd16 cm-flex-col">
                            <div class="cm-bd16-content">
                              <table class="cm-bd16-table">
                                <thead>
                                <tr class="cm-bd6 cm-bd16-tr">
                                  <td>姓名</td>
                                  <td>组织</td>
                                  <td>租户</td>
                                </tr>
                                </thead>
                                <tbody>
                                <tr v-for="item in onlineUserList" class="cm-bd16-data-tr">
                                  <td>
                                    <span class="cm-bd16-data-span">{{ item.staffName }}</span>
                                  </td>
                                  <td>
                                    <span class="cm-bd16-data-span">{{ item.currentOrgName }}</span>
                                  </td>
                                  <td>
                                    <span class="cm-bd16-data-span">{{ item.customParam.tenantName }}</span>
                                  </td>
                                </tr>
                                </tbody>
                              </table>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="cm-section14 cm-flex-col">
                        <div class="cm-group5">
                          <div class="cm-mod17 cm-flex-row cm-justify-between">
                            <div class="cm-main11 cm-flex-col">
                              <img
                                class="cm-icon7"
                                referrerpolicy="no-referrer"
                                src="./assets/img/ps305cza2fc5ew4wl5p48rmbdhdqu0znw7wcee0f704-7210-4bfa-a425-15f8d9ceb075.png"
                              />
                            </div>
                            <span class="cm-word11">基础服务</span>
                          </div>
                          <div class="cm-main17 cm-flex-row cm-flex-col" @click="oneKeyDetection">
                            <span class="cm-info7">一键检测</span>
                          </div>
                          <img
                            class="cm-img4"
                            referrerpolicy="no-referrer"
                            src="./assets/img/ps5hg8ohr22ll6gj8ni0ovslrrc9m4l5h12bd98172-777f-471f-9229-1a14c4eddacd.png"
                          />
                          <div class="cm-layer-gateway cm-flex-col"
                               :class="basicServicesStatus.gateway === null || basicServicesStatus.gateway === undefined
                             ? '' : basicServicesStatus.gateway && (basicServicesData.gateway !== undefined &&
                                  basicServicesData.gateway.healthy) ? 'cm-layer-healthy-pic' : 'cm-layer-unhealthy-pic'">
                            <div class="cm-bd22 cm-flex-col cm-justify-between">
                            <span class="cm-info-gateway" style="margin-left: -10px;"
                                  :class="basicServicesStatus.gateway === null || basicServicesStatus.gateway === undefined
                             ? '' : basicServicesStatus.gateway && (basicServicesData.gateway !== undefined &&
                                  basicServicesData.gateway.healthy) ? 'cm-info-healthy' : 'cm-info-unhealthy'">
                              gateway
                            </span>
                            </div>
                          </div>
                          <div class="cm-layer-auth cm-flex-col"
                               :class="basicServicesStatus.auth === null || basicServicesStatus.auth === undefined
                             ? '' : basicServicesStatus.auth && (basicServicesData.auth !== undefined &&
                                  basicServicesData.auth.healthy) ? 'cm-layer-healthy-pic' : 'cm-layer-unhealthy-pic'">
                            <div class="cm-bd22 cm-flex-col cm-justify-between">
                            <span class="cm-info-gateway"
                                  :class="basicServicesStatus.auth === null || basicServicesStatus.auth === undefined
                              ? '' : basicServicesStatus.auth && (basicServicesData.auth !== undefined &&
                                  basicServicesData.auth.healthy) ? 'cm-info-healthy' : 'cm-info-unhealthy'">
                              auth
                            </span>
                            </div>
                          </div>
                          <div class="cm-layer-usercenter cm-flex-col"
                               :class="basicServicesStatus.usercenter === null || basicServicesStatus.usercenter === undefined
                             ? '' : basicServicesStatus.usercenter && (basicServicesData.usercenter !== undefined &&
                                  basicServicesData.usercenter.healthy) ? 'cm-layer-healthy-pic' : 'cm-layer-unhealthy-pic'">
                            <div class="cm-bd22 cm-flex-col cm-justify-between">
                            <span class="cm-info-gateway" style="margin-left: -20px;"
                                  :class="basicServicesStatus.usercenter === null || basicServicesStatus.usercenter === undefined
                             ? '' : basicServicesStatus.usercenter && (basicServicesData.usercenter !== undefined &&
                                  basicServicesData.usercenter.healthy) ? 'cm-info-healthy' : 'cm-info-unhealthy'">
                              usercenter
                            </span>
                            </div>
                          </div>
                          <div class="cm-layer-cms cm-flex-col"
                               :class="basicServicesStatus.cms === null || basicServicesStatus.cms === undefined
                             ? '' : basicServicesStatus.cms && (basicServicesData.cms !== undefined &&
                                  basicServicesData.cms.healthy) ? 'cm-layer-healthy-pic' : 'cm-layer-unhealthy-pic'">
                            <div class="cm-bd22 cm-flex-col cm-justify-between">
                            <span class="cm-info-gateway"
                                  :class="basicServicesStatus.cms === null || basicServicesStatus.cms === undefined
                             ? '' : basicServicesStatus.cms && (basicServicesData.cms !== undefined &&
                                  basicServicesData.cms.healthy) ? 'cm-info-healthy' : 'cm-info-unhealthy'">
                              cms
                            </span>
                            </div>
                          </div>
                          <div class="cm-layer-exchange cm-flex-col"
                               :class="basicServicesStatus.exchange === null || basicServicesStatus.exchange === undefined
                           ? '' : basicServicesStatus.exchange && (basicServicesData.exchange !== undefined &&
                                  basicServicesData.exchange.healthy) ? 'cm-layer-healthy-pic' : 'cm-layer-unhealthy-pic'">
                            <div class="cm-bd22 cm-flex-col cm-justify-between">
                            <span class="cm-info-gateway" style="margin-left: -15px;"
                                  :class="basicServicesStatus.exchange === null || basicServicesStatus.exchange === undefined
                             ? '' : basicServicesStatus.exchange && (basicServicesData.exchange !== undefined &&
                                  basicServicesData.exchange.healthy) ? 'cm-info-healthy' : 'cm-info-unhealthy'">
                              exchange
                            </span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="cm-section15 cm-flex-col">
                        <div class="cm-layer9 cm-flex-col">
                          <span class="cm-redis-txt">Redis</span>
                          <div class="cm-layer9-box10 cm-flex-col" @click="getRedisStatus()">
                            <span class="cm-word12">检测</span>
                          </div>
                          <span class="cm-check-tip-txt">{{ redisTipText !== undefined ? redisTipText : '' }}</span>
                        </div>
                        <div class="cm-layer10 cm-flex-col">
                          <span class="cm-es-txt">Elasticsearch</span>
                          <div class="cm-layer9-box10 cm-flex-col" @click="getElasticsearchStatus()">
                            <span class="cm-word12">检测</span>
                          </div>
                          <span class="cm-check-tip-txt">{{ elasticsearchTipText !== undefined ? elasticsearchTipText : '' }}</span>
                        </div>
                        <div class="cm-layer11 cm-flex-col">
                          <span class="cm-oss-txt">对象存储</span>
                          <div class="cm-layer9-box10 cm-flex-col" @click="getOSSStatus()">
                            <span class="cm-word12">检测</span>
                          </div>
                          <span class="cm-check-tip-txt">{{ ossTipText !== undefined ? ossTipText : '' }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="cm-layer14 cm-flex-col"></div>
                  <div class="cm-layer15 cm-flex-col">
                    <div class="cm-layer16 cm-flex-col cm-justify-between">
                      <span class="cm-info12">{{ total.staffTotal }}</span>
                      <span class="cm-word18">用户</span>
                    </div>
                  </div>
                  <div class="cm-layer17 cm-flex-col">
                    <div class="cm-bd22 cm-flex-col cm-justify-between">
                      <span class="cm-word19">{{ total.orgTotal }}</span>
                      <span class="cm-info13">组织</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </v-scale-screen>
  </div>
</template>

<script setup name="MonitorCenter">
import {
  getServiceHealthStatus as getServiceHealthStatusOrigin,
  getDatabaseStatus as getDatabaseStatusOrigin,
  getRedisStatus as getRedisStatusOrigin,
  getKafkaStatus as getKafkaStatusOrigin,
  getElasticsearchStatus as getElasticsearchStatusOrigin,
  getOSSStatus as getOSSStatusOrigin,
  getBasicServicesHealthStatusList as getBasicServicesHealthStatusListOrigin,
} from "@/api/index/monitor";
import { listAll } from "@/api/release/notice";
import { onlineUsers } from "@/api/operation/online";
import VScaleScreen from 'v-scale-screen'
import { getTotals } from "@/api/grid/useStatistics";
import { configData } from "@/api/login";
import {ElMessage} from "element-plus";
// 数量统计
const total=ref({})
const noticeTotal=ref(0)
    // 应用列表
const basicServicesData=ref({
  auth: {},
  cms: {},
  gateway: {},
  usercenter: {},
  exchange: {},
})
const basicServicesStatus = ref({})
const mainWindowHeight = ref()
const mainWindowWidth = ref()
const basicServiceTipText = ref()
const databaseTipText = ref()
const kafkaTipText = ref()
const redisTipText = ref()
const elasticsearchTipText = ref()
const ossTipText = ref()
const homeSystemTitle = ref('统一门户-监控中心')
const onlineUserList = ref([])
onMounted(()=>{
  nextTick(
      ()=>{
        mainWindowHeight.value = (document.getElementById("mainWindow").offsetHeight)
        mainWindowWidth.value = (document.getElementById("mainWindow").offsetWidth)
      }

  )
})
async function oneKeyDetection() {
  await getServiceHealthStatus()
  await getDatabaseStatus()
  await getRedisStatus()
  await getKafkaStatus()
  await getElasticsearchStatus()
  await getOSSStatus()
}
function getBasicServicesHealthStatusList() {
  getBasicServicesHealthStatusListOrigin().then((response) => {
    if (response.success) {
      basicServicesData.value.auth =
          response.data.auth && response.data.auth.length > 0 ? response.data.auth[0] : undefined
      if (basicServicesData.value.auth !== undefined) {
        basicServicesData.value.auth.enabledTitle = basicServicesData.value.auth.enabled ? '启用' : '未启用'
      } else {
        basicServicesData.value.auth = { enabledTitle: '未启用' }
      }
      basicServicesData.value.cms =
          response.data.cms && response.data.cms.length > 0 ? response.data.cms[0] : undefined
      if (basicServicesData.value.cms !== undefined) {
        basicServicesData.value.cms.enabledTitle = basicServicesData.value.cms.enabled ? '启用' : '未启用'
      } else {
        basicServicesData.value.cms = { enabledTitle: '未启用' }
      }
      basicServicesData.value.gateway =
          response.data.gateway && response.data.gateway.length > 0 ? response.data.gateway[0] : undefined
      if (basicServicesData.value.gateway !== undefined) {
        basicServicesData.value.gateway.enabledTitle = basicServicesData.value.gateway.enabled ? '启用' : '未启用'
      } else {
        basicServicesData.value.gateway = { enabledTitle: '未启用' }
      }
      basicServicesData.value.usercenter =
          response.data.usercenter && response.data.usercenter.length > 0 ? response.data.usercenter[0] : undefined
      if (basicServicesData.value.usercenter !== undefined) {
        basicServicesData.value.usercenter.enabledTitle = basicServicesData.value.usercenter.enabled ? '启用' : '未启用'
      } else {
        basicServicesData.value.usercenter = { enabledTitle: '未启用' }
      }
      basicServicesData.value.exchange =
          response.data.exchange && response.data.exchange.length > 0 ? response.data.exchange[0] : undefined
      if (basicServicesData.value.exchange !== undefined) {
        basicServicesData.value.exchange.enabledTitle = basicServicesData.value.exchange.enabled ? '启用' : '未启用'
      } else {
        basicServicesData.value.exchange = { enabledTitle: '未启用' }
      }
    } else {
      ElMessage.error(response.message);
    }
  });
}
function getServiceHealthStatus() {
  basicServiceTipText.value = '检测中，请稍候。。。'
  getServiceHealthStatusOrigin().then((response) => {
    if (response.success) {
      basicServicesStatus.value = response.data
      basicServiceTipText.value =
          `基础服务${response.success ? '健康' : '不健康'},耗时${response.data.totalTime}ms.`
    } else {
      ElMessage.error(response.message);
    }
  });
}
function getSingleServiceHealthStatus(type) {
  getServiceHealthStatusOrigin().then((response) => {
    if (response.success) {
      switch (type) {
        case 'auth':
          basicServicesStatus.value = { auth: response.data.auth }
          break
        case 'cms':
          basicServicesStatus.value = { cms: response.data.cms }
          break
        case 'gateway':
          basicServicesStatus.value = { gateway: response.data.gateway }
          break
        case 'usercenter':
          basicServicesStatus.value = { usercenter: response.data.usercenter }
          break
        case 'exchange':
          basicServicesStatus.value = { exchange: response.data.exchange }
          break
      }
    } else {
      ElMessage.error(response.message);
    }
  });
}
function getDatabaseStatus() {
  databaseTipText.value = '检测中，请稍候。。。'
  getDatabaseStatusOrigin().then((response) => {
    if (response.success) {
      databaseTipText.value =
          `写${response.data.write ? '健康' : '不健康'},耗时${response.data.writeTime}ms,
              读${response.data.read ? '健康' : '不健康'},耗时${response.data.readTime}ms`
    } else {
      ElMessage.error(response.message);
    }
  });
}
function getRedisStatus() {
  redisTipText.value = '检测中，请稍候。。。'
  getRedisStatusOrigin().then((response) => {
    if (response.success) {
      redisTipText.value =
          `写${response.data.write ? '健康' : '不健康'},耗时${response.data.writeTime}ms,
              读${response.data.read ? '健康' : '不健康'},耗时${response.data.readTime}ms`
    } else {
      ElMessage.error(response.message);
    }
  });
}
function getKafkaStatus() {
  kafkaTipText.value = '检测中，请稍候。。。'
  getKafkaStatusOrigin().then((response) => {
    if (response.success) {
      kafkaTipText.value =
          `发布${response.data.write ? '健康' : '不健康'},耗时${response.data.writeTime}ms,
              消费${response.data.read ? '健康' : '不健康'},耗时${response.data.readTime}ms`
    } else {
      ElMessage.error(response.message);
    }
  });
}
function getElasticsearchStatus() {
  elasticsearchTipText.value = '检测中，请稍候。。。'
  getElasticsearchStatusOrigin().then((response) => {
    if (response.success) {
      elasticsearchTipText.value =
          `写${response.data.write ? '健康' : '不健康'},耗时${response.data.writeTime}ms,
              读${response.data.read ? '健康' : '不健康'},耗时${response.data.readTime}ms`
    } else {
      ElMessage.error(response.message);
    }
  });
}
function getOSSStatus() {
  ossTipText.value = '检测中，请稍候。。。'
  getOSSStatusOrigin().then((response) => {
    if (response.success) {
      ossTipText.value =
          `上传${response.data.write ? '健康' : '不健康'},耗时${response.data.writeTime}ms,
              读取${response.data.read ? '健康' : '不健康'},耗时${response.data.readTime}ms`
    } else {
      ElMessage.error(response.message);
    }
  });
}
function getTotal() {
  getTotals().then((response) => {
    if (response.success) {
      total.value = response.data;
    } else {
      ElMessage.error(response.message);
    }
  });
  listAll({ programaType: "system_notice" }).then((response) => {
    if (response.success) {
      noticeTotal.value = response.data.length;
    } else {
      ElMessage.error(response.message);
    }
  });
}
import useUserStore from "@/store/modules/user";

function getSystemHomeTitle() {
  // 获取动态配置数据
  configData({
    clientType: "1",
    configCodes: ["tenant_title"],
    tenantId: useUserStore().userInfo.customParam.tenantId,
  }).then((res) => {
    if (res.success && res.data.length > 0) {
      homeSystemTitle.value = `${res.data[0].configValue}-监控中心`
    }
  });
}
function getOnlineUserList() {
  onlineUsers().then(res => {
    if (res.success) {
      onlineUserList.value = res.data
      if (onlineUserList.value.length > 5) {
        onlineUserList.value = onlineUserList.value.slice(0, 5)
      }
    }
  })
}
getBasicServicesHealthStatusList();
getSystemHomeTitle();
getTotal();
getOnlineUserList();
</script>


<style scoped src="./assets/command-module.css" />
<style scoped src="./assets/command-module-common.css" />
