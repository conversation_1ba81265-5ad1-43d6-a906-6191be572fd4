<template>
    <template v-for="item in data" :key="item.permissionId">
        <!-- 有子菜单的节点 -->
        <el-sub-menu v-if="item.children?.length" :index="item.permissionId">
            <!-- <template #title>
          <i :class="'el-icon-' + (item.icon || 'folder')"></i>
          <span>{{ item.permissionName }}</span>
        </template> -->

            <template #title>
                <svg-icon :icon-class="item.icon" />
                <span class="menu-title" :title="hasTitle(item.permissionName)">
                    {{ item.permissionName }}
                </span>
            </template>
            <sidebar-tree :data="item.children" :active-path="activePath" @menu-click="handleMenuClick" />
        </el-sub-menu>

        <!-- 叶子节点 -->
        <el-menu-item v-else :index="item.path" @click="handleMenuClick(item)">
            <svg-icon :icon-class="item.icon" />
            <span>{{ item.permissionName}}</span>
        </el-menu-item>


    </template>
</template>

<script setup>
import { defineProps } from 'vue'
import { useRouter } from 'vue-router'

const props = defineProps({
    data: {
        type: Array,
        required: true
    },
    activePath: {
        type: String,
        required: true
    }
})

const router = useRouter()

const handleMenuClick = (item) => {
    if (item.feedback === 'blank') {
        window.open(item.path)
    } else {
        router.push(item.path)
    }
}

function hasTitle(title) {
    if (title.length > 5) {
        return title;
    } else {
        return "";
    }
}
</script>