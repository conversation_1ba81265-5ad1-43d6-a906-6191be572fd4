<template>
  <div class="userInfo">
    <el-form ref="userInfoForm" :model="user" :rules="rules" label-width="80px">
      <el-form-item label="用户名称" prop="staffName">
        <el-input v-model="user.staffName" />
      </el-form-item>
      <el-form-item label="手机号码" prop="cellphone">
        <el-input v-model="user.cellphone" maxlength="11" />
      </el-form-item>
      <el-form-item label="邮箱" prop="email">
        <el-input v-model="user.email" maxlength="50" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" size="small" @click="submit">保存</el-button>
        <el-button type="danger" size="small" @click="close">关闭</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import useUserStore from "@/store/modules/user";
import {updateUserProfile} from "@/api/system/user";
import useTagsViewStore from "@/store/modules/tagsView";

const { proxy } = getCurrentInstance()
const userStore = useUserStore();
const userInfoForm = ref(null)
const props = defineProps({
  user: {
    type: Object,
  }
})

const rules = {
  staffName: [{ required: true, message: "用户名称不能为空", trigger: "blur" }],
  email: [
    { required: true, message: "邮箱地址不能为空", trigger: "blur" },
    { type: "email", message: "'请输入正确的邮箱地址", trigger: ["blur", "change"]}
  ],
  cellphone: [
    { required: true, message: "手机号码不能为空", trigger: "blur" },
    { pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: "请输入正确的手机号码", trigger: "blur"}
  ]
}

const submit = () => {
  proxy.$refs["userInfoForm"].validate(valid => {
    if (valid) {
      updateUserProfile(props.user).then(res => {
        if (res.success) {
          proxy.$modal.msgSuccess("个人信息修改成功，请重新登录后查看")
          // getInfo({ token: getToken() }).then(res => {
          //   userStore.setInfo(res)
          // })
        } else {
          proxy.$modal.msgError(res.message)
        }
      })
    }
  })
}

const close = () => {
  useTagsViewStore().delView(proxy.$route)
  window.history.back(1)
}

defineExpose({
  userInfoForm,
});
</script>


