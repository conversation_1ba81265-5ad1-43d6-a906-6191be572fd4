<template>
  <div class="banner-content">
    <img src="../../../assets/images/pageDesign/case/background.png" alt class="bananer-background" />
    <div class="search">
      <p class="search-title">{{ systemTitle !== "" ? systemTitle : "统一门户" }}</p>
      <div class="search-input">
        <el-select v-model="queryParams.source" placeholder="类别" >
          <el-option label="全部" value=""></el-option>
          <el-option
              v-for="dict in sys_list"
              :key="dict.label"
              :label="dict.label"
              :value="dict.label">
          </el-option>
        </el-select>
        <el-input
            placeholder="请输入内容"
            v-model="queryParams.keyWords"
            class="input-with-select"
            @keyup.enter="noticeSearch"
            clearable
        ></el-input>
        <el-button class="myButton" @click="noticeSearch">搜索</el-button>
      </div>
    </div>
  </div>
</template>

<script setup name="CaseSearch">
import { configData } from "@/api/login";
import {useRouter} from 'vue-router';
import useUserStore from "@/store/modules/user";

const router = useRouter();
const {proxy} = getCurrentInstance();
const userStore = useUserStore();
const {sys_list} = proxy.useDict("sys_list");
const systemTitle = ref("");
const queryParams = ref({
  keyWords: "",
  source: "",
  page: 1,
  pageSize: 10,
})

// 搜索按钮操作
const noticeSearch = () => {
  const keyWords = queryParams.value.keyWords.trim();
  if (!keyWords) {
    return;
  }
  const routeData = router.resolve({
    path: proxy.portalIndex +"/search",
    query: { source: queryParams.value.source, keyWords },
  });
  if (proxy.$route.path.includes("/search")) {
    router.replace({
      path: proxy.portalIndex +"/search",
      query: { source: queryParams.value.source, keyWords },
    });
  } else {
    window.open(routeData.href, "_blank");
  }
}

// 获取自定义配置的系统标题
function getTenantTitle() {
  configData({
    configCodes: ["tenant_title"],
    clientType: "1",
    tenantId: userStore.userInfo.customParam.tenantId,
  }).then((res) => {
    if (res.success) {
      if (res.data[0]) {
        systemTitle.value = res.data[0].configValue;
      }
    }
  });
}

getTenantTitle();
</script>

<style scoped lang="less">
// banner图内容
.banner-content {
  width: 100%;
  height: 485px;
  margin: 0 auto;
  // background-color: #328dc7;
  position: relative;

  .bananer-background {
    width: 100%;
    height: 100%;
  }

  .search {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;

    .search-title {
      font-size: 48px;
      font-family: Microsoft YaHei;
      font-weight: bold;
      color: #FFFFFF;
      letter-spacing: 10px;
      margin-top: 0px;
    }

    .search-input {
      display: flex;
      margin-top: 40px;

      :deep(.el-input--medium .el-input__inner) {
        height: 50px;
        text-align: center;
      }

      .input-with-select {
        width: 520px;

      }

      .myButton {
        width: 130px;
        height: 50px;
        background: linear-gradient(90deg, #6ac5e2, #54b3a5);
        border-radius: 5px;
        color: #fff;
        border: 0px;
        font-size: 18px;
        letter-spacing: 5px;
        margin-left: 10px;
      }

      .el-select {
        width: 120px;
      }

      :deep(.el-select__wrapper) {
        min-height: 50px;
      }

      .input-with-select .el-input__icon:hover {
        color: black;
        cursor: pointer;
      }
    }
  }
}
</style>
