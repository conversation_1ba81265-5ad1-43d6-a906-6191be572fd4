// 字体


@font-face {
	font-family: 'HYSongYangTiW';
	src: url('@/assets/fonts/HYSongYangTiW.ttf');
	font-weight: normal;
	font-style: normal;
	font-display: swap;
	/* 优化字体加载显示 */
}

@font-face {
	font-family: PingFangRegular;
	src: url('@/assets/fonts/PingFang Regular/PingFang Regular.ttf')
}

@font-face {
	font-family: SYPXZT;
	// src: url('@/assets/fonts/siyuanyuanti.otf')
}

/**
 * 通用css样式布局处理
 */

/** 基础通用 **/
.pt5 {
	padding-top: 5px;
}

.pr5 {
	padding-right: 5px;
}

.pb5 {
	padding-bottom: 5px;
}

.mt5 {
	margin-top: 5px;
}

.mr5 {
	margin-right: 5px;
}

.mb5 {
	margin-bottom: 5px;
}

.mb8 {
	margin-bottom: 8px;
}

.ml5 {
	margin-left: 5px;
}

.mt10 {
	margin-top: 10px;
}

.mr10 {
	margin-right: 10px;
}

.mb10 {
	margin-bottom: 10px;
}

.ml10 {
	margin-left: 10px;
}

.mt20 {
	margin-top: 20px;
}

.mr20 {
	margin-right: 20px;
}

.mb20 {
	margin-bottom: 20px;
}

.ml20 {
	margin-left: 20px;
}

.h1,
.h2,
.h3,
.h4,
.h5,
.h6,
h1,
h2,
h3,
h4,
h5,
h6 {
	font-family: inherit;
	font-weight: 500;
	line-height: 1.1;
	color: inherit;
}

.el-form .el-form-item__label {
	font-weight: 700;
}

.el-dialog:not(.is-fullscreen) {
	margin-top: 10vh !important;
}

.el-dialog:not(.is-fullscreen).my_height_1 {
	margin-top: 25vh !important;
}

.el-dialog.scrollbar .el-dialog__body {
	overflow: auto;
	overflow-x: hidden;
	max-height: 70vh;
	padding: 10px 20px 0;
}

.el-table {

	.el-table__header-wrapper,
	.el-table__fixed-header-wrapper {
		th {
			word-break: break-word;
			background-color: #f8f8f9 !important;
			color: #515a6e;
			height: 40px !important;
			font-size: 13px;
		}
	}

	.el-table__body-wrapper {
		.el-button [class*="el-icon-"]+span {
			margin-left: 1px;
		}
	}
}

/** 表单布局 **/
.form-header {
	font-size: 15px;
	color: #6379bb;
	border-bottom: 1px solid #ddd;
	margin: 8px 10px 25px 10px;
	padding-bottom: 5px
}

/** 表格布局 **/
.pagination-container {
	// position: relative;
	height: 25px;
	margin-bottom: 10px;
	margin-top: 15px;
	padding: 10px 20px !important;
}

/* tree border */
.tree-border {
	margin-top: 5px;
	border: 1px solid #e5e6e7;
	background: #FFFFFF none;
	border-radius: 4px;
	width: 100%;
}

.pagination-container .el-pagination {
	right: 0;
	position: absolute;
}


@media (max-width : 768px) {
	.pagination-container .el-pagination>.el-pagination__jump {
		display: none !important;
	}

	.pagination-container .el-pagination>.el-pagination__sizes {
		display: none !important;
	}
}

.el-table .fixed-width .el-button--small {
	padding-left: 0;
	padding-right: 0;
	width: inherit;
}

/** 表格更多操作下拉样式 */
.el-table .el-dropdown-link {
	cursor: pointer;
	color: #409EFF;
	margin-left: 10px;
}

.el-table .el-dropdown,
.el-icon-arrow-down {
	font-size: 12px;
}

.el-tree-node__content>.el-checkbox {
	margin-right: 8px;
}

.list-group-striped>.list-group-item {
	border-left: 0;
	border-right: 0;
	border-radius: 0;
	padding-left: 0;
	padding-right: 0;
}

.list-group {
	padding-left: 0px;
	list-style: none;
}

.list-group-item {
	border-bottom: 1px solid #e7eaec;
	border-top: 1px solid #e7eaec;
	margin-bottom: -1px;
	padding: 11px 0px;
	font-size: 13px;
}

.pull-right {
	float: right !important;
}

.el-card__header {
	padding: 14px 15px 7px !important;
	min-height: 40px;
}

.el-card__body {
	padding: 15px 15px 20px 15px !important;
}

.card-box {
	padding-right: 15px;
	padding-left: 15px;
	margin-bottom: 10px;
}

/* button color */
.el-button--cyan.is-active,
.el-button--cyan:active {
	background: #20B2AA;
	border-color: #20B2AA;
	color: #FFFFFF;
}

.el-button--cyan:focus,
.el-button--cyan:hover {
	background: #48D1CC;
	border-color: #48D1CC;
	color: #FFFFFF;
}

.el-button--cyan {
	background-color: #20B2AA;
	border-color: #20B2AA;
	color: #FFFFFF;
}

/* text color */
.text-navy {
	color: #1ab394;
}

.text-primary {
	color: inherit;
}

.text-success {
	color: #1c84c6;
}

.text-info {
	color: #23c6c8;
}

.text-warning {
	color: #f8ac59;
}

.text-danger {
	color: #ed5565;
}

.text-muted {
	color: #888888;
}

/* image */
.img-circle {
	border-radius: 50%;
}

.img-lg {
	width: 120px;
	height: 120px;
}

.avatar-upload-preview {
	position: absolute;
	top: 50%;
	transform: translate(50%, -50%);
	width: 200px;
	height: 200px;
	border-radius: 50%;
	box-shadow: 0 0 4px #ccc;
	overflow: hidden;
}

/* 拖拽列样式 */
.sortable-ghost {
	opacity: .8;
	color: #fff !important;
	background: #42b983 !important;
}

/* 表格右侧工具栏样式 */
.top-right-btn {
	margin-left: auto;
}


// 表格数据样式

.el-dialog.commons_popup .el-dialog__header {
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	width: 100%;
	height: 45px;
	background: #f4f4f4;
	opacity: 1;
	border-radius: 4px 4px 0px 0px;
}


.el-dialog.commons_popup .el-dialog__headerbtn {
	top: 0px;
}

// 全屏样式
/* 全屏样式 */
.fullscreen-dialog {
	width: 100vw !important;
	height: 100vh !important;
	margin: 0 !important;
	top: -95px !important;
	left: 0 !important;
	max-width: none !important;
}


.commons_popup .el-dialog__headerbtn .el-dialog__close svg {
	color: $--color-black;
}

.commons_popup .el-dialog__body {
	padding: 10px 20px;
}


//**********  新增 统一标题样式 **********//

.common-header {
	display: flex;
	align-items: center;
	border-bottom: 1px solid #e8ebec;
	padding-bottom: 10px;
}

.common-header .common-header-line {
	width: 4px;
	height: 15px;
	background: $--color-main;
	margin-right: 10px;
}

.common-header .common-header-text {
	font-size: 16px;
	font-family: PingFang SC-Bold, PingFang SC;
	font-weight: bold;
	color: #333333;
}

.common-header-btn {
	height: 28px;
	background: #fff;
	border: 1px solid $--color-main;
	opacity: 1;
	border-radius: 2px;
	padding: 0px 10px;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
	cursor: pointer;
}

.common-header-btn .icons {
	font-size: 18px;
	margin-right: 5px;
	color: $--color-main;
}

.common-header-btn .common-header-btn-one {
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.common-header-btn .common-header-btn-one-name {
	font-size: 12px;
	font-family: PingFang SC;
	font-weight: 400;
	color: $--color-main;
}

.common-box-one {
	width: 100%;
	margin-bottom: 12px;
}

.dialog-box-view .el-input,
.dialog-box-view .el-select,
.dialog-box-view .el-radio-group {
	pointer-events: none;
}

.container-table-box {
	padding: 10px;
}

.container-table-box .el-card {
	height: calc(100vh - 110px);
	overflow-y: auto;
}

.container-table-box .dialog-search-top {
	// padding-top: 15px;
}

.container-table-box .search-common-btn-list {
	padding: 0px 10px;
	padding-bottom: 12px;
}

#portal-container.special .el-card {
	height: calc(100vh - 154px) !important;
	min-height: calc(100vh - 154px) !important;
	overflow-y: auto;
}
#portal-container.special .el-card .el-card__body {
	height: 100%;
	box-sizing: border-box;
}
#portal-container.special .el-tabs__content .el-card {
	height: calc(100vh - 194px) !important;
	min-height: calc(100vh - 194px) !important;
	overflow-y: auto;
}

//**********  新增 统一标题样式 **********//
//flex
.text-center {
	text-align: center;
}

.text-right {
	text-align: right;
}

.text-left {
	text-align: left;

}

.flex-1 {
	flex: 1
}

.flex {
	display: flex;
}


.align-items-center {
	align-items: center;
}

.flex-direction-column {
	flex-direction: column;

}

.justify-content-center {
	justify-content: center;
}

// 弹窗样式
.dialog-box .el-form .el-form-item__label {
	color: #595959 !important;
	font-weight: 400 !important;
	font-family: SYPXZT;
	font-size: 14px;
}

.dialog-box .el-input__inner {
	color: #282828 !important;
	font-weight: 400 !important;
	font-family: SYPXZT;
	font-size: 14px;
}

.dialog-box .el-select__input {
	color: #282828 !important;
	font-weight: 400 !important;
	font-family: SYPXZT;
	font-size: 14px;
}

.dialog-box .el-form-item__content {
	color: #000000 !important;
	font-weight: 400 !important;
	font-family: SYPXZT;
	font-size: 14px;
}

.dialog-box-view .el-form-item__label {
	position: relative;
}

.dialog-box-view .el-form-item__label::after {
	content: '：';
	position: absolute;
	right: -2px;
	display: block;
	color: #282828 !important;
	font-weight: 400 !important;
	font-family: SYPXZT;
	font-size: 14px;
}


.dialog-box .el-date-editor.el-date-editor--date {
	width: 100%;
}

.el-table--border .el-table__inner-wrapper::after,
.el-table--border::after,
.el-table--border::before,
.el-table__inner-wrapper::before {
	z-index: 0 !important;
}

.el-image-viewer__wrapper {
	z-index: 9999 !important;
}




.el-popper.is-light {
	color: #333;
	background: rgba(255, 255, 255, 0.92) !important; /* 半透明白色背景 */
	border-radius: 8px;
	box-shadow: 0px 0px 12px 1px rgba(0, 0, 0, 0.06);
	max-width: 700px !important;
	line-height: 1.8 !important;
	padding: 10px;
	border: 1px solid #e4e7ed;
}

.popover-buttons {
	.el-button {
		margin-left: 0px !important;
		display: block;
	}
}

.table-card-box {
	display: flex;
	flex-wrap: wrap;

	.table-card-one {
		width: calc((100% - 30px) / 4);
		border: 1px solid #eee;
		border-radius: 5px;
		margin-right: 10px;
		margin-bottom: 10px;
		padding: 10px;
		box-sizing: border-box;
		position: relative;

		.fixed-btn {
			margin-left: 8px;

		}

		&:hover {
			box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2); // 悬停时加深阴影
		}

		&:nth-child(4n) {
			margin-right: 0px;
		}

		.cars-place {
			padding: 0px 5px 8px 5px;
			border-bottom: 1px solid #eee;
			margin-bottom: 8px;
			display: flex;
			align-items: center;

			.cars-place-box {
				display: inline-block;
				background: #eee;
				color: #303133;
				padding: 4px 8px;
				border-radius: 4px;
			}

			img {
				width: 18px;
				margin-right: 5px;
				vertical-align: middle;
				margin-top: -3px;
			}
		}

		.cars-plateNo {
			display: flex;
			justify-content: center;
			// margin-bottom: 10px;
			margin-right: 10px;
			font-size: 15px;
			color: #c20000;
			border-bottom: 1px solid #eee;
			cursor: pointer;

			// padding-bottom: 8px;
			.blue-plate {
				background: blue;
				color: #fff;
				border-radius: 4px;
				padding: 2px 5px;
			}

			.blue-plate {
				background: blue;
				color: #fff;
				border-radius: 4px;
				padding: 2px 5px;
			}

			.green-plate {
				background: green;
				color: #fff;
				border-radius: 4px;
				padding: 2px 5px;
			}
		}

		.card-top-row {
			display: flex;
			align-items: center;
			border-bottom: 1px solid #eee;
			padding-bottom: 10px;

			.el-button {
				font-size: 13px;
				height: 30px;
			}

			.left-dollor {
				color: #c20000;
				font-size: 18px;

				.changft-1 {
					display: inline-block;
					background-color: #eee;
					border-radius: 5px;
					color: #303133;
					font-size: 14px;
					height: 28px;
					line-height: 28px;
					padding: 0px 12px;
					margin-right: 8px;
				}

				.changft-2 {
					display: inline-block;
					background-color: #eee;
					border-radius: 5px;
					color: #303133;
					font-size: 14px;
					height: 28px;
					line-height: 28px;
					padding: 0px 12px;
				}
			}

			.car-card {
				background: #c20000;
				color: #ffffff;
				border-radius: 4px;
				padding: 0px 12px;
				display: inline-block;
				height: 28px;
				line-height: 28px;

				&.lanColor {
					background: #eee;
					color: #303133;
				}
			}

			.openDoorBtn {
				margin-left: 15px;
				height: 28px;
				position: relative;
				left: -5px;
			}
		}

		.card-content {
			margin-top: 10px;

			.top-image {
				.el-image {
					width: 100%;
					height: 90px;
				}
			}

			.mid-content {
				font-size: 14px;
				margin-bottom: 8px;
				white-space: nowrap;
				/* 防止文本换行 */
				overflow: hidden;
				/* 隐藏溢出的内容 */
				text-overflow: ellipsis;
				/* 在文本溢出时显示省略号 */
			}

			.bottom-info {
				border: 1px solid #eee;
				padding: 0px 10px;
				height: 40px;
				box-sizing: border-box;

				.info-row {
					display: flex;
					align-items: center;
					height: 100%;

					.info-point {
						width: 5px;
						height: 5px;
						border-radius: 50%;
						background: #c20000;
						flex-shrink: 0;
						margin-right: 3px;
						margin-top: -2px;

					}

					.info-text {
						flex: 1;
						font-size: 13px;
						white-space: nowrap;
						/* 防止文本换行 */
						overflow: hidden;
						/* 隐藏溢出的内容 */
						text-overflow: ellipsis;
						/* 在文本溢出时显示省略号 */
					}
				}
			}
		}
	}
}

.table-card-box-pagination {
	margin-top: 0px !important;
	display: flex;
}

.table-card-box-pagination.pagination-container .el-pagination {
	right: 50px;
}

.top-table-list-btn {
	// position: absolute;
	// right: 160px;
	// top:112px;
	width: 32px;
	height: 32px;
	border-radius: 50%;
	border: 1px solid #eee;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-left: 12px;

	img {
		width: 16px;
		cursor: pointer;
	}
}

.dialog-box-edit .el-date-editor {
	width: 100%;
}
.area-path-style{
	font-weight: bold; color: #606266;
	padding-left: 2px;
	margin-top: -10px;
	padding-bottom: 8px;
	border-bottom: 1px solid #eee;
	margin-bottom: 15px;
	font-size: 15px;
	
}
.area-path-style .iconfont{
	margin-right: 8px;
}
.el-popper .el-tag{
	margin-bottom: 10px;  
  }
@media (max-width: 1680px) and (min-width: 1442px) {
	.table-card-box .table-card-one .cars-plateNo {
		font-size: 12px;
	}

	.table-card-box .table-card-one .card-top-row .left-dollor .changft-2 {
		font-size: 12px;
	}

	.table-card-box .table-card-one .card-top-row .car-card {
		font-size: 12px;
		height: 27px;
	}

	.table-card-box .table-card-one .card-top-row .el-button {
		font-size: 12px;
		height: 27px;
	}

	.table-card-box .table-card-one .cars-place .cars-place-box {
		font-size: 12px;
	}

	.table-card-box .table-card-one .cars-place img {
		width: 14px;
	}

	.table-card-box .table-card-one .fixed-btn {
		font-size: 12px;
		height: 27px;
	}

	.table-card-box .table-card-one .card-content .mid-content {
		font-size: 12px;
	}

	.table-card-box .table-card-one .card-content .bottom-info .info-row .info-text {
		font-size: 12px;
	}

	.el-sub-menu__title {
		font-size: 13px;
	}

	#app .sidebar-container .svg-icon {
		margin-right: 12px;
	}

	.el-menu-item {
		font-size: 13px;
	}

	.uni-header {
		padding: 3px 25px !important;
		height: 48px !important;

	}

	.uni-header-logo.xiao-logo {
		width: 38px !important;
		top: 12px !important;
	}

	.uni-header .uni-header-logo.xiao-logo>img {
		width: 34px !important;
		height: 34px !important;
	}

	.uni-header .uni-header-title {
		font-size: 16px !important;
	}

	#adminbutton {
		font-size: 13px !important;
	}

	.uni-header-nav .uni-nav-item+.uni-nav-item {
		margin-left: 20px !important;
		font-size: 13px !important;
	}

	.uni-nav-item .el-dropdown img {
		width: 20px !important;
		height: 20px !important;
	}

	.uni-nav-item-title {
		font-size: 13px !important;
	}

	.uni-topnav-md {
		top: 48px !important;
	}

	.uni-topnav .uni-topnav-body .menu-title {
		font-size: 12px !important;
	}

	.uni-topnav .uni-topnav-body .svg-icon {
		font-size: 12px !important;
	}

	.uni-topnav.uni-topnav-md {
		height: 40px !important;
	}

	.uni-topnav.uni-topnav-md>.uni-topnav-body {
		height: 40px !important;
	}

	.uni-topnav.uni-topnav-md>.uni-topnav-body>.uni-topnav-body-item {
		height: 40px !important;
	}

	#app .main-container .sidebar-container {
		top: 89px !important;
		height: calc(100% - 89px) !important;
	}

	.special {
		top: 89px !important;
		height: calc(100% - 89px) !important;
	}

	.tags-view-container .tags-view-wrapper .tags-view-item {
		height: 24px;
		line-height: 24px;
		padding: 0 8px;
	}

	.pagination-container .el-pagination {
		font-size: 13px !important;
	}

	.el-select__wrapper {
		font-size: 12px;
		padding: 2px 10px;
		min-height: 28px;
		line-height: 20px;
	}

	.el-pagination button {
		height: 28px !important;
	}

	.el-pager li {
		font-size: 13px;
		height: 28px;
		line-height: 28px;
	}

	.el-table .el-table__body tr {
		// 适当减小高度值
		height: 40px !important;
	}
.el-table .el-table__body td {
  // 调整内边距以配合行高
  padding: 5px 0; 
}


}

@media (max-width: 1442px) {
	.table-card-box .table-card-one .cars-plateNo {
		font-size: 12px;
	}

	.table-card-box .table-card-one .card-top-row .left-dollor .changft-2 {
		font-size: 12px;
	}

	.table-card-box .table-card-one .card-top-row .car-card {
		font-size: 12px;
		height: 27px;
	}

	.table-card-box .table-card-one .card-top-row .el-button {
		font-size: 12px;
		height: 27px;
	}

	.table-card-box .table-card-one .cars-place .cars-place-box {
		font-size: 12px;
	}

	.table-card-box .table-card-one .cars-place img {
		width: 14px;
	}

	.table-card-box .table-card-one .fixed-btn {
		font-size: 12px;
		height: 27px;
	}

	.table-card-box .table-card-one .card-content .mid-content {
		font-size: 13px;
	}

	.table-card-box .table-card-one .card-content .bottom-info .info-row .info-text {
		font-size: 12px;
	}


	.el-sub-menu__title {
		font-size: 13px;
	}

	#app .sidebar-container .svg-icon {
		margin-right: 12px;
	}

	.el-menu-item {
		font-size: 13px;
	}

	.uni-header {
		padding: 3px 25px !important;
		height: 48px !important;

	}

	.uni-header-logo.xiao-logo {
		width: 38px !important;
		top: 12px !important;
	}

	.uni-header .uni-header-logo.xiao-logo>img {
		width: 34px !important;
		height: 34px !important;
	}

	.uni-header .uni-header-title {
		font-size: 16px !important;
	}

	#adminbutton {
		font-size: 13px !important;
	}

	.uni-header-nav .uni-nav-item+.uni-nav-item {
		margin-left: 20px !important;
		font-size: 13px !important;
	}

	.uni-nav-item .el-dropdown img {
		width: 20px !important;
		height: 20px !important;
	}

	.uni-nav-item-title {
		font-size: 13px !important;
	}

	.uni-topnav-md {
		top: 48px !important;
	}

	.uni-topnav .uni-topnav-body .menu-title {
		font-size: 12px !important;
	}

	.uni-topnav .uni-topnav-body .svg-icon {
		font-size: 12px !important;
	}

	.uni-topnav.uni-topnav-md {
		height: 40px !important;
	}

	.uni-topnav.uni-topnav-md>.uni-topnav-body {
		height: 40px !important;
	}

	.uni-topnav.uni-topnav-md>.uni-topnav-body>.uni-topnav-body-item {
		height: 40px !important;
	}

	#app .main-container .sidebar-container {
		top: 89px !important;
		height: calc(100% - 89px) !important;
	}

	.special {
		top: 89px !important;
		height: calc(100% - 89px) !important;
	}

	.tags-view-container .tags-view-wrapper .tags-view-item {
		height: 24px;
		line-height: 24px;
		padding: 0 8px;
	}

	.pagination-container .el-pagination {
		font-size: 13px !important;
	}

	.el-select__wrapper {
		font-size: 12px;
		padding: 2px 10px;
		min-height: 28px;
		line-height: 20px;
	}

	.el-pagination button {
		height: 28px !important;
	}

	.el-pager li {
		font-size: 13px;
		height: 28px;
		line-height: 28px;
	}

	.public-box .search-common-btn-list .searchBtnList button {
		font-size: 13px;
		height: 30px !important;
	}

	.el-table .el-table__body tr {
		// 适当减小高度值
		height: 40px !important;
	}

.el-table .el-table__body td {
  // 调整内边距以配合行高
  padding: 5px 0; 
}
}



@media (max-width: 1680px) and (min-width: 1442px) {
	.el-table .cell {
		font-size: 12px !important;
	}

	.el-table .tooltipFlagStyle {
		font-size: 13px !important;
	}

	.dialog-search .el-form-item__label {
		font-size: 13px !important;
	}

	.dialog-search .el-input__inner {
		font-size: 13px !important;
	}

	.dialog-search .el-select__wrapper {
		font-size: 13px !important;
	}

	.el-card__body {
		padding: 10px 10px 10px 10px !important;
	}

	.public-box .search-common-btn-list .searchBtnList button {
		font-size: 13px;
		height: 30px !important;
	}

	.dialog-search .searchList .el-button {
		font-size: 13px;
	}

	.rightToolbar .el-button.is-circle {
		width: 30px;
		height: 30px;
	}

	.rightToolbar .el-button.is-circle .el-icon {
		font-size: 13px;
	}

	.top-table-list-btn {
		width: 30px;
		height: 30px;

	}

	.top-table-list-btn img {
		width: 13px;
	}

	.dialog-search .el-date-editor .el-range-input {
		font-size: 12px;
	}

	.dialog-search .el-range-separator {
		font-size: 12px;
		padding: 0px 3px;
	}

	.dialog-search .el-range-editor.el-input__wrapper {
		padding: 0px 5px;
	}
}


