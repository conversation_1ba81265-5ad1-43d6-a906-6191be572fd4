import request from '@/utils/request'
//区域配置分页列表
export function pageList(data,params) {
  return request({
    url: '/visitor/visitorarea/list',
    method: 'post',
    data:data,
    params:params
  })
}

//区域配置保存

export function saveArea(data) {
  return request({
    url: '/visitor/visitorarea/save',
    method: 'post',
    data:data
  })
}
//查询回显
export function getArea(id) {
  return request({
    url: '/visitor/visitorarea/' + id,
    method: 'get'
  })
}
// 修改访客区域配置
export function updateArea(data) {
  return request({
    url: '/visitor/visitorarea',
    method: 'put',
    data: data
  })
}
// 删除访客区域配置
export function delArea(id) {
  return request({
    url: '/visitor/visitorarea/deleted/' + id,
    method: 'delete'
  })
}

// 测试查询区域名称
export function getAreaName() {
  return request({
    url: '/visitor/visitorarea/usableList',
    method: 'get'
  })
}
