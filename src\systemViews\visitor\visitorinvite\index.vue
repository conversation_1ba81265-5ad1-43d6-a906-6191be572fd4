<!-- 预约记录 -->
<template>
  <div class="container-table-box">
    <el-card>
      <el-row :gutter="24">
        <el-col :span="24" :xs="24">
          <dialog-search
            @getList="getList"
            formRowNumber="3"
            :columns="tabelForm.columns"
          >
            <template #formList>
              <el-form
                :model="formData"
                ref="queryForm"
                :inline="true"
                label-width="80px"
              >
                <el-form-item label="访客姓名" prop="visitorName">
                  <el-input
                    v-model="formData.visitorName"
                    placeholder="请输入访客姓名"
                    clearable
                  />
                </el-form-item>

                <el-form-item label="手机号码" prop="visitorTelephone">
                  <el-input
                    v-model="formData.visitorTelephone"
                    placeholder="请输入手机号码"
                    clearable
                  />
                </el-form-item>
                <el-form-item label="访问时间" prop="visitorTime">
                  <div class="flex">
                    <el-date-picker
                      v-model="formData.visitorTime"
                      @change="registTimeChange"
                      type="date"
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd"
                      placeholder="开始日期"
                    />
                    <span class="formRangeTime-tip">至</span>
                    <el-date-picker
                      v-model="formData.visitorEndTime"
                      @change="registEndTimeChange"
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd"
                      type="date"
                      placeholder="结束日期"
                    />
                  </div>
                </el-form-item>
                <el-form-item label="访客单位" prop="unit">
                  <el-input
                    v-model="formData.unit"
                    placeholder="请输入访客单位"
                    clearable
                  />
                </el-form-item>
                <el-form-item label="被访人员" prop="inviter">
                  <el-input
                    v-model="formData.inviter"
                    placeholder="请输入被访人"
                    clearable
                  />
                </el-form-item>
              </el-form>
            </template>
            <template #searchList>
              <el-button type="primary" icon="Search" @click="handleQuery">
                搜索
              </el-button>
              <el-button icon="Refresh" @click="resetQuery"> 重置 </el-button>
            </template>

            <template v-slot:searchBtnList>
              <!-- <el-button class="add"
                       type="primary" v-if="pageName !== 'chooseTables'" icon="el-icon-circle-plus-outline" size="mini"
                      @click="addRoom">新增 -->

              <el-button
                class="add"
                type="primary"
                icon="Plus"
                size="mini"
                @click="handleAdd"
                >新增
              </el-button>
              <el-button icon="Search" size="mini" @click="queryIewm"
                >我的名片
              </el-button>
              <el-button icon="Download" size="mini" @click="handleExport"
                >导出
              </el-button>
              <!-- <el-button class="other"
                       type="primary" v-if="pageName == 'chooseTables'" icon="el-icon-circle-plus-outline" size="mini"
                       @click="chooseCanteen">选择 -->
              <!-- </el-button> -->
            </template>
          </dialog-search>

          <PublicTable
            ref="publictable"
            :rowKey="tabelForm.tableKey"
            :tableData="userList"
            :columns="tabelForm.columns"
            :configFlag="tabelForm.tableConfig"
            :pageValue="formData"
            :total="total"
            :getList="getList"
          >
            <!-- 操作列 -->
            <template #operation="{ scope }">
              <el-button
                size="mini"
                type="text"
                icon="View"
                title="查看"
                @click="handleView(scope.row)"
              ></el-button>

              <el-button
                size="mini"
                v-show="
                  scope.row.processStatus == 0 && scope.row.qrCodeId == null
                "
                type="text"
                icon="Edit"
                title="修改"
                @click="handleUpdate(scope.row)"
              >
              </el-button>
              <el-button
                size="mini"
                type="text"
                icon="Delete"
                title="删除"
                @click="handleDelete(scope.row)"
              ></el-button>
              <el-button
                size="mini"
                v-show="scope.row.processStatus == 0 ? true : false"
                type="text"
                title="允许访问"
                @click="handleAllow(scope.row, '1')"
              >
                <i class="icon iconfont icon-icon-permitCount"></i>
              </el-button>
              <el-button
                size="mini"
                v-show="scope.row.processStatus == 0 ? true : false"
                type="text"
                title="拒绝访问"
                @click="handleRefuse(scope.row)"
              >
                <i class="icon iconfont icon-rejectPolicyCount"></i>
              </el-button>

              <el-button
                size="mini"
                type="text"
                v-show="
                  scope.row.accessStatus === '0' &&
                  scope.row.processStatus === '1'
                "
                title="关闭门禁权限"
                @click="handleLock(scope.row)"
              >
                <i class="icon iconfont icon-no-permission"></i>
              </el-button>
            </template>
          </PublicTable>

          <DialogBox
            :visible="open"
            :dialogWidth="dialogWidth"
            @save="submits('0')"
            @submit="submits('1')"
            @cancellation="cancellation"
            @close="close"
            :dialogFooterBtn="dialogFooterBtn"
            :dialogFooterSubmitBtn="dialogFooterSubmitBtn"
            CloseSubmitText="取消"
            SaveSubmitText="保存"
            :dialogTitle="headerTitle"
          >
            <template #content>
              <addDom
                v-if="
                  popupType == 'add' ||
                  popupType == 'edit' ||
                  popupType == 'view'
                "
                ref="addRef"
                :rowData="rowData"
                @submitClose="submitClose"
                :popup-type="popupType"
                :popupTypeOther="popupTypeOther"
              ></addDom>
              <myCardDom v-if="popupType == 'myCard'"></myCardDom>
              <refuseDom
                ref="refuseRef"
                v-if="popupType == 'refuse'"
                @submitClose="submitClose"
              ></refuseDom>
            </template>
          </DialogBox>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>
  
  <script setup>
import { ref, onMounted, getCurrentInstance, reactive } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  getConfigInfo,
  findVisitorInvitationList,
  delById,
  editResult,
  lockById,
} from "@/api/visitor/visitorinvite/index";
import addDom from "./components/add";
import myCardDom from "./components/myCard";
import refuseDom from "./components/refuse";
import { formatMinuteTime } from "@/utils";
const { proxy } = getCurrentInstance();
const {
  visitor_status,
  visit_type,
  id_type,
  visit_reason,
  visitor_access_status,
} = proxy.useDict(
  "visitor_status",
  "visit_type",
  "id_type",
  "visit_reason",
  "visitor_access_status"
);
// 表格配置
const tabelForm = ref({
  tableKey: "1",
  isShowRightToolbar: true,
  showSearch: true,
  columns: [
    {
      fieldIndex: "visitorName", // 对应列内容的字段名
      label: "访客姓名", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: false, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "120px", //最小宽度%
      width: "", //宽度
      align: "center", //表格对齐方式
    },

    {
      label: "手机号码",
      slotname: "visitorTelephone",
      width: "",
      minWidth: "120px", //最小宽度%
      fixed: "", //固定
      align: "center", //表格对齐方式
      visible: true,
      type: "phoneHidden",
    },
    {
      fieldIndex: "licenseType", // 对应列内容的字段名
      label: "证件类型", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: false, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "120px", //最小宽度%
      align: "center", //表格对齐方式
      type: "dict",
      dictList: id_type,
    },
    {
      label: "证件号码",
      slotname: "idNumber",
      width: "",
      minWidth: "150px", //最小宽度%
      align: "center", //表格对齐方式
      fixed: "", //固定
      visible: true,
      type: "idNumberHidden",
    },
    {
      fieldIndex: "unit", // 对应列内容的字段名
      label: "访客单位", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: false, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "150px", //最小宽度%
      width: "", //宽度
      align: "left", //表格对齐方式
    },
    {
      fieldIndex: "inviter", // 对应列内容的字段名
      label: "被访人员", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: false, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "", //最小宽度%
      width: "120px", //宽度
      align: "center", //表格对齐方式
    },
    {
      label: "被访人电话",
      slotname: "phone",
      width: "",
      minWidth: "120px", //最小宽度%
      fixed: "", //固定
      align: "center", //表格对齐方式
      visible: true,
      type: "phoneHidden",
    },
    {
      fieldIndex: "visitorTime", // 对应列内容的字段名
      label: "到访时间", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "150px", //最小宽度%
      align: "center", //表格对齐方式
    },
    {
      fieldIndex: "visitorEndTime", // 对应列内容的字段名
      label: "离开时间", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "150px", //最小宽度%
      width: "", //宽度
      align: "center", //表格对齐方式
    },
    {
      fieldIndex: "reason", // 对应列内容的字段名
      label: "来访事由", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: false, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "120px", //最小宽度%
      align: "center", //表格对齐方式
      type: "dict",
      dictList: visit_reason,
      align: "left",
    },
    {
      fieldIndex: "processStatus", // 对应列内容的字段名
      label: "状态", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: false, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "120px", //最小宽度%
      align: "center", //表格对齐方式
      type: "dict",
      dictList: visitor_status,
    },
    {
      fieldIndex: "accessStatus", // 对应列内容的字段名
      label: "门禁权限", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: false, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "120", //最小宽度%
      align: "center", //表格对齐方式
      type: "dict",
      align: "left",
      dictList: visitor_access_status,
    },
    {
      label: "操作",
      slotname: "operation",
      width: "150",
      fixed: "right", //固定
      visible: true,
    },
  ],
  tableConfig: {
    needPage: true,
    index: true,
    selection: false,
    reserveSelection: false,
    indexFixed: false,
    selectionFixed: false,
    indexWidth: "50",
    loading: false,
    showSummary: false,
    height: null,
  },
});

// 查询参数
const formData = ref({
  pageNum: 1,
  pageSize: 10,
});
// 是否显示身份证
const isIdCard = ref(false);
// 表格数据
const userList = ref([{}]);

// 表格总数
const total = ref(0);
// 是否显示弹窗底部按钮
const dialogFooterBtn = ref(true);
// 弹窗标题
const headerTitle = ref("");

// dom
const addRef = ref(null);
// 点击行信息
const rowData = ref({});

// 是否显示弹窗
const open = ref(false);

// 弹窗宽度
const dialogWidth = ref("");

// 弹窗类型
const popupType = ref("");

const popupTypeOther = ref("");
// 是否开启底部提交

const dialogFooterSubmitBtn = ref(true);

// 获取配置的方法
const getConfig = async () => {
  try {
    const res = await getConfigInfo();
    const isIdC = res.rows[0].isIdcard;

    // 更新响应式变量
    isIdCard.value = isIdC === "0";

    // 等待DOM更新后执行
    await nextTick();
    // 操作响应式对象
    tabelForm.columns[3].visible = isIdCard.value;
  } catch (error) {
    console.error("获取配置失败:", error);
  }
};
// 列表请求
const getList = () => {
  tabelForm.value.tableConfig.loading = true;
  findVisitorInvitationList(formData.value).then((res) => {
    userList.value = res.data.records;
    total.value = res.data.total;
    tabelForm.value.tableConfig.loading = false;
  });
};

// 查询
const handleQuery = () => {
  formData.value.pageNum = 1;
  getList();
};

// 重置
const resetQuery = () => {
  formData.value = {};
  formData.value.pageNum = 1;
  formData.value.pageSize = 10;
  getList();
};

// 新增
const handleAdd = () => {
  headerTitle.value = "新增访客邀约";
  popupType.value = "add";
  dialogWidth.value = "50%";
  dialogFooterSubmitBtn.value = true;
  rowData.value = {};
  dialogFooterBtn.value = true;
  open.value = true;
};

// 修改
const handleUpdate = (row) => {
  headerTitle.value = "修改访客邀约";
  popupType.value = "edit";
  dialogWidth.value = "50%";
  popupTypeOther.value = "";
  dialogFooterSubmitBtn.value = true;
  dialogFooterBtn.value = true;
  rowData.value = row;
  open.value = true;
};

// 查看详情
const handleView = (row) => {
  dialogFooterBtn.value = false;
  headerTitle.value = "查看访客邀约";
  rowData.value = row;
  popupTypeOther.value = "";
  dialogWidth.value = "50%";
  dialogFooterSubmitBtn.value = false;
  popupType.value = "view";
  open.value = true;
};

// 我的名片
const queryIewm = () => {
  dialogFooterBtn.value = false;
  headerTitle.value = "我的名片";
  rowData.value = {};
  popupTypeOther.value = "";
  dialogWidth.value = "500px";
  dialogFooterSubmitBtn.value = false;
  popupType.value = "myCard";
  open.value = true;
};

const handleAllow = (row, processStatus) => {
  headerTitle.value = "设置访问区域";
  popupType.value = "view";
  popupTypeOther.value = "submitMain";
  dialogWidth.value = "50%";
  dialogFooterSubmitBtn.value = true;
  dialogFooterBtn.value = false;
  rowData.value = row;
  open.value = true;
};

const handleRefuse = (row) => {
  headerTitle.value = "拒绝访问";
  popupType.value = "refuse";
  dialogWidth.value = "40%";
  popupTypeOther.value = "";
  dialogFooterSubmitBtn.value = false;
  dialogFooterBtn.value = true;
  rowData.value = row;
  open.value = true;
};

// 方法定义
const handleLock = async (row) => {
  try {
    // 显示确认对话框
    await ElMessageBox.confirm(
      '确认关闭该条数据的门禁权限吗？(关闭后无法恢复)',
      '操作确认',
      { type: 'warning' }
    )

    // 执行锁定操作
    const res = await lockById(row.id)
    
    if (res.success) {
      ElMessage.success('关闭成功')
      getList() // 刷新列表
    } 
  } catch (error) {
    // 错误类型判断：
    // - 当用户点击取消时，error 为 'cancel'
    // - 当 API 调用出错时，error 为 Error 对象
    if (error !== 'cancel') {
      ElMessage.error(error.message || '操作失败')
    }
  }
}
// 删除
const handleDelete = (row) => {
  ElMessageBox.confirm("确认删除该条访客信息？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      delById(row.id).then((res) => {
        if (res.code == "1") {
          ElMessage.success("删除成功");
          getList();
        }
      });
    })
    .catch(() => {
      // 用户取消删除
    });
};
const handleExport = () => {
  proxy.download(
    `${apiUrl}/visitorinvite/invite/export`,
    {
      ...formData,
    },
    `访客邀约信息_${formatMinuteTime(new Date())}.xlsx`
  );
};

// 提交关闭
const submitClose = () => {
  open.value = false;
  setTimeout(() => {
    getList();
  }, 100);
};

/** 点击提交 */
const submits = (type) => {
  if(!popupTypeOther.value ){
    addRef.value.saveParentBtn(type);
  }else{
    if(popupType!='refuse'){
      addRef.value.allowSubmitBtn();
    } else{
      refuseRef.value.handleRefuseSave()
    }
    
  }
 
};
/** 点击取消保存 */
const cancellation = (val) => {
  close(false);
};
/** 关闭弹窗 */
const close = (val) => {
  open.value = val;
};

const computedTimes = (type) => {
  const { registTime, registEndTime } = formData.value;

  let d1 = "";
  let d2 = "";

  if (registTime) {
    d1 = new Date(registTime.replace(/\-/g, "/"));
  }
  if (registEndTime) {
    d2 = new Date(registEndTime.replace(/\-/g, "/"));
  }

  // 检查时间是否合法
  if (
    registTime !== undefined &&
    registEndTime !== undefined &&
    registTime !== "" &&
    registEndTime !== "" &&
    d1 > d2
  ) {
    ElMessage.error("结束日期必须大于开始日期！");
    if (type === "start") {
      formData.value.registTime = "";
    } else if (type === "end") {
      formData.value.registEndTime = "";
    }
    return false;
  }
};

// 开始时间变化时触发
const registTimeChange = () => {
  computedTimes("start");
};

// 结束时间变化时触发
const registEndTimeChange = () => {
  computedTimes("end");
};
// 生命周期钩子：组件挂载时调用
onMounted(() => {
  getConfig();
  getList();
});
</script>
  
  <style scoped>
/* 添加自定义样式 */
</style>
  