<template>
  <div class="table-common">
    <el-table ref="publicTable" v-loading="configFlag.loading" @header-dragend="surverWidth" style="width: 100%"
      :data="tableData" :row-key="getRowKey" @selection-change="handleSelectionChange"
      @handleclearSelection="handleclearSelection" :height="tableHeight" :max-height="maxHeight" :header-cell-style="headerCellStyle"
      :row-style="rowStyle" :show-summary="showSummary" border>
      <!-- 全选单选 -->
      <el-table-column v-if="configFlag.selection" align="center" width="55" type="selection" :fixed="selectionFixed || false"
        :reserve-selection="reserveSelection" />
      <el-table-column type="index" v-if="configFlag.index" :label="indexName || '#'"
        :width="indexWidth || 60" align="center" :index="indexMethod"
        :fixed="indexFixed || false" :resizable="true">
      </el-table-column>

      <!-- 循环遍历表头展示数据 -->

      <el-table-column v-for="item in visibleColumns" :key="item.fieldIndex" :width="item.width || ''"
        :min-width="item.minWidth || ''" :prop="item.fieldIndex" :label="item.label" :align="item.align || 'center'"
        :sortable="item.sortable" :fixed="item.fixed || false" :header-align="item.align || 'center'"
        :resizable="item.resizable">
        <template #header="{ column }" v-if="item.visible">
          {{ column.label }}
          <el-tooltip class="item" effect="dark" v-if="item.headertip" :content="item.headertip" placement="top-start"
            popper-class="custom-tooltip">
            <i class="el-icon-warning"></i>
          </el-tooltip>
        </template>
        <template #default="scope">
          <!-- 字典的情况 -->
          <div v-if="item.type == 'dict'" :class="item.showOverFlowTooltip ? 'show-tooltip' : ''">
            <el-tooltip effect="light" placement="top" :show-after="openDelayNumber" :disabled="!tooltipFlag"
              popper-class="custom-tooltip">
              <div class="tooltipFlagStyle">
                <div @mouseenter="handleDictHover(scope.row, $event, item)" :style="textStyle(item)">
                  <div v-show="isDictReady(item)">
                    <dict-tag :options="getDictOptions(item)" :value="scope.row[item.fieldIndex]" />
                  </div>
                  <div v-show="!isDictReady(item)" class="dict-loading-status">
                    <span v-if="!item.loadError"></span>
                    <span v-else class="error-text">数据加载失败</span>
                  </div>
                </div>
              </div>
              <template #content>
                <dict-tag v-if="isDictReady(item)" :options="getDictOptions(item)"
                  :value="scope.row[item.fieldIndex]" />
                <span v-else>字典数据未就绪</span>
              </template>
            </el-tooltip>
          </div>

           <div v-else-if="item.type == 'dicts'" :class="item.showOverFlowTooltip ? 'show-tooltip' : ''">
            <el-tooltip effect="light" placement="top" :show-after="openDelayNumber" 
              popper-class="custom-tooltip">
              <div class="tooltipFlagStyle">
                <div @mouseenter="handleDictHovers(scope.row, $event, item)" :style="textStyle(item)">
                  <div v-show="isDictReadys(item)">
                  
                    <dict-tag :options="getDictOptions2(item)" :value="scope.row[item.fieldIndex]" />
                  </div>
                  <div v-show="!isDictReadys(item)" class="dict-loading-status">
                    <span v-if="!item.loadError"></span>
                    <span v-else class="error-text">数据加载失败</span>
                  </div>
                </div>
              </div>
              <template #content>
                <dict-tag v-if="isDictReadys(item)" :options="getDictOptions2(item)"
                  :value="scope.row[item.fieldIndex]" />
                <span v-else>字典数据未就绪</span>
              </template>
            </el-tooltip>
          </div>
         
          <div v-else-if="item.type == 'dollor'" :class="item.showOverFlowTooltip ? 'show-tooltip' : ''">
            <el-tooltip :disabled="!tooltipFlag" effect="light" placement="top" :show-after="openDelayNumber"
              popper-class="custom-tooltip">
              <div class="tooltipFlagStyle">
                <div @mouseenter="visibilityChange(scope.row, $event)" :style="{
                  overflow: 'hidden',
                  display: '-webkit-box',
                  'text-overflow': 'ellipsis',
                  '-webkit-line-clamp': item.lines ? item.lines : '1',
                  '-webkit-box-orient': 'vertical',
                  'white-space': 'normal',
                }">
                  <div class="amount-display">
                    {{ formatAmountToTwoDecimals(scope.row[item.fieldIndex]) }}
                  </div>
                </div>
              </div>
              <template #content>
                {{ formatAmountToTwoDecimals(scope.row[item.fieldIndex]) }}
              </template>
            </el-tooltip>
          </div>

          <div v-else-if="item.type == 'phoneHidden'" :class="item.showOverFlowTooltip ? 'show-tooltip' : ''">
            <el-tooltip :disabled="!tooltipFlag" effect="light" placement="top" :show-after="openDelayNumber"
              popper-class="custom-tooltip">
              <div class="tooltipFlagStyle">
                <div @mouseenter="visibilityChange(scope.row, $event)" :style="{
                  overflow: 'hidden',
                  display: '-webkit-box',
                  'text-overflow': 'ellipsis',
                  '-webkit-line-clamp': item.lines ? item.lines : '1',
                  '-webkit-box-orient': 'vertical',
                  'white-space': 'normal',
                }">
                  {{
                    scope.row[item.fieldIndex]
                      ? scope.row[item.fieldIndex].replace(
                        /(\d{3})(\d{4})(\d{4})/,
                        "$1****$3"
                      )
                      : "-"
                  }}
                </div>
              </div>
              <template #content>
                {{
                  scope.row[item.fieldIndex]
                    ? scope.row[item.fieldIndex].replace(
                      /(\d{3})(\d{4})(\d{4})/,
                      "$1****$3"
                    )
                    : "-"
                }}
              </template>
            </el-tooltip>
          </div>

          <div v-else-if="item.type == 'idNumberHidden'" :class="item.showOverFlowTooltip ? 'show-tooltip' : ''">
            <el-tooltip :disabled="!tooltipFlag" effect="light" placement="top" :show-after="openDelayNumber"
              popper-class="custom-tooltip">
              <div class="tooltipFlagStyle">
                <div @mouseenter="visibilityChange(scope.row, $event)" :style="{
                  overflow: 'hidden',
                  display: '-webkit-box',
                  'text-overflow': 'ellipsis',
                  '-webkit-line-clamp': item.lines ? item.lines : '1',
                  '-webkit-box-orient': 'vertical',
                  'white-space': 'normal',
                }">
                  {{
                    scope.row[item.fieldIndex]
                      ? scope.row[item.fieldIndex].replace(
                        /^(.{6})(?:\w+)(.{4})$/,
                        "\$1********\$2"
                      )
                      : "-"
                  }}
                </div>
              </div>
              <template #content>
                {{
                  scope.row[item.fieldIndex]
                    ? scope.row[item.fieldIndex].replace(
                      /^(.{6})(?:\w+)(.{4})$/,
                      "\$1********\$2"
                    )
                    : "-"
                }}
              </template>
            </el-tooltip>
          </div>

          <div v-else-if="item.type == 'showZero'" :class="item.showOverFlowTooltip ? 'show-tooltip' : ''">
            <el-tooltip :disabled="!tooltipFlag" effect="light" placement="top" :show-after="openDelayNumber"
              popper-class="custom-tooltip">
              <div class="tooltipFlagStyle">
                <div @mouseenter="visibilityChange(scope.row, $event)" :style="{
                  overflow: 'hidden',
                  display: '-webkit-box',
                  'text-overflow': 'ellipsis',
                  '-webkit-line-clamp': item.lines ? item.lines : '1',
                  '-webkit-box-orient': 'vertical',
                  'white-space': 'normal',
                }">
                  {{
                    scope.row[item.fieldIndex]
                      ? scope.row[item.fieldIndex]
                      : "0"
                  }}
                </div>
              </div>
              <template #content>
                {{
                  scope.row[item.fieldIndex] ? scope.row[item.fieldIndex] : "0"
                }}
              </template>
            </el-tooltip>
          </div>

          <div v-else-if="item.type == 'clickText'" :class="item.showOverFlowTooltip ? 'show-tooltip' : ''">
            <el-tooltip :disabled="!tooltipFlag" effect="light" placement="top" :show-after="openDelayNumber"
              popper-class="custom-tooltip">
              <div class="tooltipFlagStyle click-text">
                <div @click="clickTextDetail(scope.row)" @mouseenter="visibilityChange(scope.row, $event)" :style="{
                  overflow: 'hidden',
                  display: '-webkit-box',
                  'text-overflow': 'ellipsis',
                  '-webkit-line-clamp': item.lines ? item.lines : '1',
                  '-webkit-box-orient': 'vertical',
                  'white-space': 'normal',
                }">
                  {{ scope.row[item.fieldIndex] }}
                </div>
              </div>
              <template #content>
                {{ scope.row[item.fieldIndex] }}
              </template>
            </el-tooltip>
          </div>

          <!-- 根据需求添加效果 返回的slot可以优化.自己来吧.可以实现操作列等 -->
          <slot v-else-if="
            (item.type == undefined || item.type == '') && item.slotname
          " :scope="scope" :name="item.slotname">
          </slot>

          <!-- 最普通的情况 -->
          <div v-else :class="item.showOverFlowTooltip ? 'show-tooltip' : ''">
            <el-tooltip :disabled="!tooltipFlag" effect="light" placement="top-start" :show-after="openDelayNumber"
              popper-class="custom-tooltip">
              <div class="tooltipFlagStyle">
                <div @mouseover="visibilityChange(scope.row, $event)" :style="{
                  overflow: 'hidden',
                  display: '-webkit-box',
                  'text-overflow': 'ellipsis',
                  '-webkit-line-clamp': item.lines ? item.lines : '1',
                  '-webkit-box-orient': 'vertical',
                  'white-space': 'normal',
                }">
                  {{ scope.row[item.fieldIndex] || item.defaultLabel }}
                </div>
              </div>
              <template #content>
                {{ scope.row[item.fieldIndex] || item.defaultLabel }}
              </template>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <pagination :key="indexChange" v-show="configFlag.needPage && total > 0" :total="total"
      v-model:page="pageValue.pageNum" v-model:limit="pageValue.pageSize" @pagination="getList" />
  </div>
</template>


<script setup>
import { ref, reactive, watch, onMounted, computed, nextTick } from "vue";

const indexChange = ref(0);
const tableHeight = ref(null);
const tooltipFlag = ref(false);
const publicTable = ref(null); // Define publicTable here
const dataToParent = ref("我是子组件的数据");
const props = defineProps({
  openDelayNumber: {
    type: Number,
    default: 200,
  },
  // 多选保存选中数据
  rowKey: {
    type: String,
    default: "id",
  },
  showSearch: {
    type: Boolean,
    default: true,
  },
  // 为表头设计样式
  headerCellStyle: {
    default: () => {
      return {
        background: "#f8f8f9",
        color: "#515a6e",
        fontWeight: "600",
        borderTop: "1px solid #dfe6ec",
        fontSize: "13px",
        height: "40px",
        fontFamily: "PingFangRegular",
      };
    },
  },

  // 为每一行设计样式，比如设置每行的高度等等
  rowStyle: {
    default: () => {
      return { height: "50px" };
    },
  },
  columns: {
    // 表头数据  文案和绑定值，以及需要特殊处理的slot
    type: Array,
    default: () => [],
  },
  tableData: {
    type: Array, // 后台数据
    default: () => [],
  },
  // 分页参数
  pageValue: {
    // 分页数据
    type: Object,
    default: () => {
      return {
        pageNum: 1,
        pageSize: 10,
        currentPage: 1, //当前页
      };
    },
  },

  // 每页多少条的选项
  pageSizes: {
    type: Array,
    default: () => {
      return [10, 20, 50, 100];
    },
  },
  //总页码
  total: {
    type: Number,
    default: 0,
  },
  // 表格配置项
  configFlag: {
    // 配置  其他table配置依次添加
    type: Object,
    default: () => {
      return {
        loading: false,
        needPage: false, // 是否需要分页
        selection: false, // 是否需要多选
        index: false, // 是否需要序号
        indexWidth: 60, //序号列宽
        indexFixed: false,
        height: null,
        showSummary: false,
        btn: false, //序号添加自定义html
        // 这里不全面，可根据实际情况添加
      };
    },
  },

  maxHeight: {
    // 可以监听屏幕高度获取。
    // 高度
    type: Number,
    default: () => 900,
  },

  getList: {
    type: Function,
  },

  // 序号列宽度
  indexWidth: {
    type: Number,
    default: 60,
  },

  // 序号列名称
  indexName: {
    type: String,
    default: '#',
  },

  // 序号列是否固定
  indexFixed: {
    type: [Boolean, String],
    default: false,
  },
});

const {
  openDelayNumber,
  rowKey,
  showSearch,
  headerCellStyle,
  rowStyle,
  columns,
  tableData,
  pageValue,
  pageSizes,
  total,
  configFlag,
  maxHeight,
} = toRefs(props);

const {
  loading,
  needPage,
  selection,
  index,
  indexWidth,
  indexFixed,
  reserveSelection,
  selectionFixed,
  showSummary,
} = toRefs(configFlag);

const emit = defineEmits(['clickTextDetail', 'indexWidthChange'])
const visibleColumns = computed(() =>
  columns.value
    .filter((item) => item && item.visible)
    .map((item) => ({
      ...item,
      label: item.label || "",
    }))
);

watch(
  configFlag,
  (newValue, oldValue) => {
    nextTick(() => {
      if (configFlag.value) {
      }
    });
  },
  { immediate: true, deep: true }
);

onMounted(() => {
  nextTick(() => {
    tableHeight.value = configFlag.value.height;

    columns.value.forEach((item, index) => {
      if (item.replaceIndex || item.replaceIndex == 0) {
        moveElement(item, item.replaceIndex, item.replaceIndex);
      }
    });
  });
  // const clueTable = JSON.parse(localStorage.getItem("clueTable17"));
  // if (clueTable.length > 0) {
  //   this.columns = clueTable;
  // }
  //订阅消息
  // PubSub.subscribe('handleDrop', (msg, searchName) => {
  //     this.handleDrop()
  // })
});



// 增强字典状态管理
const dictState = reactive({
  loadedColumns: new Map(), // 使用Map存储加载状态和缓存数据
  loadingColumns: new Set(),
  errorColumns: new Set()
})

// 优化后的字典检测方法
const isDictReady = (item) => {
  return Boolean(
    dictState.loadedColumns.get(item.fieldIndex)?.data &&
    !dictState.errorColumns.has(item.fieldIndex)
  )
}


// 增强字典状态管理
const dictStates = reactive({
  loadedColumns2: new Map(), // 使用Map存储加载状态和缓存数据
  loadingColumns2: new Set(),
  errorColumns2: new Set()
})

// 优化后的字典检测方法
const isDictReadys = (item) => {
  return Boolean(
    dictStates.loadedColumns2.get(item.fieldIndex)?.data &&
    !dictStates.errorColumns2.has(item.fieldIndex)
  )
}


// 处理字典列hover事件
const handleDictHover = async (row, event, item) => {
  visibilityChange(row, event)

  // 如果已经有缓存数据或正在加载，不再请求
  if (
    dictState.loadedColumns.has(item.fieldIndex) ||
    dictState.loadingColumns.has(item.fieldIndex)
  ) return

  try {
    dictState.loadingColumns.add(item.fieldIndex)
    
    // 模拟异步获取字典数据
    const dictData = await fetchDictData(item.dictType)
    
    dictState.loadedColumns.set(item.fieldIndex, {
      data: dictData,
      timestamp: Date.now()
    })
    item.dictList = dictData // 更新原始数据
    
    dictState.errorColumns.delete(item.fieldIndex)
  } catch (error) {
    console.error('字典数据加载失败:', error)
    dictState.errorColumns.add(item.fieldIndex)
  } finally {
    dictState.loadingColumns.delete(item.fieldIndex)
  }
}
// 处理字典列hover事件
const handleDictHovers = async (row, event, item) => {
      tooltipFlag.value =true
  // 如果已经有缓存数据或正在加载，不再请求
  if (
    dictStates.loadedColumns2.has(item.fieldIndex) ||
    dictStates.loadingColumns2.has(item.fieldIndex)
  ) return
  try {
    dictStates.loadingColumns2.add(item.fieldIndex)
    
    // 模拟异步获取字典数据
    const dictData = await fetchDictData(item.dictType)
    
    dictStates.loadedColumns2.set(item.fieldIndex, {
      data: dictData,
      timestamp: Date.now()
    })
    item.dictList = dictData // 更新原始数据
    
    dictStates.errorColumns2.delete(item.fieldIndex)

  } catch (error) {
    console.error('字典数据加载失败:', error)
    dictStates.errorColumns2.add(item.fieldIndex)
  } finally {
    dictStates.loadingColumns2.delete(item.fieldIndex)
  }
}

// 样式统一处理
const textStyle = (item) => ({
  overflow: 'hidden',
  display: '-webkit-box',
  textOverflow: 'ellipsis',
  WebkitLineClamp: item.lines || 1,
  WebkitBoxOrient: 'vertical',
  whiteSpace: 'normal'
})

// 获取字典选项时优先使用缓存
const getDictOptions = (item) => {
  const cached = dictState.loadedColumns.get(item.fieldIndex)
  return cached?.data || item.dictList || []
}
// 获取字典选项时优先使用缓存
const getDictOptions2 = (item) => {
  const cached = dictStates.loadedColumns2.get(item.fieldIndex)
  return cached?.data || item.dictList || []
}
// 监听columns变化（父组件需要确保使用深拷贝更新）
// 初始化时处理已有字典数据
watch(() => props.columns, (newColumns) => {
  newColumns.forEach(col => {
    if (col.type === 'dict' && col.dictList?.length) {
      dictState.loadedColumns.set(col.fieldIndex, {
        data: col.dictList,
        timestamp: Date.now()
      })
    }
    // 缺少对 dicts 类型的初始化
    if (col.type === 'dicts' && col.dictList?.length) {
      dictStates.loadedColumns2.set(col.fieldIndex, {
        data: col.dictList,
        timestamp: Date.now()
      })
    }
  })
}, { immediate: true, deep: true })


const moveElement = (element, replaceIndex, toIndex) => {
  var element = element;
  columns.value.forEach((item, index) => {
    if (item.replaceIndex == replaceIndex) {
      if (index === toIndex) return columns.value;
      columns.value.splice(index, 1);
      columns.value.splice(toIndex, 0, element);
    }
  });

  return columns.value;
};

const visibilityChange = (row, event) => {
  const ev = event.target;
  if (ev.clientHeight < ev.scrollHeight) {
    // 如果实际内容高度 > 文本高度 ，就代表内容溢出了
    tooltipFlag.value = true;
  } else {
    // 否则为不溢出
    tooltipFlag.value = null;
  }
};

const peoplesArrar = (str) => {
  let list = [];
  if (str) {
    list = str.split(",");
  }
  return list;
};

const indexMethod = (index) => {
  return (pageValue.value.pageNum - 1) * pageValue.value.pageSize + index + 1;
};

const handleDrop = () => {
  Number(rowKey.value++);
  saveTableColumns();
};

const surverWidth = (newWidth, oldWidth, column, event) => {
  // 如果是序号列，单独处理
  if (column.type === 'index') {
    // 序号列宽度调整不影响其他列，只更新自身宽度
    emit('indexWidthChange', newWidth);
    return;
  }

  // 处理其他列的宽度调整
  columns.value = columns.value.map((v) => {
    if (v.prop === column.property) v.width = newWidth;
    return v;
  });
  saveTableColumns();
};

// 显示隐藏切换 && 保存表格配置
const saveTableColumns = () => {
  // setStorage 封装了 localStorage
  localStorage.setItem("clueTable17", JSON.stringify(columns.value));
};

const getRowKey = (row) => {
  return row[rowKey.value];
};

const statMaps = (list) => {
  if (!list) return;
  let obj = {};
  list.forEach((item) => {
    obj[item.value || item.id] = item.label || item.value;
  });
  return obj;
};

// 金额千位展示：1,234,567,890
const getMoneyK = (money) => {
  if (typeof money === "number") {
    money = money.toString();
  }
  var pattern = /(-?\d+)(\d{3})/;
  while (pattern.test(money)) {
    money = money.replace(pattern, "$1,$2");
  }
  return money;
};

// 清空选中
const clearSelected = () => {
  // 父组件通过ref调用clearSelected方法，例如 this.$refs.clearTable.clearSelected()
  publicTable.value.clearSelection();
};

/*
     默认选中
     需要默认选中的在组件中通过this.$refs.publicTable.selected(默认选中的数据:Array)
    */
const selected = (data) => {
  if (data.length > 0) {
    data.forEach((item) => {
      publicTable.value.toggleRowSelection(item, true);
    });
  }
};

// 设置条数
const sizeChange = (size) => {
  emit("sizeChange", size);
};

// 翻页，直接跳转
const currentChange = (page) => {
  emit("handleChange", page);
};

//上一页
const prevClick = (val) => {
  emit("prevClick", val);
};

//下一页
const nextClick = (val) => {
  emit("nextClick", val);
};

// 多选
const handleSelectionChange = (val) => {
  emit("handleSelectionChange", val);
};

// 多选
const handleSelection = (val, row) => {
  emit("handleSelection", { val, row });
};

// 清空多选
const handleclearSelection = () => {
  publicTable.value.clearSelection();
};

const handleCellEnter = (row, column, cell, event) => {
  emit("handleCellEnter", { row, column, cell, event });
};

//编辑
const handleEdit = (index, row, colIndex, field) => {
  emit("handleEdit", { index, row, colIndex, field });
};

//下拉框事件
const onSelected = (index, row, field) => {
  emit("onSelected", { index, row, field });
};

//按钮点击事件
const onClickBtn = (index, row) => {
  emit("onClickBtn", { index, row });
};

// 点击文件详情
const clickTextDetail = (row) => {
  emit("clickTextDetail", row);
};

//下一页
const getList = () => {
  props.getList();
};



/**
 * 表格列宽自适应
 * @param prop 属性
 * @param records 数据
 * @param minWidth 最小宽度
 */
const getColumnWidth = (prop, records, minWidth = 80) => {
  const padding = 12; // 列内边距

  const contentWidths = records.map((item) => {
    const value = item[prop] ? String(item[prop]) : "";
    const textWidth = getTextWidth(value);
    return textWidth + padding;
  });

  const maxWidth = Math.max(...contentWidths);
  return Math.max(minWidth, maxWidth);
};
/**
 * el-table扩展工具  -- 列宽度自适应 - 获取列宽内文本宽度
 * @param {*} text 文本内容
 * @returns 文本宽度(int)
 */
const getTextWidth = (text) => {
  const span = document.createElement("span");
  span.style.visibility = "hidden";
  span.style.position = "absolute";
  span.style.top = "-9999px";
  span.style.whiteSpace = "nowrap";
  span.style.fontSize = "16px";
  span.innerText = text;
  document.body.appendChild(span);
  const width = span.offsetWidth + 16;
  document.body.removeChild(span);
  return width;
};

/**
 * 将金额格式化为两位小数（如果不是的话），返回字符串
 * @param {number|string} value - 输入的金额
 * @returns {string} 格式化后的两位小数字符串
 */
const formatAmountToTwoDecimals = (value) => {
  // 1. 转换为数值类型
  const number = Number(value);

  // 2. 检查是否为有效数字
  if (isNaN(number)) {
    console.warn("Invalid amount value:", value);
    return ""; // 或返回 '0.00' 或其他默认值
  }

  // 3. 格式化为两位小数（四舍五入）
  return number.toFixed(2);
};

defineExpose({
  selected
})
</script>

<style lang="scss" scoped>
.table-common {
  background: #fff;
}

.show-tooltip {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

:deep(.el-table .cell) {
  font-family: SYPXZT;
  // min-width: 120px;
  font-size: 14px;
}

:deep(.show-tooltip > p) {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

:deep(.el-table td.el-table__cell),
:deep(.el-table th.el-table__cell.is-leaf) {
  border-bottom: 1px solid #dfe6ec;
}

:deep(.el-table--border .el-table__cell),
:deep(.el-table__body-wrapper .el-table--border.is-scrolling-left ~ .el-table__fixed) {
  border-right: 1px solid #dfe6ec;
}

:deep(.el-table__body-wrapper .el-table--border .el-table__cell .cell) {
  color: #606266;
}

.nameBlcok {
  display: flex;
  flex-wrap: wrap;
  width: 200px;

  .nameBlock-one {
    padding: 0px 10px;
    width: calc((100% - 12px) / 2);
    margin-right: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 30px;
    line-height: 30px;
    background: #1979d8;
    color: #ffffff;
    font-size: 14px;
    margin-bottom: 10px;
    border-radius: 5px;

    &:nth-child(2n) {
      margin-right: 0px;
    }
  }
}

.enevTexts {
  text-align: left;
}

.tooltipFlagStyle {
  white-space: pre-line;
  // max-width: 600px;
  // min-width:120px;
  line-height: 22px;
  font-size: 14px;
  // max-height: 16rem;
  overflow: auto;
}

.table-common .pagination-container {
  height: 60px;
  margin-top: 10px;
}

.click-text {
  cursor: pointer;
  color: #1979d8;
}

.amount-display {
  text-align: right;
}

/* 序号列样式优化 */
:deep(.el-table .el-table-column--selection),
:deep(.el-table .el-table__column) {
  &.is-center {
    text-align: center;
  }
}

/* 确保序号列宽度调整不影响其他列 */
:deep(.el-table th.el-table__cell) {
  &[aria-label*="#"] {
    min-width: 60px;
    max-width: 120px;
  }
}

/* 序号列拖拽调整时的样式 */
:deep(.el-table .el-table__header .el-table__cell) {
  .cell {
    position: relative;
  }

  &.gutter {
    width: 15px !important;
  }
}
</style>
