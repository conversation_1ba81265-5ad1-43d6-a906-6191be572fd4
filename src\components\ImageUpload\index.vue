<template>
  <div class="component-upload-image">
    <el-upload
      multiple
      :action="baseUrl + uploadImgUrl"
      list-type="picture-card"
      :on-success="handleUploadSuccess"
      :before-upload="handleBeforeUpload"
      :limit="limit"
      :on-error="handleUploadError"
      :on-exceed="handleExceed"
      ref="imageUpload"
      :before-remove="handleDelete"
      :show-file-list="true"
      :headers="headers"
      :file-list="fileList"
      :on-preview="handlePictureCardPreview"
        :data="paramsData"
      :class="{ hide: fileList.length >= limit }"
    >
      <el-icon class="avatar-uploader-icon"><plus /></el-icon>

      <!-- 自定义文件列表 -->
      <template #file="{ file }">
        <div class="image-item">
          <img :src="file.url" class="image" @click="handlePictureCardPreview(file)" />
          <span class="delete-icon" @click.stop="handleDelete(file)">
            <el-icon><delete /></el-icon>
          </span>
        </div>
      </template>
    </el-upload>
    <!-- 上传提示 -->
    <div class="el-upload__tip" v-if="showTip">
      请上传
      <template v-if="fileSize">
        大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b>
      </template>
      <template v-if="fileType">
        格式为 <b style="color: #f56c6c">{{ fileType.join("/") }}</b>
      </template>
      的文件
    </div>

    <el-dialog
      v-model="dialogVisible"
      title="预览"
      width="800px"
      append-to-body
    >
      <img
        :src="dialogImageUrl"
        style="display: block; max-width: 100%; margin: 0 auto"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import { getToken } from "@/utils/auth";
import useUserStore from "@/store/modules/user"
import axios from 'axios'; // 确保已导入axios
const props = defineProps({
  modelValue: [String, Object, Array],
  // 图片数量限制
  limit: {
    type: Number,
    default: 5,
  },
  // 大小限制(MB)
  fileSize: {
    type: Number,
    default: 5,
  },
  // 文件类型, 例如['png', 'jpg', 'jpeg']
  fileType: {
    type: Array,
    default: () => ["png", "jpg", "jpeg"],
  },
  // 是否显示提示
  isShowTip: {
    type: Boolean,
    default: true
  },
  uploadImgUrl:{
    type: String,
    default:"/common/upload"
  },
  paramsData:{
    type:Object,
    default:()=>{}
  }
});
import { cmsImg } from '@/utils/config';
const { proxy } = getCurrentInstance();
const userStore = useUserStore();
const emit = defineEmits();
const number = ref(0);
const uploadList = ref([]);
const dialogImageUrl = ref("");
const dialogVisible = ref(false);
const baseUrl = import.meta.env.VITE_APP_BASE_API;
// const uploadImgUrl = ref(import.meta.env.VITE_APP_BASE_API + "/common/upload"); // 上传的图片服务器地址
const headers = ref({ Authorization: "Bearer " + getToken() });
const fileList = ref([]);
const showTip = computed(
  () => props.isShowTip && (props.fileType || props.fileSize)
);




// 在组件外部定义Blob URL清理逻辑
const blobUrlStore = new Set();

// 组件卸载时释放所有Blob URL
onUnmounted(() => {
  blobUrlStore.forEach(url => URL.revokeObjectURL(url));
  blobUrlStore.clear();
});

// 图片请求缓存（防止重复请求）
const imageCache = new Map();

// 独立图片加载方法
const loadImageBlob = async (fileId, jwtToken) => {
  try {
    // 检查缓存
    if (imageCache.has(fileId)) {
      return imageCache.get(fileId);
    }

    const response = await axios.get(
      `${import.meta.env.VITE_APP_BASE_API}/${cmsImg}${fileId}`,
      {
        responseType: 'blob',
        headers: { jwtToken },
        validateStatus: status => status === 200 // 只认200成功
      }
    );

    // 直接使用响应中的Blob（无需重新包装）
    const blob = response.data;
    const url = URL.createObjectURL(blob);
    
    // 缓存结果
    imageCache.set(fileId, url);
    blobUrlStore.add(url);

    return url;
  } catch (error) {
    console.error(`图片加载失败: ${fileId}`, error);
    return null; // 返回null而不是抛出错误
  }
};


watch(
  () => props.modelValue,
  async (val) => {
    // 清理旧数据
    fileList.value = [];

    if (!val) {
      return;
    }

    // 统一转为数组
    const rawList = Array.isArray(val) 
      ? [...val] 
      : val.split(',').map(s => s.trim());

    // 并行处理但限制并发数
    const MAX_CONCURRENT = 3;
    const resultList = [];
    
    for (let i = 0; i < rawList.length; i += MAX_CONCURRENT) {
      const chunk = rawList.slice(i, i + MAX_CONCURRENT);
      const chunkResults = await Promise.all(
        chunk.map(async (item) => {
          // 处理字符串和对象两种格式
          const fileId = typeof item === 'string' 
            ? item.replace(baseUrl, '') 
            : item.fileId;

          const url = await loadImageBlob(fileId, userStore.jwtToken);
          
          return {
            name: fileId,
            url: url || `${baseUrl}${fileId}`, // 失败时回退到原始URL
            status: url ? 'success' : 'error'
          };
        })
      );
      
      resultList.push(...chunkResults);
    }

    // 更新响应式数据
    fileList.value = resultList;
  },
  { deep: true, immediate: true }
);








// watch(() => props.modelValue, val => {
//   if (val) {
//     // 首先将值转为数组
//     const list = Array.isArray(val) ? val : props.modelValue.split(",");
//     // 然后将数组转为对象数组
//     fileList.value = list.map(item => {
//       if (typeof item === "string") {
//         if (item.indexOf(baseUrl) === -1) {
//           item = { name: baseUrl + item, url: baseUrl + item };
//         } else {
//           item = { name: item, url: item };
//         }
//       }
//       return item;
//     });
//   } else {
//     fileList.value = [];
//     return [];
//   }
// },{ deep: true, immediate: true });





// 上传前loading加载
function handleBeforeUpload(file) {
  let isImg = false;
  if (props.fileType.length) {
    let fileExtension = "";
    if (file.name.lastIndexOf(".") > -1) {
      fileExtension = file.name.slice(file.name.lastIndexOf(".") + 1);
    }
    isImg = props.fileType.some(type => {
      if (file.type.indexOf(type) > -1) return true;
      if (fileExtension && fileExtension.indexOf(type) > -1) return true;
      return false;
    });
  } else {
    isImg = file.type.indexOf("image") > -1;
  }
  if (!isImg) {
    proxy.$modal.msgError(
      `文件格式不正确, 请上传${props.fileType.join("/")}图片格式文件!`
    );
    return false;
  }
  if (props.fileSize) {
    const isLt = file.size / 1024 / 1024 < props.fileSize;
    if (!isLt) {
      proxy.$modal.msgError(`上传头像图片大小不能超过 ${props.fileSize} MB!`);
      return false;
    }
  }
  proxy.$modal.loading("正在上传图片，请稍候...");
  number.value++;
}

// 文件个数超出
function handleExceed() {
  proxy.$modal.msgError(`上传文件数量不能超过 ${props.limit} 个!`);
}

// 上传成功回调
function handleUploadSuccess(res, file) {
  if (res.code == '1') {
    uploadList.value.push({ name: res.data.fileName, url: res.data.url,fileId:res.data.fileId });
    uploadedSuccessfully();
  } else {
    number.value--;
    proxy.$modal.closeLoading();
    proxy.$modal.msgError(res.message);
    proxy.$refs.imageUpload.handleRemove(file);
    uploadedSuccessfully();
  }
}

// 删除图片
function handleDelete(file) {
  const findex = fileList.value.map(f => f.name).indexOf(file.name);
  if (findex > -1 && uploadList.value.length === number.value) {
    fileList.value.splice(findex, 1);
    emit("update:modelValue", listToString(fileList.value));
    return false;
  }
}

// 上传结束处理
function uploadedSuccessfully() {
  if (number.value > 0 && uploadList.value.length === number.value) {
    fileList.value = fileList.value.filter(f => f.url !== undefined).concat(uploadList.value);
    uploadList.value = [];
    number.value = 0;
    // emit("update:modelValue", listToString(fileList.value));
    emit("update:modelValue",fileList.value);
    proxy.$modal.closeLoading();
  }
}

// 上传失败
function handleUploadError() {
  proxy.$modal.msgError("上传图片失败");
  proxy.$modal.closeLoading();
}

// 预览
function handlePictureCardPreview(file) {
  dialogImageUrl.value = file.url;
  dialogVisible.value = true;
}

// 对象转成指定字符串分隔
function listToString(list, separator) {
  let strs = "";
  separator = separator || ",";
  for (let i in list) {
    if (undefined !== list[i].url && list[i].url.indexOf("blob:") !== 0) {
      strs += list[i].url.replace(baseUrl, "") + separator;
    }
  }
  return strs != "" ? strs.substr(0, strs.length - 1) : "";
}
</script>

<style scoped lang="scss">
// .el-upload--picture-card 控制加号部分
:deep(.hide .el-upload--picture-card) {
    display: none;
}


// 图片容器
.image-item {
  position: relative;
  width: 100%;
  height: 100%;

  .image {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  // 删除按钮
  .delete-icon {
    position: absolute;
    top: 0;
    right: 0;
    cursor: pointer;
    color: #fff;
    background-color: rgba(0, 0, 0, 0.5);
    padding: 4px;
    border-radius: 0 0 0 4px;

    &:hover {
      background-color: rgba(0, 0, 0, 0.7);
    }
  }
}
</style>