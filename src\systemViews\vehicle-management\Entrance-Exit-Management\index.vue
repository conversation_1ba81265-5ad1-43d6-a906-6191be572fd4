<!-- 出入口管理 -->

<template>
  <div class="app-container">
    <Splitpanes class="default-theme">
      <Pane :size="20" :min-size="10">
        <el-card class="dep-card" style="height: 100%">
      
          <treeLoad
            ref="myTree"
            :isShowSearch="true"
            :defaultProps="defaultProps"
            :treeData="treeData"
            :treeBtnEdit="false"
            :treeBtnDelete="false"
            treeName="label"
            :isLazy="false"
            @checkedKeys="handleNodeClick"
    />
        </el-card>
      </Pane>
      <Pane :size="80" :min-size="60">
        <el-card class="dep-card" style="height: 100%">
          <dialog-search @getList="getList" formRowNumber="3" :columns="tabelForm.columns" :isShowRightBtn="$checkPermi(['parking:passageway:list'])">
            <template v-slot:formList>
              <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="90px">
                <el-form-item label="出入口名称" prop="parkingName">
                  <el-input v-model="queryParams.parkingName" placeholder="请输入出入口名称" clearable  />
                </el-form-item>
                <el-form-item label="启用状态" prop="status">
                  <el-select v-model="queryParams.status" placeholder="请选择启用状态" clearable >
                    <el-option v-for="(item, index) of major_parking_lot_status" :key="index" :label="item.label"
                      :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-form>
            </template>
            <template v-slot:searchList>
              <el-button type="primary" icon="Search" @click="handleQuery"  v-hasPermi="['parking:passageway:list']">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery" v-hasPermi="['parking:passageway:list']">重置</el-button>
            </template>

            <template v-slot:searchBtnList>
              <el-button type="primary"  icon="Plus" @click="handleAdd" v-hasPermi="['parking:passageway:add']">新增
              </el-button>

              <el-button  icon="Download" v-hasPermi="['parking:passageway:export']"
                @click="handleExport">导出
              </el-button>
            </template>
          </dialog-search>

          <public-table ref="publictable" :rowKey="tabelForm.tableKey" :tableData="list" :columns="tabelForm.columns"
            :configFlag="tabelForm.tableConfig" :pageValue="pageParams" :total="total" :getList="getList">
            <template #operation="{ scope }">
              <el-button size="mini" type="text" icon="View" @click="handleView(scope.row)" title="查看"  v-hasPermi="['parking:passageway:list']"></el-button>

              <el-button link icon="Edit" type="primary" title="修改" @click="handleUpdate(scope.row)"
                v-hasPermi="['parking:passageway:edit']">
              </el-button>
              <el-button size="mini" type="text" icon="Delete" @click="handleDelete(scope.row)"
                v-hasPermi="['parking:passageway:remove']" title="删除"></el-button>

            
            </template>
          </public-table>
        </el-card>
      </Pane>
    </Splitpanes>

    <DialogBox :visible="open1" :dialogWidth="diaWindow.dialogWidth" @save="save" @cancellation="cancellation"
      @close="close"  :dialogFooterBtn="diaWindow.dialogFooterBtn" CloseSubmitText="取消" SaveSubmitText="确定" :dialogTitle="diaWindow.dialogTitle">
      <template #content>
        <addparkinglot ref="addparkinglotRef" :popupType="diaWindow.popupType" v-if="
          diaWindow.popupType == 'edit' ||
          diaWindow.popupType == 'add' ||
          diaWindow.popupType == 'view'
          
        " @cancellationRefresh="cancellationRefresh"   :rowData="diaWindow.rowData"></addparkinglot>
      </template>

    </DialogBox>
  </div>
</template>

<script setup name="EntranceExitManagement">

import { ref, reactive,getCurrentInstance} from "vue";
import { apiUrl } from '@/utils/config'; 
import {
  findpassageway,
  deleteParkingById,
  findparkinglotTree,
} from "@/api/majorParking/exit";
import { formatMinuteTime } from '@/utils';
import { ElMessage, ElMessageBox } from 'element-plus';
import addparkinglot from "@/systemViews/vehicle-management/Entrance-Exit-Management/components/addparkinglot.vue"; //修改出入口
import treeLoad from "@/components/Tree/treeLoad";
const addparkinglotRef = ref(null);
const { proxy } = getCurrentInstance();
const { major_parking_lot_status } = proxy.useDict("major_parking_lot_status");



const diaWindow = reactive({
  popupType: '',
  rowData: {},
  dialogWidth: '75%',
  dialogTitle:"",
  dialogFooterBtn:false
})



const pageParams = ref({
  pageNum: 1,
  pageSize: 10,
});

const queryParams = ref({

  parentId:'',
  parkingName: "",
  status: "",
});
const open1 = ref(false);

const list = ref([]);

// 遮罩层
const total = ref(0);
const tabelForm = reactive({
  tableKey: "1", //表格key值
  isShowRightToolbar: true, //是否显示右侧显示和隐藏
  showSearch: true,
  // 表格表格数据
  columns: [
    {
      fieldIndex: "parkingName", // 对应列内容的字段名
      label: "出入口名称", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "", //最小宽度%
      width: "180px", //最小宽度%
      align: "left", //表格对齐方式
    },
    {
      fieldIndex: "parkingAreaName", // 对应列内容的字段名
      label: "上级区域", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "", //最小宽度%
      width: "180px", //宽度
      align: "left", //表格对齐方式
    },

    {
      fieldIndex: "managerNickName", // 对应列内容的字段名
      label: "负责人", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "", //最小宽度%
      width: "120px", //宽度
      align: "left", //表格对齐方式
    },
    {
      fieldIndex: "managerPhone", // 对应列内容的字段名
      label: "手机号码", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "", //最小宽度%
      width: "150px", //宽度
      align: "", //表格对齐方式
       type:'phoneHidden'
    },


    {
      fieldIndex: "status", // 对应列内容的字段名
      label: "启用状态", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "", //最小宽度%
      width: "120px", //宽度
      align: "", //表格对齐方式
      type: "dict",
      dictList: major_parking_lot_status,
    },
    {
      fieldIndex: "addr", // 对应列内容的字段名
      label: "详细地址", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "200px", //最小宽度%
      width: "", //宽度
      align: "left", //表格对齐方式
    },
    {
      fieldIndex: "createDate", // 对应列内容的字段名
      label: "创建时间", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "", //最小宽度%
      width: "180px", //宽度
      align: "", //表格对齐方式
    },
    {
      label: "操作",
      slotname: "operation",
      width: "150",
      fixed: "right", //固定
      visible: true,
    },
  ],
  // 表格基础配置项，看个人需求添加
  tableConfig: {
    needPage: true, // 是否需要分页
    index: true, // 是否需要序号
    selection: false, // 是否需要多选
    reserveSelection: false, // 是否需要支持跨页选择
    indexFixed: false, // 序号列固定 left true right
    selectionFixed: false, //多选列固定left true right
    indexWidth: "50", //序号列宽度
    loading: false, //表格loading
    showSummary: false, //是否开启合计
    height: null, //表格固定高度 比如：300px
  },
});

/** 查询用户列表 */
const getList = () => {
  tabelForm.tableConfig.loading = true;
  findpassageway(pageParams.value, queryParams.value).then((response) => {
   
    list.value = response.data.records;
    total.value = response.data.total;
    tabelForm.tableConfig.loading = false;
  });
};

/** 搜索按钮操作 */
const handleQuery = () => {
  pageParams.value.pageNum = 1;
  getList();
};
/** 重置按钮操作 */
const resetQuery = () => {
  proxy.resetForm("queryForm");
  handleQuery();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy.download(
    `park${apiUrl}/majorParking/passageway/export`,
    {
      ...queryParams.value,
    },
    `出入口列表_${formatMinuteTime(new Date())}.xlsx`
  );
};
/** 删除 */
const handleDelete = (row) => {
  ElMessageBox.confirm('确定删除该出入口吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      deleteParkingById(row.id).then((res) => {
        if (res.code == '1') {
          ElMessage.success('删除成功');
          getList(); // 调用获取列表的方法
          findparkinglotTreeFun()
        }
      });
    })
    .catch(() => {
      // 用户点击了取消
    });
};

/** 新增 */
const handleAdd = () => {
  diaWindow.popupType = 'add'
  diaWindow.dialogTitle = '新增出入口'
  diaWindow.dialogWidth = '60%'
  diaWindow.dialogFooterBtn = true
  diaWindow.rowData= {}
  open1.value = true;
};
/** 查看 */
const handleView = (data) => {
  diaWindow.popupType = 'view'
  diaWindow.rowData = data
   diaWindow.dialogTitle = '查看出入口'
   diaWindow.dialogFooterBtn = false
     diaWindow.dialogWidth = '60%'
  open1.value = true;
};

/** 修改 */
const handleUpdate = (data) => {
  diaWindow.popupType = 'edit'
  diaWindow.rowData = data
  diaWindow.dialogTitle = '修改出入口'
  diaWindow.dialogFooterBtn = true
    diaWindow.dialogWidth = '60%'
  open1.value = true;
};

/** 提交保存 */
const save = (val) => {
  addparkinglotRef.value.submitForm()
};

/** 点击取消保存并刷新 */
const cancellationRefresh = (val) => {
  close(false);
  getList()
  findparkinglotTreeFun()
};


/** 点击取消保存 */
const cancellation = (val) => {
  close(false);
};

/** 关闭弹窗 */
const close = (val) => {
  open1.value = val;
};




/** 树结构数据 */

const treeData = ref([])

/** 查询树结构 */
const findparkinglotTreeFun = () => {
  findparkinglotTree({}).then((response) => {
 
    treeData.value = response.data
  });
};
/** 树结构数据默认 */
const defaultProps = reactive({
  children: "children",
  label: "label",
})


/** 树结构点击 */
const handleNodeClick = (data)=>{
  queryParams.value.parentId = data.id;
getList();
}



getList();
findparkinglotTreeFun()

</script>

<style scoped>
.dep-card {
  min-height: calc(100vh - 110px);
}


</style>