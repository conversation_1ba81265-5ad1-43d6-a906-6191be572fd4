
import request from '@/utils/request'
import { apiUrl } from '@/utils/config'; 

//获取车场主键
export function getParkingId() {
  return request({
    url: `park${apiUrl}/majorParking/parkingLot/getParkingId`,
    method: 'post'
  })
}

//人员列表
export function selectApplyUserList(data) {
  return request({
    url: `park${apiUrl}/majorParking/parkingLot/selectUserList`,  
    method: "post",
    data: data,
  })
}


//根据userId查询用户信息
export function selectByUserId(data) {
  return request({
    url:  `park${apiUrl}/majorParking/parkingLot/selectByUserId/` +data,
    method: 'post',
    data:data
  })
}

// 查询出入口
export function findpassageway(param,data) {
    return request({
      url: `park${apiUrl}/majorParking/passageway/findpassageway`,
      method: 'post',
      data:{...data,...param},
      params: param
    })
  }

  //查询停车场树结构
export function findparkinglotTree(data) {
    return request({
      url: `park${apiUrl}/majorParking/parkingLot/findparkinglotTree`,
      method: 'post',
      data:data
    })
  }

//   新增出入场信息


export function addPassageway(data) {
    return request({
      url: `park${apiUrl}/majorParking/passageway/addPassageway`,
      method: 'post',
      data:data
    })
  }

  //删除车场信息
export function deleteParkingById(data) {
    return request({
      url: `park${apiUrl}/majorParking/passageway/deletePassagewayById/`+data,
      method: 'post'
    })
  }

  //   修改出入场信息


export function updatePassagewayById(data) {
    return request({
      url: `park${apiUrl}/majorParking/passageway/updatePassagewayById`,
      method: 'post',
      data:data
    })
  }
  //根据id查询出入场场信息
  export function findpassagewayById(data) {
    return request({
      url: `park${apiUrl}/majorParking/passageway/findpassagewayById/`+data,
      method: 'post'
    })
  }