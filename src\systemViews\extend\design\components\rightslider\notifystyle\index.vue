<template>
  <div class="notifystyle">
    <!-- 标题 -->
    <h2>{{ datas.config.moduleTitle }}</h2>

    <!-- 公告 -->
    <el-form
      label-width="90px"
      :model="datas"
      :rules="rules"
      size="small"
      class="lef"
    >
      <el-form-item
        label="公告"
        :hide-required-asterisk="true"
        prop="config.notifyText"
      >
        <el-input v-model="datas.config.notifyText" placeholder="请输入公告" />
      </el-form-item>
      <el-form-item
        label="路由类型"
        :hide-required-asterisk="true"
      >
        <el-select
          style="width: 100%"
          v-model="datas.config.routerType"
          placeholder="请选择路由类型"
          size="small"
        >
          <el-option
            v-for="item in routerTypeList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        label="跳转地址"
        :hide-required-asterisk="true"
      >
        <el-input
          v-model="datas.config.routerUrl"
          placeholder="请输入http地址，本地路由页面地址、本地html路径"
          size="small"
          type="textarea"
        ></el-input>
      </el-form-item>

      <div style="height: 10px" />

      <!-- 背景颜色 -->
      <el-form-item label="背景颜色" class="lef">
        <!-- 颜色选择器 -->
        <el-color-picker
          v-model="datas.config.backColor"
          show-alpha
          class="picke"
          :predefine="predefineColors"
        >
        </el-color-picker>
      </el-form-item>

      <div style="height: 10px" />

      <!-- 文字颜色 -->
      <el-form-item label="文字颜色" class="lef">
        <!-- 颜色选择器 -->
        <el-color-picker
          v-model="datas.config.textColor"
          show-alpha
          class="picke"
          :predefine="predefineColors"
        >
        </el-color-picker>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'notifystyle',
  props: {
    datas: Object,
  },
  data() {
    return {
      routerTypeList: [
        {
          value: 'netWeb',
          label: '网页h5页面',
        },
        {
          value: 'localPage',
          label: '内部路由',
        },
        {
          value: 'localWeb',
          label: '本地网页',
        },
      ],
      rules: {
        //校验表单输入
        notifyText: [
          //页面名称
          { required: true, message: '请输入公告', trigger: 'blur' },
        ],
      },
      predefineColors: [
        // 颜色选择器预设
        '#ff4500',
        '#ff8c00',
        '#ffd700',
        '#90ee90',
        '#00ced1',
        '#1e90ff',
        '#c71585',
        '#409EFF',
        '#909399',
        '#C0C4CC',
        'rgba(255, 69, 0, 0.68)',
        'rgb(255, 120, 0)',
        'hsv(51, 100, 98)',
        'hsva(120, 40, 94, 0.5)',
        'hsl(181, 100%, 37%)',
        'hsla(209, 100%, 56%, 0.73)',
        '#c7158577',
      ],
    }
  },
}
</script>

<style scoped lang="less">
.notifystyle {
  width: 100%;
  //position: relative;
  left: 0;
  top: 0;
  padding: 0 10px 20px;
  box-sizing: border-box;
  /* 标题 */
  h2 {
    padding: 24px 16px 24px 0;
    margin-bottom: 15px;
    border-bottom: 1px solid #f2f4f6;
    font-size: 18px;
    font-weight: 600;
    color: #323233;
  }

  .lef {
    :deep(.el-form-item__label) {
      text-align: left;
    }
  }

  /* 颜色选择器 */
  .picke {
    float: right;
  }
}
</style>
