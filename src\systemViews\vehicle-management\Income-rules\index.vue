

<template>
    <div>
        <TabContainers :tabList="tabList" :activeName="activeName"></TabContainers>
    </div> 

</template>

<script setup name="ParkingAccredit">
// tab切换
import TabContainers from '@/components/TabContainer/index';
import index1 from '@/systemViews/vehicle-management/Income-rules/index1.vue'
import index2 from '@/systemViews/vehicle-management/Income-rules/index2.vue'

import { ref } from "vue";
// 定义 tab 列表
const tabList = ref([
  {
    label: '停车场规则',
    value: '01',
    component: index1
  },
  {
    label: '人员规则',
    value: '02',
    component: index2
  },
])
// 默认激活的 tab
const activeName = ref('01')
</script>

<style scoped lang="scss">
:deep(.el-tabs .el-tabs__content .content) {
    height: calc(100vh - 250px);
}
</style>