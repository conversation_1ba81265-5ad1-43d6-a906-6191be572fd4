vue3 + Element Plus
参考文档： https://element-plus.org/zh-CN/#/zh-CN
          https://cn.vuejs.org/guide/built-ins/transition-group.html

项目启动： npm install  或者 cnpm install  或者 pnpm install  
          npm run dev

项目本地启动开发：  vite.config.js  修改 target 中的代理地址  
  server: {
      port: 9090,
      host: true,
      open: true,
      proxy: {
        // https://cn.vitejs.dev/config/#server-proxy
        "/dev-api": {
          target: "http://************:18080/",
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/dev-api/, ""),
        }
      },
    }
    
项目打包： nom run build:prod