<template>
  <div class="app-container">
    <el-tabs v-model="activeName" type="card" @tab-click="tabsHandleClick">
      <el-tab-pane label="个人" name="user"></el-tab-pane>
      <el-tab-pane label="机构" name="org"></el-tab-pane>
    </el-tabs>
    <Splitpanes class="default-theme">
      <Pane :size="15" >
        <el-card class="dep-card" style="height: 100%">
          <template #header>
            <div class="card-header">
              <span>组织</span>
              <el-button
                  type="primary"
                  style="float: right; padding: 3px 0"
                  link
                  icon="Refresh"
                  @click="reloadTree"
              >刷新
              </el-button>
              <el-form
                  style="margin-top: 20px; margin-bottom: -20px"
                  v-if="userType === 'admin'"
              >
                <el-form-item label="租户：">
                  <el-select
                      v-model="queryParams.tenantId"
                      style="width: 120px"
                      remote
                      :remote-method="initTenantList"
                      :loading="getTenantLoading"
                      @change="handleTenantChange"
                  >
                    <el-option
                        v-for="item in tenantList"
                        :key="item.tenantId"
                        :label="item.tenantName"
                        :value="item.tenantId"
                    />
                  </el-select>
                </el-form-item>
              </el-form>
            </div>
          </template>

          <!-- 组织树 -->
          <el-tree
              :props="{label:'orgName',children:'children',isLeaf:(data) => !data.isParent} "
              :load="loadNode"
              lazy
              :expand-on-click-node="false"
              ref="asyncTree"
              @node-click="handleNodeClick"
              :default-expanded-keys="[defaultOrgId]"
              node-key="orgId"
              highlight-current>
          </el-tree>
        </el-card>
      </Pane>
      <Pane :size="85" >
        <el-card class="dep-card" style="height: 100%">

          <!-- 搜索表单 -->
          <el-form
              :model="queryParams"
              ref="queryForm"
              :inline="true"
              label-width="68px"
          >
<!--            用户-->
            <el-form-item label="用户名称" prop="staffName" v-if="activeName==='user'">
              <el-input
                  v-model="queryParams.staffName"
                  placeholder="请输入用户名称"
                  clearable
                  style="width: 240px"
                  @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item label="手机号" prop="cellphone" v-if="activeName==='user'">
              <el-input
                  v-model="queryParams.cellphone"
                  placeholder="请输入手机号"
                  clearable
                  style="width: 240px"
                  @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item label="邮箱" prop="email" v-if="activeName==='user'">
              <el-input
                  v-model="queryParams.email"
                  placeholder="请输入邮箱"
                  clearable
                  style="width: 240px"
                  @keyup.enter.native="handleQuery"
              />
            </el-form-item>
<!--  机构-->
            <el-form-item label="组织机构" prop="orgName"  v-if="activeName!=='user'">
              <el-input
                  v-model="queryParams.orgName"
                  placeholder="请输入组织名称"
                  clearable

                  style="width: 240px"
                  @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item label="组织电话" prop="phone" v-if="activeName!=='user'">
              <el-input
                  v-model="queryParams.phone"
                  placeholder="办公电话"
                  clearable
                  style="width: 240px"
                  @keyup.enter.native="handleQuery"/>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>



          <!-- 用户信息表格 -->
          <el-table v-loading="loading" :data="userList">
            <el-table-column v-for="columnItem in listColumns"
                             :prop="columnItem.name"
                             :key="columnItem.key"
                             :label="columnItem.label"
                             >
            </el-table-column>

            <el-table-column
                label="操作"
                align="center"
                class-name="small-padding fixed-width"
            >
              <template #default="scope">
                <div >
                  <el-button link icon="View" type="primary"
                             :loading="getUserLoading && scope.row.staffId === loadUserId"
                             @click="handleView(scope.row)"
                             >查看
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>

          <!--分页 -->
          <pagination
              v-show="total > 0"
              :total="total"
              v-model:page="queryParams.pageNum"
              v-model:limit="queryParams.pageSize"
              @pagination="getList"
          />
        </el-card>
      </Pane>
    </Splitpanes>
    <!-- 添加或修改用户信息对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="formRef" :model="form"  label-width="80px">
        <el-row :gutter="12" v-if="activeName==='user'">
          <el-col :span="12">
            <el-form-item label="用户名称" prop="staffName">
              <el-input v-model="form.staffName" placeholder="无" maxlength="64" disabled/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="归属组织" prop="orgName">
              <el-input v-model="form.orgName" placeholder="无" disabled>
              </el-input>
            </el-form-item>
            <el-form-item v-show="false">
              <el-input v-model="form.orgId"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="登录名称" prop="loginName">
              <el-input
                  v-model="form.loginName"
                  placeholder="无"
                  maxlength="64"
                  disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号码" prop="cellphone">
              <el-input v-model="form.cellphone" placeholder="无" maxlength="11" disabled/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="form.email" placeholder="无" maxlength="50" disabled/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="12" v-else>
          <el-col :span="12">
            <el-form-item label="组织名称" prop="orgName">
              <el-input v-model="form.orgName" placeholder="无" maxlength="64" disabled/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="组织电话" prop="phone">
              <el-input v-model="form.phone" placeholder="无" disabled>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="传真" prop="fax">
              <el-input
                  v-model="form.fax"
                  placeholder="无"
                  maxlength="64"
                  disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="form.email" placeholder="无" maxlength="11" disabled/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="组织类型" prop="orgTypeText">
              <el-input v-model="form.orgTypeText" placeholder="无" maxlength="50" disabled/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

    </el-dialog>

  </div>
</template>

<script setup name="AddressList">
import useUserStore from "@/store/modules/user";
import {ref} from "vue";
import {getTenants} from "@/api/tenant/tenant";
import {
  getUser,
  listUser,
} from "@/api/system/user";
import {getDept, treeselect, listDept} from "@/api/system/dept";
import {ElMessageBox} from 'element-plus'


//----------------用户
const {proxy} = getCurrentInstance();
const {
  staff_kind,
  user_status,
  sys_user_sex
} = proxy.useDict("staff_kind", "user_status", "sys_user_sex");

const userStore = useUserStore();
const userType = userStore.userInfo.customParam.userType;
const defaultOrgId = userStore.userInfo.orgId;
const activeName=ref("user")
const lazyTreeProps={
  children: "children",
  label: "orgName",
  isLeaf: "leaf",
}
const getUserLoading = ref(false);
const getTenantLoading = ref(false);
const queryParams = ref({
  tenantId: userStore.userInfo.tenantId,
  pageNum: 1,
  pageSize: 10,
  email: null,
  cellphone: null,
  orgId: userStore.userInfo.orgId,
  staffOrgType: "F",
  tenantAdminId: userStore.userInfo.customParam.tenantAdminId,
  tenantName: userStore.userInfo.customParam.tenantName,
  phone:null,
  orgName:null,
  orgType:''
})

const treeLoading = ref(false);
const selectNode = ref(undefined);
const loadUserId = ref(undefined);

const selectNodeName = ref("全部人员");
const tenantList = ref([]);
const userList = ref([]);

const form = ref({
  staffStatus: "valid",
  sex: 'male'
})

// 遮罩层
const loading = ref(true);
const total = ref(0);
// 显示搜索条件

const open = ref(false);
const title = ref("查看用户");

const listColumns = ref([
  {key: 1, label: `用户名称`, name: 'staffName', visible: true},
  {key: 2, label: `所属组织`, name: 'orgName', visible: true},
  {key: 4, label: `手机号码`, name: 'cellphone', visible: true},
  {key: 5, label: `邮箱`, name: 'email', visible: true},
])
watch(activeName,(newVal)=>{
  reset()
  proxy.resetForm("queryForm");
  if (newVal=="user"){
    listColumns.value=[
      {key: 1, label: `用户名称`, name: 'staffName', visible: true},
      {key: 2, label: `所属组织`, name: 'orgName', visible: true},
      {key: 4, label: `手机号码`, name: 'cellphone', visible: true},
      {key: 5, label: `邮箱`, name: 'email', visible: true},
    ]
  }else {
    listColumns.value= [
      {key: 2, label: `组织名称`, name: 'orgName', visible: true},
      {key: 7, label: `组织电话`, name: 'phone', visible: true},
      {key: 8, label: `组织传真`, name: 'fax', visible: true},
      {key: 9, label: `组织电子邮箱`, name: 'email', visible: true},
    ];
  }
  reloadTree();
  getList();
})





// 刷新加载树形结构的组织
function reloadTree() {
  if (selectNode.value) {
    const node = proxy.$refs.asyncTree.getNode(selectNode.value);
    node.childNodes = [];
    node.loaded = false;
    node.expand();
  } else {
    proxy.$refs.asyncTree.root.loaded = false;
    proxy.$refs.asyncTree.root.expand();
  }
}

//懒加载树形结构的组织
function loadNode(node, resolve) {
  treeLoading.value = true;
  if (node.level === 0) {
    treeselect({
      orgId: defaultOrgId,
      queryType: 'current',
      tenantId: queryParams.value.tenantId,
    }).then((response) => {
      resolve(response.data);
      treeLoading.value = false;
    });
  } else {
    treeselect({
      orgId: node.data.orgId,
      queryType: 'down',
      tenantId: queryParams.value.tenantId,
    }).then((response) => {
      resolve(response.data);
      treeLoading.value = false;
    });
  }
}

// 节点单击事件
function handleNodeClick(data) {
  queryParams.value.orgId = data.orgId;
  selectNode.value = data;
  selectNodeName.value = data.orgName;
  getList();
}

// 获取租户列表
function initTenantList(tenantName) {
  getTenantLoading.value = true;
  let query = {};
  if (tenantName !== undefined && tenantName !== "") {
    query.tenantName = tenantName;
    query.tenantId = undefined;
  } else {
    query.tenantId = queryParams.value.tenantId;
  }
  getTenants(query)
      .then((response) => {
        tenantList.value = response.data;
      })
      .finally(() => {
        getTenantLoading.value = false;
      });
}

// 下拉框切换租户回调
function handleTenantChange(tenantId) {
  if (tenantId !== "") {
    const tenantObj = tenantList.value.find(
        (item) => item.tenantId === tenantId
    );
    queryParams.value.tenantName = tenantObj.tenantName;
    queryParams.value.tenantAdminId = tenantObj.tenantAdminId;
  }
  proxy.$refs.asyncTree.root.loaded = false;
  proxy.$refs.asyncTree.root.expand();
  selectNode.value = undefined;
  queryParams.value.orgId = undefined;
  handleQuery();
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryForm");
  handleQuery();
}

/** 查询用户列表 */
function getList() {
  loading.value = true;
  if(activeName.value =="user"){
    selectNodeName.value="全部人员"
    queryParams.value.orgType = ''
    queryParams.value.phone = null
    queryParams.value.orgName = null
    listUser(queryParams.value).then(
        (response) => {
          userList.value = response.data.records;
          total.value = response.data.total;
          loading.value = false;
        }
    );
  }else {
    selectNodeName.value="机构"
    queryParams.value.orgType = 'all'
    queryParams.value.email=null
    queryParams.value.cellphone=null
    listDept(queryParams.value).then((response)=>{
      userList.value = response.data.records;
      total.value = response.data.total;
      loading.value = false;
    })
  }

}

// 获取全部人员
function allUser() {
  const node = proxy.$refs.asyncTree.root;
  node.loaded = false;
  node.expand();
  queryParams.value.orgId = defaultOrgId;
  selectNodeName.value = "全部人员";
  selectNode.value = undefined;
  getList();
}



// 修改按钮操作
function handleView(row) {
  reset();
  getUserLoading.value = true;
  if (activeName.value==="user"){
    const userid = row.staffId;
    loadUserId.value = userid;
    getUser(userid)
        .then((response) => {
          getDept(response.data.orgId).then((r) => {
            getUserLoading.value = false;
            loadUserId.value = undefined;
            if (response.data) {
              form.value = {
                ...form.value,
                ...response.data,
                orgName: r.data.orgName,
              };
              open.value = true;
              title.value = "查看用户";
            } else {
              proxy.$modal.msgError("数据异常！");
            }
          });
        })
        .catch((e) => {
          getUserLoading.value = false;
          loadUserId.value = undefined;
          proxy.$modal.msgError("数据异常！");
        });
  }else {
    getDept(row.orgId).then((response) => {
      getDept(response.data.orgId).then((r) => {
        if (response.data){
          form.value={
            ...form.value,
            ...response.data,
            orgName:r.data.orgName,
            orgId:response.data.orgId
          }
          open.value = true;
          title.value = "查看组织";
          getUserLoading.value = false;
        }else {
          proxy.$modal.msgError("数据异常")
        }
      }).catch(()=>{
        getUserLoading.value = false;
        proxy.$modal.msgError("数据异常")

      })
    }).catch(()=>{
      getUserLoading.value = false;
      proxy.$modal.msgError("数据异常")

    })
  }

}

// 表单重置
function reset() {
  form.value = {
    staffId: undefined,
    orgId: undefined,
    staffName: undefined,
    cellphone: undefined,
    email: undefined,
    staffStatus: "valid",
    orgName: "",
    tenantName: undefined,
    sex: 'male'
  };
  proxy.resetForm("formRef");

}

function tabsHandleClick(tab, event){
  activeName.value = tab.name;
}

//----------机构












initTenantList();
getList();
</script>

<style scoped>
.dep-card {
  min-height: calc(100vh - 120px);
}
.error-msg {
  color: #f56c6c;
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
}
</style>
