<!-- 支付类型 -->


<template>
  <div class="app-container-other">
    <Splitpanes class="default-theme">
      <Pane :size="100" :min-size="62">
        <el-card class="dep-card" shadow="never" v-loading="treeLoading">

          <dialog-search @getList="getList" formRowNumber="3" :columns="tabelForm.columns" :isShowRightBtn="$checkPermi(['pay:payType:list'])">
            <template #formList>
              <el-form ref="queryForm" :inline="true" :model="pageParams" label-width="75px">
                <el-form-item label="类型名称" prop="typeName">

                  <el-input v-model="pageParams.typeName" placeholder="请输入类型名称" clearable />

                </el-form-item>
                <el-form-item label="类型编码" prop="typeCode">
                  <el-input v-model="pageParams.typeCode" placeholder="请输入类型编码" clearable />
                </el-form-item>
              </el-form>
            </template>
            <template v-slot:searchList>
              <el-button type="primary" icon="Search" @click="handleQuery" v-hasPermi="['pay:payType:list']">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery"  v-hasPermi="['pay:payType:list']">重置</el-button>
            </template>

            <template v-slot:searchBtnList>
              <el-button type="primary" icon="Plus" @click="handleAdd"  v-hasPermi="['pay:payType:add']">新增
              </el-button>

              <el-button icon="Download" @click="handleExport"  v-hasPermi="['pay:payType:export']">导出
              </el-button>
            </template>
          </dialog-search>

          <public-table ref="publictable" :rowKey="tabelForm.tableKey" :tableData="userData"
            :columns="tabelForm.columns" :configFlag="tabelForm.tableConfig" :pageValue="pageParams" :total="total"
            :getList="getList">
          
            <template #operation="{ scope }">
              <div>
                <el-button link icon="View" type="primary" @click="handleView(scope.row)" title="查看" v-hasPermi="['pay:payType:info']"> 
                </el-button>
                <el-button link icon="Edit" type="primary" @click="handleEdit(scope.row)" title="修改" v-hasPermi="['pay:payType:edit']">
                </el-button>
                <el-button link icon="Delete" type="primary" @click="handleDelete(scope.row)" title="删除" v-hasPermi="['pay:payType:remove']">
                </el-button>
              </div>
            </template>
          </public-table>
        </el-card>
      </Pane>
    </Splitpanes>

    <DialogBox :visible="diaWindow.open1" :dialogWidth="diaWindow.dialogWidth" :customClass="diaWindow.customClass"
      :dialogFooterBtn="diaWindow.dialogFooterBtn" @save="save" @cancellation="cancellation" @close="close"
      :dialogTitle="diaWindow.headerTitle">
      <template #content>
        <add ref="addRef" :popupType="diaWindow.popupType" :rowData="diaWindow.rowData"
          @closeBtn="cancellationRefresh"></add>
      </template>
    </DialogBox>

  </div>
</template>

<script setup>

import {
    screenIndex
  } from "@/api/paymentCenter/payScene/index";
import { ElMessageBox, ElMessage } from "element-plus";
import add from "./components/add";
import { formatMinuteTime } from '@/utils';
import { ref, reactive, getCurrentInstance } from "vue";
import { apiUrl } from '@/utils/config';
const { proxy } = getCurrentInstance();
const { settle_type,type_status } = proxy.useDict("settle_type","type_status");
const userData = ref([{}]);
const publictable = ref(null);
const total = ref(0);

const addRef = ref(null);
const defaultExpandedKeys = ref([]);
// 定义事件发射器
const emit = defineEmits(["editNodes", "delNodes"]);
const pageParams = ref({
  pageNum: 1,
  pageSize: 10,
  typeName: "",
  typeCode: ''
});
// 弹窗
const diaWindow = reactive({
  open1: false,
  headerTitle: "",
  popupType: "",
  rowData: "",
  dialogWidth: "50%",
  dialogFooterBtn: false,
   customClass: 'my_height_1'
});


// 树结构数据默认
const tabelForm = reactive({
  tableKey: "1", //表格key值
  isShowRightToolbar: true, //是否显示右侧显示和隐藏
  showSearch: true,
  // 表格数据
  columns: [

    {
      fieldIndex: "typeName", // 对应列内容的字段名
      label: "类型名称", // 显示的标题
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      minWidth: "120px", //最小宽度%
      width: "", //宽度
      align: "left",
    },
    {
      fieldIndex: "typeCode", // 对应列内容的字段名
      label: "类型编码", // 显示的标题
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      minWidth: "120px", //最小宽度%
      width: "", //宽度
    },
    {
      fieldIndex: "sceneName", // 对应列内容的字段名
      label: "区域名称", // 显示的标题
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      minWidth: "150px", //最小宽度%
      width: "", //宽度
      align:'left'
    },

    {
      fieldIndex: "settleType", // 对应列内容的字段名
      label: "结算类型", // 显示的标题
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      minWidth: "100px", //最小宽度%
      width: "", //宽度
      type: "dict",
      dictList: settle_type,
    },
    {
      fieldIndex: "typeSort", // 对应列内容的字段名
      label: "排序", // 显示的标题
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      minWidth: "90px", //最小宽度%
      width: "", //宽度
    },

    {
        fieldIndex: "status", // 对应列内容的字段名
        label: "支付状态", // 显示的标题
        resizable: true, // 对应列是否可以通过拖动改变宽度
        visible: true, // 展示与隐藏
        sortable: true, // 对应列是否可以排序
        fixed: "", //固定
        minWidth: "100", //最小宽度%
        width: "", //宽度
        align: "", //表格对齐方式
        type: "dict",
        dictList: type_status,
      },
  
    {
      fieldIndex: "createDate", // 对应列内容的字段名
      label: "创建时间", // 显示的标题
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      minWidth: "140px", //最小宽度%
      width: "", //宽度
    },

    {
      fieldIndex: "updateName", // 对应列内容的字段名
      label: "操作人", // 显示的标题
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      minWidth: "120px", //最小宽度%
      width: "", //宽度
    },
    {
      label: "操作",
      slotname: "operation",
      width: "120",
      fixed: "right", //固定
      visible: true,
    },
  ],
  // 表格基础配置项，看个人需求添加
  tableConfig: {
    needPage: true, // 是否需要分页
    index: true, // 是否需要序号
    selection: false, // 是否需要多选
    reserveSelection: false, // 是否需要支持跨页选择
    indexFixed: false, // 序号列固定 left true right
    selectionFixed: false, //多选列固定left true right
    indexWidth: "50", //序号列宽度
    loading: false, //表格loading
    showSummary: false, //是否开启合计
    height: null, //表格固定高度 比如：300px
  },
});

/** 查询公告列表 */
function getList() {
  tabelForm.tableConfig.loading = true;
  screenIndex.pagePayType(pageParams.value).then((res) => {
    // 将英文逗号替换为中文逗号
    userData.value = res.data.records.map(item => {
      if (item.sceneName) {
        item.sceneName = item.sceneName.replace(/,/g, '，');
      }
      return item;
    });
    total.value = res.data.total;
    tabelForm.tableConfig.loading = false;
  });
}

/** 搜索按钮操作 */
const handleQuery = () => {
  pageParams.value.pageNum = 1;
  getList();
};



function handleDelete(row, callback) {
  ElMessageBox.confirm(
    "确认删除吗？",
    "提示",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }
  )
    .then(() => {
      screenIndex.deletePayType({id:row.id}).then((res) => {
        if (res.code == "1") {
          ElMessage.success("删除成功");
          getList();
          callback(true);
        }
      });
    })
    .catch(() => { });
}









const handleAdd = () => {
  diaWindow.dialogWidth = "35%";
  diaWindow.headerTitle = "新增支付类型";
  diaWindow.rowData = {};
  diaWindow.dialogFooterBtn = true;
  diaWindow.popupType = "add";
  diaWindow.open1 = true;
};

const handleEdit = (row) => {
  diaWindow.dialogWidth = "35%";
  diaWindow.headerTitle = "修改支付类型";
  diaWindow.rowData =row

  diaWindow.dialogFooterBtn = true;
  diaWindow.popupType = "edit";
  diaWindow.open1 = true;
};
const handleView = (row) => {
  diaWindow.dialogWidth = "35%";
  diaWindow.headerTitle = "查看支付类型";
  diaWindow.rowData = row;
  diaWindow.dialogFooterBtn = false;
  diaWindow.popupType = "view";
  diaWindow.open1 = true;
};
const handleExport = () => {
  proxy.download(
    'wisdompay' + apiUrl + '/pay/payType/exportPayType',
    {
      typeName: pageParams.value.typeName, typeCode: pageParams.value.typeCode
    },
    `支付类型_${formatMinuteTime(new Date())}.xlsx`
  );
};
/** 提交保存 */
const save = (val) => {
  addRef.value.saveForm();
};

/** 点击取消保存并刷新 */
const cancellationRefresh = (val) => {
  close(false);
  getList();
};

/** 点击取消保存 */
const cancellation = (val) => {
  close(false);
};

/** 关闭弹窗 */
const close = (val) => {
  diaWindow.open1 = val;
};



// 重置
const resetQuery = () => {
  defaultExpandedKeys.value = []
  pageParams.value.id = ''
  proxy.resetForm("queryForm");
  handleQuery()
};
onMounted(() => {
  getList();
});
</script>

<style scoped>
.dep-card {
  min-height: calc(100vh - 160px);
}
</style>