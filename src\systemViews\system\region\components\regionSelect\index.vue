<template>
  <div v-if="visible">
    <el-dialog
        width="25%"
        title="行政区划选择"
        v-model="visible"
        append-to-body
        destroy-on-close
    >
      <el-card
          class="select-dep-card"
          shadow="never"
          v-loading="selectTreeLoading"
          destroy-on-close>
        <template #header>
          <div>
            <span>请选择</span>
            <el-button style="float: right; padding: 3px 0" link icon="Refresh" @click="reloadTree">刷新</el-button>
            <el-button style="float: right; padding: 3px 5px" link :icon="expandAll ? 'ArrowUp' : 'ArrowDown'"
                       @click="setExpandAll">
              {{ expandAll ? "展开一级" : "展开全部" }}
            </el-button>
          </div>
        </template>
        <el-tree
            v-if="reload"
            :props="lazyTreeProps"
            :load="selectLoadNode"
            lazy
            :expand-on-click-node="false"
            ref="selectAsyncTree"
            :default-expanded-keys="[defaultRegionId]"
            node-key="id"
            highlight-current
            :default-expand-all="expandAll">
        </el-tree>
      </el-card>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="selectRegion" :loading="selectRegionLoading">确 定</el-button>
          <el-button @click="visible = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="RegionSelect">
import {loadTree} from "@/api/system/region";
import {getCurrentInstance, ref} from "vue";

const {proxy} = getCurrentInstance();

const props = defineProps({
  name: {
    type: String,
    default: "",
  },
  value: {
    type: String,
    default: "",
  },
  tenantId: {
    type: String,
    default: "",
  },
});
defineExpose({close, open})
const emit = defineEmits(["selectedRegion"])
const selectTreeLoading = ref(false);
const lazyTreeProps = {
  children: "children",
  label: "regionName",
  isLeaf: "leaf",
}
const visible = ref(false);
const reload = ref(true);
const expandAll = ref(false);
const selectRegionLoading = ref(false);
const defaultRegionId = ref("1");

function selectLoadNode(node, resolve) {
  selectTreeLoading.value = true;
  if (node.level === 0) {
    loadTree({
      id: defaultRegionId.value,
      queryType: "current",
    }).then((response) => {
      resolve(response.data);
      selectTreeLoading.value = false;
    });
  } else {
    loadTree({
      id: node.data.id,
      queryType: "down",
    }).then((response) => {
      resolve(response.data);
      selectTreeLoading.value = false;
    });
  }
}

function selectRegion() {
  selectRegionLoading.value = true;
  const cnode = proxy.$refs.selectAsyncTree.getCurrentNode();
  if (cnode) {
    emit("selectedRegion", cnode);
  } else {
    proxy.$modal.msgWarning("请选择一条数据！");
    selectRegionLoading.value = false;
  }
}

function open() {
  selectRegionLoading.value = false;
  visible.value = true;
}

function close() {
  visible.value = false;
}

function reloadTree() {
  reload.value = true;
  const cnode = proxy.$refs.selectAsyncTree.getCurrentNode();
  if (cnode) {
    const node = proxy.$refs.selectAsyncTree.getNode(cnode);
    node.childNodes = [];
    node.loaded = false;
    node.expand();
  } else {
    proxy.$refs.selectAsyncTree.root.loaded = false;
    proxy.$refs.selectAsyncTree.root.expand();
  }
}

function setExpandAll() {
  reload.value = false;
  expandAll.value = !expandAll.value;
  setTimeout(() => {
    reload.value = true;
  }, 100);
}


</script>

<style scoped lang="scss">
.select-dep-card {
  height: 500px;
  overflow-y: auto;
}
</style>
