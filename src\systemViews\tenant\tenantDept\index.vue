<template>
  <div class="app-container">
    <Splitpanes class="default-theme">
      <Pane :size="15" :min-size="15">
        <el-card class="dep-card" style="height: 100%">
          <template #header>
            <div class="card-header">
              <span>组织</span>
              <el-button
                type="primary"
                style="float: right; padding: 3px 0"
                link
                icon="Refresh"
                @click="reloadTree"
                >刷新
              </el-button>
              <el-form
                style="margin-top: 20px; margin-bottom: -20px"
                v-if="userType === 'admin'"
              >
                <el-form-item label="租户：">
                  <el-select
                    v-model="queryParams.tenantId"
                    style="width: 120px"
                    remote
                    :remote-method="initTenantList"
                    :loading="getTenantLoading"
                    @change="handleTenantChange"
                  >
                    <el-option
                      v-for="item in tenantList"
                      :key="item.tenantId"
                      :label="item.tenantName"
                      :value="item.tenantId"
                    />
                  </el-select>
                </el-form-item>
              </el-form>
            </div>
          </template>

          <!-- 组织树 -->
          <el-input
            placeholder="输入关键字进行查询"
            v-model="filterText"
            clearable
            style="margin-bottom: 8px"
          >
            <template #append>
              <el-button
                icon="search"
                @click="searchFiler(filterText)"
              ></el-button>
            </template>
          </el-input>
          <treeLoad
            ref="asyncTree"
            :key="treeFatherIndex"
            :isShowSearch="false"
            :idDefaultExpandAll="false"
            :defaultProps="defaultProps"
            :treeBtnEdit="false"
            @loadFirstNodeFather="loadNodeRooter"
            @loadChildNodeFather="loadNode"
            :treeBtnDelete="false"
            tree-name="deptName"
            nodeKey="deptId"
            :treeData="treeData"
            :defaultSelectedKey="defaultSelectedKey"
            :defaultExpandedKeys="[defaultOrgId]"
            :isLazy="isLazy"
            :treeindex="treeindex"
            @checkedKeys="handleNodeClick"
          />
        </el-card>
      </Pane>
      <Pane :size="85" :min-size="65">
        <el-card class="dep-card" style="height: 100%">
          <!-- 搜索表单 -->
          <dialog-search @getList="getList" formRowNumber="4">
            <template v-slot:formList>
              <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
                <el-form-item label="租户">
                  <TenantSelect v-model="queryParams.vestTenantId" placeholder="请选择授权租户" clearable></TenantSelect>
                </el-form-item>
                <el-form-item label="根节点" prop="rootNode">
                  <el-select v-model="queryParams.rootNode" placeholder="请选择根节点状态" clearable>
                    <el-option v-for="(item, index) of root_node_status"
                              :key="index" :label="item.label" :value="item.value"/>
                  </el-select>
                </el-form-item>
                <el-form-item label="状态" prop="status">
                  <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
                    <el-option v-for="(item, index) of dept_status"
                              :key="index" :label="item.label" :value="item.value"/>
                  </el-select>
                </el-form-item>
            </el-form>
            </template>
            <template v-slot:searchList>
              <el-button type="primary" icon="Search" @click="handleQuery"
                >搜索</el-button
              >
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </template>

            <template v-slot:searchBtnList>
              <el-button
                type="primary"
                icon="Plus"
                @click="handleAdd"
                v-hasPermi="['system:tenantDept:add']">
                新增
              </el-button>
            </template>
          </dialog-search>

          <public-table
            ref="publictable"
            :rowKey="tabelForm.tableKey"
            :tableData="dataList"
            :columns="tabelForm.columns"
            :configFlag="tabelForm.tableConfig"
            :pageValue="queryParams"
            :total="total"
            :getList="getList"
          >
            <template #operation="{ scope }">
              <el-button
                size="mini"
                type="text"
                title="修改"
                icon="Edit"
                v-hasPermi="['system:tenantDept:edit']"
                @click="handleUpdate(scope.row)"
              >
              </el-button>
              <el-button
                size="mini"
                type="text"
                title="删除"
                icon="Delete"
                v-hasPermi="['system:tenantDept:remove']"
                @click="handleDelete(scope.row)"
              >
              </el-button>
            </template>
          </public-table>
        </el-card>
      </Pane>
    </Splitpanes>

    <!-- 添加或修改租户部门信息对话框 -->
   <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
        <el-row :gutter="12">
          <el-col :span="24">
            <el-form-item label="部门">
              <el-input
                v-model="form.deptName"
                placeholder="部门"
                maxlength="50"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="租户" prop="vestTenantName">
              <TenantSelect v-model="form.vestTenantId" placeholder="请选择授权租户"></TenantSelect>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio
                  v-for="dict in dept_status"
                  :key="dict.value"
                  :label="dict.value"
                  >{{ dict.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer" style="text-align: right;">
        <el-button @click="cancel('edit')">取 消</el-button>
        <el-button type="primary" @click="submitForm" :loading="saveLoading">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup name="User">
import treeLoad from "@/components/Tree/treeLoad";
import useUserStore from "@/store/modules/user";
import { ref } from "vue";
import { getTenants } from "@/api/tenant/tenant";
import { findDeptTree, tenantDeptList,getTenantDeptInfo,addTenantDept,editTenantDept,delTenantDept } from "@/api/tenant/tenantDept";
import { ElMessage,ElMessageBox } from "element-plus"
const isLazy = ref(true);
const treeData = ref([]);
const treeindex = ref(0);
const treeFatherIndex = ref(0);
/** 树结构数据默认 */
const defaultProps = reactive({
  children: "children",
  label: "deptName",
  isLeaf: (data) => !data.isParent,
});

const { proxy } = getCurrentInstance();
const { root_node_status, dept_status} = proxy.useDict(
  "root_node_status",
  "dept_status"
);

const userStore = useUserStore();
const userType = userStore.userInfo.customParam.userType;
let defaultOrgId = userStore.userInfo.orgId;
const defaultSelectedKey = ref()
const getTenantLoading = ref(false);
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  vestTenantId:undefined,
  tenantId: userStore.userInfo.tenantId
});
//const isShowFile = ref(true);
const treeLoading = ref(false);
const selectNode = ref(undefined);
const tenantList = ref([]);
const dataList = ref([]);
const form = ref({
   status: "0",
});
// 遮罩层
const loading = ref(true);
const total = ref(0);
// 显示搜索条件
const showSearch = ref(true);
const saveLoading = ref(false);
const tabelForm = reactive({
  tableKey: "1", //表格key值
  isShowRightToolbar: true, //是否显示右侧显示和隐藏
  showSearch: true,
  // 表格表格数据
  columns: [
         {
            fieldIndex: "vestTenantName", // 对应列内容的字段名
            label: "租户", // 显示的标题
            resizable: true, // 对应列是否可以通过拖动改变宽度
            visible: true, // 展示与隐藏
            sortable: true, // 对应列是否可以排序
            fixed: "", //固定
            minWidth: "150", //最小宽度%
            width: "", //宽度
            align: "left", //表格对齐方式
          },
          {
            fieldIndex: "deptName", // 对应列内容的字段名
            label: "部门", // 显示的标题
            resizable: true, // 对应列是否可以通过拖动改变宽度
            visible: true, // 展示与隐藏
            sortable: true, // 对应列是否可以排序
            fixed: "", //固定
            minWidth: "140", //最小宽度%
            width: "", //宽度
            align: "left", //表格对齐方式
          },
          {
            fieldIndex: "rootNode", // 对应列内容的字段名
            label: "根节点", // 显示的标题
            resizable: true, // 对应列是否可以通过拖动改变宽度
            visible: true, // 展示与隐藏
            sortable: true, // 对应列是否可以排序
            fixed: "", //固定
            minWidth: "110", //最小宽度%
            width: "", //宽度
            align: "", //表格对齐方式
            type: "dict",
            dictList: root_node_status,
          },
          {
            fieldIndex: "status", // 对应列内容的字段名
            label: "状态", // 显示的标题
            resizable: true, // 对应列是否可以通过拖动改变宽度
            visible: true, // 展示与隐藏
            sortable: true, // 对应列是否可以排序
            fixed: "", //固定
            minWidth: "140", //最小宽度%
            width: "", //宽度
            align: "", //表格对齐方式
            type: "dict",
            dictList: dept_status,
          },
          {
            fieldIndex: "updateBy", // 对应列内容的字段名
            label: "操作人", // 显示的标题
            resizable: true, // 对应列是否可以通过拖动改变宽度
            visible: true, // 展示与隐藏
            sortable: true, // 对应列是否可以排序
            fixed: "", //固定
            minWidth: "110", //最小宽度%
            width: "", //宽度
            align: "", //表格对齐方式
            
          },
          {
            fieldIndex: "updateDate", // 对应列内容的字段名
            label: "操作时间", // 显示的标题
            resizable: true, // 对应列是否可以通过拖动改变宽度
            visible: true, // 展示与隐藏
            sortable: true, // 对应列是否可以排序
            fixed: "", //固定
            minWidth: "110", //最小宽度%
            width: "", //宽度
            align: "", //表格对齐方式
          },
          {
            label: "操作",
            slotname: "operation",
            width: "100",
            fixed: "right", //固定
            visible: true,
          },
  ],
  // 表格基础配置项，看个人需求添加
  tableConfig: {
    needPage: true, // 是否需要分页
    index: true, // 是否需要序号
    selection: false, // 是否需要多选
    reserveSelection: false, // 是否需要支持跨页选择
    indexFixed: false, // 序号列固定 left true right
    selectionFixed: false, //多选列固定left true right
    indexWidth: "50", //序号列宽度
    loading: false, //表格loading
    showSummary: false, //是否开启合计
    height: null, //表格固定高度 比如：300px
  },
});
const rules = {
  vestTenantId: [{ required: true, message: "请选择授权租户", trigger: "blur" }],
  status: [{ required: true, message: "请选择部门状态", trigger: "blur" }],
};
// 弹窗属性
//const deptAddRef = ref(null);
//const deptViewRef = ref(null);
const open = ref(false);
const title = ref("");
const propType = ref("");
// const diaWindow = reactive({
//   headerTitle: "",
//   popupType: "",
//   rowData: "",
//   dialogWidth: "30%",
//   dialogFooterBtn: false,
// });

// 刷新加载树形结构的组织
function reloadTree() {
  filterText.value = '';
  isLazy.value = true
  treeindex.value++;
}

const filterText = ref("");
const isSearchFirst = ref(false);
// 组织树条件查询
const searchFiler = () => {
  isSearchFirst.value = true;
  treeData.value = [];
  isLazy.value = true;
  treeindex.value++;
};
// 加载租户信息
initTenantList();
// 加载租户部门信息
getList();
// 懒加载树形结构的组织-根节点
function loadNodeRooter(resolve) {
  treeLoading.value = true;
  findDeptTree({
    deptName: filterText.value,
    tenantId: queryParams.value.tenantId,
  }).then((response) => {
    resolve(response.data);
    treeLoading.value = false;
  });
}

// 懒加载树形结构的组织-子节点
function loadNode(node, resolve) {
  treeLoading.value = true;
  if (node.level === 0) {
    findDeptTree({
      deptId: defaultOrgId,
      tenantId: queryParams.value.tenantId,
      deptName: filterText.value,
    }).then((response) => {
      resolve(response.data);
      treeLoading.value = false;
    });
  } else {
    findDeptTree({
      deptId: node.data.deptId,
      tenantId: queryParams.value.tenantId,
      deptName: isSearchFirst.value ? filterText.value : "",
    }).then((response) => {
      resolve(response.data);
      treeLoading.value = false;
      isSearchFirst.value = false;
    });
  }
}

// 节点单击事件
function handleNodeClick(data) {
  selectNode.value = data;
  queryParams.value.deptId = data.deptId;
  queryParams.value.deptName = data.deptName;
  getList();
}

// 获取租户列表
function initTenantList(tenantName) {
  getTenantLoading.value = true;
  let query = {};
  if (tenantName !== undefined && tenantName !== "") {
    query.tenantName = tenantName;
    query.tenantId = undefined;
  } else {
    query.tenantId = queryParams.value.tenantId;
  }
  getTenants(query)
    .then((response) => {
      tenantList.value = response.data;
    })
    .finally(() => {
      getTenantLoading.value = false;
    });
}

// 下拉框切换租户回调
function handleTenantChange(tenantId) {
  if (tenantId !== "") {
    const tenantObj = tenantList.value.find(
      (item) => item.tenantId === tenantId
    );
    queryParams.value.vestTenantName = tenantObj.tenantName;
    queryParams.value.vestTenantId = tenantObj.tenantId;
  }
  proxy.$refs.asyncTree.root.loaded = false;
  proxy.$refs.asyncTree.root.expand();
  selectNode.value = undefined;
  queryParams.value.deptId = undefined;
  handleQuery();
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList("noFirst");
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryForm");
  queryParams.value.pageNum = 1;
  queryParams.value.deptId = "";
  queryParams.value.vestTenantId = undefined;
  filterText.value = "";
  selectNode.value = "";
  treeData.value = [];
  isLazy.value = true;
  treeFatherIndex.value++;
  getList();
}

/** 查询租户部门授权列表 */
function getList(type) {
  loading.value = true;
  tenantDeptList(queryParams.value).then((response) => {
    dataList.value = response.data.records;
    total.value = response.data.total;
    loading.value = false;
  });
}

// 新增按钮操作
function handleAdd() {
  reset();
  if(selectNode.value === undefined || selectNode.value === ''){
    ElMessage.info("请先在左侧选择要授权的部门")
  }else{
    form.value = {
      ...form.value,
      deptId: selectNode.value.deptId,
      deptName: selectNode.value.deptName,
      parentId: selectNode.value.parentId,
      ancestors: selectNode.value.ancestors,
      ancestorsName: selectNode.value.ancestorsName,
    };
    open.value = true;
    propType.value = "add";
    title.value = "新增部门授权";
  }
  form.value.vestTenantName = queryParams.value.vestTenantName;
  form.value.vestTenantId = queryParams.value.vestTenantId;
}

// 修改按钮操作
function handleUpdate(row) {
  reset();
  let obj = {
    id: row.id
  }
  getTenantDeptInfo(obj).then((response) => {
    form.value = response.data
    open.value = true;
    propType.value = "edit";
    title.value = "修改部门授权";
  }).catch((e) => {
    open.value = false;
    proxy.$modal.msgError("数据异常！");
  });
}

// 删除按钮操作
function handleDelete(row) {
  const id = row.id;
  proxy.$modal
    .confirm('是否确认删除部门授权"' + row.vestTenantName + "/" + row.deptName + '"的数据项? 授权更改会影响人员等数据更新，请谨慎操作！')
    .then(function () {
      let params = {
        id: id
      }
      return delTenantDept(params);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    });
}

// 提交信息
function submitForm() {
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      saveLoading.value = true;
      if (propType.value === "edit") {
        proxy.$modal.confirm('是否确认修改部门授权，授权更改会影响人员等数据更新，请谨慎操作！').then(function () {
            let newForm ={
              id:form.value.id,
              vestTenantId:form.value.vestTenantId,
              deptId:form.value.deptId,
              parentId: form.value.parentId,
              ancestors: form.value.ancestors,
              ancestorsName: form.value.ancestorsName,
              status: form.value.status,
              tenantId: userStore.userInfo.tenantId
            }
            editTenantDept(newForm).then((response) => {
              saveLoading.value = false;
              if (response.success) {
                proxy.$modal.msgSuccess("修改成功");
                open.value = false;
                getList();
              } else {
                proxy.$modal.msgError(response.message);
              }
            }).catch((e) => {
              saveLoading.value = false;
              open.value = false;
              proxy.$modal.msgError("修改失败");
            });
         });   
      } else if(propType.value === "add"){
         proxy.$modal.confirm('是否确认新增部门授权，授权操作会更新人员、人脸、部门等数据，请谨慎操作！').then(function () {
            let newForm ={
              vestTenantId:form.value.vestTenantId,
              deptId:form.value.deptId,
              parentId: form.value.parentId,
              ancestors: form.value.ancestors,
              ancestorsName: form.value.ancestorsName,
              status: form.value.status,
              tenantId: userStore.userInfo.tenantId
            }
            addTenantDept(newForm).then((response) => {
              saveLoading.value = false;
              if (response.success) {
                proxy.$modal.msgSuccess("新增成功");
                open.value = false;
                getList();
              } else {
                proxy.$modal.msgError(response.message);
              }
            }).catch((e) => {
              saveLoading.value = false;
              open.value = false;
              proxy.$modal.msgError("新增失败");
            });
          });
      }
    }
  });
}

// 新增部门授权取消按钮
function cancel(type) {
 open.value = false;
 saveLoading.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    //vestTenantId: undefined,
    //vestTenantName: undefined,
    deptId: undefined,
    parentId: undefined,
    ancestors: undefined,
    ancestorsName: undefined,
    status: "0",
  };
  proxy.resetForm("formRef");
}

</script>

<style scoped>
.dep-card {
  min-height: calc(100vh - 120px);
}
.error-msg {
  color: #f56c6c;
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
}
</style>
