<template>
  <div class="app-container">
    <el-card style="height: 100%" shadow="never">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
        <el-form-item label="租户" prop="tenantId" v-if="userStore.userInfo.customParam.userType === 'admin'">
          <el-select v-model="queryParams.tenantId" placeholder="请选择租户" style="width: 180px" @change="handleQuery">
            <el-option v-for="item in tenantList" :key="item.tenantId" :label="item.tenantName" :value="item.tenantId"/>
          </el-select>
        </el-form-item>
        <el-form-item label="应用名称" prop="softName">
          <el-select v-model="queryParams.softName" placeholder="应用名称" style="width: 180px" clearable >
            <el-option v-for="dict in softName" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="版本号" prop="softVersion">
          <el-input v-model="queryParams.softVersion" placeholder="请输入版本号" style="width: 180px" clearable @keyup.enter="handleQuery"/>
        </el-form-item>
        <el-form-item label="应用平台" prop="softPlat">
          <el-select v-model="queryParams.softPlat" placeholder="应用平台" style="width: 180px" clearable >
            <el-option v-for="dict in softPlat" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="应用类型" prop="softType">
          <el-select v-model="queryParams.softType" placeholder="应用类型" style="width: 180px" clearable >
            <el-option v-for="dict in softType" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="打包方式" prop="packType">
          <el-select v-model="queryParams.packType" placeholder="打包方式" style="width: 180px" clearable >
            <el-option v-for="dict in packType" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="版本更新类型" prop="updateType">
          <el-select v-model="queryParams.updateType" placeholder="版本更新类型" style="width: 180px" clearable >
            <el-option v-for="dict in updateType" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="是否绑定其他应用" prop="isBindOrtherSoft">
          <el-select v-model="queryParams.isBindOrtherSoft" placeholder="是否绑定其他应用" style="width: 180px" clearable >
            <el-option v-for="dict in isBindOrtherSoft" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="Plus" v-hasPermi="['sys:release:version:add']" @click="handleAdd">新增</el-button>
        </el-col>
        <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <!--  表格数据展示  -->
      <el-table v-loading="loading" :data="versionList">
        <el-table-column label="序号" type="index" width="50" />
        <el-table-column label="参数主键" align="left" prop="softId" v-if="false"/>
        <el-table-column label="应用名称" align="left" prop="softName" :show-overflow-tooltip="true" >
          <template #default="scope">{{selectDictLabel(softName, scope.row.softName)}}</template>
        </el-table-column>
        <el-table-column label="应用平台" align="left" prop="softPlat" :show-overflow-tooltip="true">
          <template #default="scope">{{selectDictLabel(softPlat, scope.row.softPlat)}}</template>
        </el-table-column>
        <el-table-column label="应用类型" align="left" prop="softType" :show-overflow-tooltip="true">
          <template #default="scope">{{selectDictLabel(softType, scope.row.softType)}}</template>
        </el-table-column>
        <el-table-column label="应用版本号" align="left" prop="softVersion" :show-overflow-tooltip="true"/>
        <el-table-column label="打包方式" align="left" prop="packType" :show-overflow-tooltip="true">
          <template #default="scope">{{selectDictLabel(packType, scope.row.packType)}}</template>
        </el-table-column>
        <el-table-column label="版本更新类型" align="left" prop="updateType">
          <template #default="scope">
            <dict-tag effect="plain" :options="updateType" :value="scope.row.updateType"/>
          </template>
        </el-table-column>
        <el-table-column label="是否绑定其他应用" prop="isBindOrtherSoft" align="center" class-name="small-padding fixed-width" >
          <template #default="scope">
            <dict-tag :options="isBindOrtherSoft" :value="scope.row.isBindOrtherSoft" />
            <el-button v-if="scope.row.isBindOrtherSoft != 0" link type="primary" icon="View"
                       @click="lookBindList(scope.row)">绑定列表
            </el-button>
            <el-button link type="primary" icon="Position"
                       @click="updateBindStatus(scope.row, scope.row.isBindOrtherSoft === 0)">{{ scope.row.isBindOrtherSoft == "0" ? "绑定" : "解绑" }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button link type="primary" icon="Eidt" v-hasPermi="['sys:release:version:edit']"
                       @click="handleUpdate(scope.row)">修改</el-button>
            <el-button link type="primary" icon="Delete" v-hasPermi="['sys:release:version:remove']"
                       @click="handleDelete(scope.row)">删除</el-button>
            <el-button link type="primary" icon="View" v-hasPermi="['sys:release:version:view']"
                       @click="detail(scope.row)">查看</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize"
                  @pagination="getList"/>

      <!-- 添加或修改版本信息 -->
      <el-dialog :title="title" v-model="open" width="980px" append-to-body>
        <el-form ref="formRef" :model="form" :rules="rules" label-width="150px" :disabled="formDisabled">
          <el-row>
            <el-col :span="12">
              <el-form-item label="应用名称" prop="softName">
                <el-select v-model="form.softName" placeholder="请选择应用名称" :disabled="versionDisabled" style="width:300px">
                  <el-option v-for="dict in softName" :key="dict.value" :label="dict.label" :value="dict.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="版本号" prop="softVersion">
                <el-input v-model="form.softVersion" placeholder="请输入版本号" :disabled="versionDisabled" style="width:300px"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="应用平台" prop="softPlat">
                <el-select v-model="form.softPlat" placeholder="请选择应用平台" :disabled="versionDisabled" style="width:300px">
                  <el-option v-for="dict in softPlat" :key="dict.value" :label="dict.label" :value="dict.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="应用类型" prop="softType">
                <el-select v-model="form.softType" placeholder="请选择应用类型" :disabled="versionDisabled" style="width:300px">
                  <el-option v-for="dict in softType" :key="dict.value" :label="dict.label" :value="dict.value" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12" v-if="form.softType === 'app'">
              <el-form-item label="包名" prop="packName">
                <el-input v-model="form.packName" placeholder="请输入包名" :disabled="versionDisabled" style="width:300px"/>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="form.softType === 'certificate' && form.expirationDate !== undefined">
              <el-form-item label="证书到期时间" prop="expirationDate">
                <el-input v-model="form.expirationDate" :disabled="true"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="打包方式" prop="packType">
                <el-select v-model="form.packType" placeholder="打包方式" style="width:300px">
                  <el-option v-for="dict in packType" :key="dict.value" :label="dict.label" :value="dict.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="租户" style="width:450px">
                <el-input v-model="tenantName" placeholder="租户" maxlength="50" disabled/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="版本更新类型" prop="updateType" style="width:450px">
                <el-select v-model="form.updateType" placeholder="版本更新类型">
                  <el-option v-for="dict in updateType" :key="dict.value" :label="dict.label" :value="dict.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="是否绑定其他应用" prop="isBindOrtherSoft" >
                <el-switch v-model="form.isBindOrtherSoft" active-text="绑定其他应用" inactive-text="不绑定"
                    :active-value="1" :inactive-value="0" />
                <el-button v-if="form.isBindOrtherSoft" link type="primary" icon="Position"
                           @click="bindOrtherSoft">绑定</el-button>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="更新日志" prop="updateMessage">
                <el-input v-model="form.updateMessage" type="textarea" placeholder="请输入更新日志" style="width:780px" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="应用包上传" prop="fileUrl">
                <el-upload class="upload-demo"
                           :http-request="uploadFile"
                           :file-list="fileList"
                           :on-remove="handleRemove"
                           :on-preview="handlePreview"
                           :before-remove="beforeRemove"
                           :on-exceed="handleExceed"
                           :on-success="handleSuccess"
                           :limit="1">
                  <el-button type="primary">选择</el-button>
                  <template #tip>
                    <div class="el-upload__tip">
                      只能上传zip/rar/apk/ipa/hpk/wgt/key/csr/crt/pem/pfx/jks/cer文件，且不超过100M
                    </div>
                  </template>
                </el-upload>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForm" :disabled="formDisabled">确 定</el-button>
            <el-button @click="cancel" >取 消</el-button>
          </div>
        </template>
      </el-dialog>

      <!-- 绑定其他版本的数据 -->
      <el-dialog :title="bindTitle" v-model="openBind" width="980px" append-to-body>
        <el-card class="dep-card" shadow="never" v-loading="bindLoading">
          <el-row v-if="lookFormDisabled === false">
            <el-form :model="bindQueryParams" ref="bindQueryRef" :inline="true" v-show="showSearch">
              <el-form-item label="应用名称" prop="softName">
                <el-select v-model="bindQueryParams.softName" placeholder="应用名称" clearable  style="width: 190px">
                  <el-option v-for="dict in softName" :key="dict.value" :label="dict.label" :value="dict.value" />
                </el-select>
              </el-form-item>
              <el-form-item label="版本号" prop="softVersion">
                <el-input v-model="bindQueryParams.softVersion" placeholder="请输入版本号" clearable
                          style="width: 190px" @keyup.enter="handleQuery"/>
              </el-form-item>
              <el-form-item label="应用平台" prop="softPlat">
                <el-select v-model="bindQueryParams.softPlat" placeholder="应用平台" clearable style="width: 190px">
                  <el-option v-for="dict in softPlat" :key="dict.value" :label="dict.label" :value="dict.value"/>
                </el-select>
              </el-form-item>
              <el-form-item label="应用类型" prop="softType">
                <el-select v-model="bindQueryParams.softType" placeholder="应用类型" clearable style="width: 190px">
                  <el-option v-for="dict in softType" :key="dict.value" :label="dict.label" :value="dict.value"/>
                </el-select>
              </el-form-item>
              <el-form-item label="打包方式" prop="packType">
                <el-select v-model="bindQueryParams.packType" placeholder="打包方式" clearable style="width: 190px">
                  <el-option v-for="dict in packType" :key="dict.value" :label="dict.label" :value="dict.value"/>
                </el-select>
              </el-form-item>
              <el-form-item label="版本更新类型" prop="updateType">
                <el-select v-model="bindQueryParams.updateType" placeholder="版本更新类型" clearable style="width: 190px">
                  <el-option v-for="dict in updateType" :key="dict.value" :label="dict.label" :value="dict.value"/>
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="Search" @click="handleBindQuery">搜索</el-button>
                <el-button icon="Refresh" @click="resetBindQuery">重置</el-button>
              </el-form-item>
            </el-form>
          </el-row>
          <!-- 绑定列表 -->
          <el-table v-loading="bindLoading" :data="bindVersionList" ref="multipleTable" @selection-change="handleSelectionChange" :disabled="lookFormDisabled">
            <el-table-column type="selection" align="center" v-if="lookFormDisabled === false"/>
            <el-table-column label="参数主键" align="left" prop="versionId" v-if="false"/>
            <el-table-column label="应用名称" align="left" prop="softName" :show-overflow-tooltip="true">
              <template #default="scope">{{selectDictLabel(softName, scope.row.softName)}}</template>
            </el-table-column>
            <el-table-column label="应用平台" align="left" prop="softPlat" :show-overflow-tooltip="true">
              <template #default="scope">{{selectDictLabel(softPlat, scope.row.softPlat)}}</template>
            </el-table-column>
            <el-table-column label="应用类型" align="left" prop="softType" :show-overflow-tooltip="true">
              <template #default="scope">{{selectDictLabel(softType, scope.row.softType)}}</template>
            </el-table-column>
            <el-table-column label="应用版本号" align="left" prop="softVersion" :show-overflow-tooltip="true"/>
            <el-table-column label="打包方式" align="left" prop="packType" :show-overflow-tooltip="true">
              <template #default="scope">{{selectDictLabel(packType, scope.row.packType)}}</template>
            </el-table-column>
            <el-table-column label="版本更新类型" align="left" prop="updateType">
              <template #default="scope">{{selectDictLabel(updateType, scope.row.updateType)}}</template>
            </el-table-column>
          </el-table>
          <pagination v-show="bindTotal > 0" :total="bindTotal" v-model:page="bindQueryParams.pageNum" v-model:limit="bindQueryParams.pageSize"
                      @pagination="getBindList"/>
        </el-card>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="doBindOrtherSoft" :disabled="lookFormDisabled">确 定</el-button>
            <el-button @click="cancelBind" >取 消</el-button>
          </div>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup name="Version">
import useUserStore from "@/store/modules/user";
import {getTenants} from "@/api/tenant/tenant";
import {
  addVersion,
  bindListVersion,
  delVersion,
  listVersion,
  selectVersionByVersionId,
  updateVersion, validCert
} from "@/api/release/version";
import {selectDictLabel} from "@/utils/common";
import {uploadSingle} from "@/api/tool/upload";

const { proxy } = getCurrentInstance()
const showSearch = ref(true)
const {
  softName,
  softPlat,
  softType,
  packType,
  updateType,
  isBindOrtherSoft
} = proxy.useDict("softName", "softPlat", "softType", "packType", "updateType", "isBindOrtherSoft")

/** 初始化租户数据 */
const userStore = useUserStore();
const tenantList = ref([]);
const tenantName = ref(userStore.userInfo.customParam.tenantName);
function getTenantList() {
  getTenants().then(res => {
    tenantList.value = res.data;
  }).catch(() => {
    tenantList.value = [];
  })
}
// 获取租户名
function getTenantName() {
  tenantList.value.forEach(item => {
    if (item.tenantId === queryParams.value.tenantId) {
      tenantName.value = item.tenantName;
    }
  })
}

/** 版本列表数据展示 */
const loading = ref(false)
const versionList = ref([])
const total = ref(0)
const queryRef = ref(null)
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  tenantId: userStore.userInfo.customParam.tenantId,
})
function getList() {
  loading.value = true;
  listVersion(queryParams.value).then(res =>{
    versionList.value = res.data.records;
    total.value = res.data.total;
    loading.value = false;
  })
}

/** 搜索按钮操作 */
const handleQuery = () =>{
  queryParams.value.pageNum = 1;
  getTenantName();
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  proxy.resetForm("queryRef");
  handleQuery();
}

/**绑定/解绑按钮操作 */
const updateBindStatus = (row, flag) => {
  if (flag) {
    openBind.value = true;
    bindTitle.value = "绑定应用";
    bindLoading.value = false;
    lookFormDisabled.value = false;
    oldForm.value.oldVersionId = row.versionId;
    form.value = row;
    getBindList();
  } else {
    const name = selectDictLabel(softName.value, row.softName);
    const plat = selectDictLabel(softPlat.value, row.softPlat);
    const type = selectDictLabel(softType.value, row.softType);
    const version = row.softVersion;
    proxy.$modal.confirm('是否确认与应用"' + plat + "_" + type + "_" + name + "_" + version + '"解除绑定?').then(() => {
      updateVersion({...row, isBindOrtherSoft: 0}).then(res => {
        if (res.success && res.code === "1") {
          proxy.$modal.msgSuccess("解绑成功");
        } else {
          proxy.$modal.msgSuccess("解绑失败：" + res.data.msg);
        }
        getList();
      })
    }).catch(() =>{
      proxy.$modal.msg("取消操作")
    })
  }
}

/** 删除按钮操作 */
const handleDelete = (row) =>{
  const plat = selectDictLabel(softPlat.value, row.softPlat);
  const type = selectDictLabel(softType.value, row.softType);
  const name = selectDictLabel(softName.value, row.softName);
  const version = row.softVersion;
  proxy.$modal.confirm('是否确认删除应用为"' +
      plat + "-" + type + "-" + name + "-" + version +
      '"的版本数据吗？').then(() => {
    delVersion({versionId: row.versionId}).then(res => {
      if (res.data) {
        proxy.$modal.msgSuccess("删除成功")
      } else {
        proxy.$modal.msgError("删除失败")
      }
      getList();
    })
  }).catch(() => {
    proxy.$modal.msg("操作已取消")
  })
}

/** 查看、新增、修改对话框 */
// 包名规则验证
const checkPackName = (rule, value, callback) => {
  if (form.value.softType !== 'app') {
    callback()
  } else {
    const reg = /[a-zA-Z]+[0-9a-zA-Z_]*(\.[a-zA-Z]+[0-9a-zA-Z_]*)*/;
    if(reg.test(value)) {
      callback()
    } else {
      return callback(new Error('app应用类型需配置指定的正确包名！'))
    }
  }
}
const title = ref("")
const open = ref(false)
const formRef = ref(null)
const fileList = ref([])
const formDisabled = ref(false)
const versionDisabled = ref(false)
const form = ref({})
const oldForm = ref({oldVersionId: undefined})
const rules = {
  softVersion: [{ required: true, message: "版本号不能为空", trigger: "blur" }],
  packName: [{validator:checkPackName, trigger: "blur" }],
  softName: [{ required: true, message: "应用名称不能为空", trigger: "change" }],
  softType: [{ required: true, message: "应用类型不能为空", trigger: "change" }],
  softPlat: [{ required: true, message: "应用平台不能为空", trigger: "change" }],
  packType: [{ required: true, message: "打包方式不能为空", trigger: "change" }],
  updateType: [{required: true, message: "版本更新类型不能为空", trigger: "change" }],
}

// 表单重置
function reset() {
  form.value = {
    tenantId: undefined,
    softVersion: undefined,
    softPlat: undefined,
    softType: undefined,
    softName: undefined,
    isBindOrtherSoft: 0,
    tenantName: undefined,
    fileUrl: undefined,
    expirationDate:undefined,
  };
  proxy.resetForm("formRef");
  fileList.value = [];
}

/** 新增按钮操作 */
const handleAdd = () => {
  formDisabled.value = false;
  versionDisabled.value = false;
  reset();
  open.value = true;
  title.value = '版本上传';
  form.value.tenantId = queryParams.value.tenantId;
}

/** 修改按钮操作 */
const handleUpdate = (row) => {
  formDisabled.value = false;
  reset();
  selectVersionByVersionId({versionId: row.versionId}).then(res => {
    versionDisabled.value = true;
    if (res.data) {
      form.value = {
        versionId: res.data.versionId,
        bindVersionid: res.data.bindVersionid,
        tenantId: res.data.tenantId,
        softVersion: res.data.softVersion,
        softPlat: res.data.softPlat,
        softType: res.data.softType,
        softName: res.data.softName,
        isBindOrtherSoft: res.data.isBindOrtherSoft,
        fileUrl: res.data.fileUrl,
        expirationDate: res.data.expirationDate,
        packType: res.data.packType,
        packName: res.data.packName,
        updateType: res.data.updateType,
        updateMessage: res.data.updateMessage,
        fileSize: res.data.fileSize,
        originalName: res.data.originalName
      }
      open.value = true;
      title.value = '修改版本信息';
      form.value.fileUrl && fileList.value.push({
        name: form.value.originalName,
        url: form.value.fileUrl
      })
    } else {
      proxy.$modal.msgError("数据异常！")
    }
  })
}

/** 查看按钮操作 */
const detail = (row) => {
  reset();
  selectVersionByVersionId({versionId: row.versionId}).then(res => {
    formDisabled.value = true;
    if (res.data) {
      form.value = res.data
      open.value = true;
      title.value = '查看版本信息';
      form.value.fileUrl && fileList.value.push({
        name: form.value.originalName,
        url: form.value.fileUrl
      })
    } else {
      proxy.$modal.msgError("数据异常！")
    }
  })
}

/** 确认按钮操作 */
const submitForm = () => {
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      if (form.value.fileUrl === "" || form.value.fileUrl === undefined) {
        proxy.$modal.msgError("请先上传应用包附件或确认应用包上传完成！")
      } else {
        if (form.value.versionId !== undefined) {
          //修改版本信息
          updateVersion(form.value).then(res => {
            if (res.success && res.code === "1") {
              proxy.$modal.msgSuccess("修改成功")
              open.value = false;
              getList()
              versionDisabled.value = false;
            } else {
              if (res.data !== undefined) {
                proxy.$modal.msgError("修改失败" + res.data.msg);
              } else {
                proxy.$modal.msgError("修改失败"+ res.message);
              }
              open.value = true;
            }
          })
        } else {
          addVersion(form.value).then(res => {
            if (res.success && res.code === "1") {
              proxy.$modal.msgSuccess("新增成功")
              open.value = false;
              getList()
              versionDisabled.value = false;
            } else {
              proxy.$modal.msgError("新增失败" + res.data.msg);
              open.value = true;
            }
          })
        }
      }
    }
  })
}

/** 取消按钮操作 */
const cancel = () => {
  open.value = false;
  versionDisabled.value = false;
  reset();
}

/** 绑定列表数据展示 */
const bindTotal = ref(0)
const bindVersionList = ref([])
const bindList = ref([]) // 绑定应用列表
const arr = ref([]) // 已绑定的版本列表
const bindTitle = ref("")
const openBind = ref(false)
const bindLoading = ref(false)
const bindQueryRef = ref(null)
const lookFormDisabled = ref(false)
const bindQueryParams = ref({
  pageNum: 1,
  pageSize: 10,
})
// 多选的数据
const multipleSelection = ref([]) // 勾选列表
const currentMember = ref([[]])
const bindVersionIds = ref([])
const handleSelectionChange = (val) => {
  multipleSelection.value = val.map((value, index, array) => {
    return value.versionId;
  });
}
const getBindList = () => {
  bindLoading.value = true;
  bindListVersion({...queryParams.value, ...oldForm.value}).then(res => {
    bindVersionList.value = res.data.records;
    bindTotal.value = res.data.total;
    bindLoading.value = false;
  })
}

/** 绑定列表搜索按钮操作 */
const handleBindQuery = () => {
  bindQueryParams.value.pageNum = 1;
  getBindList();
}

/** 绑定列表重置按钮操作 */
const resetBindQuery = () => {
  proxy.resetForm("bindQueryRef")
  handleBindQuery();
}

/** 绑定操作-弹出绑定框 */
const bindOrtherSoft = () => {
  resetBindQuery();
  bindTitle.value = "绑定应用";
  oldForm.value.oldVersionId = form.value.versionId;
  if (form.value.bindVersionid) {
    arr.value = form.value.bindVersionid.split(",");
    bindListVersion({...bindQueryParams.value, ...oldForm.value}).then(res => {
      bindVersionList.value = res.data.records;
      bindTotal.value = res.data.total;
      if (form.value.bindVersionid) {
        //处理是否需要默认选中
        if (bindVersionList.value.length > 0) {
          nextTick(() => {
            if (form.value.isBindOrtherSoft === 0) {
             proxy.$refs.multipleTable.clearSelection();
            }
            bindVersionList.value.forEach(i => {
              if (arr.value.indexOf(i.versionId) > -1) {
                proxy.$refs.multipleTable.toggleRowSelection(i, true);
              } else {
                proxy.$refs.multipleTable.toggleRowSelection(i, false);
              }
            })
          })
        }
      }
    })
  }
  openBind.value = true;
  bindLoading.value = false;
  lookFormDisabled.value = false;
}

/** 确认绑定按钮操作 */
const doBindOrtherSoft = () => {
  bindVersionIds.value = Array.from(new Set([...multipleSelection.value]));
  if (oldForm.value.oldVersionId) {
    // 修改时的绑定
    form.value.bindVersionIds = bindVersionIds.value;
    form.value.isBindOrtherSoft = 1;
    // 修改版本信息
    updateVersion(form.value).then(res => {
      if (res.success && res.code === "1") {
        proxy.$modal.msgSuccess("绑定成功");
        openBind.value = false;
        getList();
      } else {
        if (res.data) {
          proxy.$modal.msgError("绑定失败：" + res.data.msg);
        } else {
          proxy.$modal.msgError("绑定失败：" + res.message);
        }
        openBind.value = false;
      }
    })
  } else {
    form.value.bindVersionIds = bindVersionIds
    openBind.value = false;
  }
}

/** 取消绑定按钮操作 */
const cancelBind = () => {
  openBind.value = false;
  proxy.resetForm("bindQueryRef")
}

/** 查看绑定列表操作 */
const lookBindList = (row) => {
  openBind.value = true;
  bindLoading.value = false;
  lookFormDisabled.value = true;
  bindList.value = [];
  let arr = row.bindVersionid.split(",");
  arr.forEach((elem, index) => {
    selectVersionByVersionId({ versionId: elem }).then(res => {
      if (res.data) {
        bindList.value.push(res.data);
      } else {
        proxy.$modal.msgError("数据异常！");
      }
    });
  });
  bindVersionList.value = bindList.value;
  bindTotal.value = bindList.value.length;
  bindTitle.value = "已绑定列表";
}

/** 文件上传按钮操作 */
const uploadFile = (content) => {
  if (content.file !== undefined) {
    if (content.file.size > 10104857600) {
      proxy.$modal.msgError("上传文件过大，不能超过100M !!");
    } else {
      let fileName = content.file.name.lastIndexOf("."); //取到文件名开始到最后一个点的长度
      let fileNameLength = content.file.name.length; //取到文件名长度
      let fileFormat = content.file.name.substring(fileName + 1, fileNameLength);
      if (
          fileFormat !== "zip" &&
          fileFormat !== "rar" &&
          fileFormat !== "apk" &&
          fileFormat !== "ipa" &&
          fileFormat !== "hpk" &&
          fileFormat !== "wgt" &&
          fileFormat !== "key" &&
          fileFormat !== "csr" &&
          fileFormat !== "crt" &&
          fileFormat !== "pem" &&
          fileFormat !== "pfx" &&
          fileFormat !== "jks" &&
          fileFormat !== "cer"
      ) {
        proxy.$modal.msgError("文件必须为.zip .rar .apk .ipa .hpk .wgt .key .csr .crt .pem .pfx .jks .cer类型 !!!");
      } else {
        //证书类型 取证书的到期时间
        if(form.value.softType ==="certificate" && (fileFormat !== "zip" &&
            fileFormat !== "rar" &&
            fileFormat !== "apk" &&
            fileFormat !== "ipa" &&
            fileFormat !== "hpk" &&
            fileFormat !== "wgt")) {
          doCertValid(content);
        }else{
          return doUploadFile(content);
        }
      }
    }
  }
}

function doCertValid(content) {
  let formData = new FormData();
  formData.append("multipartFile", content.file);
  formData.append("appCode", "portal");
  validCert(formData).then(res => {
    if (res.code === "1" && res.data.isValid) {
      form.value.expirationDate = res.data.expirationDate;
      form.value.startDate = res.data.startDate;
      form.value.validDay = res.data.validDay;
      proxy.$modal.confirm( "是否确认上传文件名为 " +res.data.originalName +
          "【证书有效期为"+
          res.data.startDate +
          " 至 " +
          res.data.expirationDate +
          " 有效期剩余 " +
          res.data.validDay +
          " 天】的证书?").then(() => {
            uploadSingle(formData).then(res => {
              if (res.code === "1") {
                proxy.$modal.msgSuccess("应用包上传成功")
                form.value.fileUrl = res.data.url;
                form.value.originalName = res.data.originalName;
                form.value.fileSize = res.data.fileSize;
                fileList.value.push({
                  name: form.value.originalName,
                  url: form.value.fileUrl,
                });
              } else {
                if (res.success) {
                  proxy.$modal.msgError("上传失败，请重新操作：" + res.message)
                } else {
                  proxy.$modal.msgError("上传失败，请重新操作：" + res.msg)
                }
              }
            })
      }).catch(() => {
        proxy.$modal.msg("取消操作")
      })
    } else {
      if (res.data.isValid === "false") {
        proxy.$modal.msgError("证书 "+res.data.originalName +"校验已过期，请上传可用证书文件！")
      }
      if (res.success) {
        proxy.$modal.msgError("校验证书失败，请重新操作：" + res.message)
      } else {
        proxy.$modal.msgError("校验证书失败，请重新操作：" + res.msg)
      }
    }
  })
}

function doUploadFile(content) {
  let formData = new FormData();
  formData.append("multipartFile", content.file);
  formData.append("appCode", "portal");
  return uploadSingle(formData).then(res => {
    if (res.code === "1") {
      proxy.$modal.msgSuccess("应用包上传成功")
      form.value.fileUrl = res.data.url;
      form.value.originalName = res.data.originalName;
      form.value.fileSize = res.data.fileSize;
      fileList.value.push({
        name: form.value.originalName,
        url: form.value.fileUrl,
      })
    } else {
      if (res.success) {
        proxy.$modal.msgError("上传失败，请重新操作：" + res.message)
      } else {
        proxy.$modal.msgError("上传失败，请重新操作：" + res.msg)
      }
    }
  })
}

const handlePreview = (file) => {
  console.log('url',form.value)
  window.open(form.value.fileUrl, "_blank");
}

const handleRemove = (file, fileList) => {
  form.value.fileUrl = "";
  fileList.value = [];
}

const beforeRemove = (file, fileList) => {
  return proxy.$modal.confirm(`确定移除${file.name}？`)
}

const handleExceed = (files, fileList) => {
  proxy.$modal.msgWarning("只能上传一个文件,请先移除前一个")
}

const handleSuccess = (response, file, fileList) => {
  console.log("res:" + response);
}

getTenantList();
getList();
</script>
