<!-- components/cancel.vue -->
<template>
    <div class="dialog-box" :class="popupType === 'view' ? 'dialog-box-view' : 'dialog-box-edit'">
      <el-form  :model="formData" label-width="110px" :rules="rules" ref="formRef">
        <el-row>
          <el-col :span="24">
            <el-form-item label="撤销金额(元)"  prop="revocationAmount">
              <NumberInput
                v-model="formData.revocationAmount"
                customPlaceholder="请输入撤销金额"
                input-type="decimal"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </template>
  
  <script setup>
  import { ref, reactive } from "vue";
  import { screenIndex } from "@/api/paymentCenter/account-recharge/index";
import { ElMessage } from "element-plus";
  
  const props = defineProps({
    popupType: {
      type: String,
      default: "",
    },
    rowData: {
      type: Object,
      default: () => ({}),
    },
  });
  const formRef = ref(null);
  const rules = reactive({
    revocationAmount: [
      { required: true, message: "请输入撤销金额", trigger: "blur" }
    ],
  });
  const emit = defineEmits(["closeBtn"]);
  
  // 表单数据
  const formData = ref({
    revocationAmount: '',
    id: props.rowData.id // 保留原始ID
  });

  
  // 保存方法
  const saveForm = async () => {
    // 校验表单
    const isValid = await formRef.value.validate();
    if (!isValid) return;
    //校验当前状态是否为撤销成功
    if(props.rowData.rechargeStatus=="4"){
      ElMessage.error("已撤销状态无法撤销");
      return;
    }
  const response=  await screenIndex.payAccountTypeCancel({
      id:formData.value.id,
      revocationAmount:formData.value.revocationAmount

    });
    if(response.code=="1"){
       ElMessage.success("撤销成功");
          emit("closeBtn");
  }

  }
  defineExpose({
    saveForm,
  });
  
 </script>
  
  <style scoped lang="scss">
  /* 保持与add.vue相同的样式 */
  </style>