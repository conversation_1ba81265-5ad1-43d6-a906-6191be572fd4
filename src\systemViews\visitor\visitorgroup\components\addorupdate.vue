<template>
   <div class="dialog-box" :class="popupType === 'view' ? 'dialog-box-view' : 'dialog-box-edit'">
    <el-form ref="formRef" :model="formData" :rules="popupType !== 'view' ? rules : {}" label-width="100px">
      <el-row>
        <el-col :span="12">
          <el-form-item label="邀约人" prop="inviter">
            <el-input
              v-if="popupType !== 'view'"
              v-model="formData.inviter"
              placeholder="请输入邀约人"
              :disabled="true"
              maxlength="30"
            />
            <span v-else>{{ formData.inviter }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="邀约部门" prop="inviteUnit">
            <el-input
              v-if="popupType !== 'view'"
              v-model="formData.inviteUnit"
              placeholder="请输入邀约部门"
              :disabled="true"
              maxlength="30"
            />
            <span v-else>{{ formData.inviteUnit }}</span>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <el-form-item label="来访事由" prop="reason">
            <div class="flex">
              <el-radio-group v-model="formData.reason" :disabled="popupType === 'view'">
                <el-radio-button
                  v-for="dict in visit_reason"
                  :key="dict.value"
                  :label="dict.value"
                >
                  {{ dict.label }}
                </el-radio-button>
              </el-radio-group>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="邀约说明" prop="remark">
            <el-input
              v-if="popupType !== 'view'"
              v-model="formData.remark"
              placeholder="请输入邀约说明"
              maxlength="50"
              show-word-limit
            />
            <span v-else>{{ formData.remark }}</span>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <el-form-item label="办公区域" prop="areaId">
            <el-select
              v-model="formData.areaId"
              :placeholder="popupType !== 'view' ? '请选择可访问区域' : ''"
              :disabled="popupType === 'view'"
              clearable
              @change="handleAreaChange"
            >
              <el-option
                v-for="item in areaManagerList"
                :key="item.id"
                :label="item.areaName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="通行区域" prop="areaIdList">
            <el-select
              v-model="formData.areaIdList"
              multiple
              :placeholder="popupType !== 'view' ? '请选择可通行区域' : ''"
              :disabled="popupType === 'view'"
              clearable
            >
              <el-option
                v-for="item in areaListData"
                :key="item.id"
                :label="item.areaName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <el-form-item label="到访时间" prop="visitorTime">
            <el-date-picker
              v-model="formData.visitorTime"
              type="datetime"
              format="YYYY-MM-DD HH:mm"
              value-format="YYYY-MM-DD HH:mm:00"
              :disabled="popupType === 'view'"
              placeholder="选择预计到访时间"
              @change="handleTimeChange('start')"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="离开时间" prop="visitorEndTime">
            <el-date-picker
              v-model="formData.visitorEndTime"
              type="datetime"
              format="YYYY-MM-DD HH:mm"
              value-format="YYYY-MM-DD HH:mm:00"
              :disabled="popupType === 'view'"
              placeholder="选择预计离开时间"
              @change="handleTimeChange('end')"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <el-form-item label="随行人数" prop="followerNum">
            <el-input
              v-if="popupType !== 'view'"
              v-model.number="formData.followerNum"
              type="number"
              placeholder="请输入随行人数"
              clearable
              maxlength="30"
            />
            <span v-else>{{ formData.followerNum }}</span>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getDept } from '@/api/system/dept'
import { areaList, selectAreaList } from '@/api/visitor/visitorinvite/components/add'
import { addVistorGroup } from '@/api/visitor/visitorgroup'
import useUserStore from "@/store/modules/user";
// 定义 props 和 emits
const props = defineProps({
  rowData: {
    type: Object,
    default: () => ({})
  },
  popupType: {
    type: String,
    validator: (value) => ['add', 'edit', 'view'].includes(value),
    default: 'view'
  }
})


// 字典
const { proxy } = getCurrentInstance();
const { visit_reason } = proxy.useDict("visit_reason");


const emit = defineEmits(['submit', 'cancel'])

// 响应式数据
const formRef = ref(null)
const userStore = useUserStore();

const userInfo = userStore.userInfo
// 提取 state 中的变量
const areaManagerList = ref([])
const areaListData = ref([])


const formData = reactive({
  inviter: userInfo.nickName,
  inviterUserId: userInfo.userId,
  inviteUnitId: userInfo.unitId,
  inviteUnit: '',
  phone: userInfo.phonenumber,
  inviterPhone: userInfo.phonenumber,
  reason: '',
  remark: '',
  areaId: '',
  areaIdList: [],
  visitorTime: '',
  visitorEndTime: '',
  followerNum: 0
})

const rules = reactive({
  inviter: [{ required: true, message: '请输入邀约人', trigger: 'blur' }],
  inviteUnit: [{ required: true, message: '请输入邀约部门', trigger: 'blur' }],
  reason: [{ required: true, message: '请选择来访事由', trigger: 'change' }],
  areaId: [{ required: true, message: '请选择办公区域', trigger: 'change' }],
  areaIdList: [{ required: true, message: '请选择通行区域', trigger: 'change' }],
  visitorTime: [{ required: true, message: '请选择到访时间', trigger: 'change' }],
  visitorEndTime: [{ required: true, message: '请选择离开时间', trigger: 'change' }],
  followerNum: [
    { required: true, message: '请输入随行人数', trigger: 'blur' },
    { type: 'number', min: 0, max: 99, message: '人数范围0-99', trigger: 'blur' }
  ]
})

// 初始化数据
onMounted(async () => {
  await initData()
  if (props.popupType === 'add') {
    formData.areaId = areaManagerList.value[0]?.id || ''
  }
})

// 初始化函数
const initData = async () => {
  try {
    // 获取部门信息
    const deptRes = await getDept(formData.inviteUnitId)
    formData.inviteUnit = deptRes.data.deptName

    // 获取区域列表
    const areaRes = await selectAreaList()
    areaManagerList.value = areaRes.data

    // 获取子区域
    if (formData.areaId) {
      const subAreaRes = await areaList({ parentAreaId: formData.areaId })
      areaListData.value = subAreaRes.data
    }
  } catch (error) {
    console.error('初始化数据失败:', error)
    ElMessage.error('数据加载失败，请刷新重试')
  }
}

// 区域选择变化
const handleAreaChange = async (val) => {
  try {
    formData.areaIdList = []
    const res = await areaList({ parentAreaId: val })
    areaListData.value = res.data
  } catch (error) {
    console.error('获取子区域失败:', error)
    ElMessage.error('区域数据加载失败')
  }
}

// 时间校验
const handleTimeChange = (type) => {
  const { visitorTime, visitorEndTime } = formData
  if (!visitorTime || !visitorEndTime) return

  const start = new Date(visitorTime)
  const end = new Date(visitorEndTime)

  if (start >= end) {
    ElMessage.error('离开时间必须晚于到访时间')
    formData[type === 'start' ? 'visitorTime' : 'visitorEndTime'] = ''
    return
  }

  const diffDays = Math.ceil((end - start) / (1000 * 60 * 60 * 24))
  if (diffDays > 30) {
    ElMessage.error('访问时间不能超过30天')
    formData[type === 'start' ? 'visitorTime' : 'visitorEndTime'] = ''
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    const params = {
      ...formData,
      areaName: areaManagerList.value.find(item => item.id === formData.areaId)?.areaName || ''
    }

    if (props.popupType === 'add') {
      await addVistorGroup(params)
      ElMessage.success('新建邀约成功')
      emit('submit')
    } else {
      // 更新逻辑
      ElMessage.success('修改成功')
      emit('submit')
    }
  } catch (error) {
    console.error('表单提交失败:', error)
    ElMessage.error('表单验证失败，请检查输入')
  }
}
defineExpose({
  handleSubmit
})
</script>

<style scoped>

</style>