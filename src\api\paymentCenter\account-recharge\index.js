import request from '@/utils/request'
import { apiUrl } from '@/utils/config'; 



export class screenIndex {
//选择明细列表

    static pageList(data,params) {
      return request({
        url: `pay${apiUrl}/pay/payRecharge/pageList`,
        method: 'post',
        data: {...data,...params},
      })
    }

    //取消
    static payAccountTypeCancel(data,params) {
      return request({
        url: `pay${apiUrl}/pay/payRecharge/cancel`,
        method: 'post',
        data: {...data,...params},
      })
    }

    //新增
    static payAccountTypeAdd(data,params) {
      return request({
        url: `pay${apiUrl}/pay/payRechargeDetails/add`,
        method: 'post',
        data: {...data,...params},
      })
    }

      static selectUserList(data) {
        return request({
          url: `pay${apiUrl}/pay/payRecharge/selectUserList`,
          method: 'post',
          data:data
        })
      }
    //   根据人员id查询账户列表
    static queryAccountList(data) {
      return request({
        url: `pay${apiUrl}/pay/payRecharge/queryAccountList`,
        method: 'post',
        data:data
      })
    }
// 分页查询充值明细

    static payAccountTypePageList(data,params) {
      return request({
        url: `pay${apiUrl}/pay/payRechargeDetails/pageList`,
        method: 'post',
        data: {...data,...params},
      })
    }
   // 根据redis锁获取导入结果
    static getImportResult(data) {
      return request({
        url: `pay${apiUrl}/pay/payRechargeDetails/getImportResult`,
        method: 'post',
        data:data
      })
    }

    // 批量新增充值信息 
    static batchAdd(data) {
      return request({
        url: `pay${apiUrl}/pay/payRechargeDetails/batchAdd`,
        method: 'post',
        data:data
      })
    }
    // 获取导入校验成功的数据

    static importDataList(data) {
      return request({
        url: `pay${apiUrl}/pay/payRechargeDetails/importDataList`,
        method: 'post',
        data:data
      })
    }
    
    // 查询供应商树
    static getProviderTree(data) {
      return request({
        url: `pay${apiUrl}/pay/payRechargeDetails/providerTree`,
        method: 'post',
        data
      })
    }

    // 撤销
    
    static formCancelRechargeDetail(data) {
      return request({
        url: `pay${apiUrl}/pay/payRechargeDetails/formCancelRechargeDetail`,
        method: 'post',
        data:data
      })
    }
}