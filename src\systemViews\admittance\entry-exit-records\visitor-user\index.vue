<!-- 访客出入记录 -->


<template>
    <div class="app-container-other">
        <Splitpanes class="default-theme">
            <Pane :size="100" :min-size="65">
                <el-card class="dep-card">
                    <dialog-search @getList="getList" formRowNumber="4" :columns="tabelForm.columns"
                        :isShowRightBtn="$checkPermi(['access:entryExitRecords:list'])">
                        <template v-slot:formList>
                            <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="75px">
                                <el-form-item label="访客姓名" prop="nickName">
                                    <el-input v-model="queryParams.nickName" placeholder="请输入访客姓名" clearable />
                                </el-form-item>
                                <el-form-item label="访客单位" prop="visitorDeptName">
                                    <el-input v-model="queryParams.visitorDeptName" placeholder="请输入访客单位" clearable />
                                </el-form-item>
                                <el-form-item label="访客电话" prop="visitorPhone">
                                    <el-input v-model="queryParams.visitorPhone" placeholder="请输入访客电话" clearable />
                                </el-form-item>

                                <el-form-item label="区域名称" prop="areaName">
                                    <el-input v-model="queryParams.areaName" placeholder="请输入区域名称" clearable />
                                </el-form-item>

                                <el-form-item label="设备名称" prop="equipmentName">
                                    <el-input v-model="queryParams.equipmentName" placeholder="请输入设备名称" clearable />
                                </el-form-item>

                                <el-form-item label="动作类型" prop="actionType">
                                    <el-select v-model="queryParams.actionType" placeholder="请选择动作类型" clearable>
                                        <el-option v-for="(item, index) in action_type" :key="index" :label="item.label"
                                            :value="item.value" />
                                    </el-select>
                                </el-form-item>

                                <el-form-item label="验证方式" prop="verification">
                                    <el-select v-model="queryParams.verification" placeholder="请选择验证方式" clearable>
                                        <el-option v-for="(item, index) in verification" :key="index"
                                            :label="item.label" :value="item.value" />
                                    </el-select>
                                </el-form-item>

                                <el-form-item label="通行时间" prop="createTime">
                                    <el-date-picker v-model="queryParams.createTime" style="width: 400px"
                                        type="datetimerange" range-separator="至" start-placeholder="开始日期"
                                        end-placeholder="结束日期" value-format="YYYY-MM-DD HH:mm:ss" :teleported="false" />
                                </el-form-item>
                            </el-form>
                        </template>
                        <template v-slot:searchList>
                            <el-button type="primary" icon="Search" @click="handleQuery"
                                v-hasPermi="['access:entryExitRecords:list']">搜索</el-button>
                            <el-button icon="Refresh" @click="resetQuery"
                                v-hasPermi="['access:entryExitRecords:list']">重置</el-button>
                        </template>

                        <template v-slot:searchBtnList>
                            <el-button icon="Download" @click="handleExport"   v-hasPermi="['access:entryExitRecords:export']">导出
                            </el-button>
                        </template>
                    </dialog-search>

                    <public-table ref="publictable" :rowKey="tabelForm.tableKey" :tableData="list"
                        :columns="tabelForm.columns" :configFlag="tabelForm.tableConfig" :pageValue="pageParams"
                        :total="total" :getList="getList" @clickTextDetail="handleView">

                        <template #operation="{ scope }">
                            <el-button size="mini" type="text" title="查看" icon="View" @click="handleView(scope.row)">
                            </el-button>
                        </template>
                    </public-table>
                </el-card>
            </Pane>
        </Splitpanes>

        <DialogBox :visible="diaWindow.open1" :dialogWidth="diaWindow.dialogWidth"
            :dialogFooterBtn="diaWindow.dialogFooterBtn" @save="save" @cancellation="cancellation"
            :custom-class="diaWindow.customClass" @close="close" :dialogTitle="diaWindow.headerTitle">
            <template #content>
                <detailsDom ref="accountAddRef" :rowData="diaWindow.rowData" :popupType="diaWindow.popupType"
                    @closeBtn="cancellationRefsh"></detailsDom>
            </template>
        </DialogBox>
    </div>
</template>

<script setup name="User">
import { ref, reactive, getCurrentInstance } from "vue";
import {
    screenIndex
} from "@/api/admittance/entry-exit-records";
import { apiUrl } from "@/utils/config";
import { formatMinuteTime } from "@/utils";
import detailsDom from "../components/details.vue";
const { proxy } = getCurrentInstance();
const { employee_type, action_type, verification } = proxy.useDict(
    "employee_type", "action_type", "verification"
);

const accountAddRef = ref(null);
const diaWindow = reactive({
    open1: false,
    headerTitle: "",
    popupType: "",
    rowData: "",
    dialogWidth: "20%",
    dialogFooterBtn: false,
});

const pageParams = ref({
    pageNum: 1,
    pageSize: 10,
});
const queryParams = ref({
    employeeType: '2'
});
const list = ref([]);
const total = ref(0);
const tabelForm = reactive({
    tableKey: "1", //表格key值
    isShowRightToolbar: true, //是否显示右侧显示和隐藏
    showSearch: true,
    // 表格表格数据
    columns: [
        {
            fieldIndex: "nickName", // 对应列内容的字段名
            label: "访客姓名", // 显示的标题
            resizable: true, // 对应列是否可以通过拖动改变宽度
            visible: true, // 展示与隐藏
            sortable: true, // 对应列是否可以排序
            minWidth: "140px", //最小宽度%
            type: 'clickText'
        },
        {
            fieldIndex: "visitorDeptName", // 对应列内容的字段名
            label: "访客单位", // 显示的标题
            resizable: true, // 对应列是否可以通过拖动改变宽度
            visible: true, // 展示与隐藏
            sortable: true, // 对应列是否可以排序
            minWidth: "150px", //最小宽度%
            width: "", //宽度
            align: "left", //表格对齐方式
        },
        {
            fieldIndex: "visitorPhone", // 对应列内容的字段名
            label: "访客电话", // 显示的标题
            resizable: true, // 对应列是否可以通过拖动改变宽度
            visible: true, // 展示与隐藏
            sortable: true, // 对应列是否可以排序
            minWidth: "150px", //最小宽度%
            width: "", //宽度
        },
        {
            fieldIndex: "createTime", // 对应列内容的字段名
            label: "通行时间", // 显示的标题
            resizable: true, // 对应列是否可以通过拖动改变宽度
            visible: true, // 展示与隐藏
            sortable: true, // 对应列是否可以排序
            minWidth: "150px", //最小宽度%
            align: "", //表格对齐方式
        },
        {
            fieldIndex: "employeeType", // 对应列内容的字段名
            label: "人员类型", // 显示的标题
            resizable: true, // 对应列是否可以通过拖动改变宽度
            visible: true, // 展示与隐藏
            sortable: true, // 对应列是否可以排序
            minWidth: "120px", //最小宽度%
            width: "", //宽度
            align: "", //表格对齐方式
            type: "dict",
            dictList: employee_type
        },


        {
            fieldIndex: "areaName", // 对应列内容的字段名
            label: "区域名称", // 显示的标题
            resizable: true, // 对应列是否可以通过拖动改变宽度
            visible: true, // 展示与隐藏
            sortable: true, // 对应列是否可以排序
            minWidth: "150px", //最小宽度%
            align: "left", //表格对齐方式
        },

        {
            fieldIndex: "equipmentName", // 对应列内容的字段名
            label: "设备名称", // 显示的标题
            resizable: true, // 对应列是否可以通过拖动改变宽度
            visible: true, // 展示与隐藏
            sortable: true, // 对应列是否可以排序
            minWidth: "120px", //最小宽度%
            align: "left", //表格对齐方式
        },
        {
            fieldIndex: "actionType", // 对应列内容的字段名
            label: "动作类型", // 显示的标题
            resizable: true, // 对应列是否可以通过拖动改变宽度
            visible: true, // 展示与隐藏
            sortable: true, // 对应列是否可以排序
            minWidth: "120px", //最小宽度%
            type: "dict",
            dictList: action_type
        },

        {
            fieldIndex: "verification", // 对应列内容的字段名
            label: "验证方式", // 显示的标题
            resizable: true, // 对应列是否可以通过拖动改变宽度
            visible: true, // 展示与隐藏
            sortable: true, // 对应列是否可以排序
            minWidth: "120px", //最小宽度%
            type: "dict",
            dictList: verification
        },


        {
            label: "操作",
            slotname: "operation",
            minWidth: "60px",
            fixed: "right", //固定
            visible: true,
        },
    ],
    // 表格基础配置项，看个人需求添加
    tableConfig: {
        needPage: true, // 是否需要分页
        index: true, // 是否需要序号
        selection: false, // 是否需要多选
        reserveSelection: false, // 是否需要支持跨页选择
        indexFixed: false, // 序号列固定 left true right
        selectionFixed: false, //多选列固定left true right
        indexWidth: "50", //序号列宽度
        loading: false, //表格loading
        showSummary: false, //是否开启合计
        height: null, //表格固定高度 比如：300px
    },
});

/** 查询列表 */
const getList = () => {
    tabelForm.tableConfig.loading = true;
    screenIndex.inAndOutInfo(queryParams.value, pageParams.value).then((response) => {
        list.value = response.data.records;
        total.value = response.data.total;
        tabelForm.tableConfig.loading = false;
    });
};

/** 搜索按钮操作 */
const handleQuery = () => {
    pageParams.value.pageNum = 1;
    getList();
};
/** 重置按钮操作 */
const resetQuery = () => {
    proxy.resetForm("queryForm");
    queryParams.value = {
        employeeType: '2'
    };
    handleQuery();
};

/** 导出按钮操作 */

const handleExport = () => {
    proxy.download(
        `wisdomaccess${apiUrl}/access/entryExitRecords/export`,
        {
            ...queryParams.value,
        },
        `访客出行记录列表_${formatMinuteTime(new Date())}.xlsx`
    );
};

/** 查看 */
const handleView = (data) => {
    diaWindow.headerTitle = "查看出行记录";
    diaWindow.popupType = "view";
    diaWindow.rowData = data;
    diaWindow.rowData.typePage = 'visitor'
    diaWindow.dialogWidth = "35%";
    diaWindow.dialogFooterBtn = false;
    diaWindow.open1 = true;
};


/** 点击确定后刷新 */
const cancellationRefsh = () => {
    close(false);
    getList();
};
/** 点击取消保存 */
const cancellation = (val) => {
    close(false);
};

/** 关闭弹窗 */
const close = (val) => {
    diaWindow.open1 = val;
};



getList();
</script>

<style scoped>
.dep-card {
    min-height: calc(100vh - 160px);
}
</style>