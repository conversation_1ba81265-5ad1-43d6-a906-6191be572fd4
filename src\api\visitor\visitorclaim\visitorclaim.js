import request from '@/utils/request'

// 查询访客认领列表
export function listVisitorclaim(data, params) {
  return request({
    url: '/visitor/visitorclaim/list',
    method: 'post',
    data: data,
    params: params,
  })
}

// 查询访客认领详细
export function getVisitorclaim(id) {
  return request({
    url: '/visitor/visitorclaim/' + id,
    method: 'get'
  })
}

// 新增访客认领
export function addVisitorclaim(data) {
  return request({
    url: '/visitor/visitorclaim',
    method: 'post',
    data: data
  })
}

// 修改访客认领
export function updateVisitorclaim(data) {
  return request({
    url: '/visitor/visitorclaim/edit',
    method: 'post',
    data: data
  })
}

// 删除访客认领
export function delVisitorclaim(id) {
  return request({
    url: '/visitor/visitorclaim/deleted/' + id,
    method: 'delete'
  })
}

// 导出访客认领
export function exportVisitorclaim(query) {
  return request({
    url: '/visitor/visitorclaim/export',
    method: 'get',
    params: query
  })
}


//查询被访人信息
export function findBeVisitedPersonByTel(tel) {
  return request({
    url: '/visitor/visitorclaim/beVisited/tel/' + tel,
    method: 'get',
  })
}
