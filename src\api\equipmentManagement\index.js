import request from '@/utils/request'
import { apiUrl } from '@/utils/config';


// 获取 设备列表
export function queryList(params,data) {
    return request({
      url: `user${apiUrl}/deviceManagement/queryList`,
      method: 'post',
      params:params,
      data:{...data,...params}
    })
  }
  // 删除设备
  export function delDeviceById(id) {
    return request({
      url: `user${apiUrl}/deviceManagement/deleteById/` + id,
      method: 'get',
    })
  }
  export function queryAbleList() {
    return request({
      url: `user${apiUrl}/deviceManagement/queryAbleList`,
      method: 'post',
      data: {}
    })
  }


  //获取区域级联选择
export function areaTree() {
    return request({
      url: `user${apiUrl}/area/areaTreeSelect`,
      method: 'get',
    })
  }
export function areaTreeByType(data) {
  return request({
    url: `user${apiUrl}/area/areaTreeSelectByType/`+data,
    method: 'get',
  })
}


  // 新增或修改设备
export function saveDevice(data) {
    return request({
      url: `user${apiUrl}/deviceManagement/saveDevice`,
      method: 'post',
      data: data
    })
  }
  //校验设备sn码并获取设备在线状态
  export function verifySnAndGetDevice(data) {
    return request({
      url: '/admittance/device/verifySnAndGetDevice',
      method: 'post',
      data: data
    })
  }




    // 新增或修改设备
export function editDevice(data) {
    return request({
      url: `user${apiUrl}/deviceManagement/editDevice`,
      method: 'post',
      data: data
    })
  }
