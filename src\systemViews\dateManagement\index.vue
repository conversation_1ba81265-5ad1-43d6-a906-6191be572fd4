<template>
  <div class="app-container">
    <el-card>
    <dialog-search
      @getList="getList"
      formRowNumber="3"
      :columns="tabelForm.columns"
    >
      <template v-slot:formList>
        <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          label-width="70px"
        >
          <el-form-item label="日期">
            <el-date-picker
              v-model="daySelects"
              type="daterange"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              @change="daySelectsChange"
            />
    
          </el-form-item>
        </el-form>
      </template>
      <template v-slot:searchList>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索
        </el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </template>
    </dialog-search>

    <public-table
      ref="publictable"
      :rowKey="tabelForm.tableKey"
      :tableData="list"
      :columns="tabelForm.columns"
      :configFlag="tabelForm.tableConfig"
      :pageValue="pageParams"
      :total="total"
      :getList="getList"
    >
      <template #dayType="{ scope }">
        <el-switch
          active-text="工作日"
          inactive-text="休息日"
          inline-prompt
          v-model="scope.row.dayType"
          :active-value="'0'"
          :inactive-value="'1'"
          :before-change="() => beforeChange1(scope.row)"
        />
      </template>
    </public-table>
    </el-card>
  </div>
</template>

<script setup name="DateManagement">  
import { getCurrentInstance, reactive, ref } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { screenIndex } from "@/api/system/dateManagement";
//当前年月日 格式 2024-01-01
const date = new Date();
const publictable = ref(null);
const queryParams = ref({
  daySelectStart: "",
  daySelectEnd: "",
});
const daySelects = ref([]);
// 表格数据
const list = ref([]);
// 表格分页参数
const pageParams = ref({
  pageNum: 1,
  pageSize: 10,
});

// 表格总数
const total = ref(0);
// 是否显示弹窗

// 停车场列表
const parkingList = ref([]);

const open1 = ref(false);
const dialogTitle = ref("");
const popupType = ref("");
const editRef = ref(null);
const rowData = ref({});
const dialogWidth = ref();
const dialogFooterBtn = ref(false);
// 字典
const { proxy } = getCurrentInstance();
const {
  parking_rule_apply_type,
  entry_exit_type,
  parking_rule_restriction_type,
  time_restriction,
  parking_rule_vehicle_type,
  date_type,
  parking_rule_status,
} = proxy.useDict(
  "parking_rule_apply_type",
  "entry_exit_type",
  "parking_rule_restriction_type",
  "time_restriction",
  "parking_rule_vehicle_type",
  "date_type",
  "parking_rule_status"
);

const daySelectsChange = (values)=>{
  queryParams.value.daySelectStart = values[0]
  queryParams.value.daySelectEnd = values[1]
}

const beforeChange1 = (row) => {
  return new Promise((resolve) => {
    const newDayType = row.dayType === "1" ? "0" : "1"; // 切换值

    ElMessageBox.confirm(
      `确认要将日期类型修改为"${newDayType === "0" ? "工作日" : "休息日"}"吗？`,
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }
    )
      .then(() => {
        const updatedRow = { id: row.id, dayType: newDayType }; // 更新 dayType
        screenIndex.dateManagementUpdate(updatedRow).then((response) => {
          if (!response.success) {
            ElMessage.error(response.message);
            resolve(false); // 返回 false 阻止切换
          } else {
            ElMessage.success("修改成功");
            resolve(true); // 返回 true 允许切换
          }
        });
      })
      .catch(() => {
        resolve(false); // 返回 false 阻止切换
      });
  });
};

// 表格配置
const tabelForm = reactive({
  tableKey: "1", //表格key值
  isShowRightToolbar: true, //是否显示右侧显示和隐藏
  showSearch: true,
  // 表格表格数据
  columns: [
    {
      fieldIndex: "date", // 对应列内容的字段名
      label: "日期", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: false, // 对应列是否可以排序
      minWidth: "40%", //最小宽度%
      align: "center",
    },
    {
      label: "日期类型",
      slotname: "dayType",
      minWidth: "40%",
      fixed: "", //固定
      visible: true,
    },
  ],
  // 表格基础配置项，看个人需求添加
  tableConfig: {
    needPage: true, // 是否需要分页
    index: true, // 是否需要序号
    selection: false, // 是否需要多选
    reserveSelection: false, // 是否需要支持跨页选择
    indexFixed: false, // 序号列固定 left true right
    selectionFixed: false, //多选列固定left true right
    indexWidth: "50", //序号列宽度
    loading: false, //表格loading
    showSummary: false, //是否开启合计
    height: null, //表格固定高度 比如：300px
  },
});

/** 查询日期列表 */
const getList = () => {
  tabelForm.tableConfig.loading = true;
  screenIndex
    .dateManagementList({}, { ...queryParams.value, ...pageParams.value })
    .then((response) => {
      list.value = response.data.records;
      total.value = response.data.total;
      tabelForm.tableConfig.loading = false;
    });
};

/** 搜索按钮操作 */
const handleQuery = () => {
  pageParams.value.pageNum = 1;
  getList();
};
/** 重置按钮操作 */
const resetQuery = () => {
  queryParams.value = {};
  daySelects.value = []
  proxy.resetForm("queryForm");
  handleQuery();
};

const cancellationRefresh = () => {
  close(false);
  getList();
};
// 生命周期：组件挂载时调用
onMounted(() => {
  getList();
});
</script>


<style scoped>
.el-card{
  height: calc(100vh - 110px);
  overflow-y: auto;
}
</style>
