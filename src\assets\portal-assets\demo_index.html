<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>iconfont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i2/O1CN01ZyAlrn1MwaMhqz36G_!!6000000001499-73-tps-64-64.ico" type="image/x-icon"/>
  <link rel="icon" type="image/svg+xml" href="//img.alicdn.com/imgextra/i4/O1CN01EYTRnJ297D6vehehJ_!!6000000008020-55-tps-64-64.svg"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
  <style>
    .main .logo {
      margin-top: 0;
      height: auto;
    }

    .main .logo a {
      display: flex;
      align-items: center;
    }

    .main .logo .sub-title {
      margin-left: 0.5em;
      font-size: 22px;
      color: #fff;
      background: linear-gradient(-45deg, #3967FF, #B500FE);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">
      <img width="200" src="https://img.alicdn.com/imgextra/i3/O1CN01Mn65HV1FfSEzR6DKv_!!6000000000514-55-tps-228-59.svg">

    </a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>

      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=2625613" target="_blank" class="nav-more">查看项目</a>

    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">

            <li class="dib">
              <span class="icon iconfont">&#xe698;</span>
                <div class="name">save</div>
                <div class="code-name">&amp;#xe698;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe638;</span>
                <div class="name">竖线</div>
                <div class="code-name">&amp;#xe638;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe608;</span>
                <div class="name">plus</div>
                <div class="code-name">&amp;#xe608;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe61c;</span>
                <div class="name">日程</div>
                <div class="code-name">&amp;#xe61c;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe668;</span>
                <div class="name">知识库</div>
                <div class="code-name">&amp;#xe668;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe6fe;</span>
                <div class="name">通讯录</div>
                <div class="code-name">&amp;#xe6fe;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe627;</span>
                <div class="name">通知公告 base/setting/systemNotice </div>
                <div class="code-name">&amp;#xe627;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe606;</span>
                <div class="name">工具</div>
                <div class="code-name">&amp;#xe606;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe61e;</span>
                <div class="name">数据</div>
                <div class="code-name">&amp;#xe61e;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe619;</span>
                <div class="name">动态</div>
                <div class="code-name">&amp;#xe619;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe62d;</span>
                <div class="name">消息</div>
                <div class="code-name">&amp;#xe62d;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe6af;</span>
                <div class="name">邮箱</div>
                <div class="code-name">&amp;#xe6af;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe670;</span>
                <div class="name">管理中心</div>
                <div class="code-name">&amp;#xe670;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe63e;</span>
                <div class="name">user</div>
                <div class="code-name">&amp;#xe63e;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe6e5;</span>
                <div class="name">工作台</div>
                <div class="code-name">&amp;#xe6e5;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe68c;</span>
                <div class="name">工作台</div>
                <div class="code-name">&amp;#xe68c;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe60a;</span>
                <div class="name">查询</div>
                <div class="code-name">&amp;#xe60a;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe607;</span>
                <div class="name">home</div>
                <div class="code-name">&amp;#xe607;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe67b;</span>
                <div class="name">管理驾驶舱</div>
                <div class="code-name">&amp;#xe67b;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe616;</span>
                <div class="name">工作台</div>
                <div class="code-name">&amp;#xe616;</div>
              </li>

          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>默认情况下不支持多色，直接添加多色图标会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持两种方式引用多色图标：SVG symbol 引用方式和彩色字体图标模式。（使用彩色字体图标需要在「编辑项目」中开启「彩色」选项后并重新生成。）</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.woff2?t=1625205706672') format('woff2'),
       url('iconfont.woff?t=1625205706672') format('woff'),
       url('iconfont.ttf?t=1625205706672') format('truetype');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">

          <li class="dib">
            <span class="icon iconfont icon-save"></span>
            <div class="name">
              save
            </div>
            <div class="code-name">.icon-save
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-shuxian"></span>
            <div class="name">
              竖线
            </div>
            <div class="code-name">.icon-shuxian
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-xinzeng"></span>
            <div class="name">
              plus
            </div>
            <div class="code-name">.icon-xinzeng
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-richeng"></span>
            <div class="name">
              日程
            </div>
            <div class="code-name">.icon-richeng
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-zhishiku"></span>
            <div class="name">
              知识库
            </div>
            <div class="code-name">.icon-zhishiku
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-V"></span>
            <div class="name">
              通讯录
            </div>
            <div class="code-name">.icon-V
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-gonggao"></span>
            <div class="name">
              通知公告 base/setting/systemNotice
            </div>
            <div class="code-name">.icon-gonggao
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-gongju"></span>
            <div class="name">
              工具
            </div>
            <div class="code-name">.icon-gongju
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-shujujiashi"></span>
            <div class="name">
              数据
            </div>
            <div class="code-name">.icon-shujujiashi
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-yewudongtai"></span>
            <div class="name">
              动态
            </div>
            <div class="code-name">.icon-yewudongtai
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-xiaoxi"></span>
            <div class="name">
              消息
            </div>
            <div class="code-name">.icon-xiaoxi
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-youxiang"></span>
            <div class="name">
              邮箱
            </div>
            <div class="code-name">.icon-youxiang
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-guanlizhongxin"></span>
            <div class="name">
              管理中心
            </div>
            <div class="code-name">.icon-guanlizhongxin
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-user"></span>
            <div class="name">
              user
            </div>
            <div class="code-name">.icon-user
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-gongzuotai"></span>
            <div class="name">
              工作台
            </div>
            <div class="code-name">.icon-gongzuotai
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-gongzuotai1"></span>
            <div class="name">
              工作台
            </div>
            <div class="code-name">.icon-gongzuotai1
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-zaitu"></span>
            <div class="name">
              查询
            </div>
            <div class="code-name">.icon-zaitu
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-home"></span>
            <div class="name">
              home
            </div>
            <div class="code-name">.icon-home
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-guanlijiashicang"></span>
            <div class="name">
              管理驾驶舱
            </div>
            <div class="code-name">.icon-guanlijiashicang
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-gongzuotai2"></span>
            <div class="name">
              工作台
            </div>
            <div class="code-name">.icon-gongzuotai2
            </div>
          </li>

        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont icon-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-save"></use>
                </svg>
                <div class="name">save</div>
                <div class="code-name">#icon-save</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shuxian"></use>
                </svg>
                <div class="name">竖线</div>
                <div class="code-name">#icon-shuxian</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xinzeng"></use>
                </svg>
                <div class="name">plus</div>
                <div class="code-name">#icon-xinzeng</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-richeng"></use>
                </svg>
                <div class="name">日程</div>
                <div class="code-name">#icon-richeng</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhishiku"></use>
                </svg>
                <div class="name">知识库</div>
                <div class="code-name">#icon-zhishiku</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-V"></use>
                </svg>
                <div class="name">通讯录</div>
                <div class="code-name">#icon-V</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gonggao"></use>
                </svg>
                <div class="name">通知公告 base/setting/systemNotice </div>
                <div class="code-name">#icon-gonggao</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gongju"></use>
                </svg>
                <div class="name">工具</div>
                <div class="code-name">#icon-gongju</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shujujiashi"></use>
                </svg>
                <div class="name">数据</div>
                <div class="code-name">#icon-shujujiashi</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yewudongtai"></use>
                </svg>
                <div class="name">动态</div>
                <div class="code-name">#icon-yewudongtai</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiaoxi"></use>
                </svg>
                <div class="name">消息</div>
                <div class="code-name">#icon-xiaoxi</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-youxiang"></use>
                </svg>
                <div class="name">邮箱</div>
                <div class="code-name">#icon-youxiang</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-guanlizhongxin"></use>
                </svg>
                <div class="name">管理中心</div>
                <div class="code-name">#icon-guanlizhongxin</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-user"></use>
                </svg>
                <div class="name">user</div>
                <div class="code-name">#icon-user</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gongzuotai"></use>
                </svg>
                <div class="name">工作台</div>
                <div class="code-name">#icon-gongzuotai</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gongzuotai1"></use>
                </svg>
                <div class="name">工作台</div>
                <div class="code-name">#icon-gongzuotai1</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zaitu"></use>
                </svg>
                <div class="name">查询</div>
                <div class="code-name">#icon-zaitu</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-home"></use>
                </svg>
                <div class="name">home</div>
                <div class="code-name">#icon-home</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-guanlijiashicang"></use>
                </svg>
                <div class="name">管理驾驶舱</div>
                <div class="code-name">#icon-guanlijiashicang</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gongzuotai2"></use>
                </svg>
                <div class="name">工作台</div>
                <div class="code-name">#icon-gongzuotai2</div>
            </li>

          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
