<template>
  <div class="app-container" style="margin: 0; padding: 0; background: #fff">
    <div class="banner-img">
      <img src="@/assets/images/mail/banner.jpg" alt="图片加载中..." />
    </div>
    <div class="crumb">
      <div class="break">
        <el-breadcrumb separator="/" class="breadcrumb">
          <el-icon class="el-icon-location-outline" style="margin-right: 10px; font-size: 20px; color: #c3c3c3"></el-icon>
          <span style="color: #333333">您的位置：</span>
          <el-breadcrumb-item :to="{ path: portalIndex }" replace>智慧园区平台</el-breadcrumb-item>
          <el-breadcrumb-item><a>{{ title }}</a></el-breadcrumb-item>
        </el-breadcrumb>

        <el-form :inline="true" v-if="userType === 'admin'">
          <el-form-item label="租 户:">
            <el-select v-model="sendInfo.tenantId" style="width: 200px" @change="tenantChange">
              <el-option v-for="item in tenantList" :key="item.tenantId" :label="item.tenantName" :value="item.tenantId"/>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <el-card class="note-card" shadow="never">
      <el-table ref="multipleTable" :data="mail" tooltip-effect="dark" @row-click="goAdd" v-loading="loading" v-if="mail">
        <el-table-column type="selection" width="60" align="center">
        </el-table-column>
        <el-table-column label="消息标题" align="left" :show-overflow-tooltip="true">
          <template #default="scope">
            <span :class="scope.row.isRead === 0 ? 'unRead' : 'read'">{{scope.row.noticeTitle}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="接收时间" :show-overflow-tooltip="true">
          <template #default="scope">
            <span :class="scope.row.isRead === 0 ? 'unRead' : 'read'">{{scope.row.createDate.substring(0, 19)}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="address" label="创建者" :show-overflow-tooltip="true">
          <template #default="scope">
            <span :class="scope.row.isRead === 0 ? 'unRead' : 'read'">{{scope.row.createByName }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="address" label="消息类型" :show-overflow-tooltip="true">
          <template #default="scope">
            <span :class="scope.row.isRead === 0 ? 'unRead' : 'read'">{{scope.row.noticeType === "notice_type_one"
                    ? "新闻"
                    : "" || scope.row.noticeType === "2"
                        ? "通知"
                        : "" || scope.row.noticeType === "notice_type_two"
                            ? "公告"
                            : ""
              }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="address" label="是否已读" :show-overflow-tooltip="true">
          <template #default="scope">
            <span :class="scope.row.isRead === 0 ? 'unRead' : 'read'">{{scope.row.isRead === 0 ? "未读" : "已读"}}</span>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" v-model:page="sendInfo.pageNum" v-model:limit="sendInfo.pageSize"
                  @pagination="getList"/>
    </el-card>
  </div>
</template>

<script setup>
import useUserStore from "@/store/modules/user";
import {getMailInfo} from "@/api/release/notice";
import {list as listTenantList} from "@/api/tenant/tenant";

const {proxy} = getCurrentInstance();
const portalIndex = ref(proxy.portalIndex);
const userStore = useUserStore();
const userType = userStore.userInfo.customParam.userType;
const tenantId = userStore.userInfo.customParam.tenantId;


const tenantList=ref([])
const mail = ref([])
const title = ref('站内信')
const total =ref(0)
const loading = ref(false)

const sendInfo = ref({
  pageNum: 1,
  pageSize: 10,
  tenantId: tenantId
})

// 查找站内信列表
function getList() {
  loading.value = true;
  getMailInfo(sendInfo.value).then(res => {
    mail.value = res.data.records;
    total.value = res.data.total;
    loading.value = false;
  })
}

const goAdd = (row, column) => {
  proxy.$router.push({
    path: "addInfo",
    query: {
      noticeId: row.noticeId,
      content: row.noticeContent,
      time: row.createDate,
      title: title.value,
      mailTitle: row.noticeTitle,
    }
  })
}

function getTenantList(){
  listTenantList().then((res)=>{
    tenantList.value = res.data
  })
}

const tenantChange = () => {
  sendInfo.value.pageNum = 1;
  getList()
}

getList()
getTenantList()
</script>

<style scoped lang="scss">
@import "@/layout/components/index.scss";
</style>
