<template>
  <div class="app-container">
    <el-card>
      <el-form :model="queryParams" ref="queryForm" v-show="showSearch" :inline="true" @submit.native.prevent>
        <el-form-item label="配置名称" prop="themeName">
          <el-input v-model="queryParams.themeName" placeholder="请输入配置名称" clearable style="width: 240px"
                    @keyup.enter.native="handleQuery"/>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="Plus" v-hasPermi="['sys:base:theme:add']" @click="handleAdd">新增
          </el-button>
        </el-col>
        <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>
      <el-row>
        <el-alert title="如果以下数据都未启用时，系统默认使用配置编码为【default】的主题配置" type="info" :closable="false"
                  show-icon style="margin-bottom: 10px"/>
      </el-row>
      <el-table v-loading="loading" :data="dataList">
        <el-table-column label="配置编码" align="center" prop="themeCode" :show-overflow-tooltip="true"/>
        <el-table-column label="配置名称" align="center" prop="themeName" :show-overflow-tooltip="true"/>
        <el-table-column label="主题风格" align="center" prop="sideTheme">
          <template #default="scope">
            {{ scope.row.sideTheme === 'theme-dark' ? '深色' : '浅色' }}
          </template>
        </el-table-column>
        <el-table-column label="是否开启TopNav" align="center" prop="topNav">
          <template #default="scope">
            <el-tag effect="plain" :type="scope.row.topNav === '1' ? 'success' : 'warning'">
              {{ scope.row.topNav === '1' ? '开启' : '未开启' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="是否开启Tags-Views" align="center" prop="tagsView" width="150px">
          <template #default="scope">
            <el-tag effect="plain" :type="scope.row.tagsView === '1' ? 'success' : 'warning'">
              {{ scope.row.tagsView === '1' ? '开启' : '未开启' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="是否固定Header" align="center" prop="fixedHeader">
          <template #default="scope">
            <el-tag effect="plain" :type="scope.row.fixedHeader === '1' ? 'success' : 'warning'">
              {{ scope.row.fixedHeader === '1' ? '固定' : '不固定' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="是否显示Logo" align="center" prop="sidebarLogo">
          <template #default="scope">
            <el-tag effect="plain" :type="scope.row.sidebarLogo === '1' ? 'success' : 'warning'">
              {{ scope.row.sidebarLogo === '1' ? '显示' : '不显示' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="是否启用" align="center" prop="enabledStatus" :show-overflow-tooltip="true">
          <template #default="scope">
            <el-switch v-model="scope.row.enabledStatus" active-value="1" inactive-value="0"
                       @change="handleEnabled(scope.row)">
            </el-switch>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="260">
          <template #default="scope">
            <el-button link icon="Edit" v-hasPermi="['sys:base:theme:edit']"
                       @click="handleUpdate(scope.row)"
                       :loading="reloadId === scope.row.id && reloadType === 'edit'"
                       :disabled="scope.row.themeCode === 'default' && userType !== 'admin'">
              修改
            </el-button>
            <el-button link icon="Delete" v-hasPermi="['sys:base:theme:remove']" @click="handleDelete(scope.row)"
                       :loading="reloadId === scope.row.id && reloadType === 'remove'"
                       :disabled="scope.row.themeCode === 'default'">删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
      />
    </el-card>

    <!-- 添加或修改主题配置对话框 -->
    <el-dialog :title="title" v-model="open" width="700px" append-to-body :close-on-press-escape="false"
               @close="cancel">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="150px">
        <el-row>
          <el-col :span="20">
            <el-form-item label="主题编码" prop="themeCode">
              <el-input v-model="form.themeCode" placeholder="请输入主题编码" maxlength="50"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="20">
            <el-form-item label="主题名称" prop="themeName">
              <el-input v-model="form.themeName" placeholder="请输入应用主键" maxlength="50"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="15">
            <el-form-item label="主题风格" prop="sideTheme">
              <div class="setting-drawer-block-checbox">
                <div class="setting-drawer-block-checbox-item" @click="handleTheme('theme-dark')">
                  <img src="@/assets/images/dark.svg" alt="dark"/>
                  <div v-if="form.sideTheme === 'theme-dark'" class="setting-drawer-block-checbox-selectIcon"
                       style="display: block">
                    <i aria-label="图标: check" class="anticon anticon-check">
                      <svg viewBox="64 64 896 896" data-icon="check" width="1em" height="1em" :fill="theme"
                           aria-hidden="true" focusable="false" class="">
                        <path
                            d="M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 0 0-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"/>
                      </svg>
                    </i>
                  </div>
                  <span class="setting-drawer-block-checbox-text">深色</span>
                </div>
                <div class="setting-drawer-block-checbox-item" @click="handleTheme('theme-light')">
                  <img src="@/assets/images/light.svg" alt="light"/>
                  <div v-if="form.sideTheme === 'theme-light'" class="setting-drawer-block-checbox-selectIcon"
                       style="display: block">
                    <i aria-label="图标: check" class="anticon anticon-check">
                      <svg viewBox="64 64 896 896" data-icon="check" width="1em" height="1em" :fill="theme"
                           aria-hidden="true" focusable="false" class="">
                        <path
                            d="M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 0 0-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"/>
                      </svg>
                    </i>
                  </div>
                  <span class="setting-drawer-block-checbox-text">浅色</span>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="15">
            <el-form-item label="主题颜色" prop="theme">
              <el-color-picker v-model="theme" :predefine="predefineColors" @change="themeChange"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="开启 TopNav" prop="topNav">
              <el-switch v-model="form.topNav" active-value="1" inactive-value="0"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="开启 Tags-Views" prop="tagsView">
              <el-switch v-model="form.tagsView" active-value="1" inactive-value="0"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="固定 Header" prop="fixedHeader">
              <el-switch v-model="form.fixedHeader" active-value="1" inactive-value="0"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="显示 Logo" prop="sidebarLogo">
              <el-switch v-model="form.sidebarLogo" active-value="1" inactive-value="0"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="20">
            <el-form-item label="主题描述">
              <el-input v-model="form.themeDescribe" placeholder="请输入主题描述" type="textarea" maxlength="255"/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm" :loading="saveLoading">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Theme">
import {add, del, find, page, update} from "@/api/system/theme";
import {ref} from "vue";
import useSettingsStore from "@/store/modules/settings";
import useUserStore from "@/store/modules/user";

const validateThemeCode = (rule, value, callback) => {
  if (value === '') {
    callback(new Error("应用主键不能为空"));
  } else if (form.value.id === '') {
    //新增時校验钉钉应用id唯一性
    find({themeCode: form.value.themeCode}).then((response) => {
      if (response.success && response.data) {
        callback("配置编码已存在，请重新输入");
      } else {
        callback()
      }
    });
  } else {
    callback()
  }
}
const validateSideTheme = (rule, value, callback) => {
  if (form.value.sideTheme === '') {
    callback(new Error("请选择一种主题风格"));
  } else {
    callback()
  }
}
const validateTheme = (rule, value, callback) => {
  if (form.value.theme === '') {
    callback(new Error("请选择一种主题颜色"));
  } else {
    callback()
  }
}

const {proxy} = getCurrentInstance();
const settingsStore = useSettingsStore();
const userStore = useUserStore();
const userType = userStore.userInfo.customParam.userType;
const predefineColors = ref(["#409EFF", "#ff4500", "#ff8c00", "#ffd700", "#90ee90", "#00ced1", "#1e90ff", "#c71585"]);
// 遮罩层
const loading = ref(true);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 参数表格数据
const dataList = ref([]);
// 弹出层标题
const title = ref("");
// 是否显示弹出层
const open = ref(false);
// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  themeName: undefined,
});
// 表单参数
const form = ref({});
// 表单校验
const rules = {
  themeCode: [
    {required: true, validator: validateThemeCode, trigger: "blur"},
  ],
  themeName: [
    {required: true, message: "主题名称不能为空", trigger: "blur"},
  ],
  sideTheme: [
    {required: true, validator: validateSideTheme, trigger: "blur"},
  ],
  theme: [
    {required: true, validator: validateTheme, trigger: "blur"},
  ],
  topNav: [
    {required: true, message: "请选择是否开启TopNav", trigger: "blur"},
  ],
  tagsView: [
    {required: true, message: "请选择是否开启Tags-Views", trigger: "blur"},
  ],
  fixedHeader: [
    {required: true, message: "请选择是否开启固定Header", trigger: "blur"},
  ],
  sidebarLogo: [
    {required: true, message: "请选择是否显示Logo", trigger: "blur"},
  ]
}

const reloadId = ref(undefined);
const reloadType = ref(undefined);
const saveLoading = ref(false);
const theme = settingsStore.theme;

/** 查询参数列表 */
function getList() {
  loading.value = true;
  page(queryParams.value).then(
      (response) => {
        dataList.value = response.data.records;
        total.value = response.data.total;
        loading.value = false;
      }
  );
}

// 取消按钮
function cancel() {
  open.value = false;
  reloadId.value = undefined;
  reloadType.value = undefined;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: '',
    themeCode: '',
    themeName: '',
    theme: '',
    sideTheme: 'theme-dark',
    topNav: '0',
    tagsView: '1',
    fixedHeader: '0',
    sidebarLogo: '1',
    themeDescribe: '',
  };
  proxy.resetForm("formRef");
  proxy.resetForm("queryForm");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryForm");
  handleQuery();
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加主题";
  reloadType.value = "add";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const id = row.id;
  reloadId.value = id;
  reloadType.value = "edit";
  find({id: id}).then((response) => {
    if (response.data) {
      form.value = response.data;
      open.value = true;
      title.value = "修改主题";
    } else {
      proxy.$modal.msgError("数据异常！");
    }
  });
}

// 启用
function handleEnabled(row) {
  const tipText = row.enabledStatus === '1' ? '启用' : '停用'
  update(row).then((response) => {
    if (response.data) {
      proxy.$modal.msgSuccess(`${tipText}成功，请刷新浏览器查看效果！`);
      getList();
    } else {
      proxy.$modal.msgError(response.message);
    }
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      saveLoading.value = true;
      if (form.value.id !== '') {
        update(form.value).then((response) => {
          if (!response.success) {
            proxy.$modal.msgError(response.message);
            saveLoading.value = false;
          } else {
            proxy.$modal.msgSuccess("修改成功");
            open.value = false;
            saveLoading.value = false;
            getList();
          }
        });
      } else {
        add(form.value).then((response) => {
          if (!response.success) {
            proxy.$modal.msgError(response.message);
            saveLoading.value = false;
          } else {
            proxy.$modal.msgSuccess("新增成功");
            open.value = false;
            saveLoading.value = false;
            getList();
          }
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const id = row.id;
  reloadId.value = id;
  reloadType.value = "remove";
  proxy.$modal.confirm('是否确认删除主题名称为"' + row.themeName + '"的数据项?').then(function () {
    del(id).then((response) => {
      if (response.success) {
        proxy.$modal.msgSuccess('删除成功');
        getList();
      } else {
        proxy.$modal.notify(response.message);
        reloadType.value = undefined;
      }
    });
  }).catch(() => {
    reloadId.value = undefined;
  });
}

function themeChange(val) {
  form.value.theme = val;
}

function handleTheme(val) {
  form.value.sideTheme = val;
}

getList();
</script>

<style lang="scss" scoped>
.setting-drawer-block-checbox {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-top: 10px;
  margin-bottom: 20px;

.setting-drawer-block-checbox-item {
  position: relative;
  margin-right: 16px;
  border-radius: 2px;
  cursor: pointer;

img {
  width: 48px;
  height: 48px;
}

.setting-drawer-block-checbox-selectIcon {
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  padding-top: 15px;
  padding-left: 24px;
  color: #1890ff;
  font-weight: 700;
  font-size: 14px;
}

.setting-drawer-block-checbox-text {
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  padding-top: 40px;
  padding-left: 10px;
}

}
}
</style>
