<!-- 合并账号 -->
<template>
  <div
    class="dialog-box"
    :class="popupType === 'view' ? 'dialog-box-view' : 'dialog-box-edit'"
  >
    <el-form
      :model="formData"
      label-width="90px"
    >
      <el-row>
        <div class="common-box-one">
          <div class="common-header">
            <div class="common-header-line"></div>
            <div class="common-header-text">员工信息</div>
          </div>
        </div>

        <el-col :span="6">
          <el-form-item label="员工姓名" prop="staffName">
            <div>
              {{ formData.staffName || "" }}
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="员工账户" prop="loginName">
            <div>
              {{ formData.loginName || "" }}
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="员工部门" prop="department">
            <div>
              {{ formData.department || "" }}
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="人员类型" prop="staffTypeName">
            <div>
              {{ formData.staffTypeName || "" }}
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <div class="common-box-one">
          <div class="common-header">
            <div class="common-header-line"></div>
            <div class="common-header-text">账户合并</div>
          </div>
        </div>

        <Splitpanes class="default-theme">
          <Pane :size="30" :min-size="10">
            <div class="flex">

          
            <el-card class="dep-card" style="height: 350px;flex:1">
              <el-scrollbar style="height: 100%">
                <div class="common-header">
                  <!-- <div class="common-header-line"></div> -->
                  <div class="common-header-text">当前账户信息</div>
                </div>
                <el-form ref="form" label-width="100px" style="margin-top: 15px">
                  <el-form-item label="账户名称" prop="accountName">
                    <div>
                      {{ formData.accountName || "" }}
                    </div>
                  </el-form-item>

                  <el-form-item label="账户编码" prop="accountCode">
                    <div>
                      {{ formData.accountCode || "" }}
                    </div>
                  </el-form-item>

                  <el-form-item label="剩余余额(元)" prop="balance">
                    <div>
                      {{ formData.balance || "0" }}
                    </div>
                  </el-form-item>
                </el-form>
              </el-scrollbar>
            </el-card>

            <el-icon class="my_right">
            <Back />
          </el-icon>
        </div> 
          </Pane>

          <Pane :size="70" :min-size="62">
            <el-card class="dep-card" style="height: 350px">
              <el-form
                 ref="formRef"
                :model="ruleData"
                :rules="rules"
                label-width="100px"
              >
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="账户选择" prop="accountManageId">
                      <el-select
                        style="width: 300px"
                        placeholder="请输入人员进行选择"
                        clearable
                        v-model="ruleData.accountManageId"
                        @change="changeUser"
                        filterable
                      >
                        <el-option
                          v-for="(item, index) in applyUserList"
                          :key="index"
                          :label="item.accountName"
                          :value="item.accountManageId"
                        >
                          <!-- <div>
                            {{
                              item.accountName +
                              "(" +
                              item.accountCode +
                              "/" +
                              item.loginName +
                              ")"
                            }}
                          </div> -->
                        </el-option>
                      </el-select>
                    </el-form-item>

                    <el-form-item label="账户名称" prop="userName">
                      <div>
                        {{ newformData.accountName || "-" }}
                      </div>
                    </el-form-item>

                    <el-form-item label="账户编码" prop="userName">
                      <div>
                        {{ newformData.accountCode || "-" }}
                      </div>
                    </el-form-item>

                    <el-form-item label="剩余余额(元)" prop="userName">
                      <div >
                        {{ newformData.balance || "-" }}
                      </div>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
              <!-- <public-table ref="publictable" :rowKey="tabelForm.tableKey" :tableData="userInfo"
                                :columns="tabelForm.columns" :configFlag="tabelForm.tableConfig">
                                <template #operation="{ scope }">

                                    <el-button size="mini" type="text" title="删除" icon="Delete"
                                        @click="handleDelete(scope.row)">
                                    </el-button>
                                </template>
                            </public-table> -->
            </el-card>
          </Pane>
        </Splitpanes>

        <div class="total-bottom">
          <span>合并后余额：{{totalBalance }}元</span>
        </div>
      </el-row>
    </el-form>
  </div>
</template>

<script setup>
import {
  ref,
  reactive,
  onMounted,
  watch,
  computed,
  getCurrentInstance,
} from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { screenIndex } from "@/api/paymentCenter/account-list/index";


const props = defineProps({
  popupType: {
    type: String,
    default: "",
  },

  rowData: {
    type: Object,
    default: () => {},
  },
});

// 字典数据
const { proxy } = getCurrentInstance();
const {} = proxy.useDict();
const emits = defineEmits(["closeBtn"]);
// 响应式数据
const formRef = ref(null);
let formData = ref({});
let newformData = ref({});
let ruleData = ref({});
const applyUserList = ref([]);
const rules = reactive({
  accountManageId: [
    { required: true, message: '请选择要合并的账户', trigger: 'change' }
  ]
});
const changeUser = () => {
  if (ruleData.value.accountManageId) {
    newformData.value = applyUserList.value.find(
      (item) => item.accountManageId === ruleData.value.accountManageId
    )

    console.log(newformData.value)
  }
};

const queryMergeAccountList = () => {
  screenIndex.queryMergeAccountList({
      accountManageId: formData.value.accountManageId,
      staffId:formData.value.staffId,
      tenantId: formData.value.tenantId,
    }).then((res) => {
      applyUserList.value = res.data;
    });
};


const submitForm = () => {
  formRef.value.validate((valid) => {
    if (valid) {
      const params = {
        oldAccountManageId: formData.value.accountManageId,
        oldAccountName: formData.value.accountName,
        oldAccountCode: formData.value.accountCode,
        oldBalance: Number(formData.value.balance) ,
        newAccountManageId: newformData.value.accountManageId,
        newAccountName: newformData.value.accountName,
        newAccountCode: newformData.value.accountCode,
        newBalance: Number(newformData.value.balance),
        newId:newformData.value.id,
        oldId:formData.value.id, 
      };

      screenIndex.listAccountMerge(params).then((res) => {
        if (res.code == "1") {
          ElMessage.success('操作成功');
          emits("closeBtn");
        }
      });
    }
  });
};

const cancel = () => {
  emit("cancelClose");
};

// 生命周期钩子
onMounted(() => {
  formData.value = props.rowData;
   queryMergeAccountList()
});

const totalBalance = computed(() => {
  const leftBalance = Number(formData.value.balance) || 0;
  const rightBalance = Number(newformData.value.balance) || 0;
  return leftBalance + rightBalance;
});

// 暴露方法
defineExpose({
  submitForm,
  cancel,
});
</script>

<style scoped>
.my_right {
  font-size: 35px;
  font-weight: 800;
  display: flex;
  align-items: center;
  min-height: 350px;
  color: #c20000;
  background: #fff;
}
.total-bottom {
  background: #c20000;
  height: 50px;
  line-height: 50px;
  border-radius: 30px;
  color: #fff;
  font-size: 16px;
  padding: 0px 30px;
  margin-top: 20px;
}
</style>