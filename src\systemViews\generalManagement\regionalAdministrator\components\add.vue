<template>
  <div class="dialog-box dialog-box-edit" style="height: 600px;">
    <el-row style="height: 100%">
      <Splitpanes class="default-theme">
        <Pane :size="30" :min-size="30">
          <el-card class="dep-card" style="height: 100%">
            <el-scrollbar style="height: 100%">
              <div style="font-size: 20px;color: #282828;">请选择区域:</div>
              <el-form ref="form" label-width="40px">
                <el-form-item>
                  <el-tree
                    style="margin: 20px 0px"
                    :check-strictly="true"
                    :data="areaOptions"
                    show-checkbox
                    default-expand-all
                    node-key="id"
                    ref="areaTree"
                    highlight-current
                    :props="treeProps"
                  />
                </el-form-item>
              </el-form>
            </el-scrollbar>
          </el-card>
        </Pane>

        <Pane :size="70" :min-size="70">
          <el-card class="dep-card" style="height: 100%">
            <el-form ref="ruleform" :model="formData" :rules="rules">
              <el-row>
                <el-col :span="24">
                  <el-form-item label="人员" prop="nameList">
            
                    <el-select
                      style="width: 300px"
                      collapse-tags
                      placeholder="请输入人员进行选择"
                      clearable
                      @change="changeUser"
                      filterable
                      remote
                      reserve-keyword
                      :remote-method="getUserList"
                    >
                      <el-option
                      v-for="(item, index) in applyUserList"
                :key="index"
                :label="item.staffName"
                :value="item.staffId"
                      >
                        <div>
                          {{
                    item.staffName +
                    "(" +
                    item.orgName +
                    "/" +
                    item.loginName +
                    ")"
                  }}
                        </div>
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              
              </el-row>
            </el-form>

            <el-table
              :key="tableKey1"
              class="limit-cell-table"
              :data="userInfo"
              border
            >
              <el-table-column
                type="index"
                label="序号"
                width="80"
                align="center"
                sortable
              />
              <template v-for="item in userColumns" :key="item.prop">
                <el-table-column
                  show-overflow-tooltip
                  :prop="item.prop"
                  align="center"
                  :label="item.label"
                  :min-width="item.minWidth"
                  :resizable="item.resizable"
                >
                  <template #default="scope">
                    <div v-if="item.prop === 'staffName'">
                      {{ scope.row.staffName }}
                    </div>
                    <div v-if="item.prop === 'loginName'" class="text-left">
                      {{ scope.row.loginName }}
                    </div>
                    <div v-if="item.prop === 'orgName'" class="text-left">
                      {{ scope.row.orgName }}
                    </div>
                  </template>
                </el-table-column>
              </template>

              <el-table-column label="操作" align="center">
                <template #default="scope">
                  <el-button
                    size="mini"
                    type="text"
                    icon="Delete"
                    @click="handleDelete(scope.row)"
                  />
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </Pane>
      </Splitpanes>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessageBox, ElMessage } from "element-plus";
import {
  getAllAreaTree,
  saveAreaAdmin,
  selectUserList,
} from "@/api/regionalAdministrator/index";
const emit = defineEmits(['dialogResult'])
const props = defineProps({
  closeBtn: {
    type: Function,
    default: () => {},
  },
  popupType: {
    type: String,
    default: "",
  },
  rowData: {
    type: Object,
    default: () => ({}),
  },
});

// 响应式数据
const applyUserList = ref([]);
const userInfo = ref([]);
const areaOptions = ref([]);
const areaTree = ref(null);
const tableKey1 = ref(1);

const formData = reactive({
  staffInfos: [],
  areaIds: [],
});

const treeProps = reactive({
  multiple: true,
  emitPath: false,
  value: "id",
});

const userColumns = reactive([
  {
    prop: "staffName",
    label: "人员姓名",
    show: true,
    sortable: true,
    minWidth: "80%",
  },
  {
    prop: "loginName",
    label: "人员账号",
    show: true,
    sortable: true,
    minWidth: "100%",
  },
  {
    prop: "orgName",
    label: "人员部门",
    show: true,
    sortable: true,
    minWidth: "150%",
  },
]);

// 方法
const handleDelete = (row) => {
  userInfo.value = userInfo.value.filter((item) => item.staffId !== row.staffId);
  formData.staffInfos = formData.staffInfos.filter((item) => item.staffId !== row.staffId);
  applyUserList.value.push(row);
};

const changeUser = (staffId) => {
  const selectedUser = applyUserList.value.find(
    (item) => item.staffId === staffId
  );
  if (selectedUser) {
    userInfo.value.push(selectedUser);
   
    userInfo.value.forEach(item=>{
      formData.staffInfos.push({
        staffId:item.staffId,
        orgId:item.orgId
    });
    })
    applyUserList.value = applyUserList.value.filter(
      (item) => item.staffId !== staffId
    );
  }
};

const getUserList = async (name) => {
  if (!name || name.trim().length < 2) {
    applyUserList.value = []; // 立即清空列表
    return;
  }
  try {
    const res = await selectUserList({
      staffName: name
    });
    applyUserList.value = res.data;
  } catch (error) {
    console.error("获取用户列表失败:", error);
  }
};

const getAreaTree = async () => {
  try {
    const res = await getAllAreaTree({ areaType: "office" });
    areaOptions.value = res.data;
  } catch (error) {
    console.error("获取区域树失败:", error);
  }
};

const saveBtn = async () => {
  formData.areaIds = areaTree.value.getCheckedKeys();

  if (formData.areaIds.length < 1) {
    ElMessage.warning("请选择区域");
    return;
  }

  if (formData.staffInfos.length < 1) {
    ElMessage.warning("请选择人员");
    return;
  }

  try {
    await saveAreaAdmin(formData);
    ElMessage.success("添加成功");
    emit('dialogResult')
  } catch (error) {
    console.error("保存失败:", error);
  }
};

const cancelBtn = () => {
  props.closeBtn();
};

// 生命周期
onMounted(() => {
  getAreaTree();
});
defineExpose({
  saveBtn
})
</script>

<style scoped lang="scss">


.limit-cell-table {

  :deep(.el-table__cell) {
    padding: 8px 0;
  }
}

.text-left {
  text-align: left;
}
::v-deep .el-tree-node__content{
  color: #282828;
  font-weight: normal;
}
</style>