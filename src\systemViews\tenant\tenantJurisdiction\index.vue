<template>
  <div class="app-container">
    <el-card shadow="never">
    <!--    查询-->
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">

      <el-form-item label="租户" v-if="userType === 'admin'">
        <el-select
            v-model="queryParams.manageTenantId"
            placeholder="请选择租户"
            remote
            style="width: 240px"
            :remote-method="initTenantList"
            :loading="getTenantLoading"
            @change="handleTenantChange"
        >
          <el-option
            v-for="item in tenantList"
            :key="item.tenantId"
            :label="item.tenantName"
            :value="item.tenantId"
        />

        </el-select>
      </el-form-item>

      <el-form-item label="租户名称" prop="tenantName">
        <el-input v-model="queryParams.tenantName" placeholder="请输入租户名称" clearable style="width: 180px"
                  @keyup.enter="handleQuery">
        </el-input>
      </el-form-item>
      <el-form-item label="租户登录名" prop="tenantLoginName">
        <el-input v-model="queryParams.tenantLoginName" placeholder="请输入租户登录名" clearable style="width: 180px"
                  @keyup.enter="handleQuery">
        </el-input>
      </el-form-item>
      <el-form-item label="租户管理员" prop="displayName">
        <el-input v-model="queryParams.displayName" placeholder="请输入租户管理员" clearable style="width: 180px"
                  @keyup.enter="handleQuery">
        </el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <!--  刷新按钮-->
    <el-row :gutter="10" class="mb8">
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <!--  展示-->
    <el-table v-loading="loading" :data="dataList">
      <el-table-column prop="tenantName" label="租户名称" :show-overflow-tooltip="true" width="150" align="center"></el-table-column>
      <el-table-column prop="tenantLoginName" label="租户登录名" :show-overflow-tooltip="true"
                       align="center"></el-table-column>
      <el-table-column prop="displayName" label="租户管理员" :show-overflow-tooltip="true"
                       align="center"></el-table-column>
      <el-table-column prop="tenantDomain" label="租户域名" :show-overflow-tooltip="true"
                       align="center"></el-table-column>
      <el-table-column prop="tenantHtmlPath" label="租户前台位置" :show-overflow-tooltip="true"
                       align="center"></el-table-column>
      <el-table-column prop="businessId" label="业务ID" :show-overflow-tooltip="true"
                       align="center"></el-table-column>
      <el-table-column prop="maxStaff" label="最大用户数" :show-overflow-tooltip="true"
                       align="center"></el-table-column>
      <el-table-column prop="effectiveDate" label="有效截止时间" :show-overflow-tooltip="true"
                       align="center"></el-table-column>
      <el-table-column prop="tenantStatus" label="租户状态"   align="center">
        <template #default="scope">
          <dict-tag :options="tenant_status" :value="scope.row.tenantStatus"/>
        </template>
      </el-table-column>
    </el-table>

    <!--    分页器-->
    <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
    />
    </el-card>
  </div>
</template>

<script setup name="TenantJurisdiction">
import { page } from "@/api/tenant/tenantJurisdiction";
import useUserStore from "@/store/modules/user";
import {ref} from "vue";
import {getTenants} from "@/api/tenant/tenant";
const {proxy} = getCurrentInstance();

const userStore = useUserStore();
const customParam = userStore.userInfo.customParam
const userType = customParam.userType

const tenantList = ref([]);
const getTenantLoading = ref(false);
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  manageTenantId:customParam.tenantId
})
//获得租户列表
function initTenantList(tenantName) {
  getTenantLoading.value = true;
  let query = {};
  if (tenantName !== undefined && tenantName !== "") {
    query.tenantName = tenantName;
    query.tenantId = undefined;
  } else {
    query.tenantId = queryParams.value.manageTenantId;
  }
  getTenants(query)
      .then((response) => {
        tenantList.value = response.data;
      })
      .finally(() => {
        getTenantLoading.value = false;
      });
}
// 下拉框切换租户回调
function handleTenantChange() {

  handleQuery();
}

//---------------------查询列表
const {tenant_status} = proxy.useDict('tenant_status')

// <editor-fold desc="查询列表">
const total = ref(0)

const showSearch = ref(true);
const dataList = ref([])
const loading = ref(true)
/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 查询列表 */
function getList() {
  loading.value=true;
  page({
    ...queryParams.value,
  }).then(response => {
    if (response.success) {
      // if (response.data.records!=null &&response.data.records.length!=0 && response.data.records[0]!=null){
      //   total.value = response.data.total
      //   dataList.value = response.data.records
      // }
      total.value = response.data.total
      dataList.value = response.data.records

    }
    loading.value = false;
  }).catch(() => {
    loading.value = false;
  })
}

getList()
initTenantList();
// </editor-fold>
</script>

<style scoped>

</style>
