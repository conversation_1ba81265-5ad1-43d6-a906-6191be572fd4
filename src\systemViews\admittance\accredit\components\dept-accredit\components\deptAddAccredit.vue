<!--新增权限 -->

<script setup>
import { ref, reactive, onMounted, getCurrentInstance, defineProps, defineEmits } from 'vue'
import { ElMessage } from 'element-plus'
import { screenIndex } from "@/api/admittance/accredit";

const props = defineProps({
    popupType: {
        type: String,
        default: ''
    },
    rowData: {
        type: Object,
        default: () => ({})
    }
})
const emit = defineEmits(['submitClose'])
const ruleform = ref()
const deptTree = ref()
const areaTreeRef = ref()

const form = reactive({
    isUnit: 'Y',
})

const formData = reactive({
    deptIds: [],
    areaIds: [],
    expirationTime: ''
})

const deptOptions = ref([])
const areaOptions = ref([])

const treeProps = { multiple: true, emitPath: false, value: 'id' }

const rules = {
    expirationTime: [
        { required: true, message: '请选择过期日期', trigger: 'change' }
    ]
}

const timepickerOptions = {
    disabledDate(time) {
        const today = new Date()
        today.setHours(0, 0, 0, 0)
        return time.getTime() < today.getTime()
    }
}

const { proxy } = getCurrentInstance()

const getDeptTree = async () => {
    try {
        const res = await unitTreeSelect(form);
        deptOptions.value = res.data;
    } catch (error) {
        console.error(error);
    }
}
const getAreaTree = async () => {
    try {
        const res = await areaTree();
        areaOptions.value = res.data;
    } catch (error) {
        console.error(error);
    }
}
const assignmentDept = async () => {
    try {
        await selectDeptNames(formData.deptIds);
        // deptNames.value = response.data
    } catch (error) {
        console.error(error);
    }
}
const assignmentArea = async () => {
    try {
        await selectAreaNames(formData.areaIds);
        // areaNames.value = res.data
    } catch (error) {
        console.error(error);
    }
}
const saveBtn = () => {
    try {
        ruleform.value.validate((valid) => {
            if (!valid) return;
            formData.areaIds = areaTreeRef.value.getCheckedKeys();
            formData.deptIds = deptTree.value.getCheckedKeys();
            if (formData.areaIds.length < 1) {
                ElMessage.warning('请选择区域');
                return;
            }
            if (formData.deptIds.length < 1) {
                ElMessage.warning('请选择部门');
                return;
            }
            screenIndex.addDeptAccredit(formData).then((res) => {
                if (res.success) {
                    ElMessage.success('授权成功');
                    emit('submitClose');
                }
            });
        });
    } catch (error) {
        console.error(error);
    }
}
const handleCheckChange = (data, checked, indeterminate) => {
    try {
        if (checked) {
            selectAllChildren(data, true)
        } else {
            selectAllChildren(data, false)
        }
    } catch (error) {
        console.error(error);
    }
}
const selectAllChildren = (node, checked) => {
    try {
        if (node.children && node.children.length > 0) {
            node.children.forEach(child => {
                deptTree.value.setChecked(child, checked)
                selectAllChildren(child, checked)
            })
        }
    } catch (error) {
        console.error(error);
    }
}

const submits = () => {
    if (props.popupType === 'add') {
        saveBtn();
    } else {
        // 其他类型可根据需要扩展
        // 例如：if (props.popupType === 'edit') { ... }
    }
}

defineExpose({
    saveBtn,
    submits
})

onMounted(() => {
    // getDeptTree()
    // getAreaTree()
})
</script>
<template>
    <div class="dialog-box dialog-box-edit">
        <el-form ref="ruleform" :model="formData" :rules="rules" :inline="true">
            <el-row>
                <el-col :span="12" style="margin-top: 10px">
                    <el-form-item label="过期日期" prop="expirationTime">
                        <el-date-picker v-model="formData.expirationTime" format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD 23:59:59" type="date"
                            :disabled-date="timepickerOptions.disabledDate" placeholder="默认长期有效" />
                    </el-form-item>
                </el-col>

            </el-row>
            <el-row style="height: 500px;">
                <Splitpanes class="default-theme">
                    <Pane :size="50" :min-size="50">
                        <el-card class="dep-card" style="height: 100%">
                            <el-form-item label="部门选择">
                                <el-tree style="margin: 20px auto " :check-strictly="true" :data="deptOptions"
                                    show-checkbox default-expand-all node-key="id" ref="deptTree" highlight-current
                                    @check-change="handleCheckChange" :props="treeProps" />
                            </el-form-item>
                        </el-card>
                    </Pane>

                    <Pane :size="50" :min-size="50">
                        <el-card class="dep-card" style="height: 100%">
                            <el-form-item label="区域选择">
                                <el-tree style="margin: 20px auto " :check-strictly="true" :data="areaOptions"
                                    show-checkbox default-expand-all node-key="id" ref="areaTreeRef" highlight-current
                                    :props="treeProps" />
                            </el-form-item>
                        </el-card>
                    </Pane>
                </Splitpanes>



            </el-row>
        </el-form>
    </div>
</template>
