

<template>
    <div>
        <TabContainers :tabList="tabList" :activeName="activeName"></TabContainers>
    </div> 

</template>

<script setup name="ParkingAccredit">
// tab切换
import TabContainers from '@/components/TabContainer/index';
import AllAccredit from '@/systemViews/vehicle-management/parkingAccredit/all-accredit/allAccredit.vue'
import TypeAccredit from'@/systemViews/vehicle-management/parkingAccredit/type-accredit/typeAccredit.vue'
import NumberAccredit from '@/systemViews/vehicle-management/parkingAccredit/number-accredit/numberAccredit.vue'

import { ref } from "vue";
// 定义 tab 列表
const tabList = ref([
  {
    label: '汇总查询',
    value: '01',
    component: AllAccredit
  },
  {
    label: '编组授权',
    value: '02',
    component: TypeAccredit
  },
  {
    label: '车牌授权',
    value: '03',
    component: NumberAccredit
  }
])
// 默认激活的 tab
const activeName = ref('01')
</script>

<style scoped lang="scss">
:deep(.el-tabs .el-tabs__content .content) {
    height: calc(100vh - 250px);
}
</style>