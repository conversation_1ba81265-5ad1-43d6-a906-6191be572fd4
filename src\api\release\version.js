import request from '@/utils/request'

// 查询公告列表
export function listVersion(data) {
    return request({
        url: "/cms/v1.0/newVersion/selectForPage",
        method: "post",
        data
    });
}
// 查询要绑定的列表
export function bindListVersion(data) {
    return request({
        url: "/cms/v1.0/newVersion/selectBindForPage",
        method: "post",
        data
    });
}
// 查询版本信息
export function selectVersionByVersionId(params) {
    return request({
        url: "/cms/v1.0/newVersion/selectVersionByVersionId",
        method: "post",
        params
    });
}


// 新增版本信息
export function addVersion(data) {
    return request({
        url: "/cms/v1.0/newVersion/insertVersion",
        method: "post",
        data: data
    })
}

// 新增版本信息
export function validCert(data) {
    return request({
        url: "/cms/v1.0/newVersion/validCert",
        method: 'post',
        headers:{
            ContentType: 'multipart/form-data',
        },
        data
    })
}

// 修改版本信息
export function updateVersion(data) {
    return request({
        url: "/cms/v1.0/newVersion/updateVersion",
        method: "post",
        data: data
    })
}

// 删除版本信息
export function delVersion(data) {
    return request({
        url: "/cms/v1.0/newVersion/delVersion",
        method: "post",
        data: data
    })
}

// 绑定其他版本信息
export function bindVersion(data) {
    return request({
        url: "/cms/v1.0/newVersion/bindVersion",
        method: "post",
        data: data
    })
}

