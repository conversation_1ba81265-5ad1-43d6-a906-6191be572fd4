{"name": "unifast-vue3", "version": "1.0.0", "description": "", "scripts": {"dev": "vite", "build:prod": "vite build", "build:stage": "vite build --mode staging", "preview": "vite preview"}, "dependencies": {"@antv/g2": "^5.1.18", "@element-plus/icons-vue": "^2.3.1", "@kangc/v-md-editor": "^2.3.18", "@tinymce/tinymce-vue": "^5.1.1", "@vueup/vue-quill": "1.2.0", "@vueuse/core": "10.7.2", "amfe-flexible": "^2.2.1", "axios": "1.6.7", "cos-js-sdk-v5": "^1.4.6", "dayjs": "^1.11.13", "default-passive-events": "^2.0.0", "echarts": "^5.5.0", "element-plus": "2.5.3", "file-saver": "2.0.5", "highlight.js": "^11.9.0", "mavon-editor": "^2.10.4", "moment": "^2.30.1", "nprogress": "0.2.0", "pinia": "2.1.7", "pinyin": "^4.0.0-alpha.2", "pinyin-pro": "^3.26.0", "postcss-px2rem": "^0.3.0", "prismjs": "^1.29.0", "px2rem-loader": "^0.1.9", "quill": "^2.0.3", "skywalking-client-js": "^0.11.0", "sm-crypto": "^0.3.13", "sm-crypto-v2": "^1.9.0", "splitpanes": "^3.1.5", "swiper": "^5.3.6", "tinymce": "^5.10.9", "uuid": "^9.0.1", "v-scale-screen": "^2.2.0", "vant": "^3.4.4", "vditor": "^3.10.2", "vue": "3.4.15", "vue-router": "4.2.5", "vue3-grid-layout": "^1.0.0", "vuedraggable": "^4.1.0"}, "devDependencies": {"@vitejs/plugin-vue": "5.0.3", "less": "^4.2.0", "postcss-pxtorem": "^6.1.0", "sass": "1.70.0", "unplugin-auto-import": "0.17.5", "vite": "5.0.12", "vite-plugin-compression": "0.5.1", "vite-plugin-svg-icons": "2.0.1", "vite-plugin-vue-setup-extend": "0.4.0"}}