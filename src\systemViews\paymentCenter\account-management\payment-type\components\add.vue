<!-- 新增支付类型 -->

<template>
  <div class="dialog-box" :class="popupType === 'view' ? 'dialog-box-view' : 'dialog-box-edit'">
    <el-form ref="formRef" :model="formData" label-width="90px" :rules="popupType !== 'view' ? rules : ''">
      <el-row>
        <el-col :span="24">
          <el-form-item label="上级名称" prop="superiorType">
            <!-- 添加或编辑时显示输入框 -->
            <el-select
              v-if="popupType!= 'view'"
              filterable
              :filter-method="dataFilter"
              class="main-select-tree"
              ref="selectTreeRef"
              clearable
              v-model="formData.parentName"
              placeholder="请选择上级名称"
            >
              <el-option
                :label="formData.parentName"
                :value="formData.parentName"
                style="display: none"
              />
              <el-tree
                :check-strictly="true"
                :data="areaTreeData"
                :filter-node-method="filterNode"
                @node-click="handleNodeClick"
                default-expand-all
                node-key="id"
                ref="areaTreeRef"
                highlight-current
                :props="treeProps"
              />
            </el-select>
            <!-- 查看时显示文本 -->

            <div v-else>
              {{ formData.parentName || "" }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="类型名称" prop="typeName">
            <el-input v-model="formData.typeName" :disabled="popupType === 'view'" placeholder="请输入类型名称" clearable
              v-if="popupType !== 'view'" />

            <div v-else>
              {{ formData.typeName || "" }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="类型编码" prop="typeCode">
            <el-input v-model="formData.typeCode" :disabled="popupType === 'view'" placeholder="请输入类型编码" clearable
              v-if="popupType !== 'view'" />

            <div v-else>
              {{ formData.typeCode || "" }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="结算类型" prop="settleType">
            <el-select
                    v-if="popupType !== 'view'"
                    v-model="formData.settleType"
                    placeholder="请选择结算类型"
                    collapse-tags
                    filterable
                    clearable
                  >
                    <el-option
                      v-for="(item, index) in settle_type"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                  <div v-else>
                    {{ formatCommonDict(formData.settleType, settle_type) }}
                  </div>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="排序" prop="typeSort">
            <!-- 添加或编辑时显示选择器 -->
            <NumberInput v-if="popupType !== 'view'" v-model="formData.typeSort" customPlaceholder="请输入排序"
              input-type="integer" />

            <!-- 查看时显示文本 -->

            <div v-else>
              {{ formData.typeSort }}
            </div>
          </el-form-item>
        </el-col>




      </el-row>


    </el-form>


  </div>
</template>



<script setup>
import { ref, reactive, getCurrentInstance, defineProps } from "vue";

import { ElMessage } from "element-plus";

import {
  screenIndex
} from "@/api/paymentCenter/account-management/payment-type/index";

// 定义组件 props
const props = defineProps({
  closeBtn: {
    type: Function,

    default: null,
  },

  popupType: {
    type: String,
    default: "",
  },

  rowData: {
    type: Object,
    default: () => ({}),
  },
});

// 字典
const { proxy } = getCurrentInstance();
const {
  settle_type,
} = proxy.useDict(
  "settle_type",
);
//树
const areaTreeData = ref([]);
const areaTreeRef = ref(null);
const selectTreeRef = ref(null);
const treeProps = {
  children: "children",
  label: "label",
  value: "id",
};
// 定义 emits
const emit = defineEmits(["closeBtn"]);
// 定义状态
let formData = ref({
});

// 定义校验规则

const rules = reactive({
  typeName: [
    { required: true, message: '请输入类型名称', trigger: ['blur', 'change'] }
  ],
  typeCode: [
    { required: true, message: '请输入类型编码', trigger: ['blur', 'change'] },
    {
      pattern: /^[\x21-\x7E]+$/, // 允许所有可打印的 ASCII 字符（不包含空格）
      message: "类型编码只能包含字母、数字和特殊符号",
      trigger: ["blur", "change"]
    }
  ],
  settleType: [
    { required: true, message: '请选择结算类型', trigger: ['blur', 'change'] }
  ],
  typeSort: [
    { required: true, message: '请输入支付顺序', trigger: ['blur', 'change'] }
  ],
})

// 表单引用

const formRef = ref(null);

const formatCommonDict = (value, dict) => {
  if (!value) return "";
  // 统一处理字符串，分割后映射标签
  const values = Array.isArray(value) ? value : value.split(",");
  const labels = values.map((v) => {
    const item = dict.find((item) => item.value === v);
    return item ? item.label : v;
  });
  return labels.join(", ");
};

// 定义方法


const initData = async () => {
  if (props.popupType === "add") {
  } else {
    formData.value = JSON.parse(JSON.stringify(props.rowData))

  }

};
const payTypeTree = () => {
  screenIndex.payTypeTree({ }).then((res) => {
    areaTreeData.value = res.data;
  });
};

const dataFilter = (val) => {
  if (val) {
    areaTreeRef.value.filter(val);
  }
};

const filterNode = (value, data) => {
  if (!value) return true;
  return data.label.indexOf(value) !== -1;
};

const handleNodeClick = (data) => {
  formData.value.parentId   = data.id;
  formData.value.parentName   = data.label;
  selectTreeRef.value.blur();
};

const saveForm = async () => {
  try {
    await formRef.value.validate();

    if (props.popupType === "edit") {
      const res = await screenIndex.updatePayType(formData.value);

      if (res.code == "1") {
        ElMessage.success("修改成功");

        emit("closeBtn");
      }
    } else if (props.popupType === "add") {
      const res = await screenIndex.savePayType(formData.value);

      if (res.code == "1") {
        ElMessage.success("新增成功");

        emit("closeBtn");
      }
    }
  } catch (error) {
    console.error("表单校验失败:", error);
  }
};



// 初始化数据

onMounted(() => {
  initData();
  payTypeTree()
});

defineExpose({
  saveForm,
});
</script>



<style scoped lang="scss"></style>