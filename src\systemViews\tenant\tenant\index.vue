<template>
  <div class="app-container">
    <el-card shadow="never">
    <!--    查询-->
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
      <el-form-item label="租户名称" prop="tenantName">
        <el-input v-model="queryParams.tenantName" placeholder="请输入租户名称" clearable style="width: 180px"
                  @keyup.enter="handleQuery"/>
      </el-form-item>

      <el-form-item label="租户登录名" prop="tenantLoginName">
        <el-input v-model="queryParams.tenantLoginName" placeholder="请输入租户登录名" clearable style="width: 180px"
                  @keyup.enter="handleQuery">
        </el-input>
      </el-form-item>

      <el-form-item label="租户管理员" prop="displayName">
        <el-input v-model="queryParams.displayName" placeholder="请输入租户管理员" clearable style="width: 180px"
                  @keyup.enter="handleQuery">
        </el-input>
      </el-form-item>
      <el-form-item label="有效日期" prop="effectiveDate">

        <el-date-picker
            v-model="dateRange"
            type="daterange"
            style="width: 240px"
            value-format="YYYY-MM-DD"
            unlink-panels
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
        />
      </el-form-item>
      <el-form-item label="租户状态" prop="tenantStatus">
        <el-select
            v-model="queryParams.tenantStatus"
            placeholder="租户状态"
            clearable
            style="width: 180px;"
        >
          <el-option
              v-for="dict in tenant_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <!--  新增/刷新按钮-->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd"
                   v-hasPermi="['sys:base:menu:add']">新增
        </el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <!--  展示-->
    <el-table v-loading="loading" :data="dataList">
      <el-table-column prop="tenantName" label="租户名称" :show-overflow-tooltip="true" width="150" align="center"></el-table-column>
      <el-table-column prop="tenantLoginName" label="租户登录名" :show-overflow-tooltip="true"
                       align="center"></el-table-column>
      <el-table-column prop="displayName" label="租户管理员" :show-overflow-tooltip="true"
                       align="center"></el-table-column>
      <el-table-column prop="tenantDomain" label="租户域名" :show-overflow-tooltip="true"
                       align="center"></el-table-column>
      <el-table-column prop="tenantHtmlPath" label="租户前台位置" :show-overflow-tooltip="true"
                       align="center"></el-table-column>
      <el-table-column prop="businessId" label="业务ID" :show-overflow-tooltip="true"
                       align="center"></el-table-column>
      <el-table-column prop="maxStaff" label="最大用户数" :show-overflow-tooltip="true"
                       align="center"></el-table-column>
      <el-table-column prop="effectiveDate" label="有效截止时间" :show-overflow-tooltip="true"
                       align="center"></el-table-column>
      <el-table-column prop="tenantStatus" label="租户状态"  width="180" align="center">
        <template #default="scope">
          <dict-tag :options="tenant_status" :value="scope.row.tenantStatus"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="350">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                     :loading="reloadId === scope.row.tenantId && reloadType === 'edit'"
                     :disabled="scope.row.tenantLoginName === 'system'"
                     v-hasPermi="['sys:tenant:tenant-info:edit']">修改
          </el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
                     :loading="reloadId === scope.row.tenantId && reloadType === 'remove'"
                     :disabled="scope.row.tenantLoginName === 'system'"
                     v-hasPermi="['sys:tenant:tenant-info:remove']">删除
          </el-button>
          <el-button link type="primary" icon="CircleCheck" @click="openAddRole(scope.row)"
                     v-hasPermi="['sys:tenant:tenant-info:empower']">角色赋权
          </el-button>
          <el-button link type="primary" icon="CircleCheck" @click="openJurisdiction(scope.row)"
                     :disabled="userType === 'user'"
                     v-hasPermi="['sys:tenant:tenant-info:maintain']">管辖维护
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!--    分页器-->
    <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
    />
    </el-card>
    <!-- 添加或修改参数配置对话框 -->
    <el-dialog
        :title="title"
        v-model="open"
        width="1000px"
        append-to-body
        @close="cancel"
        :close-on-press-escape="false"
    >
      <el-form ref="formRef" :model="form" :rules="rules" label-width="150px">
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="租户名称" prop="tenantName">
              <el-input
                  v-model="form.tenantName"
                  placeholder="请输入租户名称"
                  maxlength="64"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="租户登录名" prop="tenantLoginName">
              <el-input
                  v-model="form.tenantLoginName"
                  placeholder="请输入租户登录名"
                  maxlength="64"
                  :disabled="reloadType === 'edit'"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="租户管理员登录名" prop="displayName">
              <el-input
                  v-model="form.displayName"
                  placeholder="请输入登录名称"
                  maxlength="64"
                  :disabled="reloadType === 'edit'"
              />
            </el-form-item>
          </el-col>
          <el-col v-if="reloadType === 'add'" :span="12">
            <el-form-item label="租户管理员姓名" prop="staffName">
              <el-input
                  v-model="form.staffName"
                  placeholder="请输入租户管理员姓名"
                  maxlength="64"
              />
            </el-form-item>
          </el-col>
          <el-col v-if="reloadType === 'add'" :span="12">
            <el-form-item label="租户管理员手机号码" prop="cellphone">
              <el-input
                  v-model="form.cellphone"
                  placeholder="请输入租户管理员手机号码"
                  maxlength="64"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="域名" prop="tenantDomain">
              <el-input
                  v-model="form.tenantDomain"
                  placeholder="请输入域名"
                  maxlength="64"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="前台位置" prop="tenantHtmlPath">
              <el-input
                  v-model="form.tenantHtmlPath"
                  placeholder="请输入前台位置"
                  maxlength="11"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="业务ID" prop="businessId">
              <el-input
                  v-model="form.businessId"
                  placeholder="请输入业务ID"
                  maxlength="50"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="最大用户数" prop="maxStaff">
              <el-input-number
                  v-model="form.maxStaff"
                  controls-position="right"
                  :min="0"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="有效截止时间" prop="effectiveDate">
              <el-date-picker
                  v-model="form.effectiveDate"
                  style="width: 325px"
                  type="datetime"
                  value-format="YYYY-MM-DD HH:mm:ss"
              ></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <div v-if="reloadType === 'add'">
          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="组织名称" prop="orgName">
                <el-input
                    v-model="form.orgName"
                    placeholder="请输入组织名称"
                    maxlength="50"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="组织全称" prop="fullName">
                <el-input
                    v-model="form.fullName"
                    placeholder="请输入组织全称"
                    maxlength="50"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="组织编码" prop="code">
                <el-input
                    v-model="form.code"
                    placeholder="请输入组织编码"
                    maxlength="50"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="组织展示顺序" prop="orgSort">
                <el-input-number
                    v-model="form.orgSort"
                    controls-position="right"
                    :min="0"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm" :loading="saveLoading">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>

    </el-dialog>
    <!-- 角色赋权-->
    <transfer-table
        ref="transferTableRef"
        modelName="角色赋权"
        @loadData="loadData"
        :listRole="listRole"
        :listProp="{
        key: 'roleName',
        name: '角色名称',
      }"
        :selectedProp="{
        key: 'roleName',
        name: '角色名称',
      }"
        @add="addTenantRole"
        @remove="delRoleT"
    />
    <!--  管辖管理-->
    <!-- 管辖 -->
    <el-dialog
        title="管辖维护"
        v-model="jurisdictionOpen"
        width="1300px"
        height="900px"
        append-to-body
    >


      <el-card class="dep-card" shadow="never">
            <el-form
                :model="queryJurisdictionParams"
                ref="queryJurisdictionForm"
                :inline="true"
                label-width="68px"
            >
              <el-form-item label="租户名称" prop="tenantName">
                <el-input
                    v-model="queryJurisdictionParams.tenantName"
                    placeholder="请输入租户名称"
                    clearable
                    style="width: 240px"
                    @keyup.enter="handleTenantQuery"
                />
              </el-form-item>
              <el-form-item>
                <el-button
                    type="primary"
                    icon="Search"
                    @click="handleTenantQuery"
                >搜索
                </el-button>
                <el-button
                    icon="Refresh"
                    @click="resetTenantQuery"
                >重置
                </el-button
                >
              </el-form-item>
            </el-form>

            <el-table
                ref="multipleJurisdictionTable"
                v-loading="jurisdictionLoading"
                :data="tenantDataList"
                :row-key="rowKey"
                @selection-change="handleTenantSelectionChange"
            >
              <el-table-column
                  type="selection"
                  :reserve-selection="true"
                  align="center"
                  width="100"
              />
              <el-table-column
                  label="租户名称"
                  align="left"
                  prop="tenantName"
                  :show-overflow-tooltip="true"
              />
              <el-table-column
                  label="租户登录名"
                  align="left"
                  prop="tenantLoginName"
                  :show-overflow-tooltip="true"
              />
              <el-table-column
                  label="租户管理员"
                  align="left"
                  prop="displayName"
                  :show-overflow-tooltip="true"
              />
              <el-table-column
                  label="租户域名"
                  align="left"
                  prop="tenantDomain"
                  :show-overflow-tooltip="true"
              />
              <el-table-column
                  label="租户前台位置"
                  align="left"
                  prop="tenantHtmlPath"
                  :show-overflow-tooltip="true"
              />
              <el-table-column
                  label="最大用户数"
                  align="left"
                  prop="maxStaff"
                  :show-overflow-tooltip="true"
              />
              <el-table-column
                  label="有效截止时间"
                  align="left"
                  prop="effectiveDate"
                  :show-overflow-tooltip="true"
              />
              <el-table-column
                  label="租户状态"
                  align="left"
                  prop="tenantStatus"

              >
                <template #default="scope">
                  <dict-tag :options="tenant_status" :value="scope.row.tenantStatus"/>
                </template>
              </el-table-column>
            </el-table>
            <pagination
                v-show="tenantTotal > 0"
                :total="tenantTotal"
                v-model:page="queryJurisdictionParams.pageNum"
                v-model:limit="queryJurisdictionParams.pageSize"
                @pagination="getTenantList"
            />
          </el-card>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="jurisdictionSubmitForm" :loading="jurisdictionSubmitLoading">确 定</el-button>
          <el-button @click="jurisdictionCancel">取 消</el-button>
        </div>
      </template>

    </el-dialog>
  </div>
</template>

<script setup name="Tenant">
import {add, del, getById, getHadRolesAndNoRoles, page, update, updateTenantRole, delTenantRole,} from "@/api/tenant/tenant";
import {findRoleListByScope} from "@/api/system/role";
import useUserStore from "@/store/modules/user";
import {addDateRange} from "@/utils/common";
import {ElMessage, ElMessageBox} from "element-plus";
import TransferTable from "@/components/TransferTable"

//角色赋权
// <editor-fold desc="角色赋权">
import {getDictInfo} from "@/api/system/dict/data";

const {proxy} = getCurrentInstance();
const userStore = useUserStore();
const {tenant_status, scope} = proxy.useDict('tenant_status', "scope")

const customParam = userStore.userInfo.customParam
//---------------------查询列表
// <editor-fold desc="查询列表">
const total = ref(0)
const dateRange = ref([])
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  tenantName: undefined,
  tenantLoginName: undefined,
  displayName: undefined,
  effectiveDate: undefined,
  tenantStatus: undefined,
})
const showSearch = ref(true);
const dataList = ref([])
const loading = ref(true)

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  queryParams.value.tenantStatus = undefined;
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 查询租户列表 */
function getList() {
  page({
    ...addDateRange({}, dateRange.value).params,
    ...queryParams.value,
  }).then(response => {
    if (response.success) {
      total.value = response.data.total
      dataList.value = response.data.records
    }
    loading.value = false;
  }).catch(() => {
    loading.value = false;
  })
}

getList()

// </editor-fold>
//---------------------新增修改相关
// 遮罩层
// <editor-fold desc="新增修改相关">
const title = ref("");
const formRef = ref();
const form = ref({});
const rules = ref({
  tenantName: [
    {required: true, message: "租户名称不能为空", trigger: "blur"},
  ],
  tenantLoginName: [
    {required: true, message: "租户登录名不能为空", trigger: "blur"},
  ],
  staffName: [
    {
      required: true,
      message: "租户管理员姓名不能为空",
      trigger: "blur",
    },
  ],
  maxStaff: [
    {required: true, message: "最大用户数不能为空", trigger: "blur"},
  ],
  effectiveDate: [
    {required: true, message: "有效截止时间不能为空", trigger: "blur"},
  ],
  displayName: [
    {
      required: true,
      message: "租户管理员登录名不能为空",
      trigger: "blur",
    },
  ],
  orgName: [
    {required: true, message: "组织名称不能为空", trigger: "blur"},
  ],
  kind: [{required: true, message: "请选择组织类型", trigger: "blur"}],
  code: [
    {required: true, message: "组织编码不能为空", trigger: "blur"},
  ],
  cellphone: [
    {
      pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
      message: "请输入正确的手机号码",
      trigger: "blur",
    },
  ]
})
const saveLoading = ref(false)
const open = ref(false)
const reloadId = ref()
const reloadType = ref()
const userType = customParam.userType

function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加租户"
  reloadType.value = "add"
}

function handleUpdate(row) {
  reset();
  let tenantId = row.tenantId;
  reloadId.value = tenantId;
  reloadType.value = "edit"
  getById(tenantId).then((response) => {
    if (response.data) {
      form.value = response.data;
      open.value = true;
      title.value = "修改租户"
    } else {
      ElMessage.error("数据异常")
    }
  })
}

// 表单重置
function reset() {
  form.value = {
    tenantName: '',
    tenantLoginName: undefined,
    displayName: undefined,
    tenantAdminId: undefined,
    tenantDomain: undefined,
    tenantHtmlPath: undefined,
    businessId: undefined,
    maxStaff: undefined,
    effectiveDate: undefined,
    tenantStatus: undefined,
  };
  proxy.resetForm("formRef");
}

// 取消/关闭按钮
function cancel() {
  open.value = false;
  reloadId.value = undefined;
  reloadType.value = undefined;
  reset();
}

function submitForm() {
  formRef.value.validate((valid) => {
    if (valid) {
      saveLoading.value = true;
      if (form.value.tenantId !== undefined) {
        update(form.value).then((response) => {
          if (!response.success) {
            ElMessage.error(response.message);
            saveLoading.value = false;
          } else {
            ElMessage.success("修改成功")
            open.value = false;
            saveLoading.value = false;
            getList();
          }
        })
      } else {
        add(form.value).then((response) => {
          if (!response.success) {
            // ElMessage.error(response.message);
            saveLoading.value = false;
          } else {
            ElMessage.success("新增成功")
            open.value = false;
            saveLoading.value = false;
            getList();
          }
        })
      }
    }
  })
}

function handleDelete(row) {
  let tenantId = row.tenantId;
  reloadType.value = "remove"
  reloadId.value = tenantId;
  ElMessageBox.confirm(
      '是否确认删除租户名称为"' + row.tenantName + '"的数据项?',
      "警告",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }
  ).then(()=>del(tenantId)).then(() => {
    reloadId.value = undefined;
    reloadType.value = undefined;
    getList();
    ElMessage.success("删除成功")
  }).catch(() => {
    reloadId.value = undefined;
    reloadType.value = undefined;
  })
}

// </editor-fold>

// function delTenantRole(){
// }
// </editor-fold>

//管辖维护
// <editor-fold desc="管辖维护">
import {
  addJurisdictionTenant,
  getJurisdictionTenantIds,
} from "@/api/tenant/tenantJurisdiction";

const jurisdictionOpen = ref(false)
const jurisdictionLoading = ref(false)
const jurisdictionSubmitLoading = ref(false)
const tenantDataList=ref([])
const multipleJurisdictionTable=ref()
const queryJurisdictionForm=ref()
const queryJurisdictionParams=ref({
  pageNum: 1,
  pageSize: 10,
  tenantName: undefined,
  excludeTenantId: undefined,
  tenantStatus: "valid",
})
const tenantTotal=ref(0)
// 租户已管理的管辖租户数据
const currentJurisdictionTenantIds=ref([])
// 租户已选中但是未保存的管辖租户数据
const selectedJurisdictionTenantIds=ref([])
const selectedTenant=ref({})

function rowKey(row){
  return row.tenantId;
}
function getTenantList() {
  jurisdictionLoading.value=true;
  page(queryJurisdictionParams.value).then((response)=>{
    if (response.success) {
      tenantDataList.value = response.data.records
      nextTick((_)=>{
        if (currentJurisdictionTenantIds.value.length === 0){
          multipleJurisdictionTable.value.clearSelection();
        }
        tenantDataList.value.forEach(
            i=>{
              if (currentJurisdictionTenantIds.value.indexOf(i.tenantId)>-1){
                multipleJurisdictionTable.value.toggleRowSelection(i, true);
              }else {
                multipleJurisdictionTable.value.toggleRowSelection(i, false);
              }
            }
        )
      })
      tenantTotal.value=response.data.total;
    }
    jurisdictionLoading.value=false;
  })
}
function jurisdictionCancel(){
  jurisdictionOpen.value=false;
  proxy.resetForm("queryJurisdictionForm")
}
function handleTenantQuery(){
  queryJurisdictionParams.value.pageNum=1;
  getTenantList();
}
function  resetTenantQuery() {
  proxy.resetForm("queryJurisdictionForm");
  handleTenantQuery();
}
async function openJurisdiction(row) {
  selectedTenant.value=row;
  queryJurisdictionParams.value.excludeTenantId=row.tenantId
  //查询当前租户管辖的所有租户
  currentJurisdictionTenantIds.value=[];
  selectedJurisdictionTenantIds.value=[];
  await getJurisdictionTenantIds({
    manageTenantId: row.tenantId
  }).then(
      (response)=>{
        currentJurisdictionTenantIds.value=response.data
        selectedJurisdictionTenantIds.value=response.data
      }
  )
  await resetTenantQuery();
  jurisdictionOpen.value=true;
}

function handleTenantSelectionChange(val) {
  let tenantIds = val.map(function (value, index, array) {
    return value.tenantId;
  });
  selectedJurisdictionTenantIds.value=tenantIds;
}
function jurisdictionSubmitForm(){
  jurisdictionSubmitLoading.value=true;
  let managedTenantIds = Array.from(
      new Set([...selectedJurisdictionTenantIds.value])
  );
  addJurisdictionTenant({ managedTenantIds: managedTenantIds,
    manageTenantId: selectedTenant.value.tenantId,}).then((response)=>{
    if (response.success) {
      ElMessage.success("设置管辖租户成功！");
      resetQuery();
    } else {
      ElMessage.error("设置管辖租户失败！");
    }
    jurisdictionOpen.value = false;
    jurisdictionSubmitLoading.value=false;

  })
}
// </editor-fold>

const transferTableRef = ref()
const listRole = ref([])
const loadTenantId = ref()

const tenantId = customParam.tenantId

function openAddRole(row) {
  loadTenantId.value = row.tenantId;
  transferTableRef.value.open();
}

function loadData() {
  findRoleListByScope({tenantId: tenantId.value}).then((r) => {
    getDictInfo("scope").then((response) => {
      let newData = response.data.map((v) => {
        v["children"] = r.data.filter((i) => i.roleScope === v.dictValue);
        v["roleName"] = v.dictLabel;
        v["roleId"] = v.dictDataId;
        v["isOp"] = true;
        return v;
      });
      transferTableRef.value.setData(newData)
    })
  });
  getHadRolesAndNoRoles({tenantId: loadTenantId.value}).then((r) => {
    transferTableRef.value.setRightData({data: r.data.havelist});
  })
}

function addTenantRole(row,callback) {
  updateTenantRole(loadTenantId.value,row.roleId).then((res)=>{
    if (res.success){
      ElMessage.success("操作成功")
    }
    transferTableRef.value.reload();
  }).catch(()=>{
    ElMessage.error("已拥有该角色！")
    transferTableRef.value.reload();
  })


}
function delRoleT(row){
  delTenantRole(loadTenantId.value,row.roleId).then(r=>{
    transferTableRef.value.reload();
  })
}

</script>

<style scoped>

</style>
