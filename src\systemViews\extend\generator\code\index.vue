<template>
  <div class="app-container">
    <el-card shadow="never">
      <!--  搜索栏  -->
      <el-form :inline="true" label-width="58px">
        <el-form-item label="表名称" prop="tableName">
          <el-input v-model="tableName" placeholder="请输入表名称" clearable style="width: 240px" @keyup.enter="handleQuery"/>
        </el-form-item>
        <el-form-item label="表描述" prop="tableComment">
          <el-input v-model="tableComment" placeholder="请输入表描述" clearable style="width: 240px" @keyup.enter="handleQuery"/>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <!--  生成代码按钮  -->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="Upload" @click="openStepDialog">生成代码</el-button>
        </el-col>
      </el-row>
      <!--  历史数据表格  -->
      <el-table v-loading="loading" :data="genTableList">
        <el-table-column label="数据源" prop="datasourceName" :show-overflow-tooltip="true" min-width="250" />
        <el-table-column label="表名称" prop="tableName" :show-overflow-tooltip="true" min-width="200" />
        <el-table-column label="表描述" prop="tableComment" :show-overflow-tooltip="true" min-width="200" />
        <el-table-column label="实体" prop="className" :show-overflow-tooltip="true" min-width="200" />
        <el-table-column label="创建时间" prop="createDate" :show-overflow-tooltip="true" min-width="200" />
        <el-table-column label="更新时间" prop="updateDate" :show-overflow-tooltip="true" min-width="200" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right" min-width="400">
          <template #default="scope">
            <el-button link icon="Preview" type="primary" @click="handlePreview(scope.row)">预览</el-button>
            <el-button link icon="Edit" type="primary" @click="handleUpdate(scope.row)">编辑</el-button>
            <el-button link icon="Delete" type="primary" @click="handleDelete(scope.row)">删除</el-button>
            <el-button link icon="Download" type="primary" @click="handleGenTable(scope.row)">生成代码</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!--  分页  -->
      <pagination v-show="total > 0" :total="total" v-model:page="pageParams.pageNum" v-model:limit="pageParams.pageSize"
                  @pagination="getList" />

      <!--  预览界面  -->
      <el-dialog :title="previewTitle" v-model="previewOpen" width="80%" append-to-body>
        <el-tabs v-model="previewActiveName">
          <el-tab-pane v-for="(value, key) in previewData" :key="key"
                       :label="key.substring(key.lastIndexOf('/')+1,key.indexOf('.vm'))"
                       :name="key.substring(key.lastIndexOf('/')+1,key.indexOf('.vm'))" >
            <el-link :underline="false" icon="DocumentCopy" @click="copyToClipboard(value)" style="float:right">复制</el-link>
            <pre><code class="hljs" v-html="highlightedCode(value, key)"></code></pre>
          </el-tab-pane>
        </el-tabs>
      </el-dialog>

      <!--  导入组件  -->
      <import-table ref="importRef" @ok="handleImportFinished"/>
      <!--  引导对话框  -->
      <el-dialog title="生成代码" v-model="stepOpen" append-to-body width="85%" @close="handleClose">
        <el-steps finish-status="success" :active="activeStep" simple>
          <el-step title="步骤 1 - 选择数据源，导入数据库表" icon="Edit"></el-step>
          <el-step title="步骤 2" icon="Upload"></el-step>
          <el-step title="步骤 3" icon="Picture"></el-step>
        </el-steps>
        <div v-show="dataSourceShow" style="height: 500px;width: 100%; display:flex;justify-content:center;align-items:center;">
          <el-button icon="Upload" type="primary" @click="openImportTable">选择数据源</el-button>
        </div>
        <div v-show="setTableShow" style="margin-top: 15px;">
          <!-- 数据表格-->
          <el-table v-loading="loading" :data="genTableList">
            <el-table-column type="selection" width="55" />
            <el-table-column label="表名称" prop="tableName" :show-overflow-tooltip="true" min-width="200"/>
            <el-table-column label="表描述" prop="tableComment" :show-overflow-tooltip="true" min-width="200"/>
            <el-table-column label="实体" prop="className" :show-overflow-tooltip="true" min-width="200"/>
            <el-table-column label="创建时间" prop="createDate" min-width="200">
              <template #default="scope">
                <span>{{ parseTime(scope.row.createDate) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="更新时间" prop="updateDate" min-width="200">
              <template #default="scope">
                <span>{{ parseTime(scope.row.updateDate) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right" min-width="400">
              <template #default="scope">
                <el-button type="danger" @click="handlePreview(scope.row)">预览</el-button>
                <el-button type="primary" @click="handleUpdate(scope.row)">编辑</el-button>
                <el-button type="danger" @click="handleDelete(scope.row)">删除</el-button>
                <el-button type="primary" @click="handleGenTable(scope.row)">生成代码</el-button>
              </template>
            </el-table-column>
          </el-table>
          <!--  分页  -->
          <pagination v-show="total > 0" :total="total" v-model:page="pageParams.pageNum" v-model:limit="pageParams.pageSize"
                      @pagination="getList" />
        </div>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="stepOpen = false">取 消</el-button>
            <el-button type="primary" @click="stepOpen = false">确 定</el-button>
          </div>
        </template>
      </el-dialog>


      <!-- 编辑配置对话框-->
      <el-dialog title="编辑" v-model="editOpen" width="80%" append-to-body>
        <el-tabs v-model="editDialog.activeName">
          <el-tab-pane label="字段信息" name="columnInfo">
            <el-table :data="editDialog.columns" row-key="columnId" v-loading="editLoading">
              <el-table-column label="字段列名" prop="columnName" min-width="10%" :show-overflow-tooltip="true"/>
              <el-table-column label="字段描述" min-width="10%">
                <template #default="scope">
                  <el-input v-model="scope.row.columnComment"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="物理类型" prop="columnType" min-width="10%" :show-overflow-tooltip="true"/>
              <el-table-column label="Java类型" min-width="11%">
                <template #default="scope">
                  <el-select v-model="scope.row.javaType">
                    <el-option label="Long" value="Long"/>
                    <el-option label="String" value="String"/>
                    <el-option label="Integer" value="Integer"/>
                    <el-option label="Double" value="Double"/>
                    <el-option label="BigDecimal" value="BigDecimal"/>
                    <el-option label="Date" value="Date"/>
                    <el-option label="Boolean" value="Boolean"/>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="java属性" min-width="10%">
                <template #default="scope">
                  <el-input v-model="scope.row.javaField"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="新增" min-width="5%">
                <template #default="scope">
                  <el-checkbox true-label="1" false-label="0" v-model="scope.row.isInsert"></el-checkbox>
                </template>
              </el-table-column>
              <el-table-column label="修改" min-width="5%">
                <template #default="scope">
                  <el-checkbox true-label="1" false-label="0" v-model="scope.row.isEdit"></el-checkbox>
                </template>
              </el-table-column>
              <el-table-column label="列表" min-width="5%">
                <template #default="scope">
                  <el-checkbox true-label="1" false-label="0" v-model="scope.row.isList"></el-checkbox>
                </template>
              </el-table-column>
              <el-table-column label="查询" min-width="5%">
                <template #default="scope">
                  <el-checkbox true-label="1" false-label="0" v-model="scope.row.isQuery"></el-checkbox>
                </template>
              </el-table-column>
              <el-table-column label="查询方式" min-width="10%">
                <template #default="scope">
                  <el-select v-model="scope.row.queryType">
                    <el-option label="=" value="EQ"/>
                    <el-option label="!=" value="NE"/>
                    <el-option label=">" value="GT"/>
                    <el-option label=">=" value="GTE"/>
                    <el-option label="<" value="LT"/>
                    <el-option label="<=" value="LTE"/>
                    <el-option label="LIKE" value="LIKE"/>
                    <el-option label="BETWEEN" value="BETWEEN"/>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="必填" min-width="5%">
                <template #default="scope">
                  <el-checkbox true-label="1" false-label="0" v-model="scope.row.isRequired"></el-checkbox>
                </template>
              </el-table-column>
              <el-table-column label="显示类型" min-width="12%">
                <template #default="scope">
                  <el-select v-model="scope.row.htmlType">
                    <el-option label="文本框" value="input"/>
                    <el-option label="文本域" value="textarea"/>
                    <el-option label="下拉框" value="select"/>
                    <el-option label="单选框" value="radio"/>
                    <el-option label="复选框" value="checkbox"/>
                    <el-option label="日期控件" value="datetime"/>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="字典类型" min-width="12%">
                <template #default="scope">
                  <el-select v-model="scope.row.dictType" clearable filterable placeholder="请选择">
                    <el-option v-for="dict in editDialog.dictOptions" :key="dict.dictCode"
                               :label="dict.dictName" :value="dict.dictCode">
                      <span style="float: left">{{ dict.dictName }}</span>
                      <span style="float: right; color: #8492a6; font-size: 13px">{{ dict.dictCode }}</span>
                    </el-option>
                  </el-select>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="基本信息" name="basicInfo">
            <basic-info-form ref="basicInfo" :info="editDialog.info"/>
          </el-tab-pane>
          <el-tab-pane label="生成信息" name="genInfo">
            <gen-info-form ref="genInfo" :info="editDialog.info" :tables="editDialog.tables" :menus="editDialog.menus"/>
          </el-tab-pane>
        </el-tabs>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForm()">提交</el-button>
            <el-button @click="editOpen = false">返回</el-button>
          </div>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup name="Code">
import ImportTable from "./components/importTable.vue";
import genInfoForm from "./components/genInfoForm.vue";
import basicInfoForm from "./components/basicInfoForm.vue";
import {
  deleteTable,
  findGenTablePage,
  generatorCode,
  getGenTableById,
  previewCode,
  updateGenTable
} from "@/api/extend/generator/genTable";
import {ElMessageBox, ElMessage} from "element-plus";
import { getDictList } from "@/api/system/dict/type";
import {listMenu} from "@/api/system/menu";
import hljs from 'highlight.js/lib/core';
import java from 'highlight.js/lib/languages/java';
import xml from 'highlight.js/lib/languages/xml';
import sql from 'highlight.js/lib/languages/sql';
import javascript from 'highlight.js/lib/languages/javascript';
import 'highlight.js/styles/github.css';
import {handleTree} from "@/utils/common";

onMounted(() => {
  hljs.registerLanguage('java', java);
  hljs.registerLanguage('xml', xml);
  hljs.registerLanguage('html', xml);
  hljs.registerLanguage('vue', xml);
  hljs.registerLanguage('javascript', javascript);
  hljs.registerLanguage('sql', sql);
  hljs.highlightAll();
})

/** 历史表格数据显示 */
const genTableList = ref([]);
const loading = ref(true)
const total = ref(0)
const ids =ref([])
const tableName = ref()
const tableComment = ref()
const pageParams = ref({
  pageNum: 1,
  pageSize: 10
})

// 查询历史数据表
function getList() {
  loading.value = true;
  const params = {
    ...pageParams.value,
    tableName: tableName.value,
    tableComment: tableComment.value,
    ids: ids.value
  }
  findGenTablePage(params).then(res => {
    if (res.data && res.data.records) {
      genTableList.value = res.data.records
      total.value = res.data.total
    }
    loading.value = false;
  }).catch(() => {
    loading.value = false;
  })
}

// 搜索按钮操作
const handleQuery = () => {
  pageParams.value.pageNum = 1;
  getList();
}

// 重置按钮操作
const resetQuery = () => {
  tableName.value = '';
  tableComment.value = '';
  handleQuery();
}

/** 代码生成步骤条 */
const stepOpen = ref(false)
const dataSourceShow = ref(true)
const setTableShow = ref(false)
const activeStep = ref(0)
const importRef = ref(null)

// 生成代码对话框
const openStepDialog = () => {
  stepOpen.value = true
}

// 导入数据源回调
const handleImportFinished = (data) => {
  activeStep.value = 1
  dataSourceShow.value = false
  setTableShow.value = true
  pageParams.value.pageNum = 1
  ids.value = data
  getList()
}

// 关闭步骤条对话框
const handleClose = () => {
  activeStep.value = 0;
  dataSourceShow.value = true;
  setTableShow.value = false;
  ids.value = [];
  getList();
}

// 打开选择数据源弹窗
const openImportTable = () => {
  importRef.value.show();
}

/** 生成代码操作 */
const handleGenTable = (row) => {
  generatorCode(row.tableId, 'vue3').then(res => {
    const blob = new Blob([res], { type: 'application/octet-stream' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')// 创建a标签
    link.href = url
    link.download = row.className+".zip" // 重命名文件
    link.click()
    URL.revokeObjectURL(url) // 释放内存
  })
}

/** 预览 */
const previewOpen = ref(false)
const previewTitle = ref('代码预览')
const previewData = ref({})
const previewActiveName = ref('entity.java')

// 预览
const handlePreview = (row) => {
  previewCode(row.tableId, 'vue3').then(res => {
    if (res.data) {
      previewData.value = res.data
      previewOpen.value = true
      previewActiveName.value = 'entity.java'
    }
  })
}

// 高亮显示
const highlightedCode = (code, key) => {
  const vmName = key.substring(key.lastIndexOf("/") + 1, key.indexOf(".vm"));
  const language = vmName.substring(vmName.indexOf(".") + 1, vmName.length);
  const result = hljs.highlight(language, code || "", true);
  return result.value || '&nbsp;';
}

// 复制代码
const copyToClipboard = (context) => {
  navigator.clipboard.writeText(context).then(() => {
    ElMessage.success('复制成功')
  }).catch((error) => {
    ElMessage.error('复制失败')
  });
}

/** 编辑 */
const basicInfo = ref(null)
const genInfo = ref(null)
const editLoading = ref(true)
const editOpen = ref(false)
const editDialog = ref({
  activeName: "columnInfo", // 选中选项卡的 name
  info: {}, // 表详细信息
  tables: [], // 表信息
  columns: [], // 表列信息
  menus: [], // 菜单信息
  dictOptions: [], // 字典信息
})
const menuQueryParams = ref({
  permissionName: undefined,
  permissionStatus: undefined,
  permissionScope: undefined,
})

const handleUpdate = (row) => {
  // 获取表详细信息
  getGenTableById(row.tableId).then(res => {
    if (res.data) {
      editLoading.value = false;
      editDialog.value.info = res.data.genTableInfo;
      editDialog.value.tables = res.data.genTableAll;
      editDialog.value.columns = res.data.columns;
    }
  })
  // 查询字典下拉列表
  getDictList().then(res => {
    if (res.data) {
      editDialog.value.dictOptions = res.data
    }
  })
  // 查询菜单下拉列表
  listMenu(menuQueryParams.value).then((res) => {
    editDialog.value.menus = handleTree(res.data, "permissionId");
  });
  editOpen.value = true;
}

// 提交配置信息
const submitForm = () => {
  const basicForm = basicInfo.value.basicInfoForm;
  const genForm = genInfo.value.genInfoForm;
  Promise.all([basicForm, genForm].map(getFormPromise)).then(res => {
    const validateResult = res.every(item => !!item);
    if (validateResult) {
      const genTable = Object.assign({}, editDialog.value.info);
      genTable.columns = editDialog.value.columns;
      genTable.params = {
        treeCode: genTable.treeCode,
        treeName: genTable.treeName,
        treeParentCode: genTable.treeParentCode,
        parentMenuId: genTable.parentMenuId
      };
      updateGenTable(genTable).then(res => {
        if (res.data) {
          ElMessage.success("更新成功！")
          editOpen.value = false;
          editDialog.value.activeName = 'columnInfo';
          getList();
        } else {
          ElMessage.error("更新失败")
          editOpen.value = false;
          editDialog.value.activeName = 'columnInfo';
        }
      });
    } else {
      ElMessage.error("表单校验未通过，请重新检查提交内容");
    }
  });
}
const getFormPromise = (form) => {
  return new Promise(resolve => {
    form.validate(res => {
      resolve(res);
    });
  });
}

/** 删除 */
const handleDelete = (row) => {
  ElMessageBox.confirm(h('span', null, [
    h('span', null, '此操作将永久删除表名为【 '),
    h('span', { style: 'color: red' }, row.tableName),
    h('span', null, '】的数据项，是否继续?'),
  ]), '删除警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    deleteTable(row.tableId).then(res => {
      if (res.data) {
        ElMessage.success('删除成功！')
        getList()
      } else {
        ElMessage.error('删除失败！')
      }
    })
  })
}

getList()
</script>

<style scoped>

</style>
