import request from '@/utils/request'
import { apiUrl } from '@/utils/config'; 



export class screenIndex {
//新增账户合并选择账户列表
  static PayAccountManageList(data) {
    return request({
      url: `pay${apiUrl}/pay/PayAccountManage/list`,
      method: 'post',
      data: data
    })
  }

//   列表

  static payAccountConsolidationList(data) {
    return request({
      url: `pay${apiUrl}/pay/payAccountConsolidation/pageList`,
      method: 'post',
      data: data
    })
  }

//   本账户以外可合并的账户列表
  static mergeAbleList(data) {
    return request({
      url: `pay${apiUrl}/pay/PayAccountManage/mergeAbleList`,
      method: 'post',
      data: data
    })
  }
//   编辑

  static update(data) {
    return request({
      url: `pay${apiUrl}/pay/payAccountConsolidation/update`,
      method: 'post',
      data: data
    })
  }


//   删除


static delete(data) {
    return request({
      url: `pay${apiUrl}/pay/payAccountConsolidation/delete`,
      method: 'post',
      data: data
    })
  }

//   保存

static insert(data) {
    return request({
      url: `pay${apiUrl}/pay/payAccountConsolidation/insert`,
      method: 'post',
      data: data
    })
  }

//账户设置列表
  static payAccountTypePageList(data,params) {
    return request({
      url: `pay${apiUrl}/pay/payAccountType/pageList`,
      method: 'post',
      data: {...data,...params},
    })
  }

  static payAccountConsolidationFindById(data) {
    return request({
      url: `pay${apiUrl}/pay/payAccountConsolidation/findById`,
      method: 'post',
      data:data
    })
  }
}