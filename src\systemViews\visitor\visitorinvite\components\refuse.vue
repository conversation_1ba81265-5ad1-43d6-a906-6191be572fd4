<template>
    <div  class="dialog-box dialog-box-edit">
        <el-form label-width="100px">
        <el-form-item label="拒绝说明" prop="tagging">
          <el-input v-model="formData.tagging" placeholder="请输入拒绝原因" clearable :style="{ width: '100%' }">
          </el-input>
        </el-form-item>
        <el-form-item label="黑名单" prop="visitorType">
          <el-radio-group v-model="formData.visitorType">
            <el-radio-button label="1">是</el-radio-button>
            <el-radio-button label="0">否</el-radio-button>
          </el-radio-group>

        </el-form-item>
        <el-form-item label="备注" prop="remark" v-if="jjVisitorType === '1'">
          <el-input type="textarea" v-model="formData.remark" placeholder="请填写黑名单原因" clearable></el-input>
        </el-form-item>
      </el-form>
  
        
    </div>
  </template>
  
  <script setup>
  import { ref, reactive, onMounted } from 'vue'
  import { ElMessage } from 'element-plus'
  import { editResult} from '@/api/visitor/visitorinvite/index'
  const props = defineProps({
    popupType: {
      type: String,
      default: ''
    },
    rowData: {
      type: Object,
      default: () => ({})
    }
  })
  
  const emit = defineEmits(['submitClose'])
  
  // 表单引用
  const formRef = ref()
  // 表单数据
  const formData = reactive({
    processStatus:'2',
    id:props.rowData.id
  })
  
  // 验证规则
  const rules = reactive({
  
  })
  
  // 初始化数据
  onMounted(() => {
   
  })
  
  // 保存方法
  const handleRefuseSave = async () => {
  try {
  
    // 调用 API
    const res = await editResult(formData);
    if (res.success) {
      ElMessage.success('保存成功');
      emit('submitClose')
    } 
  } catch (error) {
    ElMessage.error(error.message || '请求失败');
    console.error('API Error:', error);
  }
};
  defineExpose({
    handleRefuseSave
  })
  </script>
  
  <style scoped lang="scss">
  
  </style>