<!-- 设备绑定 -->
<template>
  <div class="container-table-box">
    <Splitpanes class="default-theme">
      <Pane :size="18" :min-size="10">
        <el-card class="dep-card" style="height: 100%">
          <div class="flex" style="margin-bottom: 12px">
            <el-input
              v-model="searchValue"
              placeholder="请输入区域名称"
              clearable
              @clear="initTreeselect"
            />
            <el-button
              type="primary"
              icon="Search"
              @click="handleTree"
              style="margin-left: 8px"
              >搜索</el-button
            >
          </div>
          <!-- 组织树 -->
          <treeLoad
            ref="myTree"
            :isShowSearch="false"
            :defaultProps="defaultProps"
            :treeData="treeData"
            :treeBtnEdit="false"
            @loadFirstNodeFather="loadRootNodes"
            @loadChildNodeFather="loadChildNodes"
            :treeBtnDelete="false"
            :tree-name="isLazy ? 'name' : 'label'"
            :defaultExpandedKeys="defaultExpandedKeys"
            :isLazy="isLazy"
            :treeindex="treeindex"
            @checkedKeys="handleNodeClick"
          />
        </el-card>
      </Pane>
      <Pane :size="82" :min-size="62">
        <el-card>
          <div class="area-path-style" v-if="areaPath">
            <i class="icon iconfont icon-quyupaixu"></i>
            <span>{{ areaPath }}</span>
          </div>
          <el-row :gutter="24">
            <el-col :span="24" :xs="24">
              <dialog-search
                @getList="getList"
                formRowNumber="4"
                :columns="tabelForm.columns"
                :isShowRightBtn="true"
              >
                <template #formList>
                  <el-form
                    :model="queryParams"
                    ref="queryForm"
                    :inline="true"
                    label-width="80px"
                  >
                    <el-form-item label="设备名称" prop="deviceName">
                      <el-input
                        v-model="queryParams.deviceName"
                        placeholder="请输入设备名称"
                        clearable
                        @keyup.enter="handleQuery"
                      />
                    </el-form-item>
<!--                    <el-form-item label="设备类型" prop="deviceType">-->
<!--                      <el-select-->
<!--                        v-model="queryParams.deviceType"-->
<!--                        placeholder="请选择设备类型"-->
<!--                        clearable-->
<!--                      >-->
<!--                        <el-option-->
<!--                          v-for="dict in device_type"-->
<!--                          :key="dict.value"-->
<!--                          :label="dict.label"-->
<!--                          :value="dict.value"-->
<!--                        />-->
<!--                      </el-select>-->
<!--                    </el-form-item>-->
                    <el-form-item label="设备编码" prop="deviceCode">
                      <el-input
                        v-model="queryParams.deviceCode"
                        placeholder="请输入设备编码"
                        clearable
                        @keyup.enter="handleQuery"
                      />
                    </el-form-item>
<!--                    <el-form-item label="动作类型" prop="actionType">-->
<!--                      <el-select-->
<!--                        v-model="queryParams.actionType"-->
<!--                        placeholder="请选择动作类型"-->
<!--                        clearable-->
<!--                      >-->
<!--                        <el-option-->
<!--                          v-for="dict in device_action_type"-->
<!--                          :key="dict.value"-->
<!--                          :label="dict.label"-->
<!--                          :value="dict.value"-->
<!--                        />-->
<!--                      </el-select>-->
<!--                    </el-form-item>-->

<!--                    <el-form-item label="设备状态" prop="deviceStatus">-->
<!--                      <el-select-->
<!--                        v-model="queryParams.deviceStatus"-->
<!--                        placeholder="请选择设备状态"-->
<!--                        clearable-->
<!--                      >-->
<!--                        <el-option-->
<!--                          v-for="dict in device_status"-->
<!--                          :key="dict.id"-->
<!--                          :label="dict.label"-->
<!--                          :value="dict.value"-->
<!--                        />-->
<!--                      </el-select>-->
<!--                    </el-form-item>-->
<!--                    <el-form-item label="在线状态" prop="status">-->
<!--                      <el-select-->
<!--                        v-model="queryParams.status"-->
<!--                        placeholder="请选择在线状态"-->
<!--                        clearable-->
<!--                      >-->
<!--                        <el-option-->
<!--                          v-for="dict in device_action_status"-->
<!--                          :key="dict.id"-->
<!--                          :label="dict.label"-->
<!--                          :value="dict.value"-->
<!--                        />-->
<!--                      </el-select>-->
<!--                    </el-form-item>-->
                  </el-form>
                </template>
                <template #searchList>
                  <el-button type="primary" icon="Search" @click="handleQuery" v-hasPermi="['access:areaDevice:list']">
                    搜索
                  </el-button>
                  <el-button icon="Refresh" @click="resetQuery" v-hasPermi="['access:areaDevice:list']">重置</el-button>
                </template>
                <template #searchBtnList>
                  <el-button type="primary" icon="Plus" @click="handleAdd" v-hasPermi="['access:areaDevice:add']">
                    新增
                  </el-button>
                  <el-button icon="Download" @click="handleExport" v-hasPermi="['access:areaDevice:export']">
                    导出
                  </el-button>
                </template>
              </dialog-search>

              <PublicTable
                ref="publictable"
                :rowKey="tabelForm.tableKey"
                :tableData="deviceList"
                :columns="tabelForm.columns"
                :configFlag="tabelForm.tableConfig"
                :pageValue="pageParams"
                :total="total"
                :getList="getList"
              >
                <template #visitorPermissionType="{ scope }">
                  <el-radio-group
                    v-model="scope.row.visitorPermission"
                    @change="changePermission(scope.row)"
                  >
                    <el-radio-button label="1">启用</el-radio-button>
                    <el-radio-button label="0">禁用</el-radio-button>
                  </el-radio-group>
                </template>
                <!-- 操作列 -->
                <template #operation="{ scope }">
                  <el-button
                    link
                    icon="Edit"
                    type="primary"
                    title="修改"
                    @click="handleUpdate(scope.row)"
                    v-hasPermi="['access:areaDevice:edit']"
                  >
                  </el-button>
                  <el-button
                    link
                    icon="Delete"
                    type="primary"
                    title="解绑"
                    @click="handleDelete(scope.row)"
                    v-hasPermi="['access:areaDevice:delete']"
                  >
                  </el-button>
                </template>
              </PublicTable>

              <DialogBox
                :visible="open"
                :dialogWidth="dialogWidth"
                @save="submits"
                @cancellation="cancellation"
                @close="close"
                :dialogFooterBtn="dialogFooterBtn"
                CloseSubmitText="取消"
                SaveSubmitText="绑定"
                :dialogTitle="headerTitle"
              >
                <template #content>
                  <deviceBandingEdit
                    ref="deviceBandingEditRef"
                    @submitClose="submitClose"
                    :row-data="rowData"
                    :popup-type="popupType"
                  />
                </template>
              </DialogBox>
            </el-col>
          </el-row>
        </el-card>
      </Pane>
    </Splitpanes>
  </div>
</template>

<script setup>
import { ref, reactive } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import deviceBandingEdit from "./components/deviceBandingEdit.vue";
import { screenIndex } from "@/api/admittance/device";
import { formatMinuteTime } from "@/utils";
import treeLoad from "@/components/Tree/treeLoad";
import { apiUrl } from "@/utils/config";
const { proxy } = getCurrentInstance();
const { device_type, device_action_type, device_status, device_action_status } =
  proxy.useDict(
    "device_type",
    "device_action_type",
    "device_status",
    "device_action_status"
  );

// 表格配置
const tabelForm = ref({
  tableKey: "1",
  columns: [
    {
      fieldIndex: "deviceName",
      label: "设备名称",
      resizable: true,
      visible: true,
      sortable: true,
      minWidth: "120px",
    },
    {
      fieldIndex: "deviceCode",
      label: "设备编码",
      resizable: true,
      visible: true,
      sortable: true,
      minWidth: "120px",
    },
    {
      fieldIndex: "deviceType",
      label: "设备类型",
      resizable: true,
      visible: true,
      sortable: true,
      minWidth: "120px",
      type: "dict",
      dictList: device_type,
    },
    {
      fieldIndex: "actionType",
      label: "动作类型",
      resizable: true,
      visible: true,
      sortable: true,
      type: "dict",
      dictList: device_action_type,
    },
    {
      fieldIndex: "deviceStatus",
      label: "设备状态",
      resizable: true,
      visible: true,
      sortable: true,
      type: "dict",
      dictList: device_status,
    },
    {
      fieldIndex: "status",
      label: "在线状态",
      resizable: true,
      visible: true,
      sortable: true,
      type: "dict",
      dictList: device_action_status,
    },
    {
      fieldIndex: "sortNum",
      label: "排序",
      resizable: true,
      visible: true,
      sortable: true,
      minWidth: "100px",
    },
    {
      label: "访问权限",
      slotname: "visitorPermissionType",
      width: "140",
      visible: true,
    },
    {
      fieldIndex: "remark",
      label: "备注",
      resizable: true,
      visible: true,
      sortable: true,
      minWidth: "120px",
    },
    {
      label: "操作",
      slotname: "operation",
      width: "120",
      fixed: "right",
      visible: true,
    },
  ],
  tableConfig: {
    needPage: true,
    index: true,
    selection: false,
    reserveSelection: false,
    indexFixed: false,
    selectionFixed: false,
    indexWidth: "50",
    loading: false,
    showSummary: false,
    height: null,
  },
});

// 查询参数
const pageParams = ref({
  pageNum: 1,
  pageSize: 10,
});

// 表格数据
const deviceList = ref([{}]);
const total = ref(0);
const dialogFooterBtn = ref(true);
const headerTitle = ref("");
const rowData = ref({});
const open = ref(false);
const dialogWidth = ref("50%");
const popupType = ref("");
const queryParams = ref({
  areaId: 0,
});
const deviceBandingEditRef = ref(null);
// 树参数
const searchValue = ref("");
const defaultProps = reactive({
  children: "children",
  label: "name",
});
const treeData = ref([]);
const defaultExpandedKeys = ref([]);
const isLazy = ref(true);
const treeindex = ref(0);
const areaPath = ref("");
const myTree = ref(null);
// 懒加载根节点（当 isLazy=true 时）
const loadRootNodes = (resolve) => {
  // 这里模拟异步获取数据
  setTimeout(() => {
    screenIndex
      .areaTreeSelect({ name: searchValue.value, status: "1" })
      .then((response) => {
        resolve(response.data);
      });
  }, 500);
};

// 懒加载子节点
const loadChildNodes = (node, resolve) => {
  // 模拟异步请求
  setTimeout(() => {
    screenIndex
      .areaTreeSelect({
        ...node.data,
        ...{ name: searchValue.value, status: "1" },
      })
      .then((response) => {
        resolve(response.data);
      });
  }, 500);
};
/** 树结构点击 */
const handleNodeClick = (data) => {
  queryParams.value.areaId = data.id;
  getList();
  updateAreaPath();
};
const updateAreaPath = async () => {
  const res = await screenIndex.getAreaPath({
    areaId: queryParams.value.areaId,
  });
  areaPath.value = res.data?.map((item) => item.areaName).join(" > ") || "";
};
// 懒加载树搜索
const handleTree = () => {
  if (searchValue.value.length < 2) {
    ElMessage.warning("请至少输入两个字的查询条件！");
    return false;
  } else {
    treeindex.value++;
    isLazy.value = false;
    defaultExpandedKeys.value = [];
    screenIndex.getAllAreaTree({ areaName: searchValue.value }).then((res) => {
      treeData.value = res.data;
      expandTree(treeData.value);
    });
  }
};

const expandTree = (data) => {
  data.forEach((item) => {
    if (item.label.includes(searchValue.value)) {
      defaultExpandedKeys.value.push(item.id);
    }
    if (item.children) {
      expandTree(item.children);
    }
  });
};

const initTreeselect = () => {
  searchValue.value = "";
  treeindex.value = 0;
  treeData.value = [];
  isLazy.value = true;
};
// 获取列表
const getList = () => {
  tabelForm.value.tableConfig.loading = true;
  screenIndex.queryList(pageParams.value, queryParams.value).then((res) => {
    deviceList.value = res.data.records;
    total.value = res.data.total;
    tabelForm.value.tableConfig.loading = false;
  });
};

// 查询
const handleQuery = () => {
  pageParams.value.pageNum = 1;
  getList();
};

// 重置
const resetQuery = () => {
  queryParams.value = {
    areaId: "0",
  };
  areaPath.value = "";
  defaultExpandedKeys.value = [];
  myTree.value.setCurrentKeyFun();

  pageParams.value.pageNum = 1;

  getList();
};

// 新增
const handleAdd = () => {
  if (!areaPath.value) {
    ElMessage.error("请先在左侧选择绑定区域");
    return;
  }

  const areaObj = {
    areaId: queryParams.value.areaId,
    areaPath: areaPath.value,
  };

  headerTitle.value = "新增设备绑定";
  dialogWidth.value = "50%";
  popupType.value = "add";
  dialogFooterBtn.value = true;
  rowData.value = areaObj;
  open.value = true;
};

// 修改
const handleUpdate = (row) => {
  headerTitle.value = "修改设备绑定";
  popupType.value = "edit";
  rowData.value = row;
  open.value = true;
};

// 删除
const handleDelete = (row) => {
  ElMessageBox.confirm("确认要解绑该设备吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    // 调用解绑API

    screenIndex.deleteById(row.id).then(() => {
      getList();
      ElMessage.success("解绑成功");
    });
  });
};

// 导出admittance/deviceBanding/export
const handleExport = () => {
  proxy.download(
    `wisdomaccess${apiUrl}/access/areaDevice/export`,
    { ...queryParams.value },
    `设备绑定列表_${formatMinuteTime(new Date())}.xlsx`
  );
};
// 修改访客权限的方法
const changePermission = async (row) => {
  const submitDataForm = {
/*    id: row.id,
    deviceId: row.deviceTableId,
    areaId: row.areaId,
    visitorPermission: row.visitorPermission,
    sortNum: row.sortNum,
    remark: row.remark,
    deviceSn: row.deviceId,*/
    id: row.id,
    deviceId: row.deviceId,
    areaId: row.areaId,
    visitorPermission: row.visitorPermission,
    sortNum: row.sortNum,
    remark: row.remark,
    deviceSn: row.deviceSn,
  };

  try {
    const res = await screenIndex.updateDevice(submitDataForm);
    if (res.code === 200) {
      ElMessage.success("修改访客权限成功");
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    ElMessage.error("修改访客权限失败，请稍后重试");
  }
};
// 提交
const submits = () => {
  deviceBandingEditRef.value.saveBtn();
};

// 取消
const cancellation = () => {
  close(false);
};

// 关闭弹窗
const close = (val) => {
  open.value = val;
};

// 提交关闭
const submitClose = () => {
  open.value = false;
  getList();
};

onMounted(() => {
  getList();
});
</script>

<style scoped>
/* 自定义样式 */
</style>