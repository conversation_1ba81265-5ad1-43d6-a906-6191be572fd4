<template>
  <div class="dialog-box" :class="popupType === 'view' ? 'dialog-box-view' : 'dialog-box-edit'">
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="80px" >
      <el-row>
        <el-col :span="24">
          <el-form-item label="车牌号码" prop="plateNo">
            <el-select
              v-if="popupType !== 'view'"
              v-model="formData.plateNo"
              collapse-tags
              placeholder="请输入车牌号进行选择"
              clearable
              remote
              filterable
              :remote-method="getPlantNoList"
              @change="selectplateInfo"
              style="width: 100%"
            >
              <el-option
                v-for="(item, index) in plateList"
                :key="index"
                :label="item.plateNo"
                :value="item.plateNo"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="车主姓名" prop="staffName">
            <el-input
              v-model="formData.staffName"
              placeholder="请输入车主姓名"
              :disabled="popupType !== 'view'"
              v-if="popupType !== 'view'"
            />
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="手机号码" prop="cellphone">
            <el-input
              v-model="formData.cellphone"
              placeholder="请输入手机号码"
              :disabled="popupType !== 'view'"
              v-if="popupType !== 'view'"
            />
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="车主部门" prop="orgName">
            <el-input
              v-model="formData.orgName"
              placeholder="请输入车主部门"
              :disabled="popupType !== 'view'"
              v-if="popupType !== 'view'"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

   
  </div>
</template>

<script setup>
import { ref, reactive,nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import  { screenIndex }  from '@/api/majorParking/parkingRule';
const props = defineProps({
  rowData: {
    type: Object,
    default: () => ({})
  },
  popupType: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['submitClose', 'cancelClose']);

const formData = reactive({
  plateNo: '',
  staffName: '',
  cellphone: '',
  orgName: ''
});
const plateList = ref([]);

const rules = reactive({
  plateNo: [{ required: true, message: '请选择车牌号码', trigger: 'blur' }]
});

const formRef = ref(null);

const selectplateInfo = async (value) => {
  console.log(plateList.value)
  const selectedPlate = plateList.value.find((item) => item.plateNo == value);

  if (selectedPlate) {
    
    formData.plateNo = selectedPlate.plateNo;
    formData.staffId = selectedPlate.staffId;
    formData.staffName = selectedPlate.staffName;
    // formData.loginName = selectedPlate.loginName;
    formData.cellphone = selectedPlate.cellphone;
    formData.orgId = selectedPlate.orgId;
    formData.orgName = selectedPlate.orgName;
    await nextTick(); // 等待 DOM 更新
  }
};

const changePlantNo = (data) => {
  const plateNoListBack = plateList.value.filter((o) => o.plateNo !== data);
  plateList.value = plateNoListBack;
};

const getPlantNoList = (val) => {
  if (val) {
    const data = { plateNo: val };
    screenIndex.findPlateForRule(data).then((res) => {
      plateList.value = res.data;
    });
  }
};

const findPlateForRule = (data) => {
  screenIndex.findPlateForRule(data).then((res) => {
    plateList.value = res.data;
  });
};

const submitForm = async () => {
  const isValid = await formRef.value.validate();
  if (isValid) {
    formData.ruleId = props.rowData.id;
    emit('submitClose', formData);
  }
};

defineExpose({
  submitForm
})
const cancel = () => {
  emit('cancelClose');
};
</script>