<template>
  <div class="app-container">
    <el-card v-loading="loading"  class="app-card" shadow="never">
      <template #header>
        <span class="card-title">
          <span>应用中心管理</span>
          <span>
            <el-button link icon="Refresh"  type="primary" @click="refresh">
              刷新
            </el-button>
            <el-button link icon="DArrowRight"  type="primary"  @click="drawerOpen" v-hasPermi="['sys:extend:apps:sort-manage']">
              应用管理
            </el-button>
          </span>
        </span>
      </template>
      <el-collapse v-model="activeNames"  class="app-collapse">
        <el-collapse-item v-for="item in list" :key="item.id" :name="item.id">
          <template #title class="card-title">
            <i class="icon iconfont icon-shuxian" ></i>
            {{ item.name }}
          </template>
          <div class="apps" v-if="item.apps.length > 0">
            <div v-for="citem in item.apps" :key="citem.id" class="app-item">
              <img class="app-item-logoTemp" :src="citem.logoTemp" />
              <span>{{ citem.name }} <br/> <span class="appsRemark">{{citem.remark}}</span></span>
            </div>
            <el-button
                link
                icon="Plus"
                type="primary"
                class="app-item plus"
                @click="openCenter(item)"
                v-hasPermi="['sys:extend:apps:add']"
            >
              <span>添加应用</span>
            </el-button>

          </div>
          <el-empty class="app-empty" v-if="item.apps.length <= 0" :image-size="200">
            <el-button
                type="primary"
                icon="Plus"
                @click="openCenter(item)"
                v-hasPermi="['sys:extend:apps:add']"
            >添加应用</el-button>
          </el-empty>
        </el-collapse-item>
      </el-collapse>
    </el-card>
<!--    应用综合管理-->
    <el-drawer :with-header="false" v-model="open" size="70%">
      <el-card class="box-card">
        <template #header class="clearfix">
          <span>应用综合管理</span>
          <el-button
              style="float: right; padding: 3px 0"
              link
              icon="Plus"
              type="primary"
              @click="
              categoryVisible = true;
              categoryTitle = '新增应用分类';"
              v-hasPermi="['sys:extend:apps:add']"
          >
            <span>新增分类</span>
          </el-button>

        </template>
        <el-table :data="listAll" v-loading="allLoading">
          <el-table-column type="expand">
            <template #default="props">
              <el-card class="box-card" >
                <template #header class="clearfix">
                    <span>应用管理</span>
                    <el-button
                        link
                        style="float: right; padding: 3px 0"
                        type="primary"
                        icon="Plus"
                        @click="openCenter(props.row)"
                        v-hasPermi="['sys:extend:apps:add']"
                    >新增应用</el-button>
                </template>
                <div>
                  <el-table :data="props.row.apps" style="width: 100%">
                    <el-table-column label="序号" type="index" width="50px"></el-table-column>
                    <el-table-column property="logoTemp" width="80px" label="图标">
                      <template #default="scope">
                        <img :src="scope.row.logoTemp" style="width: 50px; height: 50px" />
                      </template>
                    </el-table-column>
                    <el-table-column property="name" label="应用名称" width="150px"></el-table-column>
                    <el-table-column property="sort" label="排序" width="50px"></el-table-column>
                    <el-table-column property="url" label="url" width="150px"></el-table-column>
                    <el-table-column property="status" label="状态" width="80px">
                      <template #default="scope">
                        <el-switch
                            v-model="scope.row.status"
                            @change="handleAppStatusChange(scope.row)"
                        ></el-switch>
                      </template>
                    </el-table-column>
                    <el-table-column property="createTime" label="创建时间"></el-table-column>
                    <el-table-column property="remark" label="备注"></el-table-column>
                    <el-table-column label="操作" width="280">
                      <template #default="scope">
                        <el-button
                            link
                            type="primary"
                            @click="editCenter(scope.row)"
                            v-hasPermi="['sys:extend:apps:edit']"
                        >编辑</el-button>
                        <el-button
                            link
                            type="primary"
                            @click="delCenter(scope.row)"
                            v-hasPermi="['sys:extend:apps:remove']"
                        >删除</el-button>
                        <el-button
                            link
                            type="primary"
                            @click="authCenter(scope.row)"
                            v-hasPermi="['sys:extend:apps:auth']"
                        >授权</el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </el-card>
            </template>
          </el-table-column>
          <el-table-column label="序号" type="index" width="80px"></el-table-column>
          <el-table-column property="name" label="名称" width="180px"></el-table-column>
          <el-table-column property="type" label="分类" :formatter="appCategoryFormat" width="180px"></el-table-column>
          <el-table-column property="sort" label="排序" width="100px"></el-table-column>
          <el-table-column property="status" label="状态" width="100px">
            <template #default="scope">
              <el-switch v-model="scope.row.status" @change="handleStatusChange(scope.row)"></el-switch>
            </template>
          </el-table-column>
          <el-table-column property="createTime" label="创建时间" width="180px"></el-table-column>
          <el-table-column property="remark" label="备注"></el-table-column>
          <el-table-column label="操作" width="180px">
            <template #default="scope">
              <el-button
                  link
                  @click="editCategory(scope.row)"
                  v-hasPermi="['sys:extend:apps:edit']"
              >编辑</el-button>
              <el-button
                  @click="delCategory(scope.row)"
                  link
                  v-hasPermi="['sys:extend:apps:remove']"
              >删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </el-drawer>
    <!-- 新增/编辑 应用分类页面 -->
    <el-drawer
        :title="categoryTitle"
        :before-close="cancelForm"
        v-model="categoryVisible"
        ref="drawer"
        size="50%"
        class="category"
        destroy-on-close
    >
      <div class="body">
        <el-form :model="categoryForm" :rules="rules" ref="categoryFormRef">
          <el-form-item label="分类" label-width="100px" prop="type">
            <el-select v-model="categoryForm.type" placeholder="请选择分类">
              <el-option
                  v-for="dict in app_category"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="名称" label-width="100px" prop="name">
            <el-input v-model="categoryForm.name" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="排序" label-width="100px" prop="sort">
            <el-input-number :min="1" v-model="categoryForm.sort"></el-input-number>
          </el-form-item>
          <el-form-item label="备注" label-width="100px" prop="remark">
            <el-input v-model="categoryForm.remark" autocomplete="off"></el-input>
          </el-form-item>
        </el-form>
        <div class="foot">
          <el-button
              type="primary"
              icon="Check"
              @click="
              categoryTitle == '新增应用分类' ? saveCategory() : updCategory()
            "
              :loading="categoryLoading"
          >{{ categoryLoading ? "提交中 ..." : "确 定" }}</el-button>
          <el-button @click="cancelForm" icon="Close">取 消</el-button>
        </div>
      </div>
    </el-drawer>
    <!-- 新增/编辑 应用页面 -->
    <el-drawer
        :title="centerTitle"
        :before-close="closeCenter"
        v-model="centerVisible"
        ref="drawer"
        size="50%"
        class="category"
        destroy-on-close
    >
      <div class="body">
        <el-form :model="centerForm" :rules="centerRules" ref="centerFormRef">
          <el-form-item label="分类" label-width="100px" prop="categoryId">
            <el-select v-model="centerForm.categoryId" placeholder="请选择分类">
              <el-option v-for="item in listAll" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="客户端" label-width="100px" prop="categoryId">
            <el-select v-model="centerForm.clientId" placeholder="请选择分类">
              <el-option
                  v-for="item in oauthClientList"
                  :key="item.clientId"
                  :label="item.clientApp"
                  :value="item.clientId"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="名称" label-width="100px" prop="name">
            <el-input v-model="centerForm.name" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="系统地址" label-width="100px" prop="url">
            <el-input v-model="centerForm.url" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="是否单点" label-width="100px" prop="urlType">
            <el-radio-group v-model="centerForm.urlType">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="图标" label-width="100px" prop="logoTemp">
            <el-upload
                class="avatar-uploader"
                action="null"
                :on-change="onChange"
                :auto-upload="false"
                :show-file-list="false"
                ref="logoTemp"
            >
              <img v-if="imageUrl" :src="imageUrl" class="avatar" />
              <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
<!--              <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过2MB</div>-->
              <template #tip >
                <div class="el-upload__tip">
                  只能上传jpg/png文件，且不超过2MB
                </div>
                </template>
            </el-upload>
            <div style="display: none">
              <el-input v-model="centerForm.logoTemp" autocomplete="off" ></el-input>

            </div>
          </el-form-item>

          <el-form-item label="排序" label-width="100px" prop="sort">
            <el-input-number :min="1" v-model="centerForm.sort"></el-input-number>
          </el-form-item>
          <el-form-item label="备注" label-width="100px" prop="remark">
            <el-input v-model="centerForm.remark" autocomplete="off"></el-input>
          </el-form-item>
        </el-form>
        <div class="foot">
          <el-button @click="closeCenter" icon="Close">取 消</el-button>
          <el-button
              type="primary"
              icon="Check"
              @click="centerTitle === '新增应用' ? saveApp() : updApp()"
              :disabled="disable"
              :loading="centerLoading"
          >{{ centerLoading ? "提交中 ..." : "确 定" }}</el-button>
        </div>
      </div>
    </el-drawer>
    <!-- 角色授权页面 -->
    <transfer-table
        ref="transferTableRef"
        modelName="角色赋权"
        @loadData="loadData"
        :listRole="listRole"
        :listProp="{
        key: 'roleName',
        name: '角色名称',
      }"
        :selectedProp="{
        key: 'roleName',
        name: '角色名称',
      }"
        @add="addAppRole"
        @remove="delAppRole"
    />
  </div>
</template>

<script setup name="Application">
import {
  list as listCategory,
  update,
  listAll as listAllCategory,
  addCategory,
  deleteCategory,
  addCenter,
  deleteCenter,
  updCenter,
  getCenter,
  authApp,
  selectTenantIds,
  getHadRolesAndNoRoles,
  addAppRole as addRole,
  delAppRole as delRole,
} from "@/api/extend/application";
import { getOauthClientList } from "@/api/tenant/systemEntry";
import {findRoleListByScope} from "@/api/system/role";
import {getDictInfo} from "@/api/system/dict/data";
import {ElMessage, ElMessageBox} from "element-plus";

const {proxy} = getCurrentInstance();
const {app_category} = proxy.useDict("app_category")
const loading=ref(true)
const oauthClientList=ref([])
//title
function refresh(){
    getList();
    all();
}
function drawerOpen(){
cancelForm();
open.value=!open.value;
getOauthClientList({}).then((res)=>{
  oauthClientList.value=res.data
})
  all();
}
//主页面
const activeNames=ref([])
const list=ref([])
const centerVisible=ref(false)
const centerLoading=ref(false)
const centerTitle=ref("")
const centerForm=ref({
  urlType: 1,
  sort: 1,
})
const disable=ref(true)
const imageUrl=ref("")

const checklogoTemp = (rule, value, callback) => {

  if (imageUrl.value==""
  ) {
    console.log("5555")
    console.log(imageUrl.value)
    callback(new Error("请上传应用图标"));
  } else {
    callback();
  }
};
const centerRules=ref(
    {
      name: [
        { required: true, message: "请输入名称", trigger: "blur" },
        { min: 1, max: 64, message: "最大输入64个字符", trigger: "blur" },
      ],
      url: [
        { required: true, message: "请输入访问地址", trigger: "blur" },
        { min: 1, max: 255, message: "最大输入255个字符", trigger: "blur" },
      ],
      remark: [
        { min: 1, max: 255, message: "最大输入255个字符", trigger: "blur" },
      ],
      logoTemp: [{ validator: checklogoTemp ,trigger: "blur" }],
    }
)
function openCenter(row){
  getOauthClientList({}).then((res)=>{
    oauthClientList.value=res.data
  })
  centerVisible.value=true;
  centerLoading.value=false;
  centerForm.value={
    urlType: 1,
    sort: 1,
    categoryId: row.id,
  }
}
function getList(id){
  loading.value=true;
  listCategory(2).then((res)=>{
    if (id) {
      list.value.map(v => {
        if (v.id === id && res.data.filter(i => i.id===id).length>0){
          v.apps = res.data.find((i) => i.id === id).apps;
        }
        return v;
      })
    }else {
      list.value=res.data
    }
    activeNames.value=list.value.map(v => v.id )
    loading.value=false;
  }).finally(()=>{


  })

}


//应用综合管理
const open=ref(false)
const categoryVisible=ref(false)
const categoryTitle=ref("")
const listAll=ref([])
const allLoading=ref(true)
const categoryLoading=ref(true)
const categoryForm=ref({
  sort:1})
const categoryFormRef =ref()
const centerFormRef=ref()
const rules=ref({
  name: [
    { required: true, message: "请输入名称", trigger: "blur" },
    { min: 1, max: 64, message: "最大输入64个字符", trigger: "blur" },
  ],
  remark: [
    { min: 1, max: 255, message: "最大输入255个字符", trigger: "blur" },
  ],
  type: [{ required: true, message: "请选择分类", trigger: "blur" }],
})
//获取应用综合数据
function all(id){
  allLoading.value=true;
  listAllCategory(2).then((res)=>{
    if (id){
      listAll.value.map((v) => {
        if (v.id === id && res.data.filter((i) => i.id === id).length > 0) {
          v.apps = res.data.find((i) => i.id === id).apps;
        }
        return v;
      })
    }else {
      listAll.value=res.data
    }
    allLoading.value=false
  })
}
function handleAppStatusChange(value){
  updCenter({
    id:value.id,
    status:value.status
  }).then((res)=>{
    if (res.success){
      all(value.categoryId)
      getList(value.categoryId)
    }else {
      ElMessage.error("修改失败！")
    }
  })
}

function appCategoryFormat(){

}
function handleStatusChange(value){
  update({
    id: value.id,
    status: value.status,
  }).then((res) => {
    all();
    getList();
  });
}
function saveCategory() {
  categoryFormRef.value.validate((valid) => {
    if (valid) {
      categoryLoading.value = true;
      addCategory(categoryForm.value).then((r) => {
        categoryLoading.value = false;
        if (r.success) {
          ElMessage.success("添加成功！")
          cancelForm();
          all();
          getList();
        } else {
          ElMessage.error("添加失败")
        }
      });
    }
  });
}
function updCategory() {
  categoryFormRef.value.validate((valid) => {
    if (valid){
      categoryLoading.value=true;
      update(this.categoryForm).then((r) => {
        categoryLoading.value = false;
        if (r.success) {
          ElMessage.success("修改成功")
          cancelForm();
          all();
          getList();
        } else {
          ElMessage.error("修改失败")
        }
      });
    }
  })
}
function editCategory(row){
  categoryTitle.value = "修改应用分类";
  categoryForm.value = {
    ...row,
  };
  categoryForm.value.type = categoryForm.value.type.toString();
  categoryVisible.value = true;
}
//TODO:数据传参有误，不能删除
function delCategory(row){
  ElMessageBox.confirm(
      "此操作将永久删除该分类, 是否继续?", "提示", {
        confirmButtonText: "删除",
        cancelButtonText: "取消",
        type: "warning",
        center: true,
        beforeClose: (action, instance, done) => {
          if (action === "confirm") {
            instance.confirmButtonLoading = true;
            instance.confirmButtonText = "删除中...";
            deleteCategory(row.id).then((r) => {
              instance.confirmButtonLoading = false;
              if (r.success) {
                ElMessage.success(
                    "删除成功！"
                );
                all();
                getList();
                done();
              } else {
                ElMessage.error("删除失败！");
                done();
              }
            });
          }else {
            done();
          }
        },
      }).catch(() => {
    ElMessage.info(
        "已取消删除"
    );
  });
}
function cancelForm(){
  categoryLoading.value=false
  categoryVisible.value=false
  categoryForm.value={ sort: 1 }
}
function closeCenter(){
  centerVisible.value=false;
  centerLoading.value=false;
  disable.value=false;
  centerTitle.value="新增应用"
  imageUrl.value=""
  centerForm.value={
    urlType: 1,
    sort: 1,
  }
}
function updApp(){
  centerFormRef.value.validate((valid)=>{
    if (valid){
      centerLoading.value=true;
      updCenter({
        ...centerForm.value,
        isAllShow: centerForm.value.isAllShow ? 0 : 1
      }).then((r) => {
        centerLoading.value = false;
        if (r.success) {
          ElMessage.success("修改成功")
          closeCenter();
          all(centerForm.value.categoryId);
          getList(centerForm.value.categoryId);
        } else {
          ElMessage.error("修改失败")
        }
      });
    } else {
      return false;
    }
  })
}
function saveApp(){
  centerFormRef.value.validate((valid)=>{
    if (valid){
      centerLoading.value=true
      addCenter({
        ...centerForm.value,
        isAllShow: centerForm.value.isAllShow ? 0 : 1
      }).then((r)=>{
        centerLoading.value=false;
        if (r.success){
          ElMessage.success("添加成功！")
          closeCenter();
          all(centerForm.value.categoryId);
          getList(centerForm.value.categoryId);
        }else {
          ElMessage.error("添加失败")

        }

      })
    }})
}
async function editCenter(row){

  let data = {};

  await getCenter(row.id).then((res) => {

    if (res.success) {
      data = res.data;
      //TODO:传输参数有误
      data = row;
    } else data = row;
  })
      .catch((err) => {
        data = row;
      })
      .finally(() => {

        centerForm.value = {
          ...data,
          isAllShow: data.isAllShow !== 1,
        };
        imageUrl.value = centerForm.value.logoTemp;
      });
  centerVisible.value = true;
  centerLoading.value = false;
  centerTitle.value = "修改应用";
  disable.value=false;
}
//TODO：删除有误
function delCenter(row){
  ElMessageBox.confirm(
      "此操作将永久删除该应用, 是否继续?", "提示",
      {
        confirmButtonText: "删除",
        cancelButtonText: "取消",
        type: "warning",
        center: true,
      }).then(()=>deleteCenter(row.id))
      .then(()=>{
        ElMessage.success("删除成功")
        all(row.categoryId)
        getList(row.categoryId)
      }).catch(() => {
    ElMessage.info(
        "已取消删除"
    );
  });
}
const appId=ref()
function authCenter(row){
  appId.value=row.id;
  transferTableRef.value.open()
}
//角色赋权
import TransferTable from "@/components/TransferTable"
import {login} from "../../../api/login";


const transferTableRef = ref()
const listRole = ref([])
function loadData() {
  findRoleListByScope().then((r) => {
    getDictInfo("scope").then((response) => {
      let newData = response.data.map((v) => {
        v["children"] = r.data.filter((i) => i.roleScope === v.dictValue);
        v["roleName"] = v.dictLabel;
        v["roleId"] = v.dictDataId;
        v["isOp"] = true;
        return v;
      });
      transferTableRef.value.setData(newData)
    })
  });
  getHadRolesAndNoRoles({ appCenterId: appId.value }).then((r) => {
    transferTableRef.value.setRightData({data: r.data.havelist});
  })
}
//TODO:后端接口没有做重复校验！
function addAppRole(row){
  addRole(appId.value,row.roleId).then((r) => {
    if (r.success){
      ElMessage.success("操作成功")
    }
    transferTableRef.value.reload();
  }).catch(()=>{
    ElMessage.error("已赋权该角色！")
  })
}
function  delAppRole(row) {
  delRole(appId.value,row.roleId).then((r) => {
    transferTableRef.value.reload();
  })
}

//图片处理
const fileList=ref([])
function getBase64(file) {
  return new Promise(function (resolve, reject) {
    let reader = new FileReader();
    let imgResult = "";
    reader.readAsDataURL(file);
    reader.onload = function () {
      imgResult = reader.result;
    };
    reader.onerror = function (error) {
      reject(error);
    };
    reader.onloadend = function () {
      resolve(imgResult);
    };
  });
}
function onChange(file) {
  // if (fileList.value.length && fileList.value.length >= 1) {
  //   /**引用对象然后验证表单域-这个可以清除上一步不通过时的提示*/
  //   centerFormRef.value.validateField("logoTemp");
  // }
  const isJPG = file.raw.type === "image/jpeg";
  const isPng = file.raw.type === "image/png";
  const isLt2M = file.raw.size / 1024 / 1024 < 2;
  if (!isJPG && !isPng) {
    ElMessage.error("上传图片只能是 JPG/Png 格式!");
    disable.value = true;
    return
  }
  if (!isLt2M) {
    ElMessage.error("上传图片大小不能超过 2MB!");
    disable.value = true;
    return
  }
  else {
    getBase64(file.raw).then((res) => {
      imageUrl.value = res;
    });
    disable.value=false;
  }

}
getList()
all()
watch(imageUrl,(newVal)=>{
  centerForm.value.logoTemp = newVal;
})
</script>

<style scoped lang="scss">
.app-card {
  margin: 0px 0px 10px 0px;
  min-height: calc(100vh - 200px);
  border: 0px solid #e6ebf5;

.card-title {
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: space-between;

> span {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.icon {
  font-size: 22px;
  color: #419eee;
  -webkit-transition: font-size 0.25s linear, width 0.25s linear;
  -moz-transition: font-size 0.25s linear, width 0.25s linear;
  transition: font-size 0.25s linear, width 0.25s linear;
}

.exec {
  padding: 3px 0;
}
}

.app-collapse {
  background-color: #00000000;
  border: 0px solid #e6ebf5;

.el-collapse-item__header {
  background-color: #00000000;

.icon {
  font-size: 22px;
  color: #419eee;
  -webkit-transition: font-size 0.25s linear, width 0.25s linear;
  -moz-transition: font-size 0.25s linear, width 0.25s linear;
  transition: font-size 0.25s linear, width 0.25s linear;
}
}
}

.app-empty {
  background-color: #f5f9fa;
}

.apps {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  background-color: #f5f9fa;
.appsRemark{
  font-weight: normal;
  color: gray;
  font-size: smaller
}
.app-item {
  padding: 40px;
  flex-basis: 30%;
  background-color: #ffffff;
  margin: 15px;
  border-radius: 10px;
  display: flex;
  flex-direction: row;
  align-items: center;
  font-size: 20px;

> span {
  font-weight: bold;
  margin-left: 20px;
  vertical-align: middle;
}

&:hover {
   box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
 }

.app-item-logoTemp {
  width: 50px;
  height: 50px;
}

cursor: pointer;
}

.plus {
  background-color: #ffffff80;
  color: #919594;

> span {
  font-weight: 300;
  margin-left: 20px;
}

.el-icon-plus {
  font-size: 40px;
}

&:hover {
   box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
   font-size: 30px;
   transition: 200ms;
   color: #1890ff;

.el-icon-plus {
  font-size: 60px;
  transition: 200ms;
}
}
}
}
}

.box-card {
  height: 100%;
  overflow: auto;
  width: 100%;
  margin-top: 0;
  margin-bottom: 0;
  //margin: 20px auto;
}

.category {
.body {
  margin: 40px;

.foot {
  text-align: center;
  margin-top: 50px;
}
}
}
:deep(.avatar-uploader .el-upload){
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
:deep(.avatar-uploader .el-upload:hover ){
  border-color: #409eff;
}
:deep(.avatar-uploader-icon){
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
:deep(.avatar){
  width: 178px;
  height: 178px;
  display: block;
}
:deep(.demo-drawer__footer){
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30px 0;
}
</style>

