import request from '@/utils/request'

// 新增客户端配置
export function addClient(data) {
	return request({
		url: '/user/client/add',
		method: 'post',
		data: data
	})
}

// 修改客户端配置
export function updateClient(data) {
	return request({
		url: '/user/client/edit',
		method: 'post',
		data: data
	})
}

// 删除客户端配置
export function delClient(configId) {
	return request({
		url: '/user/client/delete/' + configId,
		method: 'get'
	})
}

// 分页查询客户端配置
export function getClientPage(data) {
	return request({
		url: '/user/client/page',
		method: 'post',
		data: data
	})
}

// 查询单个参数详细(修改)
export function getClinet(configId) {
	return request({
			url: '/user/client/findOne',
			method: 'get',
			params:{
				id: configId
			}
	})
}

