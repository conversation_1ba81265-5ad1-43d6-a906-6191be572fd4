<script setup>
import { ref, onMounted, defineProps, defineEmits } from "vue";
import { ElMessage } from "element-plus";
import { screenIndex } from "@/api/admittance/device";

const { proxy } = getCurrentInstance()
const { device_type, device_action_type, device_status, device_action_status } = proxy.useDict(
  'device_type',
  'device_action_type',
  'device_status',
  'device_action_status'
)

const props = defineProps({
  popupType: {
    type: String,
    default: "",
  },
  rowData: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(["submitClose"]);

// 响应式数据
const formData = ref({
  visitorPermission: "1",
});
const deviceList = ref([]);
const pathLoad = ref(false);
const ruleform = ref(null);

const rules = {
  deviceTableId: [{ required: true, message: "请选择设备", trigger: "blur" }],
  deviceId: [{ required: true, message: "请选择设备", trigger: "blur" }],
  visitorPermission: [
    { required: true, message: "请选择访客权限", trigger: "blur" },
  ],
};

// 生命周期
onMounted(() => {
  getDeviceList();
  if (props.popupType === "edit" || props.popupType === "view") {
    formData.value = JSON.parse(JSON.stringify(props.rowData));
  }
  findAreaPath();
});

// 查询可用设备
const getDeviceList = async () => {
  try {
    const res = await screenIndex.queryAbleList();
    if (res.success) {
      deviceList.value = res.data;
    }
  } catch (error) {
    console.error("获取设备列表失败:", error);
  }
};

// 选择设备方法
const changeDevice = (value) => {
  const deviceObj = deviceList.value.find((item) => item.id === value);
  if (deviceObj) {
    formData.value = {
      ...formData.value,
      deviceId: deviceObj.id,
      deviceName: deviceObj.deviceName,
      deviceCode: deviceObj.deviceId,
      productId: deviceObj.productId,
      deviceStatus: deviceObj.deviceStatus,
      deviceType: deviceObj.deviceType,
      actionType: deviceObj.actionType,
      status: deviceObj.status,
    };
  }
};

// 查找区域路径
const findAreaPath = async () => {
  if (props.popupType !== "add") {
    try {
      const res = await screenIndex.getAreaPath({
        areaId: formData.value.areaId,
      });
      if (res.data) {
        pathLoad.value = false;
        formData.value.areaPath = res.data
          .map((item) => item.areaName)
          .join(">");
        pathLoad.value = true;
      }
    } catch (error) {
      console.error("获取区域路径失败:", error);
    }
  } else {
    formData.value.areaPath = props.rowData.areaPath;
    formData.value.areaId = props.rowData.areaId;
    pathLoad.value = true;
  }
};

// 提交表单
const submitForm = async () => {
  try {
    const valid = await ruleform.value.validate();
    if (valid) {
      const submitData = {
        id: formData.value.id,
        productId: formData.value.productId,
        deviceId: formData.value.deviceId,
        deviceName: formData.value.deviceName,
        deviceCode: formData.value.deviceCode,
        deviceSn: formData.value.deviceSn,
        areaId: formData.value.areaId,
        visitorPermission: formData.value.visitorPermission,
        status: formData.value.status,
        deviceStatus: formData.value.deviceStatus,
        sortNum: formData.value.sortNum,
        remark: formData.value.remark
      };

      // 根据 id 是否存在来判断是新增还是修改
      const res = formData.value.id
        ? await screenIndex.updateDevice(submitData) // 如果 id 存在，调用 updateDevice 修改
        : await screenIndex.saveDeviceBanding(submitData); // 如果 id 不存在，调用 saveDeviceBanding 新增

      if (res.success) {
        ElMessage.success(formData.value.id ? "修改成功" : "绑定成功");
        emit("submitClose");
      }
    }
  } catch (error) {
    console.error("表单提交失败:", error);
  }
};
defineExpose({
  saveBtn: submitForm,
})
</script>

<template>
  <div
    class="dialog-box"
    :class="popupType === 'view' ? 'dialog-box-view' : 'dialog-box-edit'"
  >
    <el-form
      ref="ruleform"
      :model="formData"
      label-width="120px"
      :rules="popupType !== 'view' ? rules : false"
    >
      <el-row>
        <el-col :span="24">
          <el-form-item label="区域路径" prop="areaPath">
            <div v-if="pathLoad">{{ formData.areaPath }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="设备名称" prop="deviceName">
            <el-select
              v-if="popupType == 'add'"
              v-model="formData.deviceName"
              filterable
              clear
              placeholder="请输入设备名称"
              @change="changeDevice"
            >
              <el-option
                v-for="item in deviceList"
                :key="item.id"
                :label="item.deviceName"
                :value="item.id"
              >
              </el-option>
            </el-select>
            <div v-if="popupType != 'add'">{{ formData.deviceName }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="设备编码" prop="deviceCode">
            <div>{{ formData.deviceCode }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="设备类型" prop="deviceType">
            <div>
              {{   $formatDictLabel(formData.deviceType,device_type)}}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="动作类型" prop="actionType">
            <div>
                {{   $formatDictLabel(formData.actionType,device_action_type)}}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="设备状态" prop="deviceStatus">
             {{   $formatDictLabel(formData.deviceStatus,device_status)}}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="在线状态" prop="status">
            <div>

               {{   $formatDictLabel(formData.status,device_action_status)}}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="访客权限" prop="visitorPermission">
            <el-radio-group
              v-if="popupType != 'view'"
              v-model="formData.visitorPermission"
            >
              <el-radio-button label="1">启用</el-radio-button>
              <el-radio-button label="0">禁用</el-radio-button>
            </el-radio-group>
            <div v-if="popupType == 'view'">

               {{   $formatDictLabel(formData.visitorPermission,visitor_permission)}}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="排序" prop="sortNum">
               <NumberInput v-if="popupType !== 'view'" v-model="formData.sortNum" customPlaceholder="请输入排序"
              input-type="integer" />
            <div v-if="popupType == 'view'">{{ formData.sortNum }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input
              v-if="popupType != 'view'"
              v-model="formData.remark"
              placeholder="请输入备注"
              clearable
              type="textarea"
            />
            <div v-if="popupType == 'view'">{{ formData.remark }}</div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
