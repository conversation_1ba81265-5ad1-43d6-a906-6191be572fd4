import request from '@/utils/request'
//TODO: 后端模板需要更改

// 分页查询已导入的数据库表
export function findGenTablePage(params) {
    return request({
        url: '/generator/gen/findGenTablePage',
        method: 'get',
        params
    })
}


export function findDBTablePage(params) {
    return request({
        url: '/generator/gen/findDBTablePage',
        method: 'get',
        params
    })
}

// 导入
export function importTable(data) {
    return request({
        url: '/generator/gen/importTable',
        method: 'post',
        data
    })
}

// 删除
export function deleteTable(tableId) {
    return request({
        url: '/generator/gen/delete',
        method: 'get',
        params: {
            tableId
        }
    })
}

// 查询详细信息
export function getGenTableById(tableId) {
    return request({
        url: '/generator/gen/findGenTableInfo',
        method: 'get',
        params: {
            tableId
        }
    })
}

// 修改代码生成信息
export function updateGenTable(data) {
    return request({
        url: '/generator/gen/updateGenTable',
        method: 'post',
        data
    })
}

// 查询详细信息
export function previewCode(tableId, frontEndType) {
    return request({
        url: '/generator/gen/previewCode',
        method: 'get',
        params: {
            tableId,
            frontEndType
        }
    })
}

// 下载代码
export function generatorCode(tableId, frontEndType) {
    return request({
        url: '/generator/gen/generatorCode',
        method: 'post',
        params:{
            tableId,
            frontEndType
        },
        responseType: 'blob'
    })
}
