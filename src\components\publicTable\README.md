# 表格拖拽样式混乱问题解决方案

## 问题描述
当拖拽 `<el-table-column type="index">` 序号列时，表格样式会出现混乱。

## 问题原因
1. **key值绑定问题**：使用 `index` 作为 `v-for` 的 key 值，导致拖拽后 Vue 无法正确识别组件变化
2. **固定列冲突**：序号列的 `fixed` 属性与拖拽功能产生冲突
3. **表格布局未及时更新**：拖拽后表格内部布局没有重新计算

## 解决方案

### 1. 修改 key 绑定
```vue
<!-- 修改前 -->
<el-table-column v-for="item in visibleColumns" :key="item.fieldIndex">

<!-- 修改后 -->
<el-table-column v-for="(item, index) in visibleColumns" :key="`${item.fieldIndex}-${item.label}-${index}`">
```

### 2. 添加样式修复
```scss
/* 解决拖拽时样式混乱的问题 */
:deep(.el-table) {
  .el-table__header-wrapper {
    overflow: visible;
  }
  
  .el-table__fixed {
    z-index: 10;
  }
  
  .el-table__fixed-right {
    z-index: 10;
  }
  
  .el-table__body-wrapper {
    transition: none;
  }
}

/* 拖拽过程中的样式处理 */
:deep(.sortable-ghost) {
  opacity: 0.5;
  background: #f5f7fa;
}

:deep(.sortable-chosen) {
  background: #ecf5ff;
}

:deep(.sortable-drag) {
  background: #ffffff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
```

### 3. 添加强制刷新方法
```javascript
// 强制刷新表格方法
const forceTableRefresh = () => {
  nextTick(() => {
    if (publicTable.value) {
      publicTable.value.doLayout();
    }
  });
};

// 在拖拽完成后调用
const handleDrop = () => {
  Number(rowKey.value++);
  saveTableColumns();
  forceTableRefresh();
};
```

### 4. 添加监听器
```javascript
// 监听visibleColumns变化，确保拖拽后表格正确渲染
watch(
  visibleColumns,
  () => {
    nextTick(() => {
      forceTableRefresh();
    });
  },
  { deep: true }
);
```

## 使用建议

1. **避免在序号列上设置 fixed**：如果必须固定序号列，建议将其放在最左侧
2. **确保唯一的 key 值**：使用组合字段作为 key，确保每次渲染都有唯一标识
3. **拖拽后刷新**：在拖拽操作完成后主动调用表格的 `doLayout()` 方法

## 序号列宽度调整问题解决方案

### 问题描述
序号列在拖拽调整宽度时，其他列也跟着缩小，影响表格整体布局。

### 解决方案

#### 1. 使用固定宽度而非最小宽度
```vue
<!-- 修改前 -->
<el-table-column type="index" min-width="60">

<!-- 修改后 -->
<el-table-column type="index" :width="indexWidth || 60" :resizable="true">
```

#### 2. 添加序号列专用props
```javascript
// 序号列宽度
indexWidth: {
  type: Number,
  default: 60,
},

// 序号列名称
indexName: {
  type: String,
  default: '#',
},

// 序号列是否固定
indexFixed: {
  type: [Boolean, String],
  default: false,
}
```

#### 3. 独立处理序号列宽度调整
```javascript
const surverWidth = (newWidth, oldWidth, column, event) => {
  // 如果是序号列，单独处理
  if (column.type === 'index') {
    emit('indexWidthChange', newWidth);
    return;
  }

  // 处理其他列的宽度调整
  columns.value = columns.value.map((v) => {
    if (v.prop === column.property) v.width = newWidth;
    return v;
  });
  saveTableColumns();
};
```

#### 4. 使用示例
```vue
<template>
  <public-table
    :table-data="tableData"
    :columns="columns"
    :config-flag="configFlag"
    :index-width="60"
    :index-name="'序号'"
    :index-fixed="false"
    @index-width-change="handleIndexWidthChange"
  />
</template>

<script setup>
const handleIndexWidthChange = (newWidth) => {
  console.log('序号列宽度变化:', newWidth)
  // 可以在这里保存用户的宽度偏好
}
</script>
```

## 注意事项

- 修改后的代码向后兼容，不会影响现有功能
- 序号列现在可以独立调整宽度，不会影响其他列
- 如果使用了第三方拖拽库（如 SortableJS），确保配置正确的拖拽选项
- 在大数据量表格中，频繁的 `doLayout()` 调用可能影响性能，建议添加防抖处理
- 序号列宽度建议设置在 50-150px 之间，以保证良好的用户体验
