import request from '@/utils/request'

//访客信息列表
export function findVisitorInfoList(params) {
  return request({
    url: '/visitor/visitorInfo/list',
    method: 'get',
    params: params,
  })
}

// 删除访客信息
export function deleteVisitorInfo(id) {
  return request({
    url: '/visitor/visitorInfo/del/' + id,
    method: 'post'
  })
}
// 根据id查询访客信息
export function selectVisitorInfo(id) {
  return request({
    url: '/visitor/visitorInfo/select/' + id,
    method: 'post'
  })
}
// 添加访客黑名单信息
export function addUserBlack(id) {
  return request({
    url: '/visitor/visitorInfo/addUserBlack/',
    method: 'post',
    params: {
      id: id
    }
  })
}
// 获取配置信息
export function getConfigInfo() {
  return request({
    url: '/visitor/visitorParameterConfig/list',
    method: 'get'
  })
}