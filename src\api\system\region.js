import request from '@/utils/request'

// 分页查询
export function findPage(params) {
  return request({
    url: '/user/region/findPage',
    method: 'get',
    params,
  })
}

// 查询区划下拉树结构
export function loadTree(data) {
  return request({
    url: '/user/region/findLowerList',
    method: "post",
    data
  });
}

// 根据id查询
export function findOne(id) {
  return request({
    url: '/user/region/findOne',
    method: 'get',
    params: {
      id
    },
  })
}

// 根据行政代码查询查询
export function findByRegionCode(regionCode) {
  return request({
    url: '/user/region/findByRegionCode',
    method: 'get',
    params: {
      regionCode
    },
  })
}

// 新增
export function add(data) {
  return request({
    url: '/user/region/add',
    method: 'post',
    data
  })
}

// 修改
export function update(data) {
  return request({
    url: '/user/region/update',
    method: 'post',
    data
  })
}

// 删除
export function deleteSysRegion(id) {
  return request({
    url: '/user/region/delete',
    method: 'get',
    params: {
      id
    }
  })
}
