<template>
  <div class="app-container">
    <Splitpanes class="default-theme">
      <Pane :size="15" :min-size="15">
        <el-card class="dep-card" style="height: 100%">
          <div class="head-container">
            <el-input
              v-model="areaName"
              placeholder="请输入区域名称"
              clearable
              style="margin-bottom: 15px"
              @clear="initTreeselect"
            >
              <template #append>
                <el-button icon="Search" @click="treeQuery" />
              </template>
            </el-input>
          </div>
          <div style="padding-bottom: 5px">
            <span style="color: #1979d8; font-weight: bolder">区域管理</span>
            <el-button
              type="text"
              icon="CirclePlus"
              size="mini"
              @click="handleAdd"
            />
          </div>
          <div
            class="head-container"
            style="height: calc(100vh - 126px); overflow: auto"
          >
            <tree-lazy
              v-if="isLazy || areaOptions.length > 0"
              :default-props="isLazy ? defaultProps : searhchProps"
              :tree-name="isLazy ? 'name' : 'label'"
              :default-expanded-keys="defaultExpandedArr"
              :tree-data="areaOptions"
              :is-lazy="isLazy"
              :tree-index="index"
              :key="index"
              :tree-btn-edit="treeBtn"
              :tree-btn-delete="treeBtn"
              :is-show-search="false"
              @checked-keys="checkedKeys"
              @edit-nodes="editNodes"
              @del-nodes="delNodes"
              @load-first-node-father="loadFirstNodeFather"
              @load-child-node-father="loadChildNodeFather"
            />
            <empty v-if="!isLazy && areaOptions.length === 0" />
          </div>
        </el-card>
      </Pane>
      <Pane :size="85" :min-size="85">
        <el-card class="dep-card" style="height: 100%">
          <div
            class="areaPath"
            style="font-weight: bold; padding-bottom: 5px; color: #606266"
          >
            <span>{{ areaPath }}</span>
          </div>

          <dialog-search
            @getList="getList"
            formRowNumber="3"
            :columns="tabelForm.columns"
          >
            <template #searchBtnList>
              <el-button
                class="reset"
                icon="Refresh"
                size="mini"
                @click="resetQuery"
                >重置</el-button
              >
              <el-button
                class="add"
                type="primary"
                plain
                icon="Plus"
                size="mini"
                @click="handleAdd"
                >新增</el-button
              >
            </template>
          </dialog-search>

          <public-table
            ref="publictable"
            :rowKey="tabelForm.tableKey"
            :tableData="userData"
            :columns="tabelForm.columns"
            :configFlag="tabelForm.tableConfig"
            :pageValue="queryParams"
            :total="total"
            :getList="getList"
          >
            <template #operation="{ scope }">
              <div>
                <el-button
                  link
                  icon="Edit"
                  type="primary"
                  title="修改"
                  @click="handleUpdate(scope.row)"
                >
                </el-button>
                <el-button
                  link
                  icon="Delete"
                  type="primary"
                  @click="handleDelete(scope.row)"
                >
                </el-button>
              </div>
            </template>
          </public-table>
        </el-card>
      </Pane>
    </Splitpanes>

    <el-dialog v-model="open" :title="title" width="30%" append-to-body>
      <add-model
        v-if="open"
        :close-btn="cancel"
        :popup-type="popupType"
        :row-data="rowData"
        @dialog-result="getDialogResult"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, computed } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  queryAreaList, delAreaById, areaTreeSelect,getAllAreaTree
} from "@/api/area/area";
// import AddModel from "./components/add.vue";
// import Empty from "@/components/empty.vue";
// import { useDict } from "@/utils/dict";

const { dict } = useDict(["area_status", "base_station_area_type"]);

// 响应式状态
const loading = ref(true);
const dataList = ref([]);
const total = ref(0);
const open = ref(false);
const title = ref("");
const rowData = ref({});
const areaOptions = ref([]);
const defaultExpandedArr = ref([]);
const areaName = ref("");
const searchValue = ref("");
const areaPath = ref("");
const sonMsg = ref("");
const curNode = ref(null);
const index = ref(0);
const isLazy = ref(true);
const treeBtn = ref(true);
const popupType = ref("");
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  id: undefined,
});

const tabelForm = ref({
  tableKey: "1",
  columns: [
    {
      fieldIndex: "areaName",
      label: "区域名称",
      resizable: true,
      show: true,
      sortable: true,
      showOverFlowTooltip: true,
      type: "clickText",
    },
    {
      fieldIndex: "areaId",
      label: "区域编码",
      resizable: true,
      show: true,
      sortable: true,
      showOverFlowTooltip: true,
    },
    {
      slotname: "areaType",
      label: "区域类型",
      resizable: true,
      show: true,
      sortable: true,
      showOverFlowTooltip: true,
    },
    {
      fieldIndex: "orderNum",
      label: "排序",
      resizable: true,
      show: true,
      sortable: true,
      showOverFlowTooltip: true,
    },
    {
      fieldIndex: "areaAddress",
      label: "区域地点",
      resizable: true,
      show: true,
      sortable: true,
      showOverFlowTooltip: true,
    },
    {
      slotname: "status",
      label: "状态",
      resizable: true,
      show: true,
      sortable: true,
      showOverFlowTooltip: true,
    },
    {
      label: "操作",
      slotname: "operation",
      width: "150",
      fixed: "right",
      show: true,
    },
  ],
  tableConfig: {
    needPage: true,
    index: true,
    selection: false,
    reserveSelection: false,
    indexFixed: false,
    selectionFixed: false,
    indexWidth: "50",
    loading: false,
    showSummary: false,
    height: null,
  },
});

const defaultProps = {
  children: "children",
  label: "name",
  isLeaf: (data) => !data.isParent,
};

const searhchProps = {
  children: "children",
  label: "label",
};

// 计算属性
const formatAreaType = computed(() => (type) => {
  return (
    dict.value.base_station_area_type.find((item) => item.value === type)
      ?.label || ""
  );
});

const formatStatus = computed(() => (status) => {
  return (
    dict.value.area_status.find((item) => item.value === status)?.label || ""
  );
});

// 生命周期
onMounted(() => {
  getList();
});

// 监听器
watch(sonMsg, (val) => {
  if (val) {
    handleSonMsgChange(val);
  }
});

// 方法
const getList = async () => {
  try {
    loading.value = true;
    const response = await queryAreaList(queryParams.value);
    dataList.value = response.rows;
    total.value = response.total;
  } finally {
    loading.value = false;
  }
};

const handleSonMsgChange = async (val) => {
  if (isLazy.value) {
    if (curNode.value) {
      const node = curNode.value;
      if (node.data.id === rowData.value.id) {
        node.parent.loaded = false;
        node.parent.expand();
      } else {
        node.loaded = false;
        node.expand();
      }
    }
    if (val === "edit") {
      await updateAreaPath();
    }
  } else {
    if (["edit", "add"].includes(val)) {
      await treeQuery();
      if (val === "edit") {
        await updateAreaPath();
      }
    }
  }
};

const updateAreaPath = async () => {
  const res = await getAreaPath({ areaId: queryParams.value.id });
  areaPath.value = res.data?.map((item) => item.areaName).join(" > ") || "";
};

const treeQuery = async () => {
  if (areaName.value.length < 2) {
    ElMessage.warning("请至少输入两个字的查询条件！");
    return;
  }

  searchValue.value = areaName.value;
  index.value++;
  isLazy.value = false;

  try {
    const res = await getAllAreaTree({ areaName: searchValue.value });
    defaultExpandedArr.value = [];
    areaOptions.value = res.data;
    expandTree(areaOptions.value);
  } catch (error) {
    console.error(error);
  }
};

const expandTree = (data) => {
  data.forEach((item) => {
    if (item.label.includes(searchValue.value)) {
      defaultExpandedArr.value.push(item.id);
    }
    if (item.children) {
      expandTree(item.children);
    }
  });
};

const initTreeselect = () => {
  searchValue.value = "";
  index.value = 0;
  areaOptions.value = [];
  isLazy.value = true;
};

const loadFirstNodeFather = async (resolve) => {
  try {
    const res = await areaTreeSelect({ name: searchValue.value });
    resolve(res.data);
  } catch (error) {
    console.error(error);
  }
};

const loadChildNodeFather = async (node, resolve) => {
  try {
    const res = await areaTreeSelect({ ...node.data, name: searchValue.value });
    resolve(res.data);
  } catch (error) {
    console.error(error);
  }
};

const checkedKeys = async (data, node) => {
  queryParams.value.pageNum = 1;
  queryParams.value.id = data.id;
  await getList();
  await updateAreaPath();
  curNode.value = node;
};

const editNodes = async (node, data) => {
  try {
    const res = await getAreaInfo({ id: data.id });
    handleEdit(res.data);
  } catch (error) {
    console.error(error);
  }
};

const delNodes = (node, data) => {
  handleDelete(data, (success) => {
    if (success) {
      if (isLazy.value) {
        node.parent.loaded = false;
        node.parent.expand();
      } else {
        const parent = node.parent;
        const children = parent.data.children || parent.data;
        const index = children.findIndex((d) => d.id === data.id);
        children.splice(index, 1);
      }
      queryParams.value.id = undefined;
      getList();
    }
  });
};

const handleAdd = () => {
  sonMsg.value = "";
  title.value = "新增区域信息";
  popupType.value = "add";
  open.value = true;
};

const handleEdit = (row) => {
  sonMsg.value = "";
  title.value = "修改区域信息";
  popupType.value = "edit";
  rowData.value = row;
  open.value = true;
};

const handleDelete = (row, callback) => {
  ElMessageBox.confirm(
    "确认删除吗？\n该区域下的所有子区域都会被删除，且不可恢复，请确认。"
  )
    .then(async () => {
      await delAreaById(row.id);
      ElMessage.success("删除成功");
      getList();
      sonMsg.value = "delete";
      callback?.(true);
    })
    .catch(() => callback?.(false));
};

const cancel = () => {
  open.value = false;
  sonMsg.value = popupType.value;
  if (sonMsg.value) {
    getList();
  }
};

const getDialogResult = (msg) => {
  sonMsg.value = msg;
};

const resetQuery = () => {
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    id: undefined,
  };
  areaPath.value = "";
  initTreeselect();
  getList();
};
</script>

<style lang="scss" scoped>
.app-container {
  padding-top: 8px;
}

:deep(.custom-tree-node > span) {
  font-size: 14px !important;
  padding-right: 10px;
}

.el-select {
  width: 100% !important;
}
</style>