import request from '@/utils/request'
import { apiUrl } from '@/utils/config';

export class screenIndex {
   

    // 查询出入记录
    static inAndOutInfo(query, params) {
        return request({
            url: `wisdomaccess${apiUrl}/access/entryExitRecords/findAccessEntryExitRecords`,
            method: 'post',
            data: {...query,...params},
        })
    }

    // 根据条件查询个人出入记录表
    static inAndOutInfoMyself(query, params) {
        return request({
            url: `wisdomaccess${apiUrl}/access/inAndOutRecord/findMyself`,
            method: 'post',
            data: {...query,...params},
        })
    }
}