<!-- 账户清零 -->

<template>
  <div class="dialog-box" :class="popupType === 'view' ? 'dialog-box-view' : 'dialog-box-edit'">

    <div class="common-header" style="margin-bottom: 12px;" >
      <div class="common-header-line"></div>
      <div class="common-header-text">统计结果</div>
      <div class="flex-1" />
    </div>

    <div class="stats-container">
      <div class="stats-card">
        <div class="stats-icon">
          <i class="User"></i> <el-icon><User /></el-icon>
        </div>
        <div class="stats-content">
          <div class="stats-label">{{ popupType === 'addSupplementary' ? '补扣总人数' : '退款总人数' }}</div>
          <div class="stats-value">{{ formData.refundCount || "0" }}</div>
        </div>
      </div>
      <div class="stats-card">
        <div class="stats-icon">
          <i class="User"></i> <el-icon><Money /></el-icon>
        </div>
        <div class="stats-content">
          <div class="stats-label">{{ popupType === 'addSupplementary' ? '补扣总金额' : '退款总金额' }}</div>
          <div class="stats-value">{{ formData.totalRefundAmount || "0" }} <span>元</span></div>
        </div>
      </div>
    </div>
    <!-- <el-form ref="formRef" :model="formData" label-width="95px" :rules="popupType !== 'view' ? rules : ''">
      <el-row>
        <el-col :span="8">
          <el-form-item :label="popupType === 'addSupplementary' ? '补扣总人数' : '退款总人数'">
            <div>{{ formData.refundCount }}</div>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item :label="popupType === 'addSupplementary' ? '补扣总金额' : '退款总金额'">
            <div>{{ formData.totalRefundAmount }}</div>
          </el-form-item>
        </el-col>
      </el-row>


    </el-form> -->

    <!-- 表格区域 -->
    <div class="flex" style="margin-bottom: 12px;" >

      <div class="common-header" style="border: none;">
      <div class="common-header-line"></div>
      <div class="common-header-text"> {{popupType === 'addSupplementary' ? '补扣列表' : '退款列表'}}</div>
      <div class="flex-1" />
    </div>

      <div class="flex-1"></div>
      <el-button type="primary" icon="Plus" @click="handleAdd" v-if="popupType !== 'view'">
        {{ popupType === 'addSupplementary' ? '新增一条补扣' : '新增一条退款' }}
      </el-button>
    </div>
    <public-table ref="publictable" :rowKey="tabelForm.tableKey" :tableData="lists" :columns="tabelForm.columns"
      :configFlag="tabelForm.tableConfig" :pageValue="pageParams">

      <template #paySceneSlot="{ scope }">
        <el-select v-if="popupType !== 'view'" v-model="scope.row.paySceneId" placeholder="请选择支付场景" clearable
          @change="(val) => handlePaySceneChange(val, scope.row)">
          <el-option v-for="item in paySceneList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <span v-else>{{ scope.row.paySceneName }}</span>
      </template>

      <template #payTypeSlot="{ scope }">
        <el-select v-if="popupType !== 'view'" v-model="scope.row.payTypeId" placeholder="请选择支付类型" clearable>
          <el-option v-for="item in payTypeList[scope.row.paySceneId] || []" :key="item.value" :label="item.label"
            :value="item.value" />
        </el-select>
        <span v-else>{{ scope.row.payTypeName }}</span>
      </template>

      <template #providerSlot="{ scope }">
        <el-select v-if="popupType !== 'view'" v-model="scope.row.providerId" placeholder="请选择供应商" clearable>
          <el-option v-for="item in providerList[scope.row.paySceneId] || []" :key="item.value" :label="item.label"
            :value="item.value" />
        </el-select>
        <span v-else>{{ scope.row.providerName }}</span>
      </template>

      <template #remarkSlot="{ scope }">
        <el-input v-if="popupType !== 'view'" v-model="scope.row.remark" placeholder="请输入备注" clearable></el-input>
        <span v-else>{{ scope.row.remark }}</span>
      </template>


      <template #amountSlot="{ scope }">
        <NumberInput
          v-if="popupType !== 'view'"
          v-model="scope.row.amount"
          customPlaceholder="请输入金额"
          input-type="decimal"
        />
        <!-- 查看时显示文本 -->
        <div v-else>
          {{ scope.row.amount }}
        </div>
      </template>

      <template #staffIdSlot="{ scope }">
        <el-select
          v-if="popupType !== 'view'"
              collapse-tags
              placeholder="请输入人员进行选择"
              clearable
              @change="(val) => changeUser(val, scope.row,popupType)"
              filterable
              remote
              v-model="scope.row.staffId"
              reserve-keyword
              :remote-method="getUserList"
            >
              <el-option
                v-for="(item, index) in applyUserList"
                :key="index"
                :label="item.staffName"
                :value="item.staffId"
                :disabled="isStaffSelected(item.staffId, scope.row)"
              >
                <div>
                  {{
                    item.staffName +
                    "(" +
                    item.orgName +
                    "/" +
                    item.loginName +
                    ")"
                  }}
                </div>
              </el-option>
            </el-select>
        <!-- 查看时显示文本 -->
        <div v-else>
          {{ scope.row.staffName }}
        </div>
      </template>

      <template #accountListSlot="{ scope }">
        <el-select
          v-if="popupType !== 'view'"
          v-model="scope.row.accountListId"
          placeholder="请选择账户"
          clearable
        >
          <el-option
            v-for="item in accountListMap[scope.row.staffId] || []"
            :key="item.id"
            :label="item.label"
            :value="item.id"
          />
        </el-select>
        <div v-else-if="popupType === 'view'">{{ scope.row.accountListName }}</div>
      </template>

      <template #operation="{ scope }">
        <el-button type="text" title="删除" icon="Delete" @click="handleDelete(scope.row)"></el-button>
      </template>

    </public-table>
  </div>
</template>



<script setup>
import { ref, reactive, watch, getCurrentInstance, defineProps, onMounted } from "vue";

import { ElMessage } from "element-plus";

import { screenIndex } from "@/api/paymentCenter/abnormal-adjustment/index";

import useUserStore from "@/store/modules/user";


// 定义 emits
const emit = defineEmits(["closeBtn"]);
// 定义组件 props
const props = defineProps({
  closeBtn: {
    type: Function,

    default: null,
  },

  popupType: {
    type: String,
    default: "",
  },

  rowData: {
    type: Object,
    default: () => ({}),
  },
});

// 表单引用
const applyUserList = ref([])
const formRef = ref(null);
let formData = ref({
  operator: "", // 操作人
  refundCount: 0, // 退款人数
  totalRefundAmount: 0, // 退款总金额
});
// 账户列表（按人员ID分组）
const accountListMap = ref({});
const pageParams = ref({
  pageNum: 1,
  pageSize: 10,
});
// 字典
const { proxy } = getCurrentInstance();
const {

} = proxy.useDict(

  );

// 供应商列表（按支付场景ID分组）
const providerList = ref({});
// 支付场景列表
const paySceneList = ref([]);
// 支付类型列表（按支付场景ID分组）
const payTypeList = ref({});


// 定义校验规则
const rules = reactive({});

// 表格配置
const lists = ref([]);

const tabelForm = reactive({
  tableKey: "subsidy",
  columns: [
    {
      label: "支付场景",
      slotname: "paySceneSlot",
      minWidth: 120,
      visible: true,
      slot: true,
    },
    {
      label: "支付类型",
      slotname: "payTypeSlot",
      minWidth: 120,
      visible: true,
      slot: true,
    },
    {
      label: "供应商",
      slotname: "providerSlot",
      minWidth: 140,
      visible: true,
      slot: true,
    },
    {
      label: "人员",
      slotname: "staffIdSlot",
      minWidth: 150,
      visible: true,
      slot: true,
    },
    {
      label: "账户",
      slotname: "accountListSlot",
      minWidth: 140,
      visible: true,
      slot: true,
    },
    {
      label: "金额",
      slotname: "amountSlot",
      minWidth: 100,
      visible: true,
      slot: true,
    },
    {
      label: "备注",
      slotname: "remarkSlot",
      minWidth: 120,
      visible: true,
      slot: true,
    },


    {
      label: "操作",
      slotname: "operation",
      minWidth: 80,
      fixed: "right",
      visible: true,
      slot: true,
    },
  ],
  tableConfig: {
    needPage: true,
    index: true,
    selection: false,
    indexWidth: "60",
    loading: false,
    height: null,
  },
});
// 定义方法
// 寻找人员
const getUserList = async (name) => {
  if (!name || name.trim().length < 2) {
    applyUserList.value = []; // 立即清空列表
    return;
  }
  try {
    const res = await screenIndex.selectUserList({
        params: name,
    });
    applyUserList.value = res.data;
  } catch (error) {
    console.error("获取用户列表失败:", error);
  }
};
// 判断人员是否已被选择
const isStaffSelected = (staffId, currentRow) => {
  if (!staffId) return false;
  return lists.value.some(row =>
    row !== currentRow &&
    row.staffId === staffId
  );
};

// 人员选择
const changeUser = (staffId, row,popupType) => {
  const user = applyUserList.value.find((o) => o.staffId === staffId);
  if (user) {
    row.staffId = user.staffId;
    row.unionId = user.unionId;
    row.staffName = user.staffName; // 保存人员姓名用于显示

    // 清空账户选择
    row.accountListId = "";

    // 如果已选择供应商，加载账户列表
    if (row.providerId && staffId) {
      getAccountList(staffId, row.providerId, row,popupType);
    }
  }
};

// 获取账户列表
const getAccountList = async (staffId, providerId, row,popupType) => {
  //设置类型
  let type = popupType === 'addSupplementary' ? '0' : '1'
  try {
    const res = await screenIndex.getAccountTree({
      staffId: staffId,
      providerId: providerId,
      param:type
    });
    accountListMap.value = {
      ...accountListMap.value,
      [staffId]: res.data || []
    };

    // 如果只有一个账户选项，自动选择
    if (accountListMap.value[staffId] && accountListMap.value[staffId].length === 1 && row) {
      row.accountListId = accountListMap.value[staffId][0].id;
    }
  } catch (error) {
    console.error("获取账户列表失败:", error);
    accountListMap.value[staffId] = [];
  }
};

// 支付场景变更处理
const handlePaySceneChange = async (paySceneId, row) => {
  if (paySceneId) {
    // 从paySceneList中找到对应的支付场景数据
    const paySceneItem = paySceneList.value.find(item => item.value === paySceneId);
    if (paySceneItem) {
      // 使用找到的支付场景数据的id属性来请求支付类型
      await getPayTypeList({ id: paySceneItem.id }, paySceneId);

      // 使用支付场景ID查询供应商
      await getProviderList({ id: paySceneItem.id }, paySceneId);
    }
  } else {
    if (row) {
      row.payTypeId = "";
      row.providerId = "";
    }
  }
};

// 获取支付场景列表
const getPaySceneList = async () => {
  try {
    const res = await screenIndex.getPaySceneTree({});
    paySceneList.value = res.data || [];

    // 自动选择唯一选项
    if (paySceneList.value.length === 1 && lists.value.length > 0) {
      lists.value.forEach(item => {
        if (!item.paySceneId) {
          item.paySceneId = paySceneList.value[0].value;
          // 触发支付场景变更，以加载支付类型和供应商
          handlePaySceneChange(item.paySceneId, item);
        }
      });
    }
  } catch (error) {
    console.error("获取支付场景列表失败:", error);
    paySceneList.value = [];
  }
};

// 获取支付类型列表
const getPayTypeList = async (params, paySceneId) => {
  try {
    const res = await screenIndex.getPayTypeTree(params);
    payTypeList.value = {
      ...payTypeList.value,
      [paySceneId]: res.data || []
    };

    // 自动选择唯一选项
    if (payTypeList.value[paySceneId] && payTypeList.value[paySceneId].length === 1) {
      lists.value.forEach(item => {
        if (item.paySceneId === paySceneId && !item.payTypeId) {
          item.payTypeId = payTypeList.value[paySceneId][0].value;
        }
      });
    }
  } catch (error) {
    console.error("获取支付类型列表失败:", error);
    payTypeList.value[paySceneId] = [];
  }
};

// 获取供应商列表
const getProviderList = async (params, paySceneId) => {
  try {
    const res = await screenIndex.getProviderTree(params);
    providerList.value = {
      ...providerList.value,
      [paySceneId]: res.data || []
    };

    // 自动选择唯一选项
    if (providerList.value[paySceneId] && providerList.value[paySceneId].length === 1) {
      lists.value.forEach(item => {
        if (item.paySceneId === paySceneId && !item.providerId) {
          item.providerId = providerList.value[paySceneId][0].value;

          // 如果已经选择了人员，则加载账户列表
          if (item.staffId) {
            getAccountList(item.staffId, item.providerId, item);
          }
        }
      });
    }
  } catch (error) {
    console.error("获取供应商列表失败:", error);
    providerList.value[paySceneId] = [];
  }
};

// 计算退款信息
const calculateRefundInfo = () => {
  // 计算退款人数
  formData.value.refundCount = lists.value.length;

  // 计算退款总金额
  let totalAmount = 0;
  lists.value.forEach(item => {
    if (item.amount && !isNaN(Number(item.amount))) {
      totalAmount += Number(item.amount);
    }
  });
  formData.value.totalRefundAmount = totalAmount.toFixed(2);
};

//新增表格数据
const handleAdd = () => {
  const newRow = {
    "paySceneId": "",
    "payTypeId": "",
    "providerId": "",
    "unionId": "",
    "staffId": "",
    "accountListId": "",
    "amount": 0,
    "adjustType": "",
    "remark": "",
    "tenantId": ""
  };

  // 如果只有一个支付场景，自动选择
  if (paySceneList.value.length === 1) {
    newRow.paySceneId = paySceneList.value[0].value;

    // 自动加载支付类型和供应商
    handlePaySceneChange(newRow.paySceneId, newRow);
  }

  lists.value.push(newRow);
  calculateRefundInfo();
};

// 删除表格数据
const handleDelete = (row) => {
  const index = lists.value.findIndex((item) => item === row);
  if (index !== -1) {
    lists.value.splice(index, 1);
    calculateRefundInfo();
  }
};

// 监听退款金额变化
watch(() => lists.value, () => {
  calculateRefundInfo();
}, { deep: true });

const initData = async () => {
  // 页面加载时首先获取支付场景列表
  await getPaySceneList();

  // 初始化操作人信息
  const userStore = useUserStore();
  formData.value.operator = userStore.name;

  if (props.popupType === "add") {
    // 新增时初始化空数据
    formData.value = {
      operator: userStore.name,
      refundCount: 0,
      totalRefundAmount: 0
    };
    lists.value = [];
  } else {
    // 编辑或查看时获取详情数据
    try {

    } catch (error) {
      console.error('获取详情失败:', error);
    }
  }
};

const saveForm = async () => {
  try {

    // 检查是否有退款记录
    if (lists.value.length === 0) {
      ElMessage.warning(`请至少添加一条${props.popupType === 'addSupplementary' ? '补扣' : '退款'}记录`);
      return;
    }

    // 检查表格数据是否完整
    for (let i = 0; i < lists.value.length; i++) {
      const item = lists.value[i];
      const rowNum = i + 1;

      if (!item.paySceneId) {
        ElMessage.warning(`第${rowNum}行：请选择支付场景`);
        return;
      }
      if (!item.payTypeId) {
        ElMessage.warning(`第${rowNum}行：请选择支付类型`);
        return;
      }
      if (!item.providerId) {
        ElMessage.warning(`第${rowNum}行：请选择供应商`);
        return;
      }
      if (!item.staffId) {
        ElMessage.warning(`第${rowNum}行：请选择人员`);
        return;
      }
      if (!item.accountListId) {
        ElMessage.warning(`第${rowNum}行：请选择账户`);
        return;
      }
      if (!item.amount) {
        ElMessage.warning(`第${rowNum}行：请填写金额`);
        return;
      }
      if (isNaN(Number(item.amount)) || Number(item.amount) <= 0) {
        ElMessage.warning(`第${rowNum}行：金额必须为大于0的数字`);
        return;
      }
    }

    const userStore = useUserStore();
    const tenantId = userStore.userInfo?.customParam?.tenantId || "";

    // 构造API参数
    const paramsList = lists.value.map(item => ({
      providerId: item.providerId,
      payScene: item.paySceneId,
      payType: item.payTypeId,
      unionId: item.unionId || "",
      staffId: item.staffId,
      accountListId: item.accountListId,
      amount: Number(item.amount),
      adjustType: props.popupType === 'addSupplementary' ? "1" : "0", // 根据弹窗类型设置adjustType, 1为补扣，0为退款
      remark: item.remark || "",
      tenantId: tenantId
    }));

    const res = await screenIndex.batchAdd(paramsList);
    if (res.code == "1") {
      ElMessage.success("提交成功");
      emit("closeBtn");
    }
  } catch (error) {
    console.error("表单提交失败:", error);
    ElMessage.error("提交失败");
  }
};

// 初始化数据
onMounted(() => {
  initData();
});

defineExpose({
  saveForm,
});
</script>



<style scoped lang="scss">
.stats-container {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  justify-content: center;
}

.stats-card {
  flex: 0 0 250px;
  display: flex;
  align-items: center;
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #eef2f5 100%);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.stats-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
}

.stats-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: rgba(64, 158, 255, 0.1);
  color: #409eff;
  font-size: 24px;
  margin-right: 15px;
}

.stats-content {
  flex: 1;
}

.stats-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 5px;
}

.stats-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  span{
    color: #c20000;
    font-size: 13px;
    padding-left: 4px;
  }
}
</style>
