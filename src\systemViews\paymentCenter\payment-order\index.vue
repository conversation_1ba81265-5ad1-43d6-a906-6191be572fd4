<!-- 支付订单 -->
<template>
  <div class="container-table-box">
    <Splitpanes class="default-theme">
      <Pane :size="100" :min-size="65">
        <el-card class="dep-card">
          <!-- 搜索区域 -->
          <dialog-search
            @getList="getList"
            formRowNumber="4"
            :columns="tabelForm.columns"
             :isShowRightBtn="$checkPermi(['pay:order:list'])"
          >
            <template v-slot:formList>
              <el-form
                :model="queryParams"
                ref="queryForm"
                :inline="true"
                label-width="90px"
              >
              <el-form-item label="模糊搜索">
                  <el-input
                    v-model="queryParams.staffName"
                    placeholder="请输入人员姓名、账号进行搜索"
                    clearable
                  />
                </el-form-item>
                <el-form-item label="租户" prop="tenantId">
                <TenantSelect v-model="queryParams.tenantId" :key="resetKey"></TenantSelect>
              </el-form-item>
                <el-form-item label="订单编号">
                  <el-input
                    v-model="queryParams.orderCode"
                    placeholder="请输入订单编号"
                    clearable
                  />
                </el-form-item>
                <!-- <el-form-item label="登录账号">
                  <el-input
                    v-model="queryParams.loginName"
                    placeholder="请输入登录账号"
                    clearable
                  />
                </el-form-item> -->
                <el-form-item label="账户名称">
                  <el-input
                    v-model="queryParams.accountName"
                    placeholder="请输入账户名称"
                    clearable
                  />
                </el-form-item>
                <el-form-item label="账户编码">
                  <el-input
                    v-model="queryParams.accountCode"
                    placeholder="请输入账户编码"
                    clearable
                  />
                </el-form-item>
                <el-form-item label="支付场景">
                  <el-select
                    v-model="queryParams.largeCategoryCode"
                    placeholder="请选择支付场景"
                    clearable
                    @change="handlePaySceneChange"
                  >
                    <el-option
                      v-for="item in paySceneOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="支付类型">
                  <el-select
                    v-model="queryParams.orderSubclassCode"
                    placeholder="请选择支付类型"
                    clearable
                  >
                    <el-option
                      v-for="item in payTypeOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="支付时间">
                  <el-date-picker
                    v-model="paymentTimeRange"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    value-format="YYYY-MM-DD"
                    @change="handlePaymentTimeChange"
                  />
                </el-form-item>
                <el-form-item label="订单日期">
                  <el-date-picker
                    v-model="orderTimeRange"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    value-format="YYYY-MM-DD"
                    @change="handleOrderTimeChange"
                  />
                </el-form-item>
                <!-- <el-form-item label="流水类型">
                  <el-select v-model="queryParams.flowType" clearable>
                    <el-option label="支出" value="expense" />
                    <el-option label="充值" value="recharge" />
                  </el-select>
                </el-form-item> -->
              </el-form>
            </template>

            <template v-slot:searchList>
              <el-button type="primary" icon="Search" @click="handleQuery" v-hasPermi="['pay:order:list']"
                >搜索</el-button
              >
              <el-button icon="Refresh" @click="resetQuery" v-hasPermi="['pay:order:list']">重置</el-button>
            </template>

            <template v-slot:searchBtnList>
              <el-button
              v-hasPermi="['pay:order:export']"
                type="primary"
                size="mini"
                icon="Download"
                @click="handleExport"
                >导出</el-button
              >
            </template>
          </dialog-search>

          <!-- 表格区域 -->
          <public-table
            ref="publictable"
            :rowKey="tabelForm.tableKey"
            :tableData="list"
            :columns="tabelForm.columns"
            :configFlag="tabelForm.tableConfig"
            :pageValue="pageParams"
            :total="total"
            :getList="getList"
             @clickTextDetail="clickTextDetail"
          >
          </public-table>
        </el-card>
      </Pane>
    </Splitpanes>
    <DialogBox :visible="diaWindow.open1" :dialogWidth="diaWindow.dialogWidth"
      :dialogFooterBtn="diaWindow.dialogFooterBtn"  @close="close"
      :dialogTitle="diaWindow.headerTitle" :dialogTop="diaWindow.dialogTop">
      <template #content>
    <faceInfo v-if="diaWindow.popupType == 'faceInfo'"   :rowData="diaWindow.rowData" ></faceInfo>
    </template>
    </DialogBox>
  </div>
</template>

<script setup name="paymentOrder">

import { screenIndex } from "@/api/paymentCenter/payment-order/index";
import { ref, reactive, getCurrentInstance, onMounted } from "vue";
import { formatMinuteTime } from "@/utils";
import { apiUrl } from "@/utils/config";
import useUserStore from "@/store/modules/user";
const { proxy } = getCurrentInstance();
const { recharge_status ,payment_status,trade_type,pay_mode} = proxy.useDict("recharge_status","payment_status","trade_type","pay_mode");
// 表格配置
const tabelForm = reactive({
  tableKey: "pay-order",
  columns: [

    {
      fieldIndex: "staffName",
      label: "人员姓名",
      minWidth: 120,
      sortable: true, // 对应列是否可以排序
      visible: true,
      type:'clickText'
    },
    // {
    //   fieldIndex: "loginName",
    //   label: "登录账号",
    //   minWidth: 120,
    //   sortable: true, // 对应列是否可以排序
    //   visible: true,
    // },
    {
      fieldIndex: "accountName",
      label: "账户名称",
      minWidth: 150,
      sortable: true, // 对应列是否可以排序
      visible: true,
    },
    // {
    //   fieldIndex: "accountCode",
    //   label: "账户编码",
    //   minWidth: 120,
    //   sortable: true, // 对应列是否可以排序
    //   visible: true,
    // },
    {
      fieldIndex: "largeCategory",
      label: "支付场景",
      minWidth: 120,
      sortable: true, // 对应列是否可以排序
      type: "tag",
      visible: true,
    },
    {
      fieldIndex: "orderSubclass",
      label: "支付类型",
      minWidth: 120,
      sortable: true, // 对应列是否可以排序
      type: "tag",
      visible: true,
    },
    {
      fieldIndex: "payAmount",
      label: "实付金额(元)",
      minWidth: 150,
      sortable: true, // 对应列是否可以排序
      align: "right",
      type: "dollor",
      visible: true,
    },
    {
      fieldIndex: "orderAmount",
      label: "订单金额(元)",
      minWidth: 150,
      align: "right",
      type: "dollor",
      sortable: true, // 对应列是否可以排序
      visible: true,
    },
    {
      fieldIndex: "paymentTime", // 对应列内容的字段名
      label: "支付时间", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "210px", //最小宽度%
      width: "", //宽度
    },
    {
      fieldIndex: "orderDate", // 对应列内容的字段名
      label: "订单日期", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "210px", //最小宽度%
      width: "", //宽度
    },
    {
      fieldIndex: "payMode", // 对应列内容的字段名
      label: "支付类型", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "120", //最小宽度%
      width: "", //宽度
      align: "", //表格对齐方式
      type: "dict",
      dictList: pay_mode,
    },
    {
      fieldIndex: "tradeType", // 对应列内容的字段名
      label: "订单类型", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "120", //最小宽度%
      width: "", //宽度
      align: "", //表格对齐方式
      type: "dict",
      dictList: trade_type,
    },
    {
      fieldIndex: "paymentStatus", // 对应列内容的字段名
      label: "支付状态", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "120", //最小宽度%
      width: "", //宽度
      align: "", //表格对齐方式
      type: "dict",
      dictList: payment_status,
    },
    {
      fieldIndex: "providerName",
      label: "供应商名称",
      minWidth: 150,
      sortable: true, // 对应列是否可以排序
      visible: true,
      align:'left'
    },
    {
      fieldIndex: "orderCode", // 对应列内容的字段名
      label: "订单编号", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "200", //最小宽度%
      width: "", //宽度
    },
    {
      fieldIndex: "refundOrderCode", // 对应列内容的字段名
      label: "退款订单编号", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "200", //最小宽度%
      width: "", //宽度
    },

    {
      fieldIndex: "remark",
      label: "订单备注",
      minWidth: 150,
      sortable: true, // 对应列是否可以排序
      visible: true,
      align:'left'
    },
  ],
  tableConfig: {
    needPage: true,
    index: true,
    selection: false,
    indexWidth: "60",
    loading: false,
    height: null,
  },
});
// 表格数据
const diaWindow = reactive({
  open1: false,
  headerTitle: "",
  popupType: "",
  rowData: "",
  dialogWidth: "30%",
  dialogFooterBtn: false,
  uoloadStatus: '',
  dialogTop:''
});

// 模拟数据
const list = ref([

]);
const userStore = useUserStore();
//组件挂载时保存初始的租户ID
const defaultTenantId = ref(userStore.userInfo.tenantId);
const resetKey = ref(0)  // 添加重置标识
// 查询参数
const queryParams = ref({
  tenantId: userStore.userInfo.tenantId,
});
const pageParams = ref({
  pageNum: 1,
  pageSize: 10,
});
const total = ref(3);

// 支付场景和支付类型选项
const paySceneOptions = ref([]);
const payTypeOptions = ref([]);

// 日期范围
const paymentTimeRange = ref([]);
const orderTimeRange = ref([]);

// 获取支付场景数据
const getPaySceneTree = () => {
  // 调用获取支付场景接口
  screenIndex.paySceneTree({}).then(response => {
    paySceneOptions.value = response.data.map(item => ({
      label: item.label,
      value: item.value,
      id: item.id
    }));
  });
};

// 支付场景变更事件
const handlePaySceneChange = (val) => {
  // 清空支付类型
  queryParams.value.orderSubclassCode = '';
  payTypeOptions.value = [];
  const selected = paySceneOptions.value.find(item => item.value === val);
  const id = selected ? selected.id : '';
  //调用获取支付类型接口
  getPayTypeTree(id)

};
// 获取支付类型
const getPayTypeTree = (val) => {
  // 调用获取支付类型接口
  screenIndex.payTypeTree({ id: val }).then(response => {
    payTypeOptions.value = response.data.map(item => ({
      label: item.label,
      value: item.value
    }));
  });
};
// 支付时间范围变更
const handlePaymentTimeChange = (val) => {
  if (val && val.length === 2) {
    queryParams.value.startTime = val[0];
    queryParams.value.endTime = val[1];
  } else {
    queryParams.value.startTime = null;
    queryParams.value.endTime = null;
  }
};

// 订单时间范围变更
const handleOrderTimeChange = (val) => {
  if (val && val.length === 2) {
    queryParams.value.orderStartTime = val[0];
    queryParams.value.orderEndTime = val[1];
  } else {
    queryParams.value.orderStartTime = null;
    queryParams.value.orderEndTime = null;
  }
};

// 方法保持与参考格式一致
const handleQuery = () => {
  pageParams.value.pageNum = 1;
  getList();
};

const resetQuery = () => {
  queryParams.value = {};
  paymentTimeRange.value = [];
  orderTimeRange.value = [];
  //设置当前租户为默认值
  queryParams.value.tenantId = defaultTenantId.value;
  resetKey.value++  // 改变 key 强制组件重建
  handleQuery();
};

const handleExport = () => {
  proxy.download(
    `pay${apiUrl}/pay/payPaymentOrder/export`,
    {
      ...queryParams.value,
    },
    `支付订单列表_${formatMinuteTime(new Date())}.xlsx`
  );
};

const getList = () => {
  tabelForm.tableConfig.loading = true;
  screenIndex.pageList(queryParams.value, pageParams.value).then((response) => {
    list.value = response.data.records;
    total.value = response.data.total;
    tabelForm.tableConfig.loading = false;
  });
};
/** 关闭弹窗 */
const close = (val) => {
  diaWindow.open1 = val;
};
// 人员信息
const clickTextDetail = (row) => {
  diaWindow.headerTitle = "人员信息";
  diaWindow.popupType = "faceInfo";
  diaWindow.rowData = row; // 如果需要传递数据到弹窗
  diaWindow.dialogFooterBtn = false;
  diaWindow.dialogWidth = "530px";
  diaWindow.dialogTop = '18vh'

  diaWindow.open1 = true;
};

onMounted(() => {
  getPaySceneTree();
  getPayTypeTree();
});

getList()
</script>

<style scoped></style>
