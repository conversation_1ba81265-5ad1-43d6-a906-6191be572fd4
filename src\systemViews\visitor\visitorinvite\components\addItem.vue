<template>
  <div  class="dialog-box dialog-box-edit">
        <el-form ref="formRef" :model="formData" label-width="100px" :rules="rules">
          <el-row>
            <el-col :span="24">
              <el-form-item v-if="addThingsType === '0'" label="车牌号码" prop="itemAttribute">
                <el-input
                  :disabled="popupType === 'view'"
                  placeholder="请输入车牌号码"
                  v-model="formData.itemAttribute"
                  clearable
                />
              </el-form-item>
              <el-form-item v-if="addThingsType === '1'" label="随行物品" prop="itemAttribute">
                <el-input
                  :disabled="popupType === 'view'"
                  placeholder="请输入随行物品"
                  v-model="formData.itemAttribute"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

      
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { insertVisitorItem, updateVisitorItem } from '@/api/visitor/visitorinvite/components/addItem'
import { getUUid } from '@/api/visitor/visitorinvite/components/addPerson'

const props = defineProps({
  popupType: {
    type: String,
    default: ''
  },
  rowData: {
    type: Object,
    default: () => ({})
  },
  addThingsType: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['submitClose'])

// 表单引用
const formRef = ref()
// 表单数据
const formData = reactive({
  id: '',
  inviteId: '',
  itemAttribute: '',
  itemType: ''
})

// 验证规则
const rules = reactive({
  itemAttribute: [{ required: true, message: '请输入随行信息', trigger: 'blur' }]
})

// 初始化数据
onMounted(() => {
  formData.inviteId = props.rowData.id
  if (props.popupType === 'addItem') {
    getUUid().then(res => {
      formData.id = res.id
    })
  }
  if (['EditItem', 'viewItem'].includes(props.popupType)) {
    Object.assign(formData, props.rowData)
  }
})

// 保存方法
const saveBtn = async () => {
  try {
    // 表单验证
    const valid = await formRef.value.validate()
    if (!valid) return

    formData.itemType = props.addThingsType
    
    const apiMethod = props.popupType === 'add' ? insertVisitorItem : updateVisitorItem
    const res = await apiMethod(formData)
    
    if (res.code === 200) {
      ElMessage.success({
        message: props.popupType === 'add' ? '保存成功' : '修改成功',
        type: 'success'
      })
      emit('submitClose')
    }
  } catch (error) {
    console.error('保存失败:', error)
  }
}

defineExpose({
  saveBtn
})
</script>

<style scoped lang="scss">

</style>