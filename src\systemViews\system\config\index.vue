<template>
  <div class="app-container">
    <el-card>
      <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          v-show="showSearch"
          label-width="68px"
          @submit.native.prevent
      >
        <el-form-item label="参数键名" prop="configCode">
          <el-input
              v-model="queryParams.configCode"
              placeholder="请输入参数键名"
              clearable
              style="width: 240px"
              @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['sys:base:config:add']">新增
          </el-button>
        </el-col>
        <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="configList">
        <el-table-column type="index"  label="序号" width="80"
        align="center" :index="indexMethod"  />
        <el-table-column
            label="参数键名"
            align="left"
            prop="configCode"
            :show-overflow-tooltip="true">
          <template #default="scope">
            <a @click="copyToClipboard(scope.row.configCode)">{{ scope.row.configCode }}</a>
          </template>
        </el-table-column>
        <el-table-column
            label="参数键值"
            align="left"
            prop="configValue"
            :show-overflow-tooltip="true"
        >
          <template #default="scope">
            <a @click="copyToClipboard(scope.row.configValue)">{{ scope.row.configValue }}</a>
          </template>
        </el-table-column>
        <el-table-column label="扩展A" align="left" prop="attra" :show-overflow-tooltip="true"/>
        <el-table-column label="扩展B" align="left" prop="attrb" :show-overflow-tooltip="true"/>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                       v-hasPermi="['sys:base:config:edit']"
                       :loading="reloadId === scope.row.configId && reloadType === 'edit'">修改
            </el-button>
            <el-button link icon="Delete" @click="handleDelete(scope.row)" type="primary"
                       v-hasPermi="['sys:base:config:remove']"
                       :loading="reloadId === scope.row.configId && reloadType === 'remove'">删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
      />
    </el-card>

    <!-- 添加或修改参数配置对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="参数键名" prop="configCode">
          <el-input v-model="form.configCode" placeholder="请输入参数键名"/>
        </el-form-item>
        <el-form-item label="参数键值" prop="configValue">
          <el-input v-model="form.configValue" placeholder="请输入参数键值"/>
        </el-form-item>
        <el-form-item label="扩展A" prop="attra">
          <el-input v-model="form.attra" type="textarea" placeholder="请输入内容"/>
        </el-form-item>
        <el-form-item label="扩展B" prop="attrb">
          <el-input v-model="form.attrb" type="textarea" placeholder="请输入内容"/>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm" :loading="saveLoading">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Config">
import {
  listConfig,
  getConfig,
  delConfig,
  addConfig,
  updateConfig,
} from "@/api/system/config";
import {ref} from "vue";
import {copyToClipboard} from "@/utils";

const {proxy} = getCurrentInstance();
// 遮罩层
const loading = ref(true);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 参数表格数据
const configList = ref([]);
// 弹出层标题
const title = ref("");
// 是否显示弹出层
const open = ref(false);
// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  configCode: undefined
});
// 表单参数
const form = ref({});
// 表单校验
const rules = {
  configCode: [{required: true, message: "参数键名不能为空", trigger: "blur"}],
  configValue: [{required: true, message: "参数键值不能为空", trigger: "blur"}],
};
const reloadId = ref(undefined);
const reloadType = ref(undefined);
const saveLoading = ref(false);

const indexMethod = (index) => {
  return (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1;
};
/** 查询参数列表 */
function getList() {
  loading.value = true;
  listConfig(queryParams.value).then((response) => {
    configList.value = response.data.records;
    total.value = response.data.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    configId: undefined,
    configCode: undefined,
    configValue: undefined,
    attra: undefined,
    attrb: undefined,
  };
  proxy.resetForm("formRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryForm");
  handleQuery();
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加参数";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const configId = row.configId;
  reloadId.value = configId;
  reloadType.value = "edit";
  getConfig(configId).then((response) => {
    reloadId.value = undefined;
    reloadType.value = undefined;
    if (response.data) {
      form.value = response.data;
      open.value = true;
      title.value = "修改参数";
    } else {
      proxy.$modal.msgError("数据异常！");
    }
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      saveLoading.value = true;
      if (form.value.configId != undefined) {
        updateConfig(form.value).then((response) => {
          if (!response.success) {
            proxy.$modal.msgError(response.message);
            saveLoading.value = false;
          } else {
            proxy.$modal.msgSuccess("修改成功");
            open.value = false;
            getList();
            saveLoading.value = false;
          }
        });
      } else {
        addConfig(form.value).then((response) => {
          if (!response.success) {
            proxy.$modal.msgError(response.message);
            saveLoading.value = false;
          } else {
            proxy.$modal.msgSuccess("新增成功");
            open.value = false;
            getList();
            saveLoading.value = false;
          }
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const configId = row.configId;
  reloadId.value = configId;
  reloadType.value = "remove";
  proxy.$modal.confirm('是否确认删除参数键名为"' + row.configCode + '"的数据项?')
      .then(function () {
        return delConfig(configId);
      })
      .then(() => {
        reloadId.value = undefined;
        reloadType.value = undefined;
        getList();
        proxy.$modal.msgSuccess("删除成功");
      })
      .catch(() => {
        reloadId.value = undefined;
        reloadType.value = undefined;
      });
}

getList();
</script>

<style scoped>

</style>
