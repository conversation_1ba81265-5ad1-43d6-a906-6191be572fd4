import request from '@/utils/request'

// 分页查询
export function getPageDesignerPage(data) {
  return request({
    url: '/cms/pageDesigner/page',
    method: 'post',
    data: data
  })
}

// 查询单个
export function getPageDesigner(id) {
  return request({
    url: '/cms/pageDesigner/get',
    method: 'get',
    params:{
      id: id
    }
  })
}

// 新增
export function addPageDesigner(data) {
  return request({
    url: '/cms/pageDesigner/add',
    method: 'post',
    data: data
  })
}

// 修改
export function updatePageDesigner(data) {
  return request({
    url: '/cms/pageDesigner/update',
    method: 'post',
    data: data
  })
}

export function updatePageMenuStatus(data) {
  return request({
    url: '/cms/pageDesigner/updateMenuStatus',
    method: 'post',
    params: data
  })
}

// 删除
export function deletePageDesigner(id) {
  return request({
    url: '/cms/pageDesigner/delete',
    method: 'get',
    params:{
      id: id
    }
  })
}

// 复制
export function copyPageDesigner(id) {
  return request({
    url: '/cms/pageDesigner/copy',
    method: 'get',
    params:{
      id: id
    }
  })
}
