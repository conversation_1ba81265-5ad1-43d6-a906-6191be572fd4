<!-- 临时车辆修改 -->
<template>
  <div>
    <div
        class="dialog-box"
        :class="popupType === 'view' ? 'dialog-box-view' : 'dialog-box-edit'"
    >
      <!-- 使用 el-form 包裹表单内容 -->
      <el-form
          ref="ruleformRef"
          :model="formData"
          label-width="80px"
          :rules="popupType != 'view' ? rules : {}"
      >
        <!-- 第一行：车牌号码和人员姓名 -->
        <el-row>
          <el-col :span="12">
            <el-form-item label="车牌号码" prop="plateNo">
              <!-- 新增模式显示输入框，其他模式显示文本 -->
              <!-- <el-input v-if="popupType !== 'view'" v-model="formData.plateNo" @input="val => formData.plateNo = val.toUpperCase()" placeholder="请输入车牌号码" clearable /> -->
                <el-input v-if="popupType !== 'view'" v-model="formData.plateNo" @input="val => { formData.plateNo = val.toUpperCase();  }" placeholder="请输入车牌号码" clearable />
              <div v-else>{{ formData.plateNo || "" }}</div>
            </el-form-item>
          </el-col>

          <el-col :span="12" v-if="popupType !== 'view'">
            <el-form-item prop="plateNo">
              <!-- 新增模式显示输入框，其他模式显示文本 -->
              <div style="position: relative;left: -70px;">
                <plate-input v-model="formData.plateNo" />
              </div>


            </el-form-item>
          </el-col>
      
          <el-col :span="12">
            <el-form-item label="人员姓名" prop="staffName">
              <!-- 远程搜索选择器 -->

              <el-input
                   v-if="popupType !== 'view'"
                  v-model="formData.staffName"
                  placeholder="请输入人员姓名"
                  clearable
              />
            
              <div v-else>{{ formData.staffName || "-" }}</div>
            </el-form-item>
          </el-col>

          <!-- 第二行：手机号码和车辆状态 -->
          <el-col :span="12">
            <el-form-item label="手机号码" prop="cellphone">
              <el-input
                  v-if="popupType !== 'view'"
                  v-model="formData.cellphone"
                  placeholder="请输入手机号码"
                  clearable
              />
              <div v-else>{{ formData.cellphone || "" }}</div>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="人员部门" prop="orgName">
              <el-input
                   v-if="popupType !== 'view'"
                  v-model="formData.orgName"
                  placeholder="请输入人员部门"
                  clearable
              />
              <div v-else>{{ formData.orgName || "" }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="车辆状态" prop="carStatus">
              <!-- 使用字典类型渲染选项 -->
             

              <el-radio-group v-model="formData.carStatus" v-if="popupType !== 'view'">
                <el-radio-button v-for="(item, index) in car_status" :key="index"
                  :label="item.value">{{ item.label }}</el-radio-button>
              </el-radio-group>
              <div v-else>
                {{ formatCommon(formData.carStatus, car_status) }}
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="车牌颜色" prop="plateColor">
              <el-select
                  v-if="popupType !== 'view'"
                  v-model="formData.plateColor"
                  placeholder="请选择车牌颜色"
                  clearable
              >
                <el-option
                    v-for="(item, index) in plate_color"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                />
              </el-select>
              <div v-else>
                {{
                  formatCommon(
                      formData.plateColor,
                      plate_color
                  )
                }}
              </div>
            </el-form-item>
          </el-col>
          <!-- 第三行：车辆编组和有效期限 -->
          <el-col :span="12">
            <el-form-item label="车辆编组" prop="vehicleType">
              <el-select
                  v-if="popupType !== 'view'"
                  v-model="formData.vehicleType"
                  placeholder="请选择车辆编组"
                  clearable
              >
                <el-option
                    v-for="(item, index) in listData"
                    :key="index"
                    :label="item.dictLabel"
                    :value="item.dictValue"
                />
              </el-select>
              <div v-else>
                {{ formatCommonDict(formData.vehicleType, listData) }}
              </div>
            </el-form-item>
          </el-col>
          <!-- 第三行：车辆类型和有效期限 -->
          <el-col :span="12">
            <el-form-item label="车辆类型" prop="carType">
              <el-select
                  v-if="popupType !== 'view'"
                  v-model="formData.carType"
                  placeholder="请选择车辆类型"
                  clearable
              >
                <el-option
                    v-for="(item, index) in car_type"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                />
              </el-select>
              <div v-else>
                {{ $formatDictLabel(formData.carType, car_type) }}
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="有效期限">
              <el-date-picker
                  v-if="popupType !== 'view'"
                  v-model="formData.validDate"
                  value-format="YYYY-MM-DD"
                  type="date"
                  placeholder="选择截止日期"
              />
              <div v-else>{{ formData.validDate || "" }}</div>
            </el-form-item>
          </el-col>

          <!-- 第四行：车辆品牌和品牌型号 -->
          <el-col :span="12">
            <el-form-item label="车辆品牌" prop="carBrand">
              <el-input
                  v-if="popupType !== 'view'"
                  v-model="formData.carBrand"
                  placeholder="请输入车辆品牌"
                  clearable
              />
              <div v-else>{{ formData.carBrand || "" }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="品牌型号" prop="carModel">
              <el-input
                  v-if="popupType !== 'view'"
                  v-model="formData.carModel"
                  placeholder="请输入品牌型号"
                  clearable
              />
              <div v-else>{{ formData.carModel || "" }}</div>
            </el-form-item>
          </el-col>

          <!-- 第五行：车辆规格和车辆颜色 -->
          <el-col :span="12">
            <el-form-item label="车辆规格" prop="vehicleSpecification">
              <el-select
                  v-if="popupType !== 'view'"
                  v-model="formData.vehicleSpecification"
                  placeholder="请选择车辆规格"
                  clearable
              >
                <el-option
                    v-for="(item, index) in vehicle_specification"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                />
              </el-select>
              <div v-else>
                {{
                  formatCommon(
                      formData.vehicleSpecification,
                      vehicle_specification
                  )
                }}
              </div>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="车辆颜色" prop="carColor">
              <el-input
                  v-if="popupType !== 'view'"
                  v-model="formData.carColor"
                  placeholder="请输入车辆颜色"
                  clearable
              />
              <div v-else>{{ formData.carColor || "" }}</div>
            </el-form-item>
          </el-col>

          <!-- 第六行：车辆备注 -->
          <el-col :span="24">
            <el-form-item label="车辆备注" prop="remark">
              <el-input
                  v-if="popupType !== 'view'"
                  v-model="formData.remark"
                  placeholder="请输入车辆备注"
                  clearable
              />
              <div v-else>{{ formData.remark || "" }}</div>
            </el-form-item>
          </el-col>

          <!-- 第七行：图片上传 -->
          <el-col :span="24">
            <el-form-item label="上传图像" prop="photo">
              <!-- 自定义图片上传组件 -->
              <ImageUpload
                  v-if="popupType !== 'view'"
                  v-model="fileList"
                  limit="1"
                  fileSize="10"
                  :paramsData="imageExtraData"
                  :uploadImgUrl="
                '/park' +  apiUrls + '/majorParking/userCar/uploadFile'
                "
              />
              <!-- 查看模式显示图片 -->
              <!-- 查看模式显示图片 -->
              <PreviewImage style="width: 150px;" v-if="popupType === 'view' && formData.photo"
                            :photo-id="formData.photo"></PreviewImage>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import {ref, onMounted, watch, defineProps, defineEmits} from "vue";
import {ElMessage} from "element-plus";
import {screenIndex} from "@/api/majorParking/userCar";
import {getTenants} from "@/api/tenant/tenant";
import {appCode} from "@/utils/config";
import {apiUrl} from "@/utils/config";
import PlateInput from '@/components/plateInput/index.vue';
// ===================== 属性定义 =====================
const props = defineProps({
  closeBtn: Function, // 关闭回调函数
  list: {
    // 车辆类型列表
    type: Array,
    default: () => [],
  },
  popupType: {
    // 弹窗类型: add/edit/view
    type: String,
    default: "",
  },
  rowData: {
    // 行数据
    type: Object,
    default: () => ({}),
  },
  fileUrl: {
    // 文件基础路径
    type: String,
    default: "",
  },
});
const emit = defineEmits(["submitClose"]); // 定义事件

// ===================== 响应式数据 =====================
const apiUrls = ref(apiUrl);
const ruleformRef = ref(null); // 表单引用
const formData = ref({
  carStatus: '0',
  vehicleType: '5'
}); // 表单数据
const applyUserList = ref([]); // 申请人列表
const listData = ref([]);
listData.value = props.list;
// 文件列表
const fileList = ref([]);
const srcList = ref([]); // 图片预览列表
const imageExtraData = ref({
  // 图片上传额外参数
  appCode: appCode,
});

// 字典数据
const {proxy} = getCurrentInstance();
const {car_status, vehicle_specification, plate_color, car_type} = proxy.useDict(
    "car_status",
    "vehicle_specification",
    "plate_color",
    "car_type"
);

// 表单校验规则
const rules = {
  // plateNo: [
  //   {
  //     required: true,
  //     pattern:
  //         /^(([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z]([DF][A-HJ-NP-Z0-9]{5}|[A-HJ-NP-Z0-9]{5}[DF]|[A-HJ-NP-Z0-9]{5}))|(WJ[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领]?[0-9]{4,5})|([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][0-9]{4}警))$/,
  //     message: "请输入有效车牌号码",
  //     trigger: "blur",
  //   },
  // ],
  plateNo: [
  { required: true, message: "请输入车牌号", trigger: "blur" }
  ],
  staffName: [{required: true, message: "请输入人员姓名", trigger: "blur"}],
  cellphone: [{required: true, message: "请输入手机号码", trigger: "blur"}],
  carStatus: [{required: true, message: "请选择车辆状态", trigger: "blur"}],
  vehicleType: [{required: true, message: "请选择车辆编组", trigger: "blur"}],
  carType: [{required: true, message: "请选择车辆类型", trigger: "blur"}],
  plateColor: [{required: true, message: "请选择车牌颜色", trigger: "blur"}],
};

// ===================== 生命周期钩子 =====================
onMounted(() => {
  initFormData();
  if (props.popupType === "add") {
    getUUId();
  }
});

// ===================== 方法定义 =====================
// 初始化表单数据
const initFormData = () => {
  if (props.popupType !== "add") {
    formData.value = {...props.rowData};

    if (formData.value.photo) {
      getFileInfoById();
    }
  }
};

const getFileInfoById = async () => {
  try {
    const res = await screenIndex.getFileInfoById(formData.value.photo);
    if (res.code == "1") {
      fileList.value = [{name: res.data.fileName, url: res.data.url,fileId:res.data.fileId}];
      srcList.value = [res.data.url]
    }
  } catch (error) {
    console.error("获取用户列表失败:", error);
  }
};

// 获取UUID（用于图片上传）
const getUUId = async () => {
  try {
    const res = await screenIndex.getUUId();
    // imageExtraData.value.businessId = res.businessId;
    formData.value.id = res.businessId;
  } catch (error) {
    console.error("获取UUID失败:", error);
  }
};

// 获取用户列表（远程搜索）
const getUserList = async (query) => {
  if (!query || query.trim().length < 2) {    
    applyUserList.value = [];
    return;
  }
  try {
    const res = await screenIndex.selectApplyUserList({
      staffName: query,
    });
    applyUserList.value = res.data;
  } catch (error) {
    console.error("获取用户列表失败:", error);
  }
};

// 选择用户回调
const changeUser = () => {
  const selectedUser = applyUserList.value.find(
      (user) => user.staffId === formData.value.staffId
  );
  if (selectedUser) {
    formData.value.staffName = selectedUser.staffName;
    formData.value.cellphone = selectedUser.cellphone;
    formData.value.loginName = selectedUser.loginName;
    formData.value.orgName = selectedUser.orgName;
  }
};

// 图片预览
const previewImage = (url) => {
  srcList.value = [url];
};

// 保存表单
const saveBtn = async () => {
  try {
    // 表单验证
    const valid = await ruleformRef.value.validate();
    if (!valid) return;

    if (fileList.value.length > 0) {
      formData.value.photo = fileList.value[0].fileId;
    } else {
      formData.value.photo = "";
    }
    // 根据模式调用不同API
    const apiMethod =
        props.popupType === "add"
            ? screenIndex.addTemporaryCar
            : screenIndex.updataUserCar;

    const {code, message} = await apiMethod(formData.value);
    if (code == "1") {
      ElMessage.success(props.popupType === "add" ? "新增成功" : "修改成功");
      emit("submitClose");
    }
  } catch (error) {
    // console.error("保存失败:", error);
    // ElMessage.error("操作失败，请稍后重试");
  }
};

// ===================== 通用格式化方法 =====================
// 字典通用格式化
const formatCommon = (value, dict) => {
  return dict.find((item) => item.value === value)?.label || "";
};

// 自定义字典格式化
const formatCommonDict = (value, dict) => {
  return dict.find((item) => item.dictValue === value)?.dictLabel || "";
};
const getTenantLoading = ref(false);
const tenantList = ref([]);

// 获取租户列表
function initTenantList(tenantName) {
  getTenantLoading.value = true;
  let query = {};
  if (tenantName !== undefined && tenantName !== "") {
    query.tenantName = tenantName;
    query.tenantId = undefined;
  } else {
    query.tenantId = formData.value.tenantId;
  }
  getTenants(query)
      .then((response) => {
        tenantList.value = response.data;
      })
      .finally(() => {
        getTenantLoading.value = false;
      });
}

watch(
  () => formData.value.plateNo,
  (newVal) => {
    if (!newVal) return;
    // 自动转大写并更新值
    formData.value.plateNo = newVal.toUpperCase();
    // 根据长度设置车牌颜色
    formData.value.plateColor = newVal.length >= 8 ? '4' : '1';
  }
);
initTenantList();
getUserList();
defineExpose({
  saveBtn,
});
</script>

<style scoped lang="scss">
</style>