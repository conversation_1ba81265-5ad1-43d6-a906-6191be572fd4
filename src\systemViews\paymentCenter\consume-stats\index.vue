<!-- 消费统计 -->
<template>
  <div class="container-table-box">
    <Splitpanes class="default-theme">
      <Pane :size="100" :min-size="65">
        <el-card class="dep-card">
          <!-- 搜索区域 -->
          <dialog-search
            @getList="getList"
            formRowNumber="4"
            :columns="tabelForm.columns"
             :isShowRightBtn="$checkPermi(['pay:stats:list'])"
          >
            <template v-slot:formList>
              <el-form
                :model="queryParams"
                ref="queryForm"
                :inline="true"
                label-width="90px"
              >
                <el-form-item label="租户" prop="tenantId">
                  <TenantSelect v-model="queryParams.tenantId" :key="resetKey" @change="handleTentantChange"></TenantSelect>
                </el-form-item>
                <el-form-item label="供应商">
                  <el-select
                    v-model="queryParams.providerId"
                    placeholder="请选择供应商"
                    clearable
                    @change="handleProviderChange"
                  >
                    <el-option
                      v-for="item in providerOptions"
                      :key="item.id"
                      :label="item.label"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="支付场景">
                  <el-select
                    v-model="queryParams.payScene"
                    placeholder="请选择支付场景"
                    clearable
                    @change="handlePaySceneChange"
                  >
                    <el-option
                      v-for="item in paySceneOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
                <!-- <el-form-item label="支付类型">
                  <el-select
                    v-model="queryParams.payType"
                    placeholder="请选择支付类型"
                    clearable
                  >
                    <el-option
                      v-for="item in payTypeOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item> -->
                <el-form-item label="账户">
                  <el-select
                    v-model="queryParams.accountCode"
                    placeholder="请选择账户"
                    clearable
                  >
                    <el-option
                      v-for="item in accountOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="支付时间">
                  <el-date-picker
                    v-model="paymentTimeRange"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    value-format="YYYY-MM-DD"
                    @change="handlePaymentTimeChange"
                  />
                </el-form-item>
              </el-form>
            </template>

            <template v-slot:searchList>
              <el-button type="primary" icon="Search" @click="handleQuery" v-hasPermi="['pay:stats:list']"
                >搜索</el-button
              >
              <el-button icon="Refresh" @click="resetQuery" v-hasPermi="['pay:stats:list']">重置</el-button>
            </template>

            <template v-slot:searchBtnList>
              <el-button
              v-hasPermi="['pay:stats:export']"
                type="primary"
                size="mini"
                icon="Download"
                @click="handleExport"
                >导出</el-button
              >
            </template>
          </dialog-search>

          <!-- 表格区域 -->
          <public-table
            ref="publictable"
            :rowKey="tabelForm.tableKey"
            :tableData="list"
            :columns="tabelForm.columns"
            :configFlag="tabelForm.tableConfig"
            :pageValue="pageParams"
            :total="total"
            :getList="getList"
          >
          </public-table>
        </el-card>
      </Pane>
    </Splitpanes>
  </div>
</template>

<script setup name="payCounsumeStats">

import { screenIndex } from "@/api/paymentCenter/consume-stats/index";
import { ref, reactive, getCurrentInstance, onMounted } from "vue";
import { formatMinuteTime } from "@/utils";
import { apiUrl } from "@/utils/config";
import useUserStore from "@/store/modules/user";
const { proxy } = getCurrentInstance();
const {} = proxy.useDict();
// 表格配置
const tabelForm = reactive({
  tableKey: "consume-stats",
  columns: [

    {
      fieldIndex: "accountCode",
      label: "账户编码",
      minWidth: 120,
      sortable: true, // 对应列是否可以排序
      visible: true,
    },
    {
      fieldIndex: "accountName",
      label: "账户名称",
      minWidth: 150,
      sortable: true, // 对应列是否可以排序
      visible: true,
    },
    {
      fieldIndex: "providerCode",
      label: "供应商编码",
      minWidth: 130,
      sortable: true, // 对应列是否可以排序
      visible: true,
    },
    {
      fieldIndex: "providerName",
      label: "供应商名称",
      minWidth: 150,
      sortable: true, // 对应列是否可以排序
      visible: true,
    },
    {
      fieldIndex: "payScene",
      label: "场景编码",
      minWidth: 120,
      sortable: true, // 对应列是否可以排序
      visible: true,
    },
    {
      fieldIndex: "paySceneName",
      label: "场景名称",
      minWidth: 120,
      sortable: true, // 对应列是否可以排序
      visible: true,
    },
    {
      fieldIndex: "payType",
      label: "类型编码",
      minWidth: 120,
      sortable: true, // 对应列是否可以排序
      visible: true,
    },
    {
      fieldIndex: "payTypeName",
      label: "类型名称",
      minWidth: 120,
      sortable: true, // 对应列是否可以排序
      visible: true,
    },
    {
      fieldIndex: "consumeCount",
      label: "消费笔数",
      minWidth: 120,
      sortable: true, // 对应列是否可以排序
      visible: true,
      type: "showZero"
    },
    {
      fieldIndex: "consumeAmount",
      label: "消费总金额(元)",
      minWidth: 150,
      sortable: true, // 对应列是否可以排序
      align: "right",
      type: "dollor",
      visible: true,
    },
    {
      fieldIndex: "discountCount",
      label: "优惠笔数",
      minWidth: 120,
      sortable: true, // 对应列是否可以排序
      visible: true,
      type: "showZero"
    },
    {
      fieldIndex: "discountAmount",
      label: "优惠总金额(元)",
      minWidth: 150,
      sortable: true, // 对应列是否可以排序
      align: "right",
      type: "dollor",
      visible: true,
    },
    {
      fieldIndex: "refundCount",
      label: "退款笔数",
      minWidth: 120,
      sortable: true, // 对应列是否可以排序
      visible: true,
      type: "showZero"
    },
    {
      fieldIndex: "refundAmount",
      label: "退款总金额(元)",
      minWidth: 150,
      sortable: true, // 对应列是否可以排序
      align: "right",
      type: "dollor",
      visible: true,
    },
    {
      fieldIndex: "orderAmount",
      label: "订单金额(元)",
      minWidth: 150,
      align: "right",
      type: "dollor",
      sortable: true, // 对应列是否可以排序
      visible: true,
    },
  ],
  tableConfig: {
    needPage: true,
    index: true,
    selection: false,
    indexWidth: "60",
    loading: false,
    height: null,
  },
});

// 消费统计数据
const list = ref([]);
const userStore = useUserStore();
// 组件挂载时保存初始的租户ID
const defaultTenantId = ref(userStore.userInfo.tenantId);
// 添加重置标识
const resetKey = ref(0)  
// 查询参数
const queryParams = ref({
  tenantId: userStore.userInfo.tenantId,
});
const pageParams = ref({
  pageNum: 1,
  pageSize: 10,
});
const total = ref(3);
// 供应商选项
const providerOptions = ref([]);
// 支付场景和支付类型选项
const paySceneOptions = ref([]);
const payTypeOptions = ref([]);
// 账户选项
const accountOptions = ref([]);

// 日期范围
const paymentTimeRange = ref([]);

// 租户变更事件
const handleTentantChange = () => {
  // 清空账户
  queryParams.value.accountCode = '';
  accountOptions.value = [];
  // 获取租户id
  const tenantId = queryParams.value.tenantId;
  // 获取供应商id
  const providerId = queryParams.value.providerId;
  // 判断租户和供应商都不为空
  if(tenantId && providerId){
    let params = {
      id: tenantId,
      providerId: providerId
    }
    // 调用获取账户接口
    screenIndex.accountTree(params).then(response => {
      accountOptions.value = response.data.map(item => ({
        label: item.label,
        value: item.value
      }));
    });
  }
}

// 获取供应商数据
const getProviderTree = () => {
   // 调用获取供应商接口
  screenIndex.providerTree({}).then(response => {
    providerOptions.value = response.data.map(item => ({
      label: item.label,
      id: item.id
    }));
  });
};

// 供应商变更事件
const handleProviderChange = (val) => {
  // 清空支付场景
  queryParams.value.payScene = '';
  paySceneOptions.value = [];
  // 清空账户
  queryParams.value.accountCode = '';
  accountOptions.value = [];
  // 获取租户id
  const tenantId = queryParams.value.tenantId;
  if(val){
    // 调用获取支付场景接口
    screenIndex.paySceneTree({id: val}).then(response => {
      paySceneOptions.value = response.data.map(item => ({
        label: item.label,
        value: item.value,
        id: item.id
      }));
    });
  }
  // 判断租户和供应商都不为空
  if(tenantId && val){
    let params = {
      id: tenantId,
      providerId: val
    }
    // 调用获取账户接口
    screenIndex.accountTree(params).then(response => {
      accountOptions.value = response.data.map(item => ({
        label: item.label,
        value: item.value
      }));
    });
  }
};

// 支付场景变更事件
const handlePaySceneChange = (val) => {
  // 清空支付类型
  queryParams.value.payType = '';
  payTypeOptions.value = [];
  // if (val) {
  //   const selected = paySceneOptions.value.find(item => item.value === val);
  //   const id = selected ? selected.id : '';
  //   // 调用获取支付类型接口
  //   screenIndex.payTypeTree({ id: id }).then(response => {
  //     payTypeOptions.value = response.data.map(item => ({
  //       label: item.label,
  //       value: item.value
  //     }));
  //   });
  // }
};

// 支付时间范围变更
const handlePaymentTimeChange = (val) => {
  if (val && val.length === 2) {
    queryParams.value.paymentStartTime = val[0];
    queryParams.value.paymentEndTime = val[1];
  } else {
    queryParams.value.paymentStartTime = null;
    queryParams.value.paymentEndTime = null;
  }
};

// 方法保持与参考格式一致
const handleQuery = () => {
  pageParams.value.pageNum = 1;
  getList();
};

const resetQuery = () => {
  queryParams.value = {};
  paymentTimeRange.value = [];
  //设置当前租户为默认值
  queryParams.value.tenantId = defaultTenantId.value;
  resetKey.value++  // 改变 key 强制组件重建
  handleQuery();
};

const handleExport = () => {
  proxy.download(
    `pay${apiUrl}/pay/stats/export`,
    {
      ...queryParams.value,
    },
    `消费统计列表_${formatMinuteTime(new Date())}.xlsx`
  );
};

const getList = () => {
  tabelForm.tableConfig.loading = true;
  screenIndex.pageList(queryParams.value, pageParams.value).then((response) => {
    list.value = response.data.records;
    total.value = response.data.total;
    tabelForm.tableConfig.loading = false;
  });
};

onMounted(() => {
  getProviderTree();
});

getList()
</script>

<style scoped></style>
