<!-- 门禁分组 -->
<template>
    <div class="container-table-box">
        <el-card>
            <el-row :gutter="24">
                <el-col :span="24" :xs="24">
                    <dialog-search @getList="getList" formRowNumber="4" v-model:isTable="isTable"
                        :columns="tabelForm.columns" :isShowRightBtn="$checkPermi(['access:group:list'])">
                        <template #formList>
                            <el-form :inline="true" :model="formData" ref="queryForm" size="medium" label-width="90px">

                                <el-form-item label="分组名称" prop="groupName">
                                    <el-input placeholder="请输入分组名称" v-model="formData.groupName" clearable></el-input>
                                </el-form-item>

                            </el-form>
                        </template>

                        <template #searchList>
                            <el-button class="search" type="primary" icon="Search" size="small" @click="handleQuery"
                                v-hasPermi="['access:group:list']">搜索</el-button>
                            <el-button class="reset" icon="Refresh" size="small" @click="resetQuery"
                                v-hasPermi="['access:group:list']">重置</el-button>
                        </template>

                        <template v-slot:searchBtnList  >
              <el-button type="primary"  icon="Plus" @click="handleAdd" v-hasPermi="['access:group:add']">新增
              </el-button>

              <el-button  icon="Download"    v-hasPermi="['access:group:export']"
                @click="handleExport">导出
              </el-button>

            </template>
                    </dialog-search>

                    <public-table ref="publictable" :rowKey="tabelForm.tableKey" :tableData="dataList"
                        :columns="tabelForm.columns" :configFlag="tabelForm.tableConfig" :pageValue="pageParams"
                        :total="total" :getList="getList">
                        <template #operation="{ scope }">
                            <el-button size="mini" type="text" icon="View" @click="handleView(scope.row)" title="查看"
                                v-hasPermi="['access:group:list']"></el-button>

                            <el-button link icon="Edit" type="primary" title="修改" @click="handleUpdate(scope.row)"  v-hasPermi="['access:group:edit']"
                               >
                            </el-button>
                            <el-button size="mini" type="text" icon="Delete" @click="handleDelete(scope.row)"
                                v-hasPermi="['access:group:remove']" title="删除"></el-button>
                        </template>
                    </public-table>


                </el-col>
            </el-row>
        </el-card>
        <DialogBox :visible="diaWindow.open1" :dialogWidth="diaWindow.dialogWidth" :dialogFooterBtn="diaWindow.dialogFooterBtn"
            @cancellation="cancellation" @close="close" :dialogTitle="diaWindow.dialogTitle" :dialogTop="diaWindow.dialogTop"   @save="save">
            <template #content>
                <addDom ref="addRef" @submitClose="submitClose" :row-data="diaWindow.rowData"
                    :popup-type="diaWindow.popupType" />
            </template>
        </DialogBox>
    </div>
</template>

<script setup name="group">
import { ref, reactive, onMounted, getCurrentInstance,nextTick  } from "vue";
import { screenIndex } from "@/api/admittance/group";
import useUserStore from "@/store/modules/user";
import addDom from './components/add'
import { ElMessage,ElMessageBox } from "element-plus";
import { apiUrl } from "@/utils/config";
import { formatMinuteTime } from "@/utils";
const userStore = useUserStore();
// 响应式数据
const dataList = ref([]);
const total = ref(0);
const publictable = ref(null);

// 表单数据
const formData = reactive({
    tenantId: userStore.userInfo.tenantId,
});

// 分页参数
const pageParams = reactive({
    pageNum: 1,
    pageSize: 10,
});
const addRef = ref(null)
const diaWindow = reactive({
    open1: false,
    popupType: "view",
    rowData: "",
    dialogWidth: "50%",
    dialogTitle: ''
});
// 字典

const { proxy } = getCurrentInstance();
const {group_status}=proxy.useDict("group_status")
// 表格配置
const tabelForm = reactive({
    tableKey: "1",
    tableConfig: {
        needPage: true,
        index: true,
        selection: false,
        reserveSelection: false,
        indexFixed: false,
        selectionFixed: false,
        indexWidth: "50",
        loading: false,
        showSummary: false,
        height: null,
    },
    columns: [
        {
            fieldIndex: "groupName",
            label: "分组名称",
            resizable: true,
            minWidth: "150px",
            visible: true,
            sortable: true,
            align: "left",
        },
        {
            fieldIndex: "tenantName",
            label: "租户",
            resizable: true,
            minWidth: "120px",
            visible: true,
            sortable: true,
            align: "left",
        },
        {
            fieldIndex: "count",
            label: "人员数量",
            resizable: true,
            minWidth: "100px",
            visible: true,
            sortable: true,
            align: "center",
        },
        {
            fieldIndex: "status", // 对应列内容的字段名
            label: "是否启用",
            width: "110",
            type: "dict",
            visible: true,
            dictList: group_status,
        },
        {
            fieldIndex: "operator", // 对应列内容的字段名
            label: "操作人", // 显示的标题
            resizable: true, // 对应列是否可以通过拖动改变宽度
            visible: true, // 展示与隐藏
            sortable: true, // 对应列是否可以排序
            fixed: "", //固定
            minWidth: "120", //最小宽度%
            width: "", //宽度
        },
        {
            fieldIndex: "updateDate", // 对应列内容的字段名
            label: "操作时间", // 显示的标题
            resizable: true, // 对应列是否可以通过拖动改变宽度
            visible: true, // 展示与隐藏
            sortable: true, // 对应列是否可以排序
            fixed: "", //固定
            minWidth: "150", //最小宽度%
            width: "", //宽度
        },
        {
            label: "操作",
            slotname: "operation",
            width: "120",
            fixed: "right", //固定
            visible: true,
        },
    ],
});

// 初始化
const init = () => {
    getList();
};


// 获取表格数据
const getList = async () => {
    try {
        tabelForm.tableConfig.loading = true;
        const params={
            ...formData,
            ...pageParams,
        }
        const res = await screenIndex.getScreenGroupList(params);
        dataList.value = res.data.records;
        total.value = res.data.total;

    } catch (error) {
        console.error("获取数据失败:", error);
    } finally {
        tabelForm.tableConfig.loading = false;
    }
};

// 查询
const handleQuery = () => {
    pageParams.pageNum = 1;
    getList();
};

// 重置
const resetQuery = () => {

    proxy.resetForm("queryForm");
    handleQuery();
};


/** 新增 */
const handleAdd = () => {
    diaWindow.popupType = 'add'
    diaWindow.dialogTitle = '新增门禁分组'
     diaWindow.dialogWidth = '50%'
    diaWindow.dialogFooterBtn = true
    diaWindow.rowData = {}
        diaWindow.dialogTop = '12vh'
    diaWindow.open1 = true;
};
/** 查看 */
const handleView = async (data) => {
    diaWindow.popupType = 'view';
            diaWindow.rowData = data; // 使用API返回的完整数据
            diaWindow.dialogTitle = '查看门禁分组';
            diaWindow.dialogFooterBtn = false;
            diaWindow.dialogWidth = '50%';
            diaWindow.dialogTop = '12vh';
            diaWindow.open1 = true;
};



/** 修改 */
const handleUpdate = async (data) => {
    // 先获取完整数据再打开弹窗
    try {
        const res = await screenIndex.getGroupStaffListById(data.id);
        if (res.success && res.data) {
            // 使用完整数据打开弹窗
            diaWindow.popupType = 'edit';
            diaWindow.rowData = res.data; // 使用API返回的完整数据
            diaWindow.dialogTitle = '修改门禁分组';
            diaWindow.dialogFooterBtn = true;
            diaWindow.dialogWidth = '50%';
            diaWindow.dialogTop = '12vh';
            diaWindow.open1 = true;
        } else {
            ElMessage.error('获取分组详情失败');
        }
    } catch (error) {
        console.error("获取数据失败:", error);
        ElMessage.error('获取分组详情失败');
    }
};

/** 提交保存 */
const save = (val) => {
  addRef.value.submitForm();
};

/** 点击取消保存 */
const cancellation = (val) => {
    close(false);
};

/** 关闭弹窗 */
const close = (val) => {
    diaWindow.open1 = val;
};

const submitClose = () => {
    close(false);
    getList()
}


/** 删除 */
const handleDelete = (row) => {
    ElMessageBox.confirm('确定删除该门禁分组吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
           screenIndex.deleteGroupById(row.id).then((res) => {
                if (res.success) {
                    ElMessage.success('删除成功');
                    getList(); // 调用获取列表的方法
                }
            });
        })
        .catch(() => {
            // 用户点击了取消
        });
};
/** 导出按钮操作 */
const handleExport = () => {
  proxy.download(
   `wisdomaccess${apiUrl}/access/group/export`,
    {
        ...formData,
        ...pageParams,
    },
    `门禁分组表_${formatMinuteTime(new Date())}.xlsx`
  );
}
// 生命周期钩子
onMounted(() => {
    init();
});
</script>

<style scoped lang="scss"></style>