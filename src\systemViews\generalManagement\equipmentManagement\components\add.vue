<template>
  <div
    class="dialog-box"
    :class="popupType === 'view' ? 'dialog-box-view' : 'dialog-box-edit'"
  >
    <el-form
      ref="ruleform"
      :model="formData"
      label-width="90px"
      :rules="popupType !== 'view' ? rules : false"
    >
      <el-row>
        <el-col :span="12">
          <el-form-item
            v-if="rootNode === 'false'"
            label="设备区域"
            prop="deviceAreaId"
          >
            <el-select
              v-if="popupType != 'view'"
              filterable
              :filter-method="dataFilter"
              class="main-select-tree"
              ref="selectTree"
              v-model="formData.deviceAreaId"
              placeholder="请选择设备区域"
            >
              <el-option
                :label="formData.deviceAreaName"
                :value="formData.deviceAreaId"
                style="display: none"
              />
              <el-tree
                style="margin: 20px auto; margin-top: 10px"
                :check-strictly="true"
                :data="areaTreeData"
                :filter-node-method="filterNode"
                @node-click="handleNodeClick"
                default-expand-all
                node-key="id"
                ref="areaTreeRef"
                highlight-current
                :props="treeProps"
              />
            </el-select>
            <div v-if="popupType == 'view'">
              {{ formData.deviceAreaName || "-" }}
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="设备编码" prop="deviceId">
            <el-input
              v-if="popupType == 'add'"
              v-model="formData.deviceId"
              placeholder="请输入设备编码"
              clearable
            />
            <div v-if="popupType != 'add'">{{ formData.deviceId }}</div>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="设备名称" prop="deviceName">
            <el-input
              v-if="popupType != 'view'"
              v-model="formData.deviceName"
              placeholder="请输入设备名称"
              clearable
            />
            <div v-if="popupType == 'view'">{{ formData.deviceName }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="设备类型" prop="deviceType">
            <el-select
              v-model="formData.deviceType"
              placeholder="请选择设备类型"
              clearable
              v-if="popupType != 'view'"
            >
              <el-option
                v-for="dict in device_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
            <div v-if="popupType == 'view'">
              {{ $formatDictLabel(formData.deviceType, device_type) }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="动作类型" prop="actionType">
            <el-select
              v-model="formData.actionType"
              placeholder="请选择动作类型"
              clearable
              v-if="popupType != 'view'"
            >
              <el-option
                v-for="dict in device_action_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
            <div v-if="popupType == 'view'">
              {{ $formatDictLabel(formData.actionType, device_action_type) }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="设备状态" prop="deviceStatus">
            <el-radio-group
              v-if="popupType != 'view'"
              v-model="formData.deviceStatus"
            >
              <el-radio-button label="1">启用</el-radio-button>
              <el-radio-button label="0">禁用</el-radio-button>
            </el-radio-group>
            <div v-if="popupType == 'view'">
              {{ $formatDictLabel(formData.deviceStatus, device_status) }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="popupType == 'view'">
          <el-form-item label="在线状态" prop="status">
            <div >
              {{ $formatDictLabel(formData.status, device_action_status) }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="产品信息" prop="productId">
            <el-select
              v-model="formData.productId"
              placeholder="请选择产品"
              clearable
              v-if="popupType == 'add'"
              @change="getProductName"
            >
              <el-option
                v-for="dict in product_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
            <div v-if="popupType != 'add'">
              {{ $formatDictLabel(formData.productId, product_type) }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="管理地址" prop="loginAddress">
            <el-input
              v-if="popupType != 'view'"
              v-model="formData.loginAddress"
              placeholder="请输入登录地址"
              clearable
            />
            <div v-if="popupType == 'view'">
              {{ formData.loginAddress || "-" }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="设备账号" prop="deviceAccount">
            <el-input
              v-if="popupType != 'view'"
              v-model="formData.deviceAccount"
              placeholder="请输入设备账号"
              clearable
            />
            <div v-if="popupType == 'view'">
              {{ formData.deviceAccount || "-" }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="设备密码" prop="devicePwd">
            <el-input
              v-if="popupType != 'view'"
              v-model="formData.devicePwd"
              placeholder="请输入设备密码"
              clearable
            />
            <div v-if="popupType == 'view'">
              {{ formData.devicePwd || "-" }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="IP地址" prop="ipAddress">
            <el-input
              v-if="popupType != 'view'"
              v-model="formData.ipAddress"
              placeholder="请输入IP地址"
              clearable
            />
            <div v-if="popupType == 'view'">
              {{ formData.ipAddress || "-" }}
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="备注" prop="remark">
            <el-input
              v-if="popupType != 'view'"
              v-model="formData.remark"
              placeholder="请输入备注"
              clearable
            />
            <div v-if="popupType == 'view'">{{ formData.remark || "-" }}</div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

  <script setup>
import { ref, reactive, onMounted, getCurrentInstance } from "vue";
import { ElMessage } from "element-plus";
import { areaTreeByType,areaTree, saveDevice ,editDevice} from "@/api/equipmentManagement/index";
//import {areaTreeByType} from "../../../../api/equipmentManagement";

const props = defineProps({
  closeBtn: Function,
  popupType: {
    type: String,
    default: "",
  },
  rowData: {
    type: Object,
    default: () => ({}),
  },
  dict: {
    type: Object,
    required: true,
  },
});

const emit = defineEmits(["close"]);

// 字典
const { proxy } = getCurrentInstance();
const { product_type, device_type, device_action_type,device_status, device_action_status} = proxy.useDict(
  "product_type",
  "device_type",
  "device_action_type",
  "device_status",
  "device_action_status"
);


// 响应式数据
const areaTreeData = ref([]);
const rootNode = ref("false");
const formData = reactive({
  deviceStatus: "1",
  status: "",
  ...props.rowData,
});

const treeProps = reactive({
  multiple: true,
  emitPath: false,
  value: "id",
});

const rules = reactive({
  deviceAreaId: [
    { required: true, message: "请选择设备区域", trigger: "blur" },
  ],
  deviceName: [{ required: true, message: "请输入设备名称", trigger: "blur" }],
  deviceId: [{ required: true, message: "请输入设备编码", trigger: "blur" }],
  deviceType: [
    { required: true, message: "请选择设备类型", trigger: "change" },
  ],
  actionType: [
    { required: true, message: "请选择动作类型", trigger: "change" },
  ],
  deviceStatus: [
    { required: true, message: "请选择设备状态", trigger: "blur" },
  ],
  productId: [{ required: true, message: "请选择产品信息", trigger: "change" }],
});

// 组件引用
const ruleform = ref(null);
const selectTree = ref(null);
const areaTreeRef = ref(null);

// 方法
const dataFilter = (val) => {
  if (val) {
    areaTreeRef.value.filter(val);
  }
};

const filterNode = (value, data) => {
  if (!value) return true;
  return data.label?.includes(value) ?? false;
};

const handleNodeClick = (data) => {
  formData.deviceAreaId = data.id;
  formData.deviceAreaName = data.label;
  selectTree.value.blur();
};

const selectAreaTree = async () => {
  try {
    const res = await areaTreeByType("office");
    areaTreeData.value = res.data;
  } catch (error) {
    ElMessage.error("获取区域树失败");
  }
};

const getProductName = (value) => {
  const product = product_type.value.find(
    (item) =>
   item.value === value
);
  formData.productName = product?.label || "";
};

const submitForm = async () => {
  try {


    await ruleform.value.validate();
    const api = props.popupType === "add" ? saveDevice : editDevice;
    const res = await api(formData);
    if (res.code == '1') {
      ElMessage.success("操作成功");
      emit("close");
    }
  } catch (error) {
    console.error("表单提交失败:", error);
  }
};

const closeBtn = () => {
  emit("close");
};

// 生命周期
onMounted(() => {
  if (props.popupType === "edit" || props.popupType === "view") {
    Object.assign(formData, props.rowData);
  }
  selectAreaTree();
});

defineExpose({
    submitForm
})
</script>

  <style scoped lang="scss">
</style>
