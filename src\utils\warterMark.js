'use strict'
import siteName from './config'

// 移动端使用
export function setH5Watermark(obj) {
  const {content = siteName, rotate = 20, density = 10, fontSize = 15} = obj;
  //默认设置
  const defaultSettings = {
    watermark_txt: content,
    watermark_x: 20, //水印起始位置x轴坐标
    watermark_y: 20, //水印起始位置Y轴坐标
    watermark_rows: 5, //水印行数
    watermark_cols: 5, //水印列数
    watermark_x_space: 10, //水印x轴间隔
    watermark_y_space: 60, //水印y轴间隔
    watermark_color: "#000000", //水印字体颜色
    watermark_alpha: 0.2, //水印透明度
    watermark_fontsize: fontSize + 'px', //水印字体大小
    watermark_font: '微软雅黑', //水印字体
    watermark_width: 200 + density, //水印宽度
    watermark_height: 60 + density, //水印长度
    watermark_angle: rotate //水印倾斜度数
  };
  //采用配置项替换默认值，作用类似jquery.extend

  //获取页面最大宽度
  var page_width = Math.max(document.body.scrollWidth, document.body.clientWidth) - 100;
  //获取页面最大长度
  var page_height = Math.max(document.body.scrollHeight, document.body.clientHeight) - 100
  //如果将水印列数设置为0，或水印列数设置过大，超过页面最大宽度，则重新计算水印列数和水印x轴间隔
  if (defaultSettings.watermark_cols == 0 || (parseInt(defaultSettings.watermark_x + defaultSettings.watermark_width * defaultSettings.watermark_cols + defaultSettings.watermark_x_space * (defaultSettings.watermark_cols - 1)) > page_width)) {
    defaultSettings.watermark_cols = parseInt((page_width - defaultSettings.watermark_x + defaultSettings.watermark_x_space) / (defaultSettings.watermark_width + defaultSettings.watermark_x_space));
    defaultSettings.watermark_x_space = parseInt((page_width - defaultSettings.watermark_x - defaultSettings.watermark_width * defaultSettings.watermark_cols) / (defaultSettings.watermark_cols - 1));
  }
  //如果将水印行数设置为0，或水印行数设置过大，超过页面最大长度，则重新计算水印行数和水印y轴间隔
  if (defaultSettings.watermark_rows == 0 || (parseInt(defaultSettings.watermark_y + defaultSettings.watermark_height * defaultSettings.watermark_rows + defaultSettings.watermark_y_space * (defaultSettings.watermark_rows - 1)) > page_height)) {
    defaultSettings.watermark_rows = parseInt((defaultSettings.watermark_y_space + page_height - defaultSettings.watermark_y) / (defaultSettings.watermark_height + defaultSettings.watermark_y_space));
    defaultSettings.watermark_y_space = parseInt(((page_height - defaultSettings.watermark_y) - defaultSettings.watermark_height * defaultSettings.watermark_rows) / (defaultSettings.watermark_rows - 1));
  }
  var x;
  var y;
  const p = document.createElement('div')
  for (var i = 0; i < defaultSettings.watermark_rows; i++) {
    y = defaultSettings.watermark_y + (defaultSettings.watermark_y_space + defaultSettings.watermark_height) * i;
    for (var j = 0; j < defaultSettings.watermark_cols; j++) {
      x = defaultSettings.watermark_x + (defaultSettings.watermark_width + defaultSettings.watermark_x_space) * j;
      var mask_div = document.createElement('div');
      mask_div.id = 'mask_div' + i + j;
      mask_div.appendChild(document.createTextNode(defaultSettings.watermark_txt));
      //设置水印div倾斜显示
      mask_div.style.webkitTransform = "rotate(-" + defaultSettings.watermark_angle + "deg)";
      mask_div.style.MozTransform = "rotate(-" + defaultSettings.watermark_angle + "deg)";
      mask_div.style.msTransform = "rotate(-" + defaultSettings.watermark_angle + "deg)";
      mask_div.style.OTransform = "rotate(-" + defaultSettings.watermark_angle + "deg)";
      mask_div.style.transform = "rotate(-" + defaultSettings.watermark_angle + "deg)";
      mask_div.style.visibility = "";
      mask_div.style.position = "absolute";
      //选不中
      mask_div.style.left = x + 'px';
      mask_div.style.top = y + 'px';
      mask_div.style.overflow = "hidden";
      mask_div.style.zIndex = "9999";
      mask_div.style.pointerEvents = "none";
      //mask_div.style.border="solid #eee 1px";
      mask_div.style.opacity = defaultSettings.watermark_alpha;
      mask_div.style.fontSize = defaultSettings.watermark_fontsize;
      mask_div.style.color = defaultSettings.watermark_color;
      mask_div.style.textAlign = "center";
      mask_div.style.width = defaultSettings.watermark_width + 'px';
      mask_div.style.height = defaultSettings.watermark_height + 'px';
      mask_div.style.display = "block";

      p.appendChild(mask_div)
    }
  }
  document.getElementById('1.23452123412415384164.123412415').appendChild(p);
}

// 水印设置
export function setWatermark(obj) {
  const {content = siteName, rotate = 20, density = 10, fontSize = 15,} = obj;
  let id = '1.23452384164.123412415'

  if (document.getElementById(id) !== null) {
    document.body.removeChild(document.getElementById(id))
  }

  let can = document.createElement('canvas')
  can.width = 150 + density
  can.height = 120 + density

  let cans = can.getContext('2d')
  cans.rotate(-rotate * Math.PI / 180)
  cans.font = `${fontSize}px Vedana`
  cans.fillStyle = `rgb(0, 0, 0, 0.1)`
  cans.textAlign = 'left'
  cans.textBaseline = 'Middle'
  cans.fillText(content, can.width / 180, can.height / 2)

  let div = document.createElement('div')
  div.id = id
  div.style.pointerEvents = 'none'
  div.style.top = '70px'
  div.style.left = '0px'
  div.style.position = 'fixed'
  div.style.zIndex = '100000'
  div.style.width = document.documentElement.clientWidth - 100 + 'px'
  div.style.height = document.documentElement.clientHeight - 100 + 'px'
  div.style.background = 'url(' + can.toDataURL('image/png') + ') left top repeat'
  document.body.appendChild(div)
}

// 该方法只允许调用一次
// watermark.set = (obj) => {
//     let id = setWatermark(obj)
//     setInterval(() => {
//         if (document.getElementById(id) === null) {
//             id = setWatermark(obj)
//         }
//     }, 500)
//     window.onresize = () => {
//         setWatermark(obj)
//     }
// }

// watermark.setH5 = (obj) => {
//   noViewWatermark(obj)
// }
export function setBase64Watermark(data) {
  const leftData = data.substring(0, 8).split('')
  const rightData = data.substring(8, data.length)
  let newLeftData = ''
  for (let index = leftData.length - 1; index >= 0; index--) {
    const element = leftData[index];
    newLeftData += element
  }
  const base = 'data:image/png;base64,' + newLeftData + rightData;
  let div = document.createElement('div')
  div.style.pointerEvents = 'none'
  div.style.top = '70px'
  div.style.left = '0px'
  div.style.position = 'fixed'
  div.style.zIndex = '100000'
  div.style.opacity = 0.01
  div.style.width = document.documentElement.clientWidth - 100 + 'px'
  div.style.height = document.documentElement.clientHeight - 100 + 'px'
  div.style.background = 'url(' + base + ') left top repeat'
  document.getElementById('1.23452123412415384164.123412415').appendChild(div)
}
