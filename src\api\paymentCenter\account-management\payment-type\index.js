import request from '@/utils/request'
import { apiUrl } from '@/utils/config'; 



export class screenIndex {

  // 树
  static payTypeTree(data) {
    return request({
      url: `pay${apiUrl}/pay/payType/payTypeTree`,
      method: 'post',
      data:data
    })
  }

//账户设置列表
  static pagePayType(data) {
    return request({
      url: `pay${apiUrl}/pay/payType/pagePayType`,
      method: 'post',
      data: data
    })
  }

// 删除  ttp://localhost:9001/pay/payAccountType/id?id=
static deletePayType(data) {
    return request({
      url: `pay${apiUrl}/pay/payType/deletePayType` ,
      method: 'post',
      data
    })
  }

// 查询

static payAccountTypeGet(data) {
    return request({
      url: `pay${apiUrl}/pay/payAccountType`,
      method: 'post',
      data
    })
  }
// 添加
  static savePayType(data) {
    return request({
      url: `pay${apiUrl}/pay/payType/savePayType`,
      method: 'post',
      data
    })
  }

//修改 
static updatePayType(data) {
    return request({
      url: `pay${apiUrl}/pay/payType/updatePayType`,
      method: 'post',
      data
    })
  }

}