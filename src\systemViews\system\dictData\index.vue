<template>
  <div class="app-container">
    <el-card>
      <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="字典标签" prop="dictLabel">
          <el-input v-model="queryParams.dictLabel" placeholder="请输入字典标签" clearable
                    @keyup.enter.native="handleQuery"/>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['sys:base:dict:add']">新增
          </el-button>
        </el-col>
        <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="dataList">
        <el-table-column label="字典编码" align="center" prop="dictCode"/>
        <el-table-column label="字典标签" align="center" prop="dictLabel"/>
        <el-table-column label="字典键值" align="center" prop="dictValue"/>
        <el-table-column label="字典排序" align="center" prop="dictSort"/>
        <el-table-column label="备注" align="center" prop="dictRemark" :show-overflow-tooltip="true"/>
        <el-table-column label="操作" align="center">
          <template #default="scope">
            <el-button link icon="Edit" type="primary" @click="handleUpdate(scope.row)"
                       v-hasPermi="['sys:base:dict:edit']">修改
            </el-button>
            <el-button link icon="Delete" type="primary" @click="handleDelete(scope.row)"
                       v-hasPermi="['sys:base:dict:remove']">删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
      />
    </el-card>

    <!-- 添加或修改字典数据对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="字典类型">
          <el-input v-model="form.dictCode" :disabled="true"/>
        </el-form-item>
        <el-form-item label="数据标签" prop="dictLabel">
          <el-input v-model="form.dictLabel" placeholder="请输入数据标签"/>
        </el-form-item>
        <el-form-item label="数据键值" prop="dictValue">
          <el-input v-model="form.dictValue" placeholder="请输入数据键值"/>
        </el-form-item>
        <el-form-item label="显示排序" prop="dictSort">
          <el-input-number v-model="form.dictSort" controls-position="right" :min="0"/>
        </el-form-item>
        <el-form-item label="备注" prop="dictRemark">
          <el-input v-model="form.dictRemark" type="textarea" placeholder="请输入内容"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm" :loading="saveLoading">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="DictData">
import {
  listData,
  getData,
  delData,
  addData,
  updateData
} from "@/api/system/dict/data";
import {listType, getType} from "@/api/system/dict/type";
import {ref} from "vue";

const {proxy} = getCurrentInstance();
import {useRoute} from 'vue-router';

const route = useRoute();
// 遮罩层
const loading = ref(true);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 字典表格数据
const dataList = ref([]);
// 默认字典类型
const defaultDictCode = ref("");
// 弹出层标题
const title = ref("");
// 是否显示弹出层
const open = ref(false);
// 状态数据字典
const statusOptions = ref([]);
// 类型数据字典
const typeOptions = ref([]);
// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  dictName: undefined,
  status: undefined,
  dictCode: undefined,
})
// 表单参数
const form = ref({});
// 表单校验
const rules = {
  dictLabel: [
    {required: true, message: "数据标签不能为空", trigger: "blur"},
  ],
  dictValue: [
    {required: true, message: "数据键值不能为空", trigger: "blur"},
  ],
  dictSort: [
    {required: true, message: "数据顺序不能为空", trigger: "blur"},
  ]
};
const saveLoading = ref(false);

/** 查询字典类型详细 */
function getTypeInfo(dictId) {
  getType(dictId).then((response) => {
    queryParams.value.dictCode = response.data.dictCode;
    defaultDictCode.value = response.data.dictCode;
    getList();
  });
}

/** 查询字典类型列表 */
function getTypeList() {
  listType().then((response) => {
    typeOptions.value = response.rows;
  });
}

/** 查询字典数据列表 */
function getList() {
  loading.value = true;
  listData(queryParams.value).then((response) => {
    dataList.value = response.data.records;
    total.value = response.data.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    dictCode: undefined,
    dictLabel: undefined,
    dictValue: undefined,
    dictSort: 0,
    dictStatus: "valid",
    dictRemark: undefined,
  };
  proxy.resetForm("formRef");
  saveLoading.value = false;
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryForm");
  queryParams.value.dictType = defaultDictCode.value;
  handleQuery();
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加字典数据";
  form.value.dictCode = queryParams.value.dictCode;
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  getData(row.dictDataId).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "修改字典数据";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      saveLoading.value = true;
      if (form.value.dictDataId != undefined) {
        form.value.dictStatus = 'valid'
        updateData(form.value).then((response) => {
          saveLoading.value = true;
          if (response.success) {
            proxy.$modal.msgSuccess("修改成功");
            open.value = false;
            getList();
          } else {
            saveLoading.value = false;
            proxy.$modal.msgError(response.message);
          }
        });
      } else {
        addData(form.value).then((response) => {
          saveLoading.value = true;
          if (response.success) {
            proxy.$modal.msgSuccess("新增成功");
            open.value = false;
            getList();
          } else {
            saveLoading.value = false
            proxy.$modal.msgError(response.message);
          }
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const dictDataId = row.dictDataId;
  const dictLabel = row.dictLabel
  proxy.$modal.confirm('是否确认删除字典标签为"' + dictLabel + '"的数据项?').then(function () {
    return delData({
      dictDataId,
    });
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  });
}

watch(route, (newRoute, oldRoute) => {
  if (route.fullPath.includes('/system/base/dictData')) {
    if (route.query.dictId) {
      getTypeInfo(route.query.dictId);
    }
  }
}, {immediate: true});

getTypeList();
</script>

<style scoped>

</style>
