<!-- 账户清零 -->


<template>
  <div class="container-table-box">
    <Splitpanes class="default-theme">
      <Pane :size="100" :min-size="65">
        <el-card class="dep-card">
          <dialog-search
            @getList="getList"
            formRowNumber="4"
            :columns="tabelForm.columns"
          >
            <template v-slot:formList>
              <el-form
                :model="queryParams"
                ref="queryForm"
                :inline="true"
                label-width="75px"
              >
                <el-form-item label="清零月份">
                  <el-date-picker
                    v-model="queryParams.month"
                    type="month"
                    format="YYYYMM"
                    value-format="YYYYMM"
                    placeholder="请选择"
                    clearable
                  />
                </el-form-item>

                <el-form-item label="清零名称">
                  <el-input
                    v-model="queryParams.typeName"
                    placeholder="请输入清零名称"
                    clearable
                  ></el-input>
                </el-form-item>

                <el-form-item label="清零类型" prop="rechargeType">
                  <el-select
                    v-model="queryParams.rechargeType"
                    placeholder="请选择清零类型"
                    clearable
                  >
                    <el-option
                      v-for="(item, index) of recharge_type"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>

                <el-form-item label="清零状态" prop="rechargeStatus">
                  <el-select
                    v-model="queryParams.status"
                    placeholder="请选择清零状态"
                    clearable
                  >
                    <el-option
                      v-for="(item, index) of recharge_status"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-form>
            </template>
            <template v-slot:searchList>
              <el-button type="primary" icon="Search" @click="handleQuery"
                >搜索</el-button
              >
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </template>

            <template v-slot:searchBtnList>
              <el-button
                type="primary"
                size="mini"
                @click="handleRecharge"
                >
                <i class="icon iconfont icon-zhanghuchongzhi icon-btn"></i>
                账户清零</el-button
              >
              <el-button  @click="handleBatchRecharge">
                <i class="icon iconfont icon-piliangchongzhi icon-btn"></i>
                批量清零 </el-button>
              <el-button icon="Download" @click="handleExport">导出 </el-button>
            </template>
          </dialog-search>

          <public-table
            ref="publictable"
            :rowKey="tabelForm.tableKey"
            :tableData="list"
            :columns="tabelForm.columns"
            :configFlag="tabelForm.tableConfig"
            :pageValue="pageParams"
            :total="total"
            :getList="getList"
          >
            <template #statusType="{ scope }">
              <el-radio-group
                v-model="scope.row.status"
                @change="radioChange(scope.row)"
              >
                <el-radio-button
                  :label="item.value"
                  v-for="(item, index) of account_type_status"
                  :key="index"
                  >{{ item.label }}</el-radio-button
                >
              </el-radio-group>
            </template>
            <template #operation="{ scope }">
              <el-button
                size="mini"
                type="text"
                title="查看明细"
                icon="View"
                @click="handleView(scope.row)"
              >
              </el-button>
              <el-button
                size="mini"
                type="text"
                title="撤销清零"
                icon="Edit"
                @click="handleRevoke(scope.row)"
              >
              <i class="icon iconfont icon-chexiao"></i>
              </el-button>

            </template>
          </public-table>
        </el-card>
      </Pane>
    </Splitpanes>

    <DialogBox
      :visible="open1"
      :dialogWidth="diaWindow.dialogWidth"
      :dialogFooterBtn="diaWindow.dialogFooterBtn"
      @save="save"
      @cancellation="cancellation"
      :custom-class="diaWindow.customClass"
      @close="close"
      :dialogTitle="diaWindow.headerTitle"
    >
      <template #content>
        <accountAdd
         v-if="diaWindow.popupType=='recharge'"
          ref="accountAddRef"
          :rowData="diaWindow.rowData"
          :popupType="diaWindow.popupType"
          @closeBtn="cancellationRefsh"
        ></accountAdd>

        <batchRecharge
         v-if="diaWindow.popupType=='batchRecharge'"
          ref="batchRechargeRef"
          :rowData="diaWindow.rowData"
          :popupType="diaWindow.popupType"
          @closeBtn="cancellationRefsh"
        ></batchRecharge>
      </template>
    </DialogBox>
  </div>
</template>
  
  <script setup >
import { ElMessage, ElMessageBox } from "element-plus";
import { ref, reactive, getCurrentInstance } from "vue";
import { screenIndex } from "@/api/paymentCenter/account-management/account-type/index";
import { apiUrl } from "@/utils/config";
import { formatMinuteTime } from "@/utils";
import accountAdd from "./components/add.vue";
import batchRecharge from "./components/batchRecharge.vue";
const { proxy } = getCurrentInstance();
const { account_type_status,recharge_status  } = proxy.useDict("account_type_status","recharge_status ");

const accountAddRef = ref(null);
const batchRechargeRef = ref(null);
const open1 = ref(false);
const diaWindow = reactive({
  headerTitle: "",
  popupType: "",
  rowData: "",
  dialogWidth: "20%",
  dialogFooterBtn: false,
  customClass: "",
});

const pageParams = ref({
  pageNum: 1,
  pageSize: 10,
});
const queryParams = ref({});
const list = ref([]);
const total = ref(0);
const tabelForm = reactive({
  tableKey: "1", //表格key值
  isShowRightToolbar: true, //是否显示右侧显示和隐藏
  showSearch: true,
  columns: [
    {
      fieldIndex: "month", // 对应列内容的字段名
      label: "清零月份", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "120", //最小宽度%
      width: "", //宽度
    },
    {
      fieldIndex: "rechargeName", // 对应列内容的字段名
      label: "清零名称", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "150", //最小宽度%
      width: "", //宽度
      align: "", //表格对齐方式
    },

    {
      fieldIndex: "rechargeNumber", // 对应列内容的字段名
      label: "清零人数", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "120", //最小宽度%
      width: "", //宽度
    },

    {
        fieldIndex: "rechargeDollor", // 对应列内容的字段名
        label: "清零金额", // 显示的标题
        resizable: true, // 对应列是否可以通过拖动改变宽度
        visible: true, // 展示与隐藏
        sortable: true, // 对应列是否可以排序
        fixed: "", //固定
        minWidth: "120", //最小宽度%
        width: "", //宽度
        type: "dollor",
      },
    {
      fieldIndex: "rechargeStatus", // 对应列内容的字段名
      label: "清零状态", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "120", //最小宽度%
      width: "", //宽度
      align: "", //表格对齐方式
      type: "dict",
      dictList: recharge_status,
    },

    {
      fieldIndex: "createDate", // 对应列内容的字段名
      label: "创建时间", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "150", //最小宽度%
      width: "", //宽度
    },

   
    {
      fieldIndex: "operator", // 对应列内容的字段名
      label: "操作人", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "120", //最小宽度%
      width: "", //宽度
    },

    {
      label: "操作",
      slotname: "operation",
      width: "120",
      fixed: "right", //固定
      headerAlign: "center",
      align: "left",
      visible: true,
    },
  ],
  // 表格基础配置项，看个人需求添加
  tableConfig: {
    needPage: true, // 是否需要分页
    index: true, // 是否需要序号
    selection: false, // 是否需要多选
    reserveSelection: false, // 是否需要支持跨页选择
    indexFixed: false, // 序号列固定 left true right
    selectionFixed: false, //多选列固定left true right
    indexWidth: "50", //序号列宽度
    loading: false, //表格loading
    showSummary: false, //是否开启合计
    height: null, //表格固定高度 比如：300px
  },
});

/** 查询列表 */
const getList = () => {
  tabelForm.tableConfig.loading = true;
  screenIndex.findPage(queryParams.value, pageParams.value).then((response) => {
    list.value = response.data.records;
    total.value = response.data.total;
    tabelForm.tableConfig.loading = false;
  });
};

/** 搜索按钮操作 */
const handleQuery = () => {
  pageParams.value.pageNum = 1;
  getList();
};
/** 重置按钮操作 */
const resetQuery = () => {
  proxy.resetForm("queryForm");
  queryParams.value = {};
  handleQuery();
};

/** 导出按钮操作 */

const handleExport = () => {
  proxy.download(
    `pay${apiUrl}/pay/payAccountType/export`,
    {
      ...queryParams.value,
    },
    `账号类型列表_${formatMinuteTime(new Date())}.xlsx`
  );
};
/** 删除 */
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm("确认要删除吗？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });

    const res = await screenIndex.payAccountTypeDelete({ id: row.id });
    if (res.code == "1") {
      ElMessage.success("删除成功");
      await getList();
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    console.error("删除失败:", error);
    // 用户取消删除或其他错误
  }
};

/** 查看 */
const handleView = (data) => {
  diaWindow.headerTitle = "查看账号类型";
  diaWindow.popupType = "view";
  diaWindow.rowData = data;
  diaWindow.dialogWidth = "28%";
  diaWindow.dialogFooterBtn = false;
  open1.value = true;
};

// 账户充值
const handleRecharge = () => {
  diaWindow.headerTitle = "账户清零";
  diaWindow.popupType = "recharge";
  diaWindow.rowData = {}; // 如果需要传递数据到弹窗
  diaWindow.dialogFooterBtn = true;
  diaWindow.dialogWidth = "28%";
  open1.value = true;
};

// 修改
const handleBatchRecharge = (row) => {
  diaWindow.headerTitle = "批量清零";
  diaWindow.popupType = "batchRecharge";
  diaWindow.rowData = row; // 如果需要传递数据到弹窗
  diaWindow.dialogFooterBtn = true;
  diaWindow.dialogWidth = "70%";
  open1.value = true;
};

const radioChange = async (row) => {
  const res = await screenIndex.payAccountTypeUpdate(row);
  if (res.success) {
    ElMessage.success("修改成功");
    getList();
  }
};
/** 点击确定保存 */
const save = () => {
  if (diaWindow.popupType == "add") {
    accountAddRef.value.saveForm();
  }

  if (diaWindow.popupType == "edit") {
    accountAddRef.value.saveForm();
  }
};
/** 点击确定后刷新 */
const cancellationRefsh = () => {
  close(false);
  getList();
};
/** 点击取消保存 */
const cancellation = (val) => {
  close(false);
};

/** 关闭弹窗 */
const close = (val) => {
  open1.value = val;
};

getList();
</script>
  
  <style scoped>
.icon-btn{
    font-size: 16px;
    margin-right: 5px;
}
</style>