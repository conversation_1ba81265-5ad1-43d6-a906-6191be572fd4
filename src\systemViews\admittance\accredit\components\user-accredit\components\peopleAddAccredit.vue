<!--新增权限 -->
<template>
    <div class="dialog-box dialog-box-edit">

        <el-form ref="ruleform" :model="formData" :rules="rules" inline="true">
            <el-row>
                <el-col :span="12">
                    <el-form-item label="过期日期" prop="expirationTime">
                        <el-date-picker v-model="formData.expirationTime" format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD 23:59:59" type="date"
                            :disabled-date="timepickerOptions.disabledDate" placeholder="默认长期有效" />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row style="height: 500px;">
                <Splitpanes class="default-theme">
                    <Pane :size="30" :min-size="20">
                        <el-card class="dep-card" style="height: 100%">
                            <el-form-item label="区域选择">
                                <el-tree style="margin: 20px auto " :check-strictly="true" :data="areaOptions"
                                    show-checkbox default-expand-all node-key="id" ref="areaTree" highlight-current
                                    :props="treeProps">
                                </el-tree>
                            </el-form-item>
                        </el-card>
                    </Pane>

                    <Pane :size="70" :min-size="20">
                        <el-card class="dep-card" style="height: 100%">

                            <el-form-item label="人员选择" prop="nameList">
                                <el-select style="width: 240px;" collapse-tags placeholder="请输入人员进行选择" clearable remote
                                    @change="changeUser" :remote-method="getUserList" filterable>
                                    <el-option v-for="(item, index) of applyUserList" :key="index"
                                        :label="item.nickName" :value="item.userId">
                                        <template #default>{{ item.nickName + '(' + item.deptName + '/' + item.userName
                                            + ')'
                                            }}</template>
                                    </el-option>
                                </el-select>
                            </el-form-item>
                            <el-table :key="tableKey1" class="limit-cell-table" :data="userInfo" style="width: 95%"
                                border>
                                <el-table-column type="index" label="序号" width="80" align="center" sortable />
                                <template v-for="(item) in userColumns" :key="item.prop">
                                    <el-table-column show-overflow-tooltip :prop="item.prop" align="center"
                                        :label="item.label" :min-width="item.minWidth" :resizable="item.resizable">
                                        <template #default="scope">
                                            <div v-if="item.prop === 'nickName'">{{ scope.row.nickName }}</div>
                                            <div v-if="item.prop === 'userName'" >{{ scope.row.userName
                                                }}</div>
                                            <div v-if="item.prop === 'deptName'" >{{ scope.row.deptName
                                                }}</div>
                                        </template>
                                    </el-table-column>
                                </template>
                                <el-table-column label="操作" align="center" fixed="right">
                                    <template #default="scope">
                                        <el-button size="small" type="text" icon="Delete"
                                            @click="handleDelete(scope.row)" />
                                    </template>
                                </el-table-column>
                            </el-table>
                        </el-card>
                    </Pane>
                </Splitpanes>

            </el-row>


        </el-form>


    </div>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import { ElMessage } from 'element-plus'
import { screenIndex } from '@/api/admittance/accredit'
// import { selectApplyUserList } from "@/api/system/user"

const props = defineProps({
    closeBtn: {
        type: Function,
        default: null
    },
    popupType: {
        type: String,
        default: ''
    },
    rowData: {
        type: Object,
        default: () => ({})
    }
})

const emit = defineEmits(['submitClose'])

// 响应式数据
const ruleform = ref()
const areaTree = ref()
const tableKey1 = ref('1')

// 人员列表
const applyUserList = ref([])
// 表格数据
const userInfo = ref([])

const formData = reactive({
    userIds: [],
    areaIds: []
})

// 区域树
const treeProps = reactive({ multiple: true, emitPath: false, value: 'id' })
const areaOptions = ref([])

// 时间选择器配置
const timepickerOptions = reactive({
    disabledDate(time) {
        const today = new Date()
        today.setHours(0, 0, 0, 0)
        return time.getTime() < today.getTime()
    }
})

// 部门表格列配置
const userColumns = ref([
    {
        prop: 'nickName',
        label: '人员姓名',
        show: true,
        sortable: true,
        minWidth: '80%'
    },
    {
        prop: 'userName',
        label: '人员账号',
        show: true,
        sortable: true,
        minWidth: '100%'
    },
    {
        prop: 'deptName',
        label: '人员部门',
        show: true,
        sortable: true,
        minWidth: '150%'
    }
])

// 区域表格列配置
const areaColumns = ref([
    {
        prop: 'areaName',
        label: '区域名称',
        resizable: true,
        show: true,
        sortable: true,
        minWidth: "100%"
    }
])

// 校验规则
const rules = reactive({
    /* nameList: [
      { required: true, message: '请选择人员', trigger: 'blur' }
    ],
    areaIds: [
      { required: true, message: '请选择区域', trigger: 'blur' }
    ] */
})

// 删除选中人员
const handleDelete = (row) => {
    let userInfoBack = []
    let userIdsBack = []

    userInfo.value.forEach((o) => {
        if (o.userId !== row.userId) {
            userInfoBack.push(o)
        } else {
            applyUserList.value.push(o)
        }
    })

    formData.userIds.forEach((o) => {
        if (o !== row.userId) {
            userIdsBack.push(o)
        }
    })

    userInfo.value = userInfoBack
    formData.userIds = userIdsBack
}

// 选择用户
const changeUser = (data) => {
    let applyUserListBack = []
    formData.userIds.push(JSON.stringify(data))

    applyUserList.value.forEach((o) => {
        if (o.userId === data) {
            userInfo.value.push(o)
        } else {
            applyUserListBack.push(o)
        }
    })

    applyUserList.value = applyUserListBack
}

// 获取申请人列表
const getUserList = (val) => {
    if (val.length > 0) {
        const data = {
            status: '0',
            nickName: val
        }
        selectApplyUserList(data).then((res) => {
            applyUserList.value = res.data
        })
    }
}



// 获取区域树
const getAreaTree = () => {
    screenIndex.areaTree().then(res => {
        areaOptions.value = res.data
    })
}



// 保存按钮
const saveBtn = () => {
    formData.areaIds = areaTree.value.getCheckedKeys()

    if (formData.areaIds.length < 1) {
        ElMessage.warning("请选择区域")
        return
    }

    if (formData.userIds.length < 1) {
        ElMessage.warning("请选择人员")
        return
    }

    screenIndex.addUserAccredit(formData).then((res) => {
        if (res.success) {
            ElMessage.success('授权成功')
            emit('submitClose')
        }
    })
}

// 组件挂载时获取区域树
onMounted(() => {
    getAreaTree()
})

// 暴露方法给父组件
defineExpose({
    saveBtn,
})
</script>

<style scoped lang="scss">
</style>