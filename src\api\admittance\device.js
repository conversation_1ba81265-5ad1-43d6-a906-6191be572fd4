import request from '@/utils/request'
import { apiUrl } from '@/utils/config';

export class screenIndex {
    // 获取 区域树
    static areaTreeSelect(data) {
        return request({
            url: `user${apiUrl}/area/queryLazyAreaTree`,
            method: 'post',
            data: data
        })
    }

    // 获取 设备绑定关系列表
    static queryList(params, data) {
        return request({
            url: `wisdomaccess${apiUrl}/access/areaDevice/findAccessAreaDevice`,
            method: 'post',
            data: { ...params, ...data }
        })
    }
    // 获取 区域路径
    static getAreaPath(params) {
        return request({
            url: `user${apiUrl}/area/findFatherPathById`,
            method: "get",
            params: params,
        })
    }


    // 删除绑定关系
    static deleteById(id) {
        return request({
            url: `wisdomaccess${apiUrl}/access/areaDevice/deleteAccessAreaDeviceById/${id}`,
            method: 'post',
        })
    }
    // 查询后端构建区域树
    static getAllAreaTree(data) {
        return request({
            url: `user${apiUrl}/area/getAllAreaTree`,
            method: 'post',
            data: data
        })
    }
    // 新增/编辑 设备绑定关系
    static saveDeviceBanding(data) {
        return request({
            url: `wisdomaccess${apiUrl}/access/areaDevice/addAccessAreaDevice`,
            method: 'post',
            data: data
        })
    }
    // 更新 设备绑定关系
    static updateDevice(data) {
        return request({
            url: `wisdomaccess${apiUrl}/access/areaDevice/updateAccessAreaDeviceById`,
            method: 'post',
            data: data
        })
    }
    // 新增设备相关接口
    static queryDeviceList(params) {
        return request({
            url: `pay${apiUrl}/admittance/device/queryList`,
            method: 'get',
            params: params
        })
    }

    static delDeviceById(id) {
        return request({
            url: `wisdomaccess${apiUrl}/admittance/device/deleteById/${id}`,
            method: 'delete'
        })
    }

    static queryAbleList() {
        return request({
            url: `wisdomaccess${apiUrl}/access/areaDevice/findSurviveDeviceList`,
            method: 'post',
            data: {}
        })
    }
    // 新增/编辑 设备绑定关系
    static saveDeviceBanding(data) {
        return request({
            url: `wisdomaccess${apiUrl}/access/areaDevice/addAccessAreaDevice`,
            method: 'post',
            data: data
        })
    }

    // 更新设备绑定关系
    static updateDevice(data) {
        return request({
            url: `wisdomaccess${apiUrl}/access/areaDevice/updateAccessAreaDeviceById`,
            method: 'post',
            data: data
        })
    }



    // 获取 区域路径

    // 查询后端构建区域树


}