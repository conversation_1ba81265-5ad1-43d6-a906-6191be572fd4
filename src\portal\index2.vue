<template>
  <div class="header">

    <div class="uni-header uni-header-bordered inner-home">
      <div class="uni-header-logo uni-header-item xiao-logo">
        <img v-if="logo" :src="logo" class="logo" />
      </div>
      <h1 class="uni-header-title uni-header-item inner-home-header">{{ permissionStore.portalName || title }}</h1>
      <div class="uni-nav uni-header-nav">


        <div class="uni-nav-item tab-menu">
          <div id="adminbutton" class="right-menu-item hover-effect" v-hasPermi="['sys:base:menu:edit']" @click="goAdmin">
            <el-icon>
              <Setting></Setting>
            </el-icon>
            <span style="margin-left: 3px;">系统管理</span>
          </div>
        </div>
        <div class="uni-nav-item">
          <screenfull id="screenfull"  class="right-menu-item hover-effect "></screenfull>
        </div>
        <div class="uni-nav-item">
          <el-dropdown @command="handleCommand" class="right-menu-item hover-effect" trigger="click">
            <div class="avatar-wrapper">
              <img style="width: 24px; height: 24px"
                   src="@/assets/styles/js-pro.v.1.1/images/avatar.png" />
              <p class="uni-nav-item-title">
                你好, <span id="userName">{{ userObj.staffName }}</span>
              </p>
              <el-icon style="align-self: flex-end;"><caret-bottom /></el-icon>

            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <router-link to="/portal/userInfo/index">
                  <el-dropdown-item>个人中心</el-dropdown-item>
                </router-link>
                <el-dropdown-item command="homeLayout" v-if="route.path === '/portal/index'">
                  <span>主页配置</span>
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <span>退出登录</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>

    <div class="uni-topnav uni-topnav-md">



      <el-menu class="uni-topnav-body" :default-active="route.path" mode="horizontal" style="border-bottom:1px">
        <menu-item class="uni-topnav-body-item" v-for="(item, index) in menusData" :key="`${item.permissionId}-${index}`"
                   :menu-index="index + ''" :item="item"></menu-item>
      </el-menu>
    </div>

   <!-- <div class="bread" v-if="levelList && levelList.length">
     <div class="content" :style="contentWidth()">
       <el-breadcrumb class="app-breadcrumb" separator=">">
         <transition-group name="breadcrumb">
           <el-breadcrumb-item v-for="item in levelList" :key="item.path">
             <a v-if="item.feedback" @click.prevent="handleLink(item)">{{ item.title }}</a>
             <span v-else class="no-redirect">{{ item.title }}</span>
           </el-breadcrumb-item>
         </transition-group>
       </el-breadcrumb>
     </div>
   </div> -->
  </div>
  <div id="portal-container" :class="[{ 'special': levelList && levelList.length }, 'comp-container']">
    <router-view v-slot="{ Component, route }">
      <component :is="Component" :key="route.path" :routeList="route"/>
       
    </router-view>

    <el-backtop target="#portal-container" :right="100" :bottom="100" />
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
import logo from '@/assets/logo/logo.png'
import MenuItem from './MenuItem'
import Screenfull from '@/components/Screenfull'
import usePermissionStore from '@/store/modules/permission'
import useUserStore from '@/store/modules/user'
import useAppStore from "@/store/modules/app"
import { portalHome } from "@/router";
import defaultAvatar from '@/assets/images/avatar.png'
const title = import.meta.env.VITE_APP_TITLE;
const permissionStore = usePermissionStore()
const userStore = useUserStore()
const appStore = useAppStore()
const route = useRoute();
const router = useRouter();
import {getUser} from "@/api/system/user";

const constantMenus = [
  {
    permissionId: "1",
    permissionType: "menu",
    openType: "tab",
    permissionName: "首页",
    path: portalHome.path,
    icon: "dashboard",
    permissionSort: 0,
    feedback: "view",
  },
];


const userObj = ref({})
const userId = userStore.userInfo.staffId;


function getUserProfile() {
  getUser(userId).then(res => {
    userObj.value =res.data;
  })
}

const menusData = computed(() => {
  return [...constantMenus, ...permissionStore.portalMenus]
});

const levelList = computed(() => {
  const { indexPath } = route.meta
  const newLevelList = [{ title: '首页', path: portalHome.path, feedback: "view" }]

  if (indexPath && indexPath.length) {
    let tempMenus = permissionStore.portalMenus
    for (let layer = 0; layer < indexPath.length; layer++) {
      const layerIndex = indexPath[layer];
      if (tempMenus && tempMenus[layerIndex]) {
        newLevelList.push({
          title: tempMenus[layerIndex].permissionName,
          path: tempMenus[layerIndex].path,
          feedback: tempMenus[layerIndex].feedback
        })
      }
      tempMenus = tempMenus[layerIndex].children
    }


    return newLevelList
  }

  return []
});


function handleLink(item) {
  const { feedback, path } = item

  if (feedback === 'view') {
    router.push({ path })
  } else if (feedback === 'blank') {
    window.open(path)
  }
}

function handleCommand(command) {
  if (command === 'homeLayout') {
    appStore.openHomeDrawer()
  } else if (command === 'logout') {
    userStore.logOut()
  }
}

const goAdmin = () => {
  router.push({ path: '/system/index' })
}

function contentWidth() {
  if (appStore.homeLayoutSize.width === '100%') {
    return {
      width: '100%'
    }
  }
  return {
    width: '100%',
    maxWidth: appStore.homeLayoutSize.width
  }
}

// 组件挂载时加载数据
onMounted(() => {
  getUserProfile();
});

</script>

<style lang="scss" scoped>
.uni-topnav-md{
  position: fixed;
  width: 100%;
  top: 56px;
}
.header {
  position: fixed;
  top: 0px;
  left: 0px;
  z-index: 999;
  width: 100%;
}

.box {
  width: 100%;
  height: 78px;
  display: flex;
  justify-content: center;
  background-color: #ffffff;
  border-bottom: 1px solid #e6e6e6;
  position: absolute;
  top: 100px;
}

.logo-box {
  height: 30px;
  width: 250px;
  display: flex;
  align-items: center;
  flex-shrink: 0;

  .logo {
    height: 30px;
    width: 30px;
    margin-right: 10px;
  }

  .title {
    font-size: 20px;
    font-weight: bold;
  }
}

.menu {
  width: calc(100% - 475px);
}

.right-menu {
  height: 60px;
  width: 225px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex-shrink: 0;

  .right-menu-item {
    display: flex;
    margin: 0px 8px;
    font-size: 18px;
    color: #5a5e66;
    align-items: center;

    &.hover-effect {
      cursor: pointer;

      &:hover {
        color: var(--el-menu-active-color);
      }
    }
  }

  .avatar-wrapper {
    height: 36px;
    display: flex;
    align-items: center;

    .user-avatar {
      width: 36px;
      height: 36px;
    }
  }
}

.content {
  width: 1366px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0px 20px;
}

.bread {
  width: 100%;
  height: 35px;
  display: flex;
  justify-content: center;
  background-color: #f6f6f6;
}

.comp-container {
  position: fixed;
  top: 94px;
  width: 100%;
  height: calc(100vh - 94px);
  overflow-y: auto;
}

.special {
  top: 93px;
  height: calc(100vh - 128px);

  .iframe-container {
    height: calc(100vh - 113px);
  }
}


.users-info {
  display: flex;
  align-items: center;

  .left-user-img {
    width: 90px;
    height: 90px;
    border-radius: 50%;
    background: #3288ff;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      width: 100%;
      border-radius: 50%;
    }
  }

  .right-user-info {
    margin-left: 24px;
    flex: 1;

    .right-user-info-top {
      font-size: 20px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #333333;
      padding-bottom: 12px;
      border-bottom: 1px solid #e8eaeb;
    }

    .right-user-info-bottom {
      font-size: 15px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #8c9398;
      padding-top: 12px;
    }
  }
}
#adminbutton{
  display: flex;
  align-items: center;
  color:#606266;
}
@media screen and (max-width: 768px) {
  .logo-box {
    width: 120px;
  }

  .menu {
    width: calc(100% - 190px);
  }

  .right-menu {
    width: 70px;
  }

  #adminbutton {
    display: none
  }

  #screenfull {
    display: none;
  }
}
</style>
