<!-- 访客信息 -->
<template>
    <div class="container-table-box">
        <el-card>
      <el-row :gutter="24">
        <el-col :span="24" :xs="24">
          <dialog-search @getList="getList" formRowNumber="3" :columns="tabelForm.columns">
            <template #formList>
              <el-form :model="formData" ref="queryForm" :inline="true" label-width="80px">
                <el-form-item label="访客姓名" prop="name">
                <el-input v-model="formData.name" placeholder="请输入访客姓名" clearable />
              </el-form-item>

              <el-form-item label="访客电话" prop="telephone">
                <el-input v-model="formData.telephone" placeholder="请输入访客电话" clearable />
              </el-form-item>

              <el-form-item label="注册时间" prop="registTime">
                <div class="formRangeTime flex">
                  <el-date-picker @change="registTimeChange" v-model=formData.registTime type="date" format="YYYY-MM-DD"
                                  value-format="YYYY-MM-DD"
                                  placeholder="开始日期"/>
                  <span class="formRangeTime-tip">至</span>
                  <el-date-picker @change="registEndTimeChange" v-model=formData.registEndTime format="YYYY-MM-DD"
                                  value-format="YYYY-MM-DD" type="date"
                                  placeholder="结束日期"/>
                </div>
              </el-form-item>
              <el-form-item label="访客单位" prop="visitorUnit">
                <el-input v-model="formData.visitorUnit" placeholder="请输入访客单位" clearable />
              </el-form-item>
              <el-form-item label="访客类型" prop="visitorType" :width="1">
                <el-select v-model="formData.type" placeholder="全部" clearable>
                  <el-option v-for="(item, index) of VisitorType" :key="index" :label="item.label" :value="item.value"/>
                </el-select>
              </el-form-item>
              </el-form>
            </template>
            <template #searchList>
              <el-button type="primary" icon="Search" @click="handleQuery">
                搜索
              </el-button>
              <el-button icon="Refresh" @click="resetQuery"> 重置 </el-button>
            </template>
            <!-- <template #searchBtnList>
              <el-button type="primary" icon="Plus" @click="handleAdd">
                新增
              </el-button>
            
            </template> -->
          </dialog-search>
  
          <PublicTable ref="publictable" :rowKey="tabelForm.tableKey" :tableData="userList" :columns="tabelForm.columns"
            :configFlag="tabelForm.tableConfig" :pageValue="formData" :total="total" :getList="getList">
  
  
            <!-- 操作列 -->
            <template #operation="{ scope }">
                <el-button size="mini" type="text" icon="View" title="查看" @click="handleView(scope.row)"></el-button>
            <el-button size="mini" type="text" icon="Edit" @click="handleUpdate(scope.row)" title="编辑"></el-button>
            <el-button size="mini" type="text" icon="Delete" @click="handleDelete(scope.row)" title="删除"></el-button>
            <el-button size="mini" type="text" @click="handleAddUserBlock(scope.row)" title="添加黑名单">
              <i class="icon iconfont icon-heimingdan"></i> 
            </el-button>
            </template>
          </PublicTable>
  

  
  
          <DialogBox :visible="open" dialogWidth="50%" @save="submits"   @cancellation="cancellation"
            @close="close" :dialogFooterBtn="dialogFooterBtn" CloseSubmitText="取消" SaveSubmitText="确定"
            :dialogTitle="headerTitle">
            <template #content>
              <addDom ref="addRef" :rowData="rowData"  @submitClose="submitClose"  :popup-type="popupType" ></addDom>
             
            </template>
          </DialogBox>
        </el-col>
      </el-row>
      </el-card>
    </div>
  </template>
  
  <script setup>
  import { ref, onMounted, getCurrentInstance,reactive } from "vue";
  import { ElMessage, ElMessageBox } from "element-plus";
  import {findVisitorInfoList, deleteVisitorInfo,addUserBlack,getConfigInfo} from '@/api/visitor/visitorInfo/index'
  import addDom from './components/add'

  // 表格配置
  const tabelForm = ref({
    tableKey: "1",
    isShowRightToolbar: true,
    showSearch: true,
    columns: [
          {
            fieldIndex: "name", // 对应列内容的字段名
            label: "访客姓名", // 显示的标题
            resizable: true, // 对应列是否可以通过拖动改变宽度
            visible: true, // 展示与隐藏
            sortable: false, // 对应列是否可以排序
            fixed: "", //固定
            minWidth: "120px", //最小宽度%
            width: "", //宽度
            align: "center", //表格对齐方式
            showOverFlowTooltip: true, //是否显示提示
          },

          {
            label: "访客电话",
            width: "",
            minWidth: "120px", ///最小宽度%
            fixed: "", //固定
            visible: true,
            type:'phoneHidden'
          },
          {
            label: "证件号码",
            slotname: "idNumber",
            width: "",
            minWidth: "120px", //最小宽度%
            fixed: "", //固定
            visible: true,
            type:'idNumberHidden'
          },
          {
            fieldIndex: "visitorUnit", // 对应列内容的字段名
            label: "访客单位", // 显示的标题
            resizable: true, // 对应列是否可以通过拖动改变宽度
            visible: true, // 展示与隐藏
            sortable: false, // 对应列是否可以排序
            fixed: "", //固定
            minWidth: "150px", //最小宽度%
            width: "", //宽度
            align: "left", //表格对齐方式
          },
        
          {
            fieldIndex: "plateNo", // 对应列内容的字段名
            label: "车牌号码", // 显示的标题
            resizable: true, // 对应列是否可以通过拖动改变宽度
            visible: true, // 展示与隐藏
            sortable: false, // 对应列是否可以排序
            fixed: "", //固定
            minWidth: "150px", //最小宽度%
            width: "", //宽度
            align: "center", //表格对齐方式
          },
          {
            fieldIndex: "registTime", // 对应列内容的字段名
            label: "注册时间", // 显示的标题
            resizable: true, // 对应列是否可以通过拖动改变宽度
            visible: true, // 展示与隐藏
            sortable: true, // 对应列是否可以排序
            fixed: "", //固定
            minWidth: "150px", //最小宽度%
            width: "", //宽度
            align: "center", //表格对齐方式
          },
          {
            label: "操作",
            slotname: "operation",
            width: "180",
            fixed: "right", //固定
            visible: true,
          },
        ],
    tableConfig: {
      needPage: true,
      index: true,
      selection: false,
      reserveSelection: false,
      indexFixed: false,
      selectionFixed: false,
      indexWidth: "50",
      loading: false,
      showSummary: false,
      height: null,
    },
  });
  
  // 查询参数
  const formData = ref({
    pageNum: 1,
    pageSize: 10,
  });
  // 是否显示身份证
  const isIdCard = ref(false)
  // 表格数据
  const userList = ref([{}]);
  

  // 表格总数
  const total = ref(0);
  // 是否显示弹窗底部按钮
  const dialogFooterBtn = ref(true)
  // 弹窗标题
  const headerTitle = ref("");
  
  // dom
  const addRef =ref(null)
  // 点击行信息
  const rowData = ref({});
  
  // 是否显示弹窗
  const open = ref(false);
  
  // 弹窗宽度
  const dialogWidth = ref("");
  
  
  // 弹窗类型
  const popupType = ref("");
  
// 字典
const VisitorType = reactive ([{label: "白名单", value: "0"}, {label: "黑名单", value: "1"}])
  // 获取配置的方法
  const getConfig = async () => {
      try {
        const res = await getConfigInfo()
        const isIdC = res.rows[0].isIdcard
        
        // 更新响应式变量
        isIdCard.value = isIdC === '0'
        
        // 等待DOM更新后执行
        await nextTick()
        // 操作响应式对象
        tabelForm.columns[2].visible = isIdCard.value
      } catch (error) {
        console.error('获取配置失败:', error)
      }
    }
  // 列表请求
  const getList = () => {
    tabelForm.value.tableConfig.loading = true;
    findVisitorInfoList(formData.value).then((res) => {
      userList.value = res.data.records;
      total.value = res.data.total;
      tabelForm.value.tableConfig.loading = false;
    });
  };
  
  // 查询
  const handleQuery = () => {
    formData.value.pageNum = 1;
    getList();
  };
  
  // 重置
  const resetQuery = () => {
    formData.value = {};
    formData.value.pageNum = 1;
    formData.value.pageSize = 10;
    getList();
  };
  
  // 新增
  const handleAdd = () => {
    headerTitle.value = "新增访客信息";
    popupType.value = "add";
    dialogWidth.value = '50%'
    rowData.value = {}
    dialogFooterBtn.value = true;
    open.value = true;
  };
  
  // 修改
  const handleUpdate = (row) => {
    headerTitle.value = "修改访客信息";
    popupType.value = "edit";
    dialogWidth.value = '50%'
    dialogFooterBtn.value = true;
    rowData.value = row;
    open.value = true;
  };
  
  // 查看详情
  const handleView = (row) => {
    dialogFooterBtn.value = false;
    headerTitle.value = "查看访客信息";
    rowData.value = row
    dialogWidth.value = '50%'
    popupType.value = "view";
    open.value = true;
  };
  
  // 删除
  const handleDelete = (row) => {
    ElMessageBox.confirm("确认删除该条访客信息？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(() => {
       deleteVisitorInfo(row.id).then((res) => {
          if (res.code == '1') {
            ElMessage.success("删除成功");
            getList();
          } 
        });
      })
      .catch(() => {
        // 用户取消删除
      });
  };
  
  const handleAddUserBlock = async (row) => {
      try {
        await ElMessageBox.confirm("确定要添加黑名单吗？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        });

        await addUserBlack(row.id);

        ElMessage({
          type: "success",
          message: "添加成功!",
        });

        getList();
      } catch (error) {
        ElMessage({
          type: "info",
          message: "已取消添加",
        });
      }
    };


  
  // 提交关闭
  const submitClose = () => {
    open.value = false;
    setTimeout(() => {
      getList();
    }, 100);
  };
  

  /** 点击提交 */
  const submits = () => {
    addRef.value.saveBtn();
  
  };
  /** 点击取消保存 */
  const cancellation = (val) => {
    close(false);
  };
  /** 关闭弹窗 */
  const close = (val) => {
    open.value = val;
  };
  
  const computedTimes = (type) => {
      const { registTime, registEndTime } = formData.value;

      let d1 = '';
      let d2 = '';

      if (registTime) {
        d1 = new Date(registTime.replace(/\-/g, '/'));
      }
      if (registEndTime) {
        d2 = new Date(registEndTime.replace(/\-/g, '/'));
      }

      // 检查时间是否合法
      if (
        registTime !== undefined &&
        registEndTime !== undefined &&
        registTime !== '' &&
        registEndTime !== '' &&
        d1 > d2
      ) {
        ElMessage.error('结束日期必须大于开始日期！');
        if (type === 'start') {
          formData.value.registTime = '';
        } else if (type === 'end') {
          formData.value.registEndTime = '';
        }
        return false;
      }
    };

    // 开始时间变化时触发
    const registTimeChange = () => {
      computedTimes('start');
    };

    // 结束时间变化时触发
    const registEndTimeChange = () => {
      computedTimes('end');
    };
  // 生命周期钩子：组件挂载时调用
  onMounted(() => {
    getConfig()
    getList()
  });



  </script>
  
  <style scoped>
  /* 添加自定义样式 */
  </style>
  