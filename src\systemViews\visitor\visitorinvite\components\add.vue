<template>
  <div
    class="dialog-box"
    :class="popupType === 'view' ? 'dialog-box-view' : 'dialog-box-edit'"
  >
    <el-form
      ref="ruleform"
      :model="formData"
      label-width="120px"
      :rules="popupType !== 'view' ? rules : {}"
    >

    <div class="common-box-one" v-if="popupTypeOther">
        <div class="common-header">
          <div class="common-header-line" />
          <div class="common-header-text">审批</div>
        </div>
      </div>

      <el-row v-if="popupTypeOther">
        <el-col :span="12">
          <el-form-item label="访问区域" prop="areaId">
            <!-- <el-select
             v-if="popupType != 'view'"
              v-model="formData.areaId"
              tag
              :placeholder="
                popupType !== 'view'
                  ? '请选择可访问区域'
                  : formData.areaId != null
                  ? ''
                  : null
              "
              clearable
             
              @change="findAreaList1()"
            >
              <el-option
                v-for="(item, index) of areaManagerList"
                :key="index"
                :label="item.areaName"
                :value="item.id"
              />
            </el-select> -->

            <span>
              {{ formData.areaId }}
            </span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="通行区域" prop="areaIdList">
            <el-select
              v-model="formData.areaIdList"
              multiple
              tag
              :placeholder="
                popupType !== 'view'
                  ? '请选择可通行区域'
                  : formData.areaIdList.length > 0
                  ? ''
                  : null
              "
              clearable
            >
              <el-option
                v-for="(item, index) of areaListData"
                :key="index"
                :label="item.areaName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <div class="common-box-one">
        <div class="common-header">
          <div class="common-header-line" />
          <div class="common-header-text">被访人信息</div>
        </div>
      </div>
      <el-row>
        <el-col :span="8">
          <el-form-item label="被访人电话" prop="phone">
            <span>{{formData.phone}}</span>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="被访人姓名" prop="inviter">
            <span>{{formData.inviter}}</span>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="被访人部门" prop="inviteUnit">
            <span>{{formData.inviteUnit}}</span>
          </el-form-item>
        </el-col>
      </el-row>

      <div class="common-box-one">
        <div class="common-header">
          <div class="common-header-line" />
          <div class="common-header-text">访客信息</div>
        </div>
      </div>

      <el-row>
        <el-col :span="12">
          <el-form-item label="访客类型" prop="type">
            <el-radio-group
              v-if="popupType != 'view'"
              v-model="formData.type"
              :disabled="popupType == 'view'"
            >
              <el-radio label="1" value="1">个人访客</el-radio>
              <el-radio label="99" value="99">团体访客</el-radio>
            </el-radio-group>
            <div v-if="popupType == 'view'">
              <span v-if="formData.type == '1'">{{ "个人访客" }}</span>
              <span v-if="formData.type == '99'">{{ "团体访客" }}</span>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="手机号码" prop="visitorTelephone">
            <el-select
              v-model="selectInfoId"
              placeholder="请输入访客手机号码"
              filterable
              remote
              reserve-keyword
              v-if="popupType != 'view'"
              :remote-method="findVisitorInfo"
              @change="findVisitorInfoChange"
            >
              <el-option
                v-for="item of phoneList"
                :key="item.id"
                :label="item.telephone"
                :value="item.id"
              >
                <template #default>
                  <div>{{ item.name + "/" + item.visitorUnit }}</div>
                </template>
              </el-option>
            </el-select>
            <span v-else>
              {{ formData.visitorTelephone }}
            </span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="访客姓名" prop="visitorName">
            <el-input
              v-model="formData.visitorName"
              v-if="popupType != 'view'"
              placeholder="请输入访客姓名"
              clearable
              @input="$forceUpdate()"
            />

            <span v-else>
              {{ formData.visitorName }}
            </span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="访客单位" prop="unit">
            <el-input
              v-model="formData.unit"
              v-if="popupType != 'view'"
              placeholder="请输入访客单位"
              clearable
              @input="$forceUpdate()"
            />
            <span v-else>
              {{ formData.unit }}
            </span>
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="isIdCard">
          <el-form-item label="证件类型" prop="licenseType">
            <el-select
              v-model="formData.licenseType"
              v-if="popupType != 'view'"
              placeholder="请选择"
            >
              <el-option
                v-for="dict in id_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>

            <span v-else>
              {{ $formatDictLabel(formData.unit, id_type) }}
            </span>
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="isIdCard">
          <el-form-item label="证件号码" prop="idNumber">
            <el-input
              v-model="formData.idNumber"
              v-if="popupType != 'view'"
              placeholder="请输入证件号码"
              clearable
              @input="$forceUpdate()"
            />

            <span v-else>
              {{ formData.isIdCard }}
            </span>
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="!popupTypeOther">
          <el-form-item label="访问区域" prop="areaId">
            <el-select
             v-if="popupType != 'view'"
              v-model="formData.areaId"
              tag
              :placeholder="
                popupType !== 'view'
                  ? '请选择可访问区域'
                  : formData.areaId != null
                  ? ''
                  : null
              "
              clearable
             
              @change="findAreaList1()"
            >
              <el-option
                v-for="(item, index) of areaManagerList"
                :key="index"
                :label="item.areaName"
                :value="item.id"
              />
            </el-select>

            <span v-else>
              {{ formData.areaId }}
            </span>
          </el-form-item>
        </el-col>
        <el-col :span="12"  v-if="!popupTypeOther">
          <el-form-item label="通行区域" prop="areaIdList">
            <el-select
              v-model="formData.areaIdList"
              multiple
              tag
              :placeholder="
                popupType !== 'view'
                  ? '请选择可通行区域'
                  : formData.areaIdList.length > 0
                  ? ''
                  : null
              "
              clearable
              :disabled="popupType === 'view'"
            >
              <el-option
                v-for="(item, index) of areaListData"
                :key="index"
                :label="item.areaName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="到访时间" prop="visitorTime">
            <el-date-picker
              v-model="formData.visitorTime"
           v-if="popupType != 'view'"
              type="datetime"
              @change="visitorTimeChange"
              format="YYYY-MM-DD HH:mm"
              value-format="YYYY-MM-DD HH:mm:00"
              placeholder="选择预计到访时间"
            />
            <span v-else>
              {{ formData.visitorTime }}
            </span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="离开时间" prop="visitorEndTime">
            <el-date-picker
              v-model="formData.visitorEndTime"
        v-if="popupType != 'view'"
              type="datetime"
              @change="visitorEndTimeChange"
              format="YYYY-MM-DD HH:mm"
              value-format="YYYY-MM-DD HH:mm:00"
              placeholder="选择预计离开时间"
            />

            <span v-else>
              {{ formData.visitorEndTime }}
            </span>
          </el-form-item>
        </el-col>
        <el-col :span="12" v-show="isSharedCubicle">
          <el-form-item label="共享工位" prop="sharedCubicle">
            <el-radio-group
               v-if="popupType != 'view'"
              v-model="formData.sharedCubicle"
              :disabled="popupType == 'view'"
            >
              <el-radio label="0">是</el-radio>
              <el-radio label="1">否</el-radio>
            </el-radio-group>

            <span  v-if="popupType == 'view' && formData.sharedCubicle=='0'">
            是
            </span>

            <span  v-if="popupType == 'view' && formData.sharedCubicle=='0'">
            否
            </span>
          </el-form-item>
        </el-col>
        <el-col :span="12" v-show="isRepast">
          <el-form-item label="餐厅就餐" prop="isDining">
            <el-radio-group
              v-model="formData.isDining"
          v-if="popupType != 'view'"
            >
              <el-radio label="0">是</el-radio>
              <el-radio label="1">否</el-radio>
            </el-radio-group>

            <span  v-if="popupType == 'view' && formData.isDining=='0'">
            是
            </span>

            <span  v-if="popupType == 'view' && formData.isDining=='0'">
            否
            </span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="来访事由" prop="reason">
              <el-radio-group
                v-model="formData.reason"
              v-if="popupType != 'view'"
              >
                <el-radio-button
                  v-for="dict in visit_reason"
                  :key="dict.value"
                  :label="dict.value"
                  >{{ dict.label }}
                </el-radio-button>
              </el-radio-group>

              <span v-else>
              {{ $formatDictLabel(formData.reason, visit_reason) }}
            </span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="formData.remark"
                v-if="popupType != 'view'"
              placeholder="备注"
              clearable
            />
            <span v-else>
              {{formData.remark }}
            </span>
          </el-form-item>
        </el-col>
      </el-row>
      <div class="common-box-one">
        <div class="common-header">
          <div class="common-header-line" />
          <div class="common-header-text">照片</div>
        </div>
      </div>
      <el-row>
        <el-col :span="12" style="margin-left: -120px">
          <el-form-item label="" prop="faceImg">
            <ImageUpload
              v-if="popupType !== 'view'"
              v-model="fileList"
              limit="1"
              fileSize="10"
              :paramsData="imageExtraData"
              :uploadImgUrl="
                '/park' + apiUrls + '/visitor/visitorInfo/uploadFaceImg'
              "
            />
          </el-form-item>

          <template v-if="popupType === 'view'">
            <!-- 查看模式显示图片 -->
            <PreviewImage
              style="width: 150px"
              :photo-id="formData.faceImg"
            ></PreviewImage>
          </template>
        </el-col>
      </el-row>
    </el-form>
    <!-- <div class="common-box-one" v-if="formData.type == '99'"> -->
    <div class="common-box-one">
      <div class="common-header">
        <div class="common-header-line" />
        <div class="common-header-text">随行人员信息</div>
        <el-button
          class="add margin-left-8-px"
          v-if="popupType !== 'view'"
          type="primary"
          plain
          icon="Plus"
          size="mini"
          @click="addPerson"
          >添加人员</el-button
        >
        <el-button
          class="add margin-left-8-px"
          v-if="popupType !== 'view'"
          type="primary"
          plain
          icon="Upload"
          size="mini"
          @click="importPerson"
          >导入人员</el-button
        >
        <el-button
          class="add margin-left-8-px"
          v-if="popupType !== 'view'"
          type="primary"
          plain
          icon="Download"
          size="mini"
          @click="handleExport"
          >模板下载</el-button
        >
        <el-button
          class="add margin-left-8-px"
          v-if="popupType !== 'view'"
          type="primary"
          plain
          icon="Download"
          size="mini"
          @click="handlePersonExport"
          >导出人员</el-button
        >
      </div>
      <div class="common-value-content">
        <el-table
          v-loading="loading"
          :data="assetDetailList"
          border
          :header-cell-style="{
            background: 'rgba(244, 248, 252, 0.39)',
            color: '#222222',
            fontWeight: 'bold',
            borderColor: '#E2E7F1',
          }"
        >
          <!-- 序号列 -->
          <el-table-column
            type="index"
            label="序号"
            min-width="10%"
            align="center"
          />

          <!-- 访客姓名列 -->
          <el-table-column
            prop="name"
            label="访客姓名"
            min-width="7%"
            align="center"
          >
            <template #default="{ row }">
              <div>{{ row.name || "-" }}</div>
            </template>
          </el-table-column>

          <!-- 手机号码列 -->
          <el-table-column
            prop="telephone"
            label="手机号码"
            min-width="10%"
            align="center"
          >
            <template #default="{ row }">
              <div>{{ row.telephone || "-" }}</div>
            </template>
          </el-table-column>

          <!-- 访客单位列 -->
          <el-table-column
            prop="personnelUnit"
            label="访客单位"
            min-width="10%"
            align="center"
          >
            <template #default="{ row }">
              <div>{{ row.personnelUnit || "-" }}</div>
            </template>
          </el-table-column>

          <!-- 证件号码列（动态显示） -->
          <el-table-column
            v-if="isIdCard"
            prop="idNumber"
            label="证件号码"
            min-width="10%"
            align="center"
          >
            <template #default="{ row }">
              <div>{{ row.idNumber || "-" }}</div>
            </template>
          </el-table-column>

          <!-- 操作列（动态显示） -->
          <el-table-column
            prop="operation"
            v-if="popupType != 'view'"
            label="操作"
            min-width="10%"
            align="center"
          >
            <template #default="{ row }">
              <el-button
                size="mini"
                type="text"
                icon="Edit"
                title="修改"
                @click="handleEdit(row)"
              >
              </el-button>
              <el-button
                size="mini"
                type="text"
                icon="Delete"
                title="删除"
                @click="handleDel(row)"
              >
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <el-row>
      <!-- <el-col :span="12" v-show="isCars"> -->
      <el-col :span="24">
        <div class="common-box-one">
          <div class="common-header">
            <div class="common-header-line" />
            <div class="common-header-text">随行车辆信息</div>

            <el-button
              class="add margin-left-8-px"
              v-if="popupType !== 'view'"
              type="primary"
              plain
              icon="Plus"
              size="mini"
              @click="addItemFun('0')"
              >添加随行车辆</el-button
            >
          </div>
        </div>
        <div class="common-value-content">
          <el-table
            :data="itemList1"
            border
            :header-cell-style="{
              background: 'rgba(244, 248, 252, 0.39)',
              color: '#222222',
              fontWeight: 'bold',
              borderColor: '#E2E7F1',
            }"
          >
            <!-- 序号列 -->
            <el-table-column
              type="index"
              label="序号"
              min-width="10%"
              align="center"
            />

            <!-- 车牌号码列 -->
            <el-table-column
              prop="itemAttribute"
              label="车牌号码"
              min-width="10%"
              align="center"
            >
              <template #default="{ row }">
                <div>{{ row.itemAttribute || "-" }}</div>
              </template>
            </el-table-column>

            <!-- 操作列（动态显示） -->
            <el-table-column
              v-if="popupType !== 'view'"
              prop="operation"
              label="操作"
              min-width="10%"
              align="center"
            >
              <template #default="{ row }">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-edit el-table-btn"
                  @click="editItem(row, '0')"
                >
                  修改
                </el-button>
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-delete el-table-btn"
                  @click="delVisitorItemByIdFun(row.id)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-col>
    </el-row>
    <el-row style="margin-top: 12px">
      <!-- <el-col :span="12" v-show="isBelongings"> -->
      <el-col :span="24">
        <div class="common-box-one">
          <div class="common-header">
            <div class="common-header-line" />
            <div class="common-header-text">随行物品信息</div>

            <el-button
              class="add margin-left-8-px"
              v-if="popupType !== 'view'"
              type="primary"
              plain
              icon="Plus"
              size="mini"
              @click="addItemFun('1')"
              >添加随行物品</el-button
            >
          </div>
        </div>
        <div class="common-value-content">
          <el-table
            :data="itemList2"
            border
            :header-cell-style="{
              background: 'rgba(244, 248, 252, 0.39)',
              color: '#222222',
              fontWeight: 'bold',
              borderColor: '#E2E7F1',
            }"
          >
            <el-table-column
              type="index"
              label="序号"
              min-width="10%"
              align="center"
            />
            <el-table-column
              prop="itemAttribute"
              label="随行物品"
              min-width="10%"
              align="center"
            >
              <template #default="{ row }">
                <div>{{ row.itemAttribute || "-" }}</div>
              </template>
            </el-table-column>
            <el-table-column
              v-if="popupType != 'view'"
              prop="operation"
              label="操作"
              min-width="10%"
              align="center"
            >
              <template #default="{ row }">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-edit el-table-btn"
                  @click="editItem(row, '1')"
                  >修改
                </el-button>
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-delete el-table-btn"
                  @click="delVisitorItemByIdFun(row.id)"
                  >删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-col>
    </el-row>

    <DialogBox
      :visible="open"
      :dialogWidth="dialogWidth"
      @save="submits"
      @cancellation="cancellation"
      @close="close"
      :dialogFooterBtn="dialogFooterBtn"
      CloseSubmitText="取消"
      SaveSubmitText="确定"
      :dialogTitle="headerTitle"
    >
      <template #content>
        <UploadFile
          v-if="popupTypeLi == 'upload'"
          ref="uploadRef"
          :uploadData="importData"
          :action="actionUrl"
          :limit="1"
          :accept="'.xlsx, .xls'"
          :auto-upload="false"
          tip="提示：仅允许导入“xls”或“xlsx”格式文件！"
          @file-success="handleFileSuccess"
          @file-error="handleFileError"
        />

        <addPersonDom
          v-if="popupTypeLi == 'addPerson' || popupTypeLi == 'editPerson'"
          ref="addPersonRef"
          @submitClose="closeRefreshPerson"
          :rowData="rowData"
          :popupType="popupTypeLi"
        ></addPersonDom>

        <addItemDom
          v-if="popupTypeLi == 'addItem' || popupTypeLi == 'EditItem'"
          ref="addItemRef"
          @submitClose="closeRefreshItem"
          :rowData="rowData"
          :addThingsType="addThingsType"
          :popupType="popupTypeLi"
        ></addItemDom>
      </template>
    </DialogBox>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { formatMinuteTime } from "@/utils";
import { apiUrl } from "@/utils/config";
import UploadFile from "@/components/UploadFile/index";
import addPersonDom from "./addPerson";
import addItemDom from "./addItem";
import {
  save,
  findById,
  getUUid,
  queryListByInviteId,
  deletePersonnelById,
  areaList,
  selectAreaList,
  getUserProfile,
  findVisitorInfoByTel,
  delAttachmentById,
  getConfigInfo,
} from "@/api/visitor/visitorinvite/components/add";
import {
  findByIdAndAttribute,
  delVisitorItemById,
} from "@/api/visitor/visitorinvite/components/addItem";
import { queryConfig } from "@/api/visitor/visitorParameterConfig/index";
import { editResult } from '@/api/visitor/visitorinvite/index'
const emit = defineEmits(["submitClose"]);
const props = defineProps({
  closeBtn: {
    type: Function,
    default: null,
  },
  popupType: {
    type: String,
    default: "",
  },
  rowData: {
    type: Object,
    default: () => ({}),
  },
  popupTypeOther:{
    type: String,
    default: "",
  }
});

const ruleform = ref(null);
const formData = reactive({
  id: "",
  type: "1",
  inviter: "",
  inviteUnitId: "",
  inviteUnit: "",
  phone: "",
  sharedCubicle: "1",
  isDining: "1",
  licenseType: "1",
  faceImg: "",
  areaIdList: [],
});

const uploadRef = ref();
const addItemRef = ref();
const addPersonRef = ref();
// 是否显示弹窗底部按钮
const dialogFooterBtn = ref(true);
// 弹窗标题
const headerTitle = ref("");
// 点击行信息
const rowData = ref({});
const addThingsType = ref();
// 是否显示弹窗
const open = ref(false);
// 弹窗宽度
const dialogWidth = ref("");
// 弹窗类型
const popupTypeLi = ref("");
const selectInfoId = ref("");
const phoneList = ref([]);
const isIdCard = ref(false);
const areaManagerList = ref([]);
const inviteId = ref("");
const importData = reactive({ inviteId: "" });
const fileList = ref([]);
const actionUrl = ref(
  `${import.meta.env.VITE_APP_BASE_API}/visitorinvite/personnel/importPersonnel`
);
const imageExtraData = reactive({ businessId: "" });
const areaListData = ref([]);
const isSharedCubicle = ref(false);
const isRepast = ref(false);
const isBelongings = ref(false);
const isCars = ref(false);
const conifData = ref({});
const visitorData = ref({});
const assetDetailList = ref([{}]);
const itemList1 = ref([]);
const itemList2 = ref([]);
const loading = ref(false);
const { proxy } = getCurrentInstance();
const { id_type, visit_reason } = proxy.useDict("id_type", "visit_reason");
const idNumberValidator = async (rule, value, callback) => {
  const re =
    /^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
  if (!value) {
    return callback(new Error("请输入证件证号"));
  }
  if (formData.licenseType == "1" && !re.test(value)) {
    return callback(new Error("请输入正确的证件号码"));
  }
  callback();
};
const rules = reactive({
  type: [{ required: true, message: "请选择访客类型", trigger: "blur" }],
  areaId: [{ required: true, message: "请选择可访问区域", trigger: "blur" }],
  areaIdList: [
    { required: true, message: "请选择可通行区域", trigger: "blur" },
  ],
  reason: [{ required: true, message: "请选择来访事由", trigger: "blur" }],
  unit: [{ required: true, message: "请输入来访单位", trigger: "blur" }],
  visitorTime: [
    { required: true, message: "请选择来访开始时间", trigger: "blur" },
  ],
  visitorEndTime: [
    { required: true, message: "请选择来访结束时间", trigger: "blur" },
  ],
  // inviter: [{ required: true, message: "请输入邀约人", trigger: "blur" }],
  // inviteUnit: [{ required: true, message: "请输入邀约单位", trigger: "blur" }],
  // phone: [{ required: true, message: "请输入联系电话", trigger: "blur" }],
  visitorTelephone: [
    { required: true, message: "请输入访客手机号码", trigger: "blur" },
    {
      required: true,
      pattern: /^[1][3,4,5,7,8,9][0-9]{9}$/,
      message: "请输入正确的手机号码",
      trigger: "blur",
    },
  ],
  visitorName: [{ required: true, message: "请输入访客姓名", trigger: "blur" }],
  idNumber: [{ required: true, validator: idNumberValidator, trigger: "blur" }],
});

onMounted(() => {
  selectAreaListFun();
  initConfig();

  if (props.popupType === "add") {
    getUUid().then((res) => {
      formData.id = res;
      props.rowData.id = res;
      importData.inviteId = res;
      imageExtraData.businessId = res.id;
    });
    getUser();
  }

  if (props.popupType == "edit" || props.popupType == "view") {
    formData.areaId = props.rowData.areaId;
    importData.inviteId = props.rowData.id;
    findByIdFun(props.rowData.id);
    queryListByInviteId(props.rowData.id);
    findVisitorItem(props.rowData.id);
    imageExtraData.businessId = props.rowData.visitorTelephone;
  }

  inviteId.value = formData.id;
  findAreaList();
  getConfig();
});

const getConfig = () => {
  getConfigInfo().then((res) => {
    let isIdC = res.rows[0].isIdcard;
    isIdCard.value = isIdC == "0";
  });
};

const selectAreaListFun = () => {
  selectAreaList().then((res) => {
    areaManagerList.value = res.data;
    if (props.popupType == "add") {
      formData.areaId = areaManagerList.value[0].id;
      nextTick(() => {
        findAreaList();
      });
    }
  });
};

const findAreaList = () => {
  let data = { parentAreaId: formData.areaId };
  areaList(data).then((res) => {
    areaListData.value = res.data;
  });
};

const findAreaList1 = () => {
  formData.areaIdList = [];
  let data = { parentAreaId: formData.areaId };
  areaList(data).then((res) => {
    areaListData.value = res.data;
  });
};

const initConfig = () => {
  queryConfig().then((res) => {
    conifData.value = res.config;
    isSharedCubicle.value = conifData.value.isSharedCubicle == 0;
    isRepast.value = conifData.value.isRepast == 0;
    isBelongings.value = conifData.value.isBelongings == 0;
    isCars.value = conifData.value.isCars == 0;
  });
};

const getUser = () => {
  getUserProfile().then((response) => {
    formData.inviter = response.data.nickName;
    formData.inviteUnitId = response.data.unitId;
    formData.inviteUnit = response.data.unitName;
    formData.phone = response.data.phonenumber;
  });
};

const findByIdFun = (id) => {
  findById(id).then((res) => {
    if (res.code === 200) {
      Object.assign(formData, res.data);
      selectInfoId.value = formData.visitorTelephone;
      imageExtraData.businessId = res.data.id;
      if (formData.faceImg != null && formData.faceImg !== "") {
        const url = fileUrl + formData.faceImg;
        fileList.value.push({
          name: "",
          url: url,
          attachmentId: formData.faceImg,
        });
      }
    }
  });
};

const queryListByInviteIdFun = (id) => {
  queryListByInviteId(id).then((res) => {
    if (res.code === 200) {
      assetDetailList.value = res.data;
    }
  });
};

const deletePersonnelByIdFun = (id) => {
  deletePersonnelById(id).then((res) => {
    if (res.code === 200) {
      queryListByInviteIdFun(formData.id);
    }
  });
};

const importPerson = () => {
  popupTypeLi.value = "upload";
  headerTitle.value = "导入人员";
  dialogWidth.value = "400px";
  open.value = true;
};

const handleExport = () => {
  proxy.download(
    `${apiUrl}/sys/documentTemplate/downloadTemplateByCode`,
    {
      docCode: "D_WIS_VISITOR_PERSONNEL_IMPORT",
    },
    `访客导入模板_${formatMinuteTime(new Date())}.xlsx`
  );
};
const handlePersonExport = () => {
  proxy.download(
    `${apiUrl}/visitorinvite/personnel/exportPersonnel`,
    {
      ...importData,
    },
    `来访人员_${formatMinuteTime(new Date())}.xlsx`
  );
};

// 文件上传失败处理
const handleFileError = (res) => {
  ElMessage.error("导入失败");
};

// 文件上传成功处理
const handleFileSuccess = (response) => {
  if (response.response.code == "1") {
    ElMessage.success("导入成功");
    setTimeout(() => {
      upload.isUploading = false;
      open.value = false;
      queryListByInviteIdFun(props.rowData.id);
    }, 800);
  } else {
    ElMessage.error(response.response.message);
  }
};

const addPerson = () => {
  rowData.value = {
    id: "",
  };
  rowData.value.id = formData.id;
  headerTitle.value = "添加人员";
  popupTypeLi.value = "addPerson";
  dialogWidth.value = "30%";
  open.value = true;
};

const handleDel = (row) => {
  ElMessageBox.confirm("确认删除该访客人员吗？")
    .then(() => {
      deletePersonnelByIdFun(row.id);
    })
    .then(() => {
      queryListByInviteIdFun(formData.id);
    })
    .catch(() => {});
};

const handleEdit = (row) => {
  headerTitle.value = "修改人员";
  popupTypeLi.value = "editPerson";
  rowData.value = row;
  dialogWidth.value = "30%";
  open.value = true;
};

// 关闭
const closeRefreshPerson = () => {
  open.value = false;
  setTimeout(() => {
    queryListByInviteIdFun(formData.id);
  }, 100);
};

const addItemFun = (type) => {
  rowData.value = {};
  rowData.value.id = formData.id;
  open.value = true;
  popupTypeLi.value = "addItem";
  headerTitle.value = type == "0" ? "添加随行车辆" : "添加随行物品";
  dialogWidth.value = "25%";
  addThingsType.value = type;
};

const editItem = (row, type) => {
  rowData.value = row;
  open.value = true;
  popupTypeLi.value = "EditItem";
  headerTitle.value = type == "0" ? "添加随行车辆" : "添加随行物品";
  dialogWidth.value = "25%";
  addThingsType.value = type;
};

const closeRefreshItem = () => {
  open.value = false;
  setTimeout(() => {
    findVisitorItem(formData.id);
  }, 100);
};
const delVisitorItemByIdFun = (id) => {
  ElMessageBox.confirm("确定删除吗？")
    .then(() => {
      delVisitorItemById(id).then((res) => {
        if (res.code === 200) {
          ElMessage.success("删除成功");
          findVisitorItem(formData.id);
        }
      });
    })
    .catch(() => {});
};

const findVisitorInfoChange = (data) => {
  phoneList.value.forEach((o) => {
    if (o.id == data) {
      formData.visitorTelephone = o.telephone;
      formData.visitorName = o.name;
      formData.unit = o.visitorUnit;
      formData.idNumber = o.idNumber;
      formData.visitorInfoId = o.id;
    }
  });
};

const findVisitorInfo = (tel) => {
  formData.visitorTelephone = tel;
  selectInfoId.value = tel;
  const queryTel = tel.replace(/\s*/g, "");
  if (queryTel.length === 11) {
    findVisitorInfoByTel(queryTel).then((res) => {
      if (res.data) {
        phoneList.value = res.data;
      }
      forceUpdate();
    });
  }
};

const findVisitorItem = (id) => {
  findByIdAndAttribute(id, "0").then((res) => {
    if (res.code === 200) {
      itemList1.value = res.rows;
    }
  });
  findByIdAndAttribute(id, "1").then((res) => {
    if (res.code === 200) {
      itemList2.value = res.rows;
    }
  });
};

const visitorTimeChange = () => {
  computedTimes("start");
};

const visitorEndTimeChange = () => {
  computedTimes("end");
};

const computedTimes = (type) => {
  const beginDate = formData.visitorTime;
  const endDate = formData.visitorEndTime;
  let d1 = beginDate ? new Date(beginDate.replace(/\-/g, "/")) : null;
  let d2 = endDate ? new Date(endDate.replace(/\-/g, "/")) : null;

  if (beginDate && endDate && d1 > d2) {
    ElMessage.error("离开时间必须大于到访时间！");
    if (type == "start") {
      formData.visitorTime = "";
    }
    if (type == "end") {
      formData.visitorEndTime = "";
    }
    return false;
  }

  let totalDays, diffDate;
  let myDate_1 = Date.parse(d1);
  let myDate_2 = Date.parse(d2);
  diffDate = Math.abs(myDate_1 - myDate_2);
  totalDays = Math.floor(diffDate / (1000 * 3600 * 24));
  if (conifData.value.visitDay) {
    if (
      beginDate &&
      endDate &&
      totalDays > parseInt(conifData.value.visitDay)
    ) {
      ElMessage.error(
        "访客访问时间不能大于" + parseInt(conifData.value.visitDay) + "天！"
      );
      if (type == "start") {
        formData.visitorTime = "";
      }
      if (type == "end") {
        formData.visitorEndTime = "";
      }
      return false;
    }
  }
};

/** 点击提交 */
const submits = () => {
  if (popupTypeLi.value == "upload") {
    if (assetDetailList.value.length > 0) {
      ElMessageBox.confirm(
        "当前导入数据会覆盖之前数据，确定要导入吗？",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          uploadRef.value.submitFileForm();
        })
        .catch(() => {});
    } else {
      uploadRef.value.submitFileForm();
    }
  }
  if (popupTypeLi.value == "addPerson") {
    addPersonRef.value.saveBtn();
  }

  if (popupTypeLi.value == "addItem") {
    addItemRef.value.saveBtn();
  }
};
/** 点击取消保存 */
const cancellation = (val) => {
  close(false);
};
/** 关闭弹窗 */
const close = (val) => {
  open.value = val;
};

const saveParentBtn = (submitType) => {
  formData.status = "1";
  formData.submitType = submitType;
  ruleform.value.validate((valid) => {
    if (valid) {
      if (formData.type == "99" && assetDetailList.value.length == "0") {
        ElMessage.error("选择团体访客，需添加随行人员！");
        return;
      }
      if (assetDetailList.value.length != "0") {
        formData.type = "99";
      } else {
        formData.type = "1";
      }
      areaManagerList.value.forEach((o) => {
        if (o.id == formData.areaId) {
          formData.areaName = o.areaName;
        }
      });
      visitorData.value.faceImg = formData.faceImg;
      save(formData).then((res) => {
        if (res.code == "1") {
          ElMessage.success(submitType == "0" ? "保存成功" : "提交成功");
          emit("submitClose");
        }
      });
      visitorData.value.name = formData.visitorName;
      visitorData.value.telephone = formData.visitorTelephone;
      visitorData.value.idNumber = formData.idNumber;
      visitorData.value.visitorUnit = formData.unit;
      visitorData.value.licenseType = formData.licenseType;
      if (submitType === "1") {
        visitorData.value.type = "0";
        emit("submitClose");
      }
    }
  });
};


const allowSubmitBtn = async (formName) => {
  if (formData.value.areaIdList.length === 0) {
    ElMessage.error("请选择可访问区域!");
    return;
  }

  try {
    await ElMessageBox.confirm(
      `确定允许${formData.value.visitorName}来访？`,
      '确认审批',
      { type: 'warning' }
    );

    formData.value.processStatus = '1';
    const res = await editResult(formData.value);
    if (res.success) {
      ElMessage.success('审批成功');
      closeBtn();
    }
  } catch (error) {
    // 用户取消操作时 error 为 'cancel'，API 错误时为 Error 对象
    if (error !== 'cancel') {
      ElMessage.error(error.message || '操作失败');
    }
  }
};

defineExpose({
  saveParentBtn,
  allowSubmitBtn
});
</script>

<style scoped lang="scss">
.margin-left-8-px {
  margin-left: 8px;
}
.common-value-content .el-table {
  margin-top: 8px;
}
</style>