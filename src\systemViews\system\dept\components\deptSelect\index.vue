<template>
  <div v-if="visible">
    <el-dialog
      width="30%"
      title="组织选择"
      v-model="visible"
      append-to-body
      destroy-on-close
    >
      <el-card
        class="select-dep-card"
        shadow="never"
        v-loading="selectTreeLoading"
        destroy-on-close
      >
        <div slot="header">
          <span>请选择</span>
          <el-button
            style="float: right; padding: 3px 0"
            link
            type="primary"
            icon="Refresh"
            @click="reloadTree"
          >
            刷新
          </el-button>
          <el-button
            style="float: right; padding: 3px 5px"
            link
            type="primary"
            :icon="expandAll ? 'ArrowUp' : 'ArrowDown'"
            @click="setExpandAll"
          >
            {{ expandAll ? "展开一级" : "展开全部" }}
          </el-button>
        </div>

        <el-input
          placeholder="输入名称进行查询"
          v-model="filterText"
          clearable
          style="margin-bottom: 8px; margin-top: 8px"
        >
          <template #append>
            <el-button
              icon="search"
              @click="searchFiler(filterText)"
            ></el-button>
          </template>
        </el-input>
        <el-tree
          v-if="reload"
          :key="treeFatherIndex"
          :props="lazyTreeProps"
          :load="selectLoadNode"
          :lazy="isLazy"
          :data="treeData"
          :expand-on-click-node="false"
          ref="selectAsyncTree"
          :default-expanded-keys="[defaultOrgId]"
          node-key="orgId"
          highlight-current
          :default-expand-all="expandAll"
        >
        </el-tree>
      </el-card>
      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          @click="selectDept"
          :loading="selectDeptLoading"
        >
          确 定
        </el-button>
        <el-button @click="visible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup name="DeptSelect">
const { proxy } = getCurrentInstance();
import { getCurrentInstance, ref } from "vue";
import useUserStore from "@/store/modules/user";
import { findByOrgName } from "@/api/system/role";
import { treeselect } from "@/api/system/dept";

defineExpose({ open, close });
const props = defineProps({
  name: {
    type: String,
    default: "",
  },
  value: {
    type: String,
    default: "",
  },
  tenantId: {
    type: String,
    default: "",
  },
});

const isLazy = ref(true);
const treeData = ref([]);
const treeFatherIndex = ref(0);
const filterText = ref("");
const isSearchFirst = ref(false);
const userStore = useUserStore();
const selectTreeLoading = ref(false);
const lazyTreeProps = ref({
  children: "children",
  label: "orgName",
  isLeaf: "leaf",
});
const visible = ref(false);
const reload = ref(true);
const expandAll = ref(false);
const selectDeptLoading = ref(false);
const defaultOrgId = userStore.userInfo.orgId;

function selectLoadNode(node, resolve) {
  selectTreeLoading.value = true;
  if (node.level === 0) {
    treeselect({
      orgId: defaultOrgId,
      queryType: "current",
      tenantId: props.tenantId,
    }).then((response) => {
      resolve(response.data);
      selectTreeLoading.value = false;
    });
  } else {
    treeselect({
      orgId: node.data.orgId,
      queryType: "down",
      tenantId: props.tenantId,
      orgName: isSearchFirst.value ? filterText.value : "",
    }).then((response) => {
      resolve(response.data);
      selectTreeLoading.value = false;
      isSearchFirst.value = false;
    });
  }
}

const emit = defineEmits(["selected"]);

function selectDept() {
  selectDeptLoading.value = true;
  const cnode = proxy.$refs.selectAsyncTree.getCurrentNode();
  if (cnode) {
    emit("selected", cnode);
  } else {
    proxy.$modal.msgWarning("请选择一条数据！");
    selectDeptLoading.value = false;
  }
}

function open() {
  selectDeptLoading.value = false;
  visible.value = true;
}

function close() {
  visible.value = false;
}

function reloadTree() {
  if (filterText.value) {
    findByOrgNames();
  } else {
    treeData.value = [];
    isLazy.value = true;
    reload.value = true;
    treeFatherIndex.value++;
    const cnode = proxy.$refs.selectAsyncTree.getCurrentNode();
    if (cnode) {
      const node = proxy.$refs.selectAsyncTree.getNode(cnode);
      node.childNodes = [];
      node.loaded = false;
      node.expand();
    } else {
      proxy.$refs.selectAsyncTree.root.loaded = false;
      proxy.$refs.selectAsyncTree.root.expand();
    }
  }
}

function setExpandAll() {
  if (!filterText.value) {
    reload.value = false;
    expandAll.value = !expandAll.value;
    setTimeout(() => {
      reload.value = true;
    }, 100);
  }else{
    treeFatherIndex.value++;
    expandAll.value = !expandAll.value;
  }
}

const searchFiler = () => {
  isSearchFirst.value = true;

  if (filterText.value) {
    isLazy.value = false;
    findByOrgNames();
  } else {
    treeData.value = [];
    isLazy.value = true;
    treeFatherIndex.value++;
  }
};
function findByOrgNames() {
  findByOrgName({
    tenantId: props.tenantId,
    orgName: filterText.value,
  }).then((response) => {
    treeData.value = response.data;
  });
}
</script>

<style scoped>
.select-dep-card {
  height: 500px;
  overflow-y: auto;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 12px;
}
</style>
