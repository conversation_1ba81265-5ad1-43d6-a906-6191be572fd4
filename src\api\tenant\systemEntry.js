import request from "/src/utils/request";

// 新增系统入驻信息
export function add(data) {
  return request({
    url: "/user/oauthClient",
    method: "post",
    data: data
  });
}

// 删除系统入驻信息
// export function del(data) {
//   return request({
//     url: "/user/oauthClient",
//     method: "delete",
//     data: data
//   });
// }
export function del(data) {
  return request({
    url: "/user/oauthClient/delete",
    method: "post",
    data: data
  });
}

// 修改系统入驻信息
export function update(data) {
  return request({
    url: "/user/oauthClient/update",
    method: "post",
    data: data
  });
}

// 根据主键查询单个系统入驻信息详情
export function findOne(id) {
  return request({
    url: "/user/oauthClient/" + id,
    method: "get"
  });
}

// 根据分页信息查询系统入驻信息列表，分页查询
export function findList(data) {
  return request({
    url: "/user/oauthClient",
    method: "get",
    params: data
  });
}

// 根据信息查询系统入驻信息列表，不分页
export function getOauthClientList(data) {
  return request({
    url: "/user/oauthClient/findListOauthClientInfo",
    method: "get",
    params: data
  });
}

// 根据入参生成认证中心 decide 接口所需的参数
export function getParam(data) {
  return request({
    url: "/user/oauthClient/createDecideParams",
    method: "post",
    params: data
  });
}

// 查询密钥
export function getSecret(data) {
  return request({
    url: "/user/oauthClient/getClientSecret",
    method: "post",
    data: data
  });
}
