import request from '@/utils/request'

/**
 * 车牌信息列表
 * @param data
 * @param params
 */
export function licensePlateInfoList(data, params) {
  return request({
    url: "/parking/licensePlateManagement/licensePlateInfoList",
    method: "post",
    data: data,
    params: params
  })
}

/**
 * 初始化车牌信息
 * @param data
 */
export function initLicensePlateInfo(data) {
  return request({
    url: "/parking/licensePlateManagement/initLicensePlateInfo",
    method: "post",
    data: data
  })
}

/**
 * 保存车牌信息
 * @param data
 */
export function saveLicensePlateInfo(data) {
  return request({
    url: "/parking/licensePlateManagement/saveLicensePlateInfo",
    method: "post",
    data: data
  })
}

/**
 * 删除车牌信息
 * @param id
 */
export function deleteLicensePlateInfo(id) {
  return request({
    url: "/parking/licensePlateManagement/deleteLicensePlateInfo/" + id,
    method: "post"
  })
}

/**
 * 用户列表
 * @param params
 */
export function userList(params) {
  return request({
    url: "/parking/licensePlateManagement/userList",
    method: "get",
    params: params
  })
}
/**
 * 批量提交信息
 * @param params
 */
export function saveLicensePlateInfoBatch(query,params) {
  return request({
    url: "/parking/licensePlateManagement/saveLicensePlateInfoBatch",
    method: "post",
    params: params,
    data:query
  })
}
