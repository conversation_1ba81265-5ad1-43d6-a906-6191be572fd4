import request from '@/utils/request'

//访客邀约列表
export function findVisitorInvitationList(data, params) {
  return request({
    url: '/visitorinvite/invite/list',
    method: 'post',
    params: params,
    data: data
  })
}

export function findVisitorInvitationListAll(data, params) {
  return request({
    url: '/visitorinvite/invite/listAll',
    method: 'post',
    params: params,
    data: data
  })
}

//访客认领列表
export function findVisitorClaimList(data, params) {
  return request({
    url: '/visitorinvite/invite/listClaim',
    method: 'post',
    params: params,
    data: data
  })
}
//访客认领同意/拒绝
export function updateTagging(data) {
  console.info(data);
  return request({
    url: '/visitorinvite/invite/edit',
    method: 'post',
    data: data
  })
}
//访客审核同意/拒绝
export function editResult(data) {
  console.info(data);
  return request({
    url: '/visitorinvite/invite/editResult',
    method: 'post',
    data: data
  })
}
// 删除访客信息
export function delById(id) {
  return request({
    url: '/visitorinvite/invite/delete/' + id,
    method: 'post'
  })
}
// 禁用门禁权限
export function lockById(id) {
  return request({
    url: '/visitorinvite/invite/lockById/' + id,
    method: 'post'
  })
}
// 获取配置信息
export function getConfigInfo() {
  return request({
    url: '/visitor/visitorParameterConfig/list',
    method: 'get'
  })
}