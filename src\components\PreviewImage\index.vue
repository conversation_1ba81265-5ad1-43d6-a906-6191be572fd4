<template>
  <el-image
    :src="imageUrl"
    :preview-src-list="previewList"
    :fit="fit"
    :class="customClass"
    :initial-index="initialIndex"
    :z-index="zIndex"
    :preview-teleported="true"
    @error="handleError"
  >
    <template #error>
      <slot name="error">
        <div class="image-error">
          <el-icon :size="40" color="#909399"><Picture /></el-icon>
        </div>
      </slot>
    </template>
  </el-image>
</template>

<script setup>
import { ref, watch, onUnmounted  } from 'vue'
import { ElIcon } from 'element-plus'
import { Picture } from '@element-plus/icons-vue'
import {getImageUrl} from '@/api/system/config'
import axios from 'axios'; // 确保已导入axios
import useUserStore from "@/store/modules/user"
import { cmsImg } from '@/utils/config';
const props = defineProps({
  // 图片ID
  photoId: {
    type: String,
    default: ''
  },
  // 图片适应方式
  fit: {
    type: String,
    default: 'contain'
  },
  // 自定义类名
  customClass: {
    type: String,
    default: 'table-img'
  },

  // 初始预览索引
  initialIndex: {
    type: Number,
    default: 0
  },
  // 预览层级
  zIndex: {
    type: Number,
    default: 9999
  },
  // 增加默认请求接口
  previewQueryUrl: {
       type: String,
    default: `${import.meta.env.VITE_APP_BASE_API}/${cmsImg}`
  }
})

const emit = defineEmits(['error'])



// 存储生成的Blob URL用于后续释放
const blobUrls = ref(new Set());

// 图片地址和预览列表
const imageUrl = ref('');
const previewList = ref([]);
const userStore = useUserStore();
// 清理Blob URL的函数
const cleanupBlobUrls = () => {
  blobUrls.value.forEach(url => {
    URL.revokeObjectURL(url); // 释放内存
  });
  blobUrls.value.clear();
};

// 在watch处理函数中修改为：
watch(() => props.photoId, async (newVal) => {
  cleanupBlobUrls();
  
  if (!newVal) {
    imageUrl.value = '';
    previewList.value = [];
    return;
  }

  try {
    // 使用原生axios直接请求
    const response = await axios.get(`${props.previewQueryUrl}${newVal}`, {
      responseType: 'blob', // 关键配置
      headers: {
        // 如果需要授权
        jwtToken: userStore.jwtToken
      }
    });

   

    // 获取二进制数据（axios会自动将blob数据放在response.data）
    const blobData = response.data;
    // 创建对象URL（注意这里不需要再包装成Blob）
    const blobUrl = URL.createObjectURL(
      new Blob([blobData], { 
        type: blobData.type || 'image/jpeg' // 确保类型正确
      })
    );
    
    blobUrls.value.add(blobUrl);
    imageUrl.value = blobUrl;
    previewList.value = [blobUrl];

  } catch (error) {
    console.error("请求失败:", error);
    imageUrl.value = '';
    previewList.value = [];
    // 可以在这里添加重试逻辑
  }
}, { immediate: true });

// 组件卸载时清理
onUnmounted(() => {
  cleanupBlobUrls();
});
// 处理错误事件
const handleError = (e) => {
  emit('error', e)
}

// 自动生成预览列表
const autoPreviewList = computed(() => {
  return props.previewList.length > 0 
    ? props.previewList 
    : imageUrl.value ? [imageUrl.value] : []
})
</script>

<style scoped>
.image-error {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
}

.table-img {
  border-radius: 4px;
  background-color: #f5f7fa;
  transition: opacity 0.3s;
  width: 100px;
  object-fit: cover;
}
.table-img:hover{
  opacity: 0.8;
}
</style>