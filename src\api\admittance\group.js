import request from '@/utils/request'
import { apiUrl } from '@/utils/config';


export class screenIndex {
    //分页查询门禁分组信息
    static getScreenGroupList(params) {
        return request({
            url: `wisdomaccess${apiUrl}/access/group/findGroup`,
            data: params,
            method: 'post'
        })
    }
    //根据分组id查询门禁分组信息
    static getGroupStaffListById(params) {
        return request({
            url: `wisdomaccess${apiUrl}/access/group/findGroupById?id=${params}`,
            method: 'post'
        })
    }

    // 新增门禁分组
    static addGroup(data) {
        return request({
            url: `wisdomaccess${apiUrl}/access/group/addGroup`,
            method: 'post',
            data: data
        })
    }

    // 修改门禁分组
    static updateGroup(data) {
        return request({
            url: `wisdomaccess${apiUrl}/access/group/updateGroupById`,
            method: 'post',
            data: data
        })
    }

    // 删除门禁分组
    static deleteGroupById(id) {
        return request({
            url: `wisdomaccess${apiUrl}/access/group/deleteGroupById/${id}`,
            method: 'post'
        })
    }

    // 查询人员列表
    static selectUserList(params) {
        return request({
            url: `wisdomaccess${apiUrl}/access/group/selectUserList`,
            method: 'post',
            data: params
        })
    }
}



