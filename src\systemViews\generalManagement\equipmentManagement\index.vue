<template>

  <div class="app-container">
    <el-card>
      <dialog-search
        @getList="getList"
        formRowNumber="4"
        :columns="tabelForm.columns"
      >
        <template v-slot:formList>
          <el-form
            :model="queryParams"
            ref="queryForm"
            :inline="true"
            label-width="80px"
          >
          <el-form-item label="产品信息" prop="deviceType">
                <el-select v-model="queryParams.productId" placeholder="请选择产品信息" clearable>
                  <el-option v-for="dict in product_type" :key="dict.id" :label="dict.label"
                             :value="dict.value"/>
                </el-select>
              </el-form-item>
              <el-form-item label="设备名称" prop="deviceName">
                <el-input v-model="queryParams.deviceName" placeholder="请输入设备名称" clearable
                          @keyup.enter.native="handleQuery"/>
              </el-form-item>
              <el-form-item label="设备类型" prop="deviceType">
                <el-select v-model="queryParams.deviceType" placeholder="请选择设备类型" clearable>
                  <el-option v-for="dict in device_type" :key="dict.id" :label="dict.label"
                             :value="dict.value"/>
                </el-select>
              </el-form-item>
              <el-form-item label="设备编码" prop="deviceId">
                <el-input v-model="queryParams.deviceId" placeholder="请输入设备编码" clearable/>
              </el-form-item>
              <el-form-item label="动作类型" prop="actionType">
                <el-select v-model="queryParams.actionType" placeholder="请选择动作类型" clearable>
                  <el-option v-for="(dict, index) in device_action_type" :key="index"
                             :label="dict.label" :value="dict.value"/>
                </el-select>
              </el-form-item>
              <el-form-item label="设备状态" prop="deviceStatus">
                <el-select v-model="queryParams.deviceStatus" placeholder="请选择设备状态" clearable>
                  <el-option v-for="dict in device_status" :key="dict.id"
                             :label="dict.label" :value="dict.value"/>
                </el-select>
              </el-form-item>
              <el-form-item label="在线状态" prop="status">
                <el-select v-model="queryParams.status" placeholder="请选择在线状态" clearable>
                  <el-option v-for="dict in device_action_status" :key="dict.id"
                             :label="dict.label" :value="dict.value"/>
                </el-select>
              </el-form-item>
          </el-form>
        </template>
        <template v-slot:searchList>
          <el-button type="primary" icon="Search" @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </template>

        <template v-slot:searchBtnList>
          <el-button
            type="primary"
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['sys:base:user:add']"
            >新增
          </el-button>
          <el-button  icon="Download" @click="handleExport"
            >导出</el-button
          >



          
        </template>
      </dialog-search>

      <public-table
        ref="publictable"
        :rowKey="tabelForm.tableKey"
        :tableData="userList"
        :columns="tabelForm.columns"
        :configFlag="tabelForm.tableConfig"
        :pageValue="pageParams"
        :total="total"
        :getList="getList"
      >
        <template #operation="{ scope }">
          <div>
            <el-button
              link
              icon="Edit"
              type="primary"
              title="修改"
              @click="handleUpdate(scope.row)"
            >
            </el-button>
            <el-button
              link
              icon="View"
              type="primary"
              title="详情"
              @click="handleView(scope.row)"
            >
            </el-button>
            <el-button
              link
              icon="Delete"
              type="primary"
              title="删除"
              @click="handleDelete(scope.row)"
            >
            </el-button>
          </div>
        </template>
      </public-table>


    <DialogBox
      :visible="diaWindow.open1"
      :dialogWidth="diaWindow.dialogWidth"
      :dialogFooterBtn="diaWindow.dialogFooterBtn"
      @save="save"
      @cancellation="cancellation"
      @close="close"
      :dialogTitle="diaWindow.headerTitle"
    >
      <template #content>
        <add ref="addRef" :popupType="diaWindow.popupType" :rowData="diaWindow.rowData" @close="cancellationRefresh"></add>
      </template>
    </DialogBox>

    </el-card>
  </div>
</template>

<script setup name="EquipmentManagement">
import {queryList,delDeviceById} from '@/api/equipmentManagement/index'
import { ref, reactive, nextTick } from "vue";
import { ElMessageBox, ElMessage } from "element-plus";
import { formatMinuteTime } from '@/utils';
import add from "./components/add";
const userList = ref([]);
const publictable = ref(null);
const addRef  = ref(null)
import { apiUrl } from '@/utils/config';
// 弹窗
const diaWindow = reactive({
  open1:false,
  headerTitle: "",
  popupType: "",
  rowData: "",
  dialogWidth: "50%",
  dialogFooterBtn: false,
});
const pageParams = ref({
  pageNum: 1,
  pageSize: 10,
});
const queryParams =  ref({
 
});
const total = ref(0);


// 字典
const { proxy } = getCurrentInstance();
const { product_type, device_type, device_action_type,device_status, device_action_status} = proxy.useDict(
  "product_type",
  "device_type",
  "device_action_type",
  "device_status",
  "device_action_status"
);

const tabelForm = reactive({
  tableKey: "1", //表格key值
  isShowRightToolbar: true, //是否显示右侧显示和隐藏
  showSearch: true,
  // 表格表格数据
  columns: [
  {
            fieldIndex: "deviceAreaName", // 对应列内容的字段名
            label: "设备区域", // 显示的标题
            resizable: true, // 对应列是否可以通过拖动改变宽度
            visible: true, // 展示与隐藏
            sortable: false, // 对应列是否可以排序
            fixed: "", //固定
            minWidth: "180px", //最小宽度%
            align: "", //表格对齐方式
            showOverFlowTooltip: true, //是否显示提示
          },
          {
            fieldIndex: "deviceName", // 对应列内容的字段名
            label: "设备名称", // 显示的标题
            resizable: true, // 对应列是否可以通过拖动改变宽度
            visible: true, // 展示与隐藏
            sortable: false, // 对应列是否可以排序
            minWidth: "140px", //最小宽度%
            showOverFlowTooltip: false, //是否显示提示

          },
          {
            fieldIndex: "deviceId", // 对应列内容的字段名
            label: "设备编码", // 显示的标题
            resizable: true, // 对应列是否可以通过拖动改变宽度
            visible: true, // 展示与隐藏
            sortable: false, // 对应列是否可以排序
            fixed: "", //固定
            minWidth: "120px", //最小宽度%
            align: "", //表格对齐方式
            showOverFlowTooltip: true, //是否显示提示
          },

          {
            fieldIndex: "productId", // 对应列内容的字段名
            label: "产品信息", // 显示的标题
            resizable: true, // 对应列是否可以通过拖动改变宽度
            visible: true, // 展示与隐藏
            sortable: false, // 对应列是否可以排序
            fixed: "", //固定
            minWidth: "150px", //最小宽度%
            align: "", //表格对齐方式
            showOverFlowTooltip: true, //是否显示提示
            type: "dict",
            dictList: product_type
          },

          {
            fieldIndex: "deviceType", // 对应列内容的字段名
            label: "设备类型", // 显示的标题
            resizable: true, // 对应列是否可以通过拖动改变宽度
            visible: true, // 展示与隐藏
            sortable: false, // 对应列是否可以排序
            fixed: "", //固定
            minWidth: "100px", //最小宽度%
            align: "", //表格对齐方式
            showOverFlowTooltip: true, //是否显示提示
            type: "dict",
            dictList: device_type
          },

          {
            fieldIndex: "actionType", // 对应列内容的字段名
            label: "动作类型", // 显示的标题
            resizable: true, // 对应列是否可以通过拖动改变宽度
            visible: true, // 展示与隐藏
            sortable: false, // 对应列是否可以排序
            fixed: "", //固定
            minWidth: "100px", //最小宽度%
            align: "", //表格对齐方式
            showOverFlowTooltip: true, //是否显示提示
            type: "dict",
            dictList: device_action_type
          },

          {
            fieldIndex: "deviceStatus", // 对应列内容的字段名
            label: "设备状态", // 显示的标题
            resizable: true, // 对应列是否可以通过拖动改变宽度
            visible: true, // 展示与隐藏
            sortable: false, // 对应列是否可以排序
            fixed: "", //固定
            minWidth: "100px", //最小宽度%
            showOverFlowTooltip: true, //是否显示提示
            align: "", //表格对齐方式
            type: "dict",
            dictList: device_status
          },

          {
            fieldIndex: "status", // 对应列内容的字段名
            label: "在线状态", // 显示的标题
            resizable: true, // 对应列是否可以通过拖动改变宽度
            visible: true, // 展示与隐藏
            sortable: false, // 对应列是否可以排序
            fixed: "", //固定
            minWidth: "100px", //最小宽度%
            align: "", //表格对齐方式
            showOverFlowTooltip: true, //是否显示提示
            type: "dict",
            dictList: device_action_status
          },

          {
            fieldIndex: "loginAddress", // 对应列内容的字段名
            label: "管理地址", // 显示的标题
            resizable: true, // 对应列是否可以通过拖动改变宽度
            visible: true, // 展示与隐藏
            sortable: false, // 对应列是否可以排序
            fixed: "", //固定
            minWidth: "180px", //最小宽度%
            align: "", //表格对齐方式
            showOverFlowTooltip: true, //是否显示提示
          }, {
            fieldIndex: "deviceAccount", // 对应列内容的字段名
            label: "设备账号", // 显示的标题
            resizable: true, // 对应列是否可以通过拖动改变宽度
            visible: true, // 展示与隐藏
            sortable: false, // 对应列是否可以排序
            fixed: "", //固定
            minWidth: "", //最小宽度%
            width: "180px", //宽度
            align: "", //表格对齐方式
            showOverFlowTooltip: true, //是否显示提示
          }, {
            fieldIndex: "devicePwd", // 对应列内容的字段名
            label: "设备密码", // 显示的标题
            resizable: true, // 对应列是否可以通过拖动改变宽度
            visible: true, // 展示与隐藏
            sortable: false, // 对应列是否可以排序
            fixed: "", //固定
            minWidth: "150px", //最小宽度%
            width: "", //宽度
            align: "", //表格对齐方式
            showOverFlowTooltip: true, //是否显示提示
          }, {
            fieldIndex: "ipAddress", // 对应列内容的字段名
            label: "IP地址", // 显示的标题
            resizable: true, // 对应列是否可以通过拖动改变宽度
            visible: true, // 展示与隐藏
            sortable: false, // 对应列是否可以排序
            fixed: "", //固定
            minWidth: "180px", //最小宽度%
            align: "", //表格对齐方式
            showOverFlowTooltip: true, //是否显示提示
          },
          {
            fieldIndex: "remark", // 对应列内容的字段名
            label: "备注", // 显示的标题
            resizable: true, // 对应列是否可以通过拖动改变宽度
            visible: true, // 展示与隐藏
            sortable: false, // 对应列是否可以排序
            fixed: "", //固定
            minWidth: "180px", //最小宽度%
            align: "", //表格对齐方式
            showOverFlowTooltip: true, //是否显示提示
          },
          {
            fieldIndex: "createDate", // 对应列内容的字段名
            label: "创建时间", // 显示的标题
            resizable: true, // 对应列是否可以通过拖动改变宽度
            visible: true, // 展示与隐藏
            sortable: false, // 对应列是否可以排序
            minWidth: "180px", //最小宽度%
            showOverFlowTooltip: false, //是否显示提示
          },
          {
            label: "操作",
            slotname: "operation",
            width: "150",
            fixed: "right", //固定
            visible: true,
          },
  ],
  // 表格基础配置项，看个人需求添加
  tableConfig: {
    needPage: true, // 是否需要分页
    index: true, // 是否需要序号
    selection: false, // 是否需要多选
    reserveSelection: false, // 是否需要支持跨页选择
    indexFixed: false, // 序号列固定 left true right
    selectionFixed: false, //多选列固定left true right
    indexWidth: "50", //序号列宽度
    loading: false, //表格loading
    showSummary: false, //是否开启合计
    height: null, //表格固定高度 比如：300px
  },
});

/** 查询用户列表 */
const getList = () => {
  tabelForm.tableConfig.loading = true;
  queryList(pageParams.value,queryParams.value).then((response) => {
    userList.value = response.data.records;
    total.value = response.data.total;
    tabelForm.tableConfig.loading = false;
  });
};

/** 搜索按钮操作 */
const handleQuery = () => {
  pageParams.value.pageNum = 1;
  getList();
};
/** 重置按钮操作 */
const resetQuery = () => {
  queryParams.value = {}
  proxy.resetForm("queryForm");
  handleQuery();
};

/** 提交保存 */
const save = (val) => {
  addRef.value.submitForm();
};

/** 点击取消保存并刷新 */
const cancellationRefresh = (val) => {
  close(false);
  getList();
};

/** 点击取消保存 */
const cancellation = (val) => {
  close(false);
};

/** 关闭弹窗 */
const close = (val) => {
  diaWindow.open1 = val;
};


const handleDelete = (row, callback)=> {
  ElMessageBox.confirm(
    "确认删除吗？",
    "提示",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }
  )
    .then(() => {
      delDeviceById(row.id).then((res) => {
        if (res.code == "1") {
          ElMessage.success("删除成功");
          getList();
          callback(true);
        }
      });
    })
    .catch(() => {});
}


const handleAdd = ()=>{
  diaWindow.dialogWidth = '45%'
  diaWindow.headerTitle = '新增设备管理'
   diaWindow.rowData = {}
   diaWindow.dialogFooterBtn = true
   diaWindow.popupType = 'add'
   diaWindow.open1 = true
}

const handleUpdate = (row)=>{
   diaWindow.dialogWidth = '45%'
   diaWindow.headerTitle = '修改设备管理'
   diaWindow.rowData = row
   diaWindow.dialogFooterBtn = true
   diaWindow.popupType = 'edit'
   diaWindow.open1 = true
}


const handleView = (row)=>{
   diaWindow.dialogWidth = '45%'
   diaWindow.headerTitle = '查看设备管理'
   diaWindow.rowData = row
   diaWindow.dialogFooterBtn = false
   diaWindow.popupType = 'view'
   diaWindow.open1 = true
}

/** 导出按钮操作 */
const handleExport = () => {


  proxy.download(
    `user${apiUrl}/deviceManagement/export`,
    {
      ...queryParams.value,
    },
   `设备管理列表_${formatMinuteTime(new Date())}.xlsx`
  );
};
getList();
</script>

<style lang="scss" scoped>
.el-card {
  min-height: calc(100vh - 110px);
}
</style>