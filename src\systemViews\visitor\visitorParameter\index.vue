<template>
  <div class="container-table-box dialog-box dialog-box-edit">
    <el-card>
      <el-row :gutter="24">
        <el-col :span="24" :xs="24">
          <div class="common-box-one">
            <div class="common-header">
              <div class="common-header-line"></div>
              <div class="common-header-text">访客参数配置</div>
            </div>

            <el-form
              ref="ruleform"
              :model="formData"
              label-width="120px"
              :rules="rules"
            >
              <el-row>
                <el-col :span="24">
                  <el-form-item label="访客须知" prop="information">
                    <el-input
                      placeholder="请输入访客须知"
                      type="textarea"
                      v-model="formData.information"
                      :rows="5"
                      clearable
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="须知时间" prop="informationTime">
                    <el-input
                      style="width: 30%"
                      v-model="formData.informationTime"
                      oninput="value=value.replace(/[^0-9.]/g,'')"
                      placeholder="请输入访客须知查看时间"
                      clearable
                    >
                      <template v-slot:suffix>
                        <i class="font-style:normal;margin-right: 10px;">秒 </i>
                      </template>
                    </el-input>
                    <i
                      >注：必须查看访客须知
                      {{ formData.informationTime }} 秒后才可进行访客申请</i
                    >
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="隐私协议" prop="privicyAgreement">
                    <el-input
                      placeholder="请输入隐私协议"
                      type="textarea"
                      v-model="formData.privicyAgreement"
                      :rows="5"
                      clearable
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="协议时间" prop="privicyAgreementTime">
                    <el-input
                      style="width: 30%"
                      v-model="formData.privicyAgreementTime"
                      oninput="value=value.replace(/[^0-9.]/g,'')"
                      placeholder="请输入隐私协议查看时间"
                      clearable
                    >
                    <template v-slot:suffix>
                        <i class="font-style:normal;margin-right: 10px;">秒 </i>
                      </template>s
                    </el-input>
                    <i
                      >注：必须查看隐私协议
                      {{ formData.privicyAgreementTime }}
                      秒后才可进行访客申请</i
                    >
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-divider content-position="left">访客认证配置</el-divider>
                </el-col>
                <el-col :span="10">
                  <el-form-item label="短信认证" prop="isMessage">
                    <el-radio-group v-model="formData.isMessage">
                      <el-radio-button label="0">是</el-radio-button>
                      <el-radio-button label="1">否</el-radio-button>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :span="14">
                  <el-form-item label="身份认证" prop="isIdcard">
                    <el-radio-group v-model="formData.isIdcard">
                      <el-radio-button label="0">是</el-radio-button>
                      <el-radio-button label="1">否</el-radio-button>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-divider content-position="left">访客填写配置</el-divider>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="人脸通行" prop="isFaceEnter">
                    <el-radio-group v-model="formData.isFaceEnter">
                      <el-radio-button label="0">是</el-radio-button>
                      <el-radio-button label="1">否</el-radio-button>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="扫码通行" prop="isQrEnter">
                    <el-radio-group v-model="formData.isQrEnter">
                      <el-radio-button label="0">是</el-radio-button>
                      <el-radio-button label="1">否</el-radio-button>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="车辆信息" prop="isCars">
                    <el-radio-group v-model="formData.isCars">
                      <el-radio-button label="0">是</el-radio-button>
                      <el-radio-button label="1">否</el-radio-button>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="随身物品" prop="isBelongings">
                    <el-radio-group v-model="formData.isBelongings">
                      <el-radio-button label="0">是</el-radio-button>
                      <el-radio-button label="1">否</el-radio-button>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="共享工位" prop="isSharedCubicle">
                    <el-radio-group v-model="formData.isSharedCubicle">
                      <el-radio-button label="0">是</el-radio-button>
                      <el-radio-button label="1">否</el-radio-button>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="餐厅就餐" prop="isRepast">
                    <el-radio-group v-model="formData.isRepast">
                      <el-radio-button label="0">是</el-radio-button>
                      <el-radio-button label="1">否</el-radio-button>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="访问期限" prop="visitDay">
                    <el-input
                      style="width: 50%"
                      v-model="formData.visitDay"
                      oninput="value=value.replace(/[^0-9.]/g,'')"
                      placeholder="访问期限"
                      clearable
                    >
                    <template v-slot:suffix>
                        <i class="font-style:normal;margin-right: 10px;">天 </i>
                      </template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-divider content-position="left">被访人配置</el-divider>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="模糊搜索" prop="visitorSearch">
                    <el-radio-group v-model="formData.visitorSearch">
                      <el-radio-button label="0">手机号</el-radio-button>
                      <el-radio-button label="1">姓名</el-radio-button>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-divider content-position="left">邀约码配置</el-divider>
                </el-col>
                <el-col :span="14">
                  <el-form-item label="邀约说明" prop="qrCodeChar">
                    <el-input
                      v-model="formData.qrCodeChar"
                      placeholder="请填写邀约码文字说明"
                      clearable
                    />
                  </el-form-item>
                  <el-form-item label="临时码天数" prop="qrCodeDay">
                    <el-input
                      v-model="formData.qrCodeDay"
                      type="number"
                      placeholder="请填写临时邀约码有效天数"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="7">
                  <el-form-item label="邀约码logo" prop="pictures">
                    <ImageUpload
                      v-model="fileLogoList"
                      limit="1"
                      fileSize="10"
                      :paramsData="imageLogoData"
                      :uploadImgUrl="
                        '/park' +
                        apiUrls +
                        '/visitor/visitorParameterConfig/uploadLogoImage'
                      "
                    />
                    <!-- <imageListUpload
                      v-model="fileLogoList"
                      :fileLimit="1"
                      :paramsData="imageLogoData"
                      listType="picture-card"
                      uploadUrlProp="/visitor/visitorParameterConfig/uploadLogoImage"
                      @input="uploadLogoFile"
                      @removeImage="removeLogoImage"
                      upLoadType="replace"
                    /> -->
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="邀约码背景图" prop="pictures">
                    <ImageUpload
                      v-model="fileList"
                      limit="1"
                      fileSize="10"
                      :paramsData="imageExtraData"
                      :uploadImgUrl="
                        '/park' +
                        apiUrls +
                        '/visitor/visitorParameterConfig/uploadImage'
                      "
                    />
                    <!-- <imageListUpload
                      v-model="fileList"
                      :fileLimit="1"
                      :paramsData="imageExtraData"
                      listType="picture-card"
                      uploadUrlProp="/visitor/visitorParameterConfig/uploadImage"
                      @input="uploadFile"
                      @removeImage="removeImage"
                      upLoadType="replace"
                    /> -->
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="卡片背景图" prop="pictures">
                    <ImageUpload
                      v-model="fileCardList"
                      limit="1"
                      fileSize="10"
                      :paramsData="imageCardData"
                      :uploadImgUrl="
                        '/park' +
                        apiUrls +
                        '/visitor/visitorParameterConfig/uploadCardImage'
                      "
                    />
                    <!-- <imageListUpload
                      v-model="fileCardList"
                      :fileLimit="1"
                      :paramsData="imageCardData"
                      listType="picture-card"
                      uploadUrlProp="/visitor/visitorParameterConfig/uploadCardImage"
                      @input="uploadCardFile"
                      @removeImage="removeCardImage"
                      upLoadType="replace"
                    /> -->
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>


            

          </div>
        </el-col>
      </el-row>
    </el-card>

    <div  class="dialog-footer">
      <el-button type="primary" @click="submitForm()">保 存</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";
import {
  getConfigInfo,
  saveConfigInfo,
  getUUid,
  removeImage,
} from "@/api/visitor/visitorParameterConfig/index";

// 响应式数据
const formData = reactive({
  information: null,
  informationTime: null,
  privicyAgreement: null,
  privicyAgreementTime: null,
  isMessage: "1",
  isFaceEnter: "0",
  isQrEnter: "0",
  isIdcard: "1",
  isCars: "0",
  isBelongings: "0",
  isSharedCubicle: "0",
  isRepast: "0",
  qrCodeChar: "",
  qrCodeDay: 3,
  visitDay: null,
  visitorSearch: "0",
  qrCode: null,
  logoId: null,
  cardId: null,
});

const fileList = ref([]);
const fileLogoList = ref([]);
const fileCardList = ref([]);
const imageExtraData = reactive({ businessId: "" });
const imageLogoData = reactive({ businessId: "" });
const imageCardData = reactive({ businessId: "" });
const dataList = ref([]);
const loading = ref(false);
const ruleform = ref(null);

// 验证规则
const rules = reactive({
  information: [{ required: true, message: "请输入访客须知", trigger: "blur" }],
  privicyAgreement: [
    { required: true, message: "请输入隐私协议", trigger: "blur" },
  ],
  isCars: [
    { required: true, message: "请选择是否录入车辆信息", trigger: "blur" },
  ],
  isFaceEnter: [
    { required: true, message: "请选择是否开启人脸通行", trigger: "blur" },
  ],
  isQrEnter: [
    { required: true, message: "请选择是否开启扫码通行", trigger: "blur" },
  ],
  isBelongings: [
    { required: true, message: "请选择是否录入随身物品", trigger: "blur" },
  ],
  isSharedCubicle: [
    { required: true, message: "请选择是否共享工位", trigger: "blur" },
  ],
  isRepast: [{ required: true, message: "请选择是否就餐", trigger: "blur" }],
  qrCodeDay: [{ required: true, message: "请选择是否就餐", trigger: "blur" }],
  visitDay: [{ required: true, message: "请输入访问期限", trigger: "blur" }],
});

// 生命周期
onMounted(() => {
  getList();
  getUUidFun();
  getLogoUUid();
  getCardUUid();
});

// 获取配置信息
const getList = async () => {
  loading.value = true;
  try {
    const res = await getConfigInfo();
    dataList.value = res.rows;
    if (dataList.value.length === 1) {
      Object.assign(formData, dataList.value[0]);
      if (formData.encodeToBase64) {
        fileList.value = [
          {
            name: "",
            url: `data:image/png;base64,${formData.encodeToBase64}`,
          },
        ];
      }
      if (formData.logoToBase64) {
        fileLogoList.value = [
          {
            name: "",
            url: `data:image/png;base64,${formData.logoToBase64}`,
          },
        ];
      }
      if (formData.cardToBase64) {
        fileCardList.value = [
          {
            name: "",
            url: `data:image/png;base64,${formData.cardToBase64}`,
          },
        ];
      }
    }
  } catch (error) {
    ElMessage.error(error.message || "获取配置失败");
  } finally {
    loading.value = false;
  }
};

// 获取 UUID
const getUUidFun = async () => {
  try {
    const res = await getUUid();
    if (res.code === 200) imageExtraData.businessId = res.id;
  } catch (error) {
    ElMessage.error(error.message || "获取UUID失败");
  }
};

const getLogoUUid = async () => {
  try {
    const res = await getUUid();
    if (res.code === 200) imageLogoData.businessId = res.id;
  } catch (error) {
    ElMessage.error(error.message || "获取UUID失败");
  }
};

const getCardUUid = async () => {
  try {
    const res = await getUUid();
    if (res.code === 200) imageCardData.businessId = res.id;
  } catch (error) {
    ElMessage.error(error.message || "获取UUID失败");
  }
};

// 提交表单
const submitForm = async () => {
  try {
    await ruleform.value.validate();
    if (fileList.value.length > 0) {
      formData.qrCode = fileList.value[0].attachmentId;
    } else {
      formData.qrCode = "";
    }

    if (fileLogoList.value.length > 0) {
      formData.logoId = fileLogoList.value[0].attachmentId;
    } else {
      formData.logoId = "";
    }

    if (fileCardList.value.length > 0) {
      formData.cardId = fileCardList.value[0].attachmentId;
    } else {
      formData.cardId = "";
    }

    const res = await saveConfigInfo(formData);

    if (res.code === 200) {
      ElMessage.success("保存成功");
    }
  } catch (error) {
    ElMessage.error(error.message || "保存失败");
  }
};
</script>

<style lang="scss" scoped>
    .dialog-footer{
        position: fixed;
        right: 40px;
        bottom: 30px;

}
</style>