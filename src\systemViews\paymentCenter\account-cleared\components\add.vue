<!-- 账户清零 -->

<template>
  <div
    class="dialog-box"
    :class="popupType === 'view' ? 'dialog-box-view' : 'dialog-box-edit'"
  >
    <el-form
      ref="formRef"
      :model="formData"
      label-width="125px"
      :rules="popupType !== 'view' ? rules : ''"
    >
      <el-row>
        <el-col :span="24">
          <el-form-item label="清零月份" prop="month">
            <!-- 添加或编辑时显示输入框 -->
            <el-date-picker
              v-if="popupType !== 'view'"
              v-model="formData.month"
              type="month"
              format="YYYYMM"
              value-format="YYYYMM"
              placeholder="请选择"
              clearable
            />

            <!-- 查看时显示文本 -->

            <div v-else>
              {{ formData.month || "" }}
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="HR编码" prop="HrCode">
            <el-input
              v-model="formData.HrCode"
              :disabled="popupType === 'view'"
              placeholder="请输入HR编码"
              clearable
              v-if="popupType !== 'view'"
            />

            <div v-else>
              {{ formData.HrCode || "" }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="员工姓名" prop="deviceAreaId">
            <el-select
              v-if="popupType != 'view'"
              filterable
              :filter-method="dataFilter"
              class="main-select-tree"
              ref="selectTree"
              v-model="formData.deviceAreaId"
              placeholder="请选择员工姓名"
            >
              <el-option
                :label="formData.deviceAreaName"
                :value="formData.deviceAreaId"
                style="display: none"
              />
              <el-tree
                style="margin: 20px auto; margin-top: 10px"
                :check-strictly="true"
                :data="areaTreeData"
                :filter-node-method="filterNode"
                @node-click="handleNodeClick"
                default-expand-all
                node-key="id"
                ref="areaTreeRef"
                highlight-current
                :props="treeProps"
              />
            </el-select>
            <div v-if="popupType == 'view'">
              {{ formData.deviceAreaName || "-" }}
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="员工部门" prop="userdept">
            <div>
              {{ formData.userdept || "" }}
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="人员类型" prop="userdept">
            <div>
              {{ formData.userdept || "" }}
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="剩余金额" prop="userdept">
            <div>
              {{ formData.userdept || "" }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="账户选择" prop="account">
            <el-select
              v-model="formData.account"
              placeholder="请选择账户"
              clearable
              filterable
            >
              <el-option
                v-for="(item, index) of recharge_type"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="清零金额" prop="paymentOrder">
            <NumberInput
              v-if="popupType !== 'view'"
              v-model="formData.paymentOrder"
              customPlaceholder="请输入清零金额"
              input-type="decimal"
            />
            <!-- 查看时显示文本 -->
            <div v-else>
              {{ formData.paymentOrder }}
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
  
    
  
  <script setup>
import { ref, reactive, watch, getCurrentInstance, defineProps } from "vue";

import { ElMessage } from "element-plus";

import { screenIndex } from "@/api/paymentCenter/account-management/account-type/index";
import { pinyin } from "pinyin-pro";

// 定义组件 props
const props = defineProps({
  closeBtn: {
    type: Function,

    default: null,
  },

  popupType: {
    type: String,
    default: "",
  },

  rowData: {
    type: Object,
    default: () => ({}),
  },
});

// 字典
const { proxy } = getCurrentInstance();
const { account_type_status } = proxy.useDict("account_type_status");

// 定义 emits
const emit = defineEmits(["closeBtn"]);

// 下拉树选择
const selectTree = ref(null);
const areaTreeRef = ref(null);
const areaTreeData = ref([]);
const treeProps = reactive({
  multiple: true,
  emitPath: false,
  value: "id",
});

const dataFilter = (val) => {
  if (val) {
    areaTreeRef.value.filter(val);
  }
};
const filterNode = (value, data) => {
  if (!value) return true;
  return data.label?.includes(value) ?? false;
};

const handleNodeClick = (data) => {
  formData.deviceAreaId = data.id;
  formData.deviceAreaName = data.label;
  selectTree.value.blur();
};
const selectAreaTree = async () => {
  try {
    const res = await areaTreeByType("office");
    areaTreeData.value = res.data;
  } catch (error) {
    ElMessage.error("获取区域树失败");
  }
};

// 定义状态

let formData = ref({
  status: "1",
});

// 定义校验规则

const rules = reactive({
  month: [
    { required: true, message: "请选择清零月份", trigger: ["blur", "change"] },
  ],
  HrCode: [
    { required: true, message: "请输入HR编码", trigger: ["blur", "change"] },
    { 
      pattern: /^[\w\-\/:.]+$/,  // 允许字母、数字、下划线、短横线、斜杠、冒号、点号
      message: "HR编码只能包含字母、数字和-_/:.等特殊字符",
      trigger: ["blur", "change"]
    }
  ],
  deviceAreaId: [
    { required: true, message: "请选择员工姓名", trigger: "change" }
  ],
  account: [
    { required: true, message: "请选择清零账户", trigger: "change" }
  ],
  paymentOrder: [
    { required: true, message: "请输入清零金额", trigger: ["blur", "change"] },
    {
      type: "number",
      message: "金额必须为数字",
      trigger: ["blur", "change"],
      transform: value => Number(value)
    },
    {
      validator: (_, value, callback) => {
        if (value <= 0) {
          callback(new Error("金额必须大于0"));
        } else {
          callback();
        }
      },
      trigger: ["blur", "change"]
    }
  ]
});

// 表单引用

const formRef = ref(null);

// 定义方法

const initData = async () => {
  if (props.popupType === "add") {
  } else {
    formData.value = JSON.parse(JSON.stringify(props.rowData));
  }
};

const saveForm = async () => {
  try {
    await formRef.value.validate();

    if (props.popupType === "edit") {
      const res = await screenIndex.payAccountTypeUpdate(formData.value);

      if (res.code == "1") {
        ElMessage.success("修改成功");

        emit("closeBtn");
      }
    } else if (props.popupType === "add") {
      const res = await screenIndex.payAccountTypeAdd(formData.value);

      if (res.code == "1") {
        ElMessage.success("新增成功");

        emit("closeBtn");
      }
    }
  } catch (error) {
    console.error("表单校验失败:", error);
  }
};
// 监听类型名称变化生成类型编码
let isAutoGenerated = true; // 标记是否自动生成的编码

watch(
  () => formData.value.typeName,
  (newVal) => {
    if (props.popupType === "view") return;

    // 生成拼音首字母（兼容中英文）
    const code = pinyin(newVal, {
      pattern: "first", // 只要首字母
      toneType: "none", // 不要声调
      nonZh: "consecutive", // 非中文连续显示
    })
      .replace(/ /g, "") // 移除空格
      .toUpperCase(); // 转为大写

    formData.value.typeCode = code;
  }
);

// 初始化数据

onMounted(() => {
  initData();
});

defineExpose({
  saveForm,
});
</script>
  
    
  
    <style scoped lang="scss">
</style>