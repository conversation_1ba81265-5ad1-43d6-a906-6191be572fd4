<template>
    <div  class="dialog-box"
    :class="popupType === 'view' ? 'dialog-box-view' : 'dialog-box-edit'">
        <!-- 使用组合式API重构的表单 -->
        <el-form
          ref="ruleformRef"
          :model="formData"
          label-width="120px"
        >
          <!-- 停车场信息 -->
          <el-row>
             <!-- 车牌号码 -->
        <el-col :span="12">
          <el-form-item label="车牌号码">
            <div>{{ formData.plateNo || "" }}</div>
          </el-form-item>
        </el-col>
    <!-- 人员姓名 -->
    <el-col :span="12">
          <el-form-item label="人员姓名">
            <div>{{ formData.staffName || "" }}</div>
          </el-form-item>
        </el-col>
  
 
      
        <!-- 手机号码 -->
        <el-col :span="12">
          <el-form-item label="手机号码">
            <div>{{ formData.cellphone || "" }}</div>
          </el-form-item>
        </el-col>

        
        <!-- 入场时间 -->
        <el-col :span="12">
          <el-form-item label="入场时间">
            <div>{{ formData.enterTime || "" }}</div>
          </el-form-item>
        </el-col>
  
        <!-- 出场时间 -->
        <el-col :span="12">
          <el-form-item label="出场时间">
            <div>{{ formData.outTime || "" }}</div>
          </el-form-item>
        </el-col>
  

         <!-- 停车场 -->
         <el-col :span="12">
          <el-form-item label="区域名称">
            <div>{{ formData.areaName || "" }}</div>
          </el-form-item>
        </el-col>
  
        <!-- 停车场 -->
        <el-col :span="12">
          <el-form-item label="停车场">
            <div>{{ formData.parkingName || "" }}</div>
          </el-form-item>
        </el-col>
  
     
        <!-- 停车天数 -->
        <el-col :span="12">
          <el-form-item label="停车天数">
            <div>{{ formData.parkingDays || "" }}</div>
          </el-form-item>
        </el-col>
  
     
  
        <!-- 停车时长 -->
        <el-col :span="12">
          <el-form-item label="停车时长(小时)">
            <div>{{ formData.parkingDuration || "" }}</div>
          </el-form-item>
        </el-col>
  
        <!-- 计费天数 -->
        <!-- <el-col :span="12">
          <el-form-item label="计费天数">
            <div>{{ formData.feeDays || "" }}</div>
          </el-form-item>
        </el-col> -->
  
      <!-- 订单状态 -->
      <el-col :span="12">
          <el-form-item label="车辆进出场">
            <div>{{$formatDictLabel(formData.enterOutStatus,enter_out_status) || "" }}</div>
          </el-form-item>
        </el-col>

        <!-- <el-col :span="12">
        <el-form-item label="订单状态">
          <div>{{ $formatDictLabel( formData.orderStatus,ORDER_STATUS) || "-" }}</div>
        </el-form-item>
      </el-col> -->
  
        <!-- 订单状态 -->
        <el-col :span="12">
          <el-form-item label="数据状态">
            <div>{{$formatDictLabel(formData.dataStatus,park_data_status) || "" }}</div>
          </el-form-item>
        </el-col>
  
       
      <!-- 停车说明 -->
      <el-col :span="12">
          <el-form-item label="停车说明">
            <div>{{ formData.parkingRemark || "" }}</div>
          </el-form-item>
        </el-col>
  
    
    <!-- 入场车身照片 -->
    <el-col :span="12">
          <el-form-item label="入场车身照片">
            <PreviewImage :photo-id="formData.enterCarImg" v-if="formData.enterCarImg"  :previewQueryUrl = "previewQueryUrl"></PreviewImage>
          </el-form-item>
        </el-col>

             <!-- 入场车牌照片 -->
             <el-col :span="12">
          <el-form-item label="入场车牌照片">
            <PreviewImage :photo-id="formData.enterLicenseImg"  v-if="formData.enterLicenseImg"  :previewQueryUrl = "previewQueryUrl"></PreviewImage>
          </el-form-item>
        </el-col>

          <!-- 出场车身照片 -->
          <el-col :span="12">
          <el-form-item label="出场车身照片">
            <PreviewImage :photo-id="formData.outCarImg" v-if="formData.outCarImg"  :previewQueryUrl = "previewQueryUrl"></PreviewImage>
          </el-form-item>
        </el-col>
   
  
  
        <!-- 出场车牌照片 -->
        <el-col :span="12">
          <el-form-item label="出场车牌照片">
            <PreviewImage :photo-id="formData.outLicenseImg"  v-if="formData.outLicenseImg"  :previewQueryUrl = "previewQueryUrl"></PreviewImage>
          </el-form-item>
        </el-col>
  
      
            </el-row>
       
       
        </el-form>
      </div> 
  
  
  </template>
  
  <script setup>
  import { ref, computed, onMounted,getCurrentInstance } from 'vue'
  import { ElMessage } from 'element-plus'
  import { updateNumberAccredit } from '@/api/majorParking/accredit/accredit.js'
  import { apiUrl } from "@/utils/config";
  // 属性定义
  const props = defineProps({
    closeBtn: Function,
    popupType: {
      type: String,
      default: 'view'
    },
    rowData: {
      type: Object,
      default: () => ({})
    }
  })
  // 字典
  
    // 字典
    
    const { proxy } = getCurrentInstance();
  
    const { park_data_status,enter_out_status,ORDER_STATUS } = proxy.useDict(
    "park_data_status",
    "enter_out_status",
    "ORDER_STATUS"
  );
  
  // 响应式数据
  const ruleformRef = ref(null)
  const formData = ref({
   
  })
  
  // 格式化金额
  const formatAmount = (value) => {
    return value ? Number(value).toFixed(2) : "";
  };
  
  
  // 初始化表单数据
  const initFormData = () => {
    formData.value =  props.rowData
    
  }
  // 图片
  const previewQueryUrl = ref( import.meta.env.VITE_APP_BASE_API + "/park" + apiUrl + "/majorParking/parkRecords/downloadBinary/")
  
  
  
  
  // 生命周期钩子
  onMounted(() => {
    initFormData()
  })
  
  
  </script>
  
  <style scoped lang="scss">
:deep(.table-img) {
  width: 200px !important;
  height: auto!important;
}
  </style>