<template>
  <div  class="dialog-box"
  :class="popupType === 'view' ? 'dialog-box-view' : 'dialog-box-edit'">
      <!-- 使用组合式API重构的表单 -->
      <el-form
        ref="ruleformRef"
        :model="formData"
        label-width="80px"
      >
        <!-- 停车场信息 -->
        <el-row>
          <el-col :span="24">
            <el-form-item label="停车场">
              <div>{{ rowData.parkingName || "" }}</div>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 车牌信息 -->
        <el-row>
          <el-col :span="24">
            <el-form-item label="车牌号码">
              <div>{{ rowData.plateNo || "" }}</div>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 查看模式专用字段 -->
          <el-row>
            <el-col :span="24">
              <el-form-item label="人员姓名">
                <div>{{ rowData.nickName || "" }}</div>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- 其他查看模式字段... -->
        <el-row>
            <el-col :span="24">
              <el-form-item  v-if="popupType == 'view'" label="人员部门" prop="abbreviation">
                <div >{{ rowData.deptName||"" }}</div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item  v-if="popupType == 'view'" label="车辆类型" prop="abbreviation">
                <div >{{ formatDict(rowData.vehicleType,parking_rule_vehicle_type) }}</div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item  v-if="popupType == 'view'" label="授权类型" prop="abbreviation">
                <div >{{ formatDict(rowData.accreditType,parking_accredit_type) }}</div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item  v-if="popupType == 'view'" label="有效状态" prop="abbreviation">
                <div >{{ formatDict(rowData.accreditStatus,accredit_status) }}</div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row >
            <el-col :span="24">
              <el-form-item  v-if="popupType == 'view'" label="创建时间" prop="abbreviation">
                <div >{{ rowData.createDate||"" }}</div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item  v-if="popupType == 'view'" label="操作人" prop="abbreviation">
                <div >{{ rowData.updateName||"" }}</div>
              </el-form-item>
            </el-col>
          </el-row>

        <!-- 有效期限 -->
        <el-row>
          <el-col :span="24">
            <el-form-item label="有效期限">
             
              <div>{{ displayExpirationTime }}</div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div> 


</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { updateNumberAccredit } from '@/api/majorParking/accredit/accredit.js'

// 属性定义
const props = defineProps({
  closeBtn: Function,
  popupType: {
    type: String,
    default: 'view'
  },
  rowData: {
    type: Object,
    default: () => ({})
  }
})
// 字典

const { proxy } = getCurrentInstance();
const { parking_rule_vehicle_type, parking_accredit_type, accredit_status } =
  proxy.useDict(
    "parking_rule_vehicle_type",
    "parking_accredit_type",
    "accredit_status"
  );



// 事件定义
const emit = defineEmits(['update:modelValue'])

// 响应式数据
const ruleformRef = ref(null)
const formData = ref({
  id: '',
  expirationTime: ''
})

// 时间选择器配置
const timepickerOptions = ref({
  disabledDate: (time) => {
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    return time.getTime() < today.getTime()
  }
})

// 计算属性
const displayExpirationTime = computed(() => {
  return props.rowData.expirationTime || "长期有效"
})

// 初始化表单数据
const initFormData = () => {
  formData.value.id = props.rowData.id
  if (props.rowData.expirationTime) {
    formData.value.expirationTime = `${props.rowData.expirationTime} 23:59:59`
  }
}

// 字典格式化方法
const formatDict = (value, dictType) => {
  const item = dictType?.find(item => item.value === value)
  return item?.label || ''
}

// 保存表单
const saveForm = async () => {
  try {
    const valid = await ruleformRef.value.validate()
    if (!valid) return

    const { code } = await updateNumberAccredit(formData.value)
    if (code === 200) {
      ElMessage.success('保存成功')
      props.closeBtn?.()
    }
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('操作失败，请稍后重试')
  }
}


// 生命周期钩子
onMounted(() => {
  initFormData()
})


</script>

<style scoped lang="scss">

</style>