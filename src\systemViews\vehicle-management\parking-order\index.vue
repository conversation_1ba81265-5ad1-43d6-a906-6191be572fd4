<!-- 停车记录 -->
<template>
  <div class="container-table-box">
    <el-card>
      <el-row :gutter="24">
        <el-col :span="24" :xs="24">
          <dialog-search
            @getList="getList"
            formRowNumber="4"
            v-model:isTable="isTable"
            :tableBtnShow="true"
            :columns="tabelForm.columns"
            :isShowRightBtn="$checkPermi(['parking:order:list'])"
          >
            <template #formList>
              <el-form
                :inline="true"
                :model="formData"
                ref="queryForm"
                size="medium"
                label-width="90px"
              >
                <!-- 表单元素保持不变 -->

                <el-form-item label="租户">
                  <TenantSelect v-model="formData.tenantId"></TenantSelect>
                </el-form-item>

                <el-form-item label="车牌号" prop="plateNo">
                  <el-input
                    placeholder="请输入车牌号"
                    v-model="formData.plateNo"
                    clearable
                  ></el-input>
                </el-form-item>
                <el-form-item label="停车场" prop="parkingId">
                  <TreeSelect v-model:parkingId="formData.parkingId" />
                  <!-- <el-select
                    v-model="formData.parkingId"
                    placeholder="请选择停车场"
                    @change="changeParkSystem"
                  >
                    <el-option
                      v-for="system in parkSystemList"
                      :key="system.id"
                      :label="system.parkingName"
                      :value="system.id"
                    />
                  </el-select> -->
                </el-form-item>

                <el-form-item label="人员姓名" prop="staffName">
                  <el-input
                    placeholder="请输入人员姓名"
                    v-model="formData.staffName"
                    clearable
                  ></el-input>
                </el-form-item>

                <el-form-item label="手机号" prop="cellphone">
                  <el-input
                    placeholder="请输入手机号"
                    v-model="formData.cellphone"
                    clearable
                  ></el-input>
                </el-form-item>

               
                <el-form-item label="订单时间">
                  <el-date-picker
                    v-model="daySelects"
                    type="datetimerange"
                    format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    range-separator="至"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    @change="handleDateChange"
                  />
                </el-form-item>
                <el-form-item label="订单状态" prop="orderStatus">
                  <el-select
                    @change="getList"
                    v-model="formData.orderStatus"
                    placeholder="请选择订单状态"
                    clearable
                  >
                    <el-option
                      v-for="(item, index) of ORDER_STATUS"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>

                <el-form-item
                  label="停车费用"
                  prop="payAmount"
                  :rules="[{ validator: validateAmount, trigger: 'blur' }]"
                >
                  <div class="flex">
                    <el-input
                      placeholder="最小金额"
                      v-model="formData.minPayAmount"
                      style="flex: 1"
                      clearable
                    ></el-input>
                    <span style="margin: 0 8px">-</span>
                    <el-input
                      placeholder="最大金额"
                      v-model="formData.maxPayAmount"
                      style="flex: 1"
                      clearable
                    ></el-input>
                  </div>
                </el-form-item>
              </el-form>
            </template>

            <template #searchList>
              <el-button
                class="search"
                type="primary"
                icon="Search"
                @click="handleQuery"
                v-hasPermi="['parking:order:list']"
                >搜索</el-button
              >
              <el-button
                class="reset"
                icon="Refresh"
                @click="resetQuery"
                v-hasPermi="['parking:order:list']"
                >重置</el-button
              >
             
            </template>

              <template v-slot:searchBtnList>
                <el-button
                type="primary"
                icon="Position"
                @click="handlePush"
                >推送</el-button
              > 
      </template>
          </dialog-search>

          <public-table
            v-show="isTable"
            ref="publictable"
            :rowKey="tabelForm.tableKey"
            :tableData="dataList"
            :columns="tabelForm.columns"
            :configFlag="tabelForm.tableConfig"
            :pageValue="pageParams"
            :total="total"
            :getList="getList"
          >
            <template #enterLicenseImgSlot="{ scope }">
              <PreviewImage
                :previewQueryUrl="previewQueryUrl"
                :photo-id="scope.row.enterLicenseImg"
                v-if="scope.row.enterLicenseImg"
              ></PreviewImage>
            </template>

            <template #enterCarImgSlot="{ scope }">
              <PreviewImage
                :previewQueryUrl="previewQueryUrl"
                :photo-id="scope.row.enterCarImg"
                v-if="scope.row.enterCarImg"
              ></PreviewImage>
            </template>

            <template #outLicenseImgSlot="{ scope }">
              <PreviewImage
                :previewQueryUrl="previewQueryUrl"
                :photo-id="scope.row.outLicenseImg"
                v-if="scope.row.outLicenseImg"
              ></PreviewImage>
            </template>

            <template #outCarImgSlot="{ scope }">
              <PreviewImage
                :previewQueryUrl="previewQueryUrl"
                :photo-id="scope.row.outCarImg"
                v-if="scope.row.outCarImg"
              ></PreviewImage>
            </template>
            <template #operation="{ scope }">
              <el-button
                v-hasPermi="['parking:order:list']"
                size="mini"
                type="text"
                @click="handleView(scope.row)"
                title="查看停车订单"
                >
               <svg-icon icon-class="tcdd-vIew" />
                </el-button
              >


              <el-button
                v-hasPermi="['parking:order:list']"
                size="mini"
                type="text"
                @click="handleView2(scope.row)"
                title="查看停车记录"
                >
               <svg-icon icon-class="tcjv_view" />
                </el-button
              >
            </template>
          </public-table>

          <div class="table-card-box" v-show="!isTable">
            <div
              class="table-card-one"
              v-for="(item, index) of dataList"
              :key="index"
            >
              <div class="cars-place">
                <div class="cars-place-box">
                  <img src="@/assets/images/zhtc.png" alt="" />

                  <span> {{ item.parkingName || "" }}</span>
                </div>
                <!-- <div class="cars-place-box" style="margin-left: 8px;">停车{{ item.parkingDuration }}小时，收费{{ item.payAmount }}元</div> -->
                <div class="flex-1"></div>
                <el-button
                  size="mini"
                  type="text"
                  v-hasPermi="['parking:order:list']"
                  title="查看停车订单"
                  @click="handleView(item)"
                >
               <svg-icon icon-class="tcdd-vIew" />
              </el-button>

               <el-button
                  size="mini"
                  type="text"
                  v-hasPermi="['parking:order:list']"
                  title="查看停车记录"
                  @click="handleView2(item)"
                >
               <svg-icon icon-class="tcjv_view" />
              </el-button>
                <!-- <el-popover ref="popoverRef" trigger="hover" title="操作">
                  <template #reference>
                    <el-button class="fixed-btn">操作</el-button>
                  </template>
                  <div class="popover-buttons">
                    <el-button size="mini" type="text" @click="handleView(item)"
                      >查看记录</el-button
                    >
                  </div>
                </el-popover> -->
              </div>
              <div class="card-top-row">
                <div class="cars-plateNo">
                  <div
                    @click="copyPlateNumber(item.plateNo)"
                    class="cars-plateNo-c"
                    :class="getPlateClass(item.plateNo)"
                  >
                    {{ formatPlateNo(item.plateNo) }}
                  </div>
                </div>

                <div class="left-dollor">
                  <div class="changft-2" v-if="item.staffName">
                    {{ item.staffName }}
                  </div>
                  <!-- <div class="changft-1">{{ item.payAmount }}元</div>  
                  <div class="changft-2">{{ item.parkingDuration }}小时</div>  -->
                </div>
                <div class="flex-1"></div>
                <div
                  class="car-card"
                  :class="item.orderStatus == '1' ? 'lanColor' : ''"
                >
                  {{ $formatDictLabel(item.orderStatus, ORDER_STATUS) }}
                </div>

                <!-- <el-popover ref="popoverRef" trigger="hover" title="操作">
                  <template #reference>
                    <el-button class="fixed-btn">操作</el-button>
                  </template>
                  <div class="popover-buttons">
                    <el-button size="mini" type="text" @click="handleView(item)"
                      >查看记录</el-button
                    >
                  </div>
                </el-popover> -->
              </div>
              <div class="card-content">
                <el-row>
                  <el-col :span="12" style="padding-right: 4px">
                    <div class="top-image">
                      <PreviewImage
                        :photo-id="item.enterCarImg"
                        :previewQueryUrl="previewQueryUrl"
                      ></PreviewImage>
                    </div>
                    <!-- <div class="mid-content" :title="item.parkingEnterName">
                     入场：{{ item.parkingEnterName || "" }}
                    </div> -->
                    <div class="bottom-info">
                      <div class="info-row">
                        <div class="info-point"></div>
                        <div class="info-text" :title="item.enterTime">
                          {{ item.enterTime }}
                        </div>
                      </div>
                    </div>
                  </el-col>

                  <el-col :span="12" style="padding-left: 4px">
                    <div class="top-image">
                      <PreviewImage
                        :photo-id="item.outCarImg"
                        :previewQueryUrl="previewQueryUrl"
                      ></PreviewImage>
                    </div>
                    <!-- <div class="mid-content" :title="item.parkingOutName">
                      出场：{{ item.parkingOutName || "" }}
                    </div> -->
                    <div class="bottom-info">
                      <div class="info-row">
                        <div class="info-point"></div>
                        <div class="info-text" :title="item.outTime">
                          {{ item.outTime }}
                        </div>
                      </div>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </div>
          </div>

          <pagination
            v-show="!isTable && total > 0"
            :total="total"
            class="table-card-box-pagination"
            v-model:page="pageParams.pageNum"
            v-model:limit="pageParams.pageSize"
            @pagination="getList"
          />
        </el-col>
      </el-row>
    </el-card>
    <DialogBox
      :visible="diaWindow.open1"
      :dialogWidth="diaWindow.dialogWidth"
      :dialogFooterBtn="false"
      @cancellation="cancellation"
      @close="close"
      :dialogTitle="diaWindow.dialogTitle"
    >
      <template #content>
        <accreditView
         v-if="diaWindow.popupType=='view'"
          :rowData="diaWindow.rowData"
          :popupType="diaWindow.popupType"
        ></accreditView>
        <recordView           v-if="diaWindow.popupType=='viewRecord'"   :rowData="diaWindow.rowData"

          :popupType="diaWindow.popupType"
        ></recordView>
      </template>
    </DialogBox>
  </div>
</template>
    
    <script setup name="ParkingOrder">
import { ref, reactive, onMounted, getCurrentInstance } from "vue";
import { screenIndex } from "@/api/park/parking-order/index";
import useUserStore from "@/store/modules/user";
import accreditView from "./accreditView";
import recordView from '@/systemViews/vehicle-management/parking-record/view'
import { ElMessage } from "element-plus";
import dayjs from "dayjs";
import { apiUrl } from "@/utils/config";
const previewQueryUrl = ref(
  import.meta.env.VITE_APP_BASE_API +
    "/park" +
    apiUrl +
    "/majorParking/parkRecords/downloadBinary/"
);
const userStore = useUserStore();
// 响应式数据
const parkSystemList = ref([]);
const dataList = ref([]);
const total = ref(0);
const publictable = ref(null);
// 表单数据
const formData = reactive({
  tenantId: userStore.userInfo.tenantId,
});

// 分页参数
const pageParams = reactive({
  pageNum: 1,
  pageSize: 10,
});

const diaWindow = reactive({
  open1: false,
  popupType: "view",
  rowData: "",
  dialogWidth: "50%",
  dialogTitle:'查看停车订单'
});
// 字典

const { proxy } = getCurrentInstance();

const { ORDER_STATUS } = proxy.useDict("ORDER_STATUS");

const isTable = ref(true);

// 表格配置
const tabelForm = reactive({
  tableKey: "1",
  tableConfig: {
    needPage: true,
    index: true,
    selection: false,
    reserveSelection: false,
    indexFixed: false,
    selectionFixed: false,
    indexWidth: "50",
    loading: false,
    showSummary: false,
    height: null,
  },
  columns: [
    {
      fieldIndex: "plateNo",
      label: "车牌号码",
      resizable: true,
      minWidth: "110px",
      visible: true,
      sortable: true,
      align: "center",
    },

    {
      fieldIndex: "staffName",
      label: "人员姓名",
      resizable: true,
      minWidth: "110px",
      visible: true,
      sortable: true,
    },

    {
      fieldIndex: "cellphone",
      label: "手机号码",
      resizable: true,
      minWidth: "120px",
      visible: true,
      sortable: true,
      type: "phoneHidden",
    },

    {
      fieldIndex: "payAmount",
      label: "停车费用(元)",
      resizable: true,
      minWidth: "150px",
      visible: true,
      sortable: true,
      type: "dollor",
      align: "right",
    },

    {
      fieldIndex: "orderStatus",
      label: "订单状态",
      resizable: true,
      minWidth: "120px",
      visible: true,
      sortable: true,
      type: "dict",
      dictList: ORDER_STATUS,
    },

    {
      fieldIndex: "feeDays",
      label: "连续停车(天)",
      resizable: true,
      minWidth: "150px",
      visible: true,
      sortable: true,
    },

    {
      fieldIndex: "orderDate",
      label: "订单时间",
      resizable: true,
      minWidth: "170px",
      visible: true,
      sortable: true,
    },
    {
      fieldIndex: "payDate",
      label: "支付时间",
      resizable: true,
      minWidth: "120px",
      visible: true,
      sortable: true,
    },

    {
      fieldIndex: "payAccount",
      label: "付款账户",
      resizable: true,
      minWidth: "120px",
      visible: true,
      sortable: true,
    },

    {
      fieldIndex: "parkingName",
      label: "停车场",
      resizable: true,
      minWidth: "150px",
      visible: true,
      sortable: true,
      align: "left",
    },

    {
      fieldIndex: "parkingDays",
      label: "停车天数",
      resizable: true,
      minWidth: "110px",
      visible: true,
      sortable: true,
    },
    {
      fieldIndex: "parkingDuration",
      label: "停车时长(小时)",
      resizable: true,
      minWidth: "150px",
      visible: true,
      sortable: true,
    },

    // {
    //   fieldIndex: "parkingEnterName",
    //   label: "停车场入口",
    //   resizable: true,
    //   minWidth: "140px",
    //   align: "left",
    //   visible: true,
    //   sortable: true,
    // },
    // {
    //   fieldIndex: "parkingOutName",
    //   label: "停车场出口",
    //   resizable: true,
    //   minWidth: "140px",
    //   align: "left",
    //   visible: true,
    //   sortable: true,
    // },

    {
      fieldIndex: "enterTime",
      label: "入场时间",
      resizable: true,
      minWidth: "170px",
      visible: true,
      sortable: true,
    },
    {
      fieldIndex: "enterCarImg",
      label: "入场车身照片",
      resizable: true,
      minWidth: "150px",
      slotname: "enterCarImgSlot",
      visible: true,
      sortable: true,
    },

    {
      fieldIndex: "outTime",
      label: "出场时间",
      resizable: true,
      minWidth: "170px",
      visible: true,
      sortable: true,
    },

    {
      fieldIndex: "outCarImg",
      label: "出场车身照片",
      resizable: true,
      minWidth: "150px",
      slotname: "outCarImgSlot",
      visible: true,
      sortable: true,
    },

    // {
    //   fieldIndex: "payRemark",
    //   label: "支付说明",
    //   resizable: true,
    //   minWidth: "180px",
    //   visible: true,
    //   sortable: true,
    //   align:'left'
    // },
    // {
    //   fieldIndex: "enterLicenseImg",
    //   label: "入场车牌照片",
    //   resizable: true,
    //   minWidth: "200px",
    //   slotname: "enterLicenseImgSlot",
    //   visible: true,
    //   sortable: true,
    // },

    // {
    //   fieldIndex: "outLicenseImg",
    //   label: "出场车牌照片",
    //   resizable: true,
    //   minWidth: "150px",
    //   slotname: "outLicenseImgSlot",
    //   visible: true,
    //   sortable: true,
    // },

    // {
    //   fieldIndex: "parkingRemark",
    //   label: "停车说明",
    //   resizable: true,
    //   minWidth: "150px",
    //   visible: true,
    //   sortable: true,
    //   align: "left",
    // },

    {
      label: "操作",
      slotname: "operation",
      width: "100",
      fixed: "right", //固定
      visible: true,
    },
  ],
});

// 初始化
const init = () => {
  getParkSystemList();
  getList();
};

// 推送按钮处理
const handlePush = () => {
  proxy.$modal.confirm('请确认 待支付订单汇总是否推送？').then(() => {
    // 这里可以添加推送的具体逻辑
    proxy.$modal.msgSuccess("推送成功");
  }).catch(() => {
    // 用户取消推送
  });
};
// 最大金额必须大于等于最小金额
const validateAmount = (rule, value, callback) => {
  if (
    formData.minPayAmount &&
    formData.maxPayAmount &&
    Number(formData.maxPayAmount) < Number(formData.minPayAmount)
  ) {
    formData.maxPayAmount = null; // 验证失败时清空最大金额
    callback(new Error("最大金额必须大于等于最小金额"));
  } else {
    callback();
  }
};
// 获取车牌样式类
const formatPlateNo = (plateNo) => {
  return plateNo.replace(/\s/g, "").toUpperCase();
};

const getPlateClass = (plateNo) => {
  const formatted = formatPlateNo(plateNo);
  return {
    "green-plate": formatted.length === 8, // 新能源绿牌
    "blue-plate": formatted.length === 7, // 传统蓝牌
  };
};

// 修改后的图片获取方法（带缓存）
const imageCache = new Map();
const getFileInfoByIdFun = async (photoId) => {
  try {
    if (!photoId) return "";

    // 检查缓存
    if (imageCache.has(photoId)) {
      return imageCache.get(photoId);
    }

    const res = await screenIndex.getFileInfoById(photoId);
    if (res.code == "1" && res.data?.url) {
      const fullUrl = res.data.url.startsWith("http")
        ? res.data.url
        : `${import.meta.env.VITE_API_BASE_URL}${res.data.url}`;

      // 缓存结果
      imageCache.set(photoId, fullUrl);
      return fullUrl;
    }
    return "";
  } catch (error) {
    console.error("获取照片失败:", error);
    return "";
  }
};

// 获取停车系统列表
const getParkSystemList = async () => {
  try {
    const res = await screenIndex.findparkinglot({}, { status: "0" });

    parkSystemList.value = res.data.records;
  } catch (error) {
    console.error("获取停车系统失败:", error);
  }
};
// 停车系统
const changeParkSystem = () => {};

// 获取表格数据
const getList = async () => {
  try {
    tabelForm.tableConfig.loading = true;
    const res = await screenIndex.findParkOrdersPage(formData, pageParams);
    // 添加预处理逻辑
    //   const listWithImages = await Promise.all(
    //     res.data.records.map(async (item) => ({
    //       ...item,
    //       // 缓存图片URL
    //       _enterLicenseImg: await getFileInfoByIdFun(item.enterLicenseImg),
    //       // 其他图片字段同理
    //       _enterCarImg: await getFileInfoByIdFun(item.enterCarImg),
    //       _outLicenseImg: await getFileInfoByIdFun(item.outLicenseImg),
    //       _outCarImg: await getFileInfoByIdFun(item.outCarImg),
    //     }))
    //   );
    //   res.data.records.map(async (item) => ({
    //     ...item,
    //     _enterLicenseImg: await getFileInfoByIdFun(item.enterLicenseImg),
    //   }));
    dataList.value = res.data.records;
    total.value = res.data.total;

    console.log(dataList.value);
  } catch (error) {
    console.error("获取数据失败:", error);
  } finally {
    tabelForm.tableConfig.loading = false;
  }
};

// 查询
const handleQuery = () => {
  pageParams.pageNum = 1;
  getList();
};

// 重置
const resetQuery = () => {
  formData.parkingId = "";
  formData.staffName = "";
  formData.cellphone = "";
  formData.plateNo = "";
  formData.minPayAmount = null; // 新增
  formData.maxPayAmount = null; // 新增
  proxy.resetForm("queryForm");
  handleQuery();
};

// 状态变更处理
const handleStatusChange = (row) => {
  console.log("状态变更:", row);
};

/** 查看 */
const handleView = (data) => {
  diaWindow.popupType = "view";
  diaWindow.rowData = data;
    diaWindow.dialogTitle = '查看停车订单'
  diaWindow.open1 = true;
};
const handleView2 = (data) => {
  diaWindow.popupType = "viewRecord";
  diaWindow.rowData = data;
    diaWindow.dialogTitle = '查看停车记录'
  diaWindow.open1 = true;
};
/** 点击取消保存 */
const cancellation = (val) => {
  close(false);
};

/** 关闭弹窗 */
const close = (val) => {
  diaWindow.open1 = val;
};

/** 复制车牌 */
const copyPlateNumber = async (plateNo) => {
  const text = plateNo.replace(/\s/g, "").toUpperCase();

  const textarea = document.createElement("textarea");
  textarea.value = text;
  document.body.appendChild(textarea);
  textarea.select();

  try {
    // 尝试执行复制操作
    const success = document.execCommand("copy");
    if (success) {
      ElMessage.success("车牌号已复制");
    } else {
      ElMessage.error("复制失败，请手动复制");
    }
  } catch (error) {
    ElMessage.error("复制失败，请手动复制");
  }

  document.body.removeChild(textarea);
};

// 订单时间
const daySelects = ref([]);
// 初始化默认值
const setDefaultDateRange = () => {
  const now = dayjs(); // 当前时间
  const startDate = now.format("YYYY-MM-DD"); // 当前日期
  const endDate = now.format("YYYY-MM-DD"); // 当前日期
  // 开始时间：当前时间
  const startTime = "00:00:00";

  // 结束时间：当天的 24:00:00
  const endTime = "23:59:59";

  // 设置默认日期范围
  daySelects.value = [`${startDate} ${startTime}`, `${endDate} ${endTime}`];
};
const handleDateChange = (dates) => {
  if (dates && dates.length === 2) {
    // 更新绑定数据
    formData.startTime = dates[0];
    formData.endTime = dates[1];
  } else {
    formData.startTime = "";
    formData.endTime = "";
  }
};

// 生命周期钩子
onMounted(() => {
  init();
});
</script>
    
    <style scoped lang="scss">
:deep(.table-img) {
  width: 100px;
  height: 26px;
}

:deep(.image-error) {
  .el-icon {
    font-size: 34px !important;
  }
}
:deep(.svg-icon){
  font-size:16px;
}
</style>