<template>
  <div class="container-table-box">
      <el-card>
    <dialog-search
      @getList="getList"
      formRowNumber="4"
      :columns="tabelForm.columns"
        :isShowRightBtn="$checkPermi(['parking:parkingRule:list'])"
    >
      <template v-slot:formList>
        <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          label-width="85px"
        >
          <el-form-item label="规则名称">
            <el-input
              v-model="queryParams.ruleName"
              placeholder="请输入规则名称"
              clearable
            ></el-input>
          </el-form-item>

          <el-form-item label="停车场">
            <TreeSelect
              v-model:parkingId="queryParams.parkingId"
            />
            <!-- <el-select
              v-model="queryParams.parkingId"
              placeholder="请选择停车场"
              clearable
            >
              <el-option
                v-for="dict in parkingList"
                :key="dict.id"
                :label="dict.parkingName"
                :value="dict.id"
              />
            </el-select> -->
          </el-form-item>

          <el-form-item label="适用类型">
            <el-select
              @change="getList"
              v-model="queryParams.applyType"
              placeholder="请选择适用类型"
              clearable
            >
              <el-option
                v-for="(item, index) of parking_rule_apply_type"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="进出类型">
            <el-select
              @change="getList"
              v-model="queryParams.entryExitType"
              placeholder="请选择进出类型"
              clearable
            >
              <el-option
                v-for="(item, index) of entry_exit_type"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="通过类型">
            <el-select
              @change="getList"
              v-model="queryParams.restrictionType"
              placeholder="请选择通过类型"
              clearable
            >
              <el-option
                v-for="(item, index) of parking_rule_restriction_type"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="日期类型">
            <el-select
              @change="getList"
              v-model="queryParams.dateType"
              placeholder="请选择日期类型"
              clearable
            >
              <el-option
                v-for="(item, index) of date_type"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="启用状态">
            <el-select
              @change="getList"
              v-model="queryParams.status"
              placeholder="请选择启用状态"
              clearable
            >
              <el-option
                v-for="(item, index) of parking_rule_status"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </template>

      <template v-slot:searchList>
        <el-button type="primary" icon="Search" @click="handleQuery" v-hasPermi="['parking:parkingRule:list']"
          >搜索</el-button
        >

        <el-button icon="Refresh" @click="resetQuery" v-hasPermi="['parking:parkingRule:list']">重置</el-button>
      </template>

      <template v-slot:searchBtnList>
        <el-button type="primary" icon="Plus" @click="handleAdd" v-hasPermi="['parking:parkingRule:add']"
          >新增
        </el-button>
      </template>
    </dialog-search>

    <public-table
      ref="publictable"
      :rowKey="tabelForm.tableKey"
      :tableData="list"
      :columns="tabelForm.columns"
      :configFlag="tabelForm.tableConfig"
      :pageValue="pageParams"
      :total="total"
      :getList="getList"
    >
      <template #operation="{ scope }">
        <el-button
         v-hasPermi="['parking:parkingRule:list']"
          size="mini"
          type="text"
          title="查看"
          icon="View"
          @click="handleView(scope.row)"
        >
        </el-button>

        <el-button
         v-hasPermi="['parking:parkingRule:edit']"
          size="mini"
          type="text"
          title="修改"
          icon="Edit"
          @click="handleUpate(scope.row)"
        >
        </el-button>

        <el-button
        v-hasPermi="['parking:parkingRule:remove']"
          size="mini"
          type="text"
          title="删除"
          icon="Delete"
          @click="handleDelete(scope.row)"
        >
        </el-button>
      </template>
    </public-table>
</el-card>
    <DialogBox
      :visible="open1"
      @save="save"
      :dialogWidth="dialogWidth"
      :dialogFooterBtn="dialogFooterBtn"
      @cancellation="cancellation"
      @close="close"
      CloseSubmitText="取消"
      SaveSubmitText="确定"
      :dialogTitle="dialogTitle"
    >
      <template #content>
        <edit
          ref="editRef"
          :popupType="popupType"
          :rowData="rowData"
          @closeBtn="cancellationRefresh"
        ></edit>
      </template>
    </DialogBox>
  </div>
</template>



<script setup name="ParkingRule">
import edit from "./components/add.vue";

import { ref, reactive, getCurrentInstance } from "vue";

import { ElMessage, ElMessageBox } from "element-plus";

import { screenIndex } from "@/api/majorParking/parkingRule";

const publictable = ref(null);

const queryParams = ref({});

// 表格数据

const list = ref([{}]);

// 表格分页参数

const pageParams = ref({
  pageNum: 1,

  pageSize: 10,
});

// 表格总数

const total = ref(0);

// 是否显示弹窗

// 停车场列表

const parkingList = ref([]);

const open1 = ref(false);

const dialogTitle = ref("");

const popupType = ref("");

const editRef = ref(null);

const rowData = ref({});

const dialogWidth = ref();

const dialogFooterBtn = ref(false);

// 字典

const { proxy } = getCurrentInstance();

const {
  parking_rule_apply_type,
  entry_exit_type,
  parking_rule_restriction_type,
  time_restriction,
  parking_rule_vehicle_type,
  date_type,
  parking_rule_status,
  car_type
} = proxy.useDict(
  "parking_rule_apply_type",

  "entry_exit_type",

  "parking_rule_restriction_type",

  "time_restriction",

  "parking_rule_vehicle_type",

  "date_type",

  "parking_rule_status",
  "car_type"
);

// 表格配置

const tabelForm = reactive({
  tableKey: "1", //表格key值

  isShowRightToolbar: true, //是否显示右侧显示和隐藏

  showSearch: true,

  // 表格表格数据

  columns: [
    {
      fieldIndex: "ruleName", // 对应列内容的字段名

      label: "规则名称", // 显示的标题

      resizable: true, // 对应列是否可以通过拖动改变宽度

      visible: true, // 展示与隐藏

      sortable: true, // 对应列是否可以排序

      minWidth: "120px", //最小宽度%

      width: "", //宽度

      align: "left",
    },

    {
      fieldIndex: "parkingName", // 对应列内容的字段名

      label: "停车场", // 显示的标题

      resizable: true, // 对应列是否可以通过拖动改变宽度

      visible: true, // 展示与隐藏

      sortable: true, // 对应列是否可以排序

      minWidth: "140px", //最小宽度%

      width: "", //宽度

      align: "left",
    },

    {
      fieldIndex: "applyType", // 对应列内容的字段名

      label: "适用类型", // 显示的标题

      resizable: true, // 对应列是否可以通过拖动改变宽度

      visible: true, // 展示与隐藏

      sortable: true, // 对应列是否可以排序

      minWidth: "120px", //最小宽度%

      width: "", //宽度

      type: "dict",

      dictList: parking_rule_apply_type,
    },

    {
      fieldIndex: "carType", // 对应列内容的字段名

      label: "车辆类型", // 显示的标题

      resizable: true, // 对应列是否可以通过拖动改变宽度

      visible: true, // 展示与隐藏

      sortable: true, // 对应列是否可以排序

      minWidth: "120px", //最小宽度%

      width: "", //宽度

      align: "",

      type: "dict",

      dictList: car_type,
    },

    {
      fieldIndex: "plateNumbers", // 对应列内容的字段名

      label: "车牌号码", // 显示的标题

      resizable: true, // 对应列是否可以通过拖动改变宽度

      visible: true, // 展示与隐藏

      sortable: true, // 对应列是否可以排序

      minWidth: "120px", //最小宽度%

      width: "", //宽度

      align: "",
    },

    {
      fieldIndex: "entryExitType", // 对应列内容的字段名

      label: "进出类型", // 显示的标题

      resizable: true, // 对应列是否可以通过拖动改变宽度

      visible: true, // 展示与隐藏

      sortable: true, // 对应列是否可以排序

      minWidth: "120px", //最小宽度%

      width: "", //宽度

      align: "center",

      type: "dict",

      dictList: entry_exit_type,
    },

    {
      fieldIndex: "restrictionType", // 对应列内容的字段名

      label: "通过类型", // 显示的标题

      resizable: true, // 对应列是否可以通过拖动改变宽度

      visible: true, // 展示与隐藏

      sortable: true, // 对应列是否可以排序

      minWidth: "120px", //最小宽度%

      width: "", //宽度

      align: "",

      type: "dict",

      dictList: parking_rule_restriction_type,
    },

    {
      fieldIndex: "validFrom", // 对应列内容的字段名

      label: "有效期起", // 显示的标题

      resizable: true, // 对应列是否可以通过拖动改变宽度

      visible: true, // 展示与隐藏

      sortable: true, // 对应列是否可以排序

      minWidth: "120px", //最小宽度%

      width: "", //宽度

      align: "",
    },

    {
      fieldIndex: "validTo", // 对应列内容的字段名

      label: "有效期止", // 显示的标题

      resizable: true, // 对应列是否可以通过拖动改变宽度

      visible: true, // 展示与隐藏

      sortable: true, // 对应列是否可以排序

      minWidth: "120px", //最小宽度%

      width: "", //宽度

      align: "",
    },

    {
      fieldIndex: "dateType", // 对应列内容的字段名

      label: "日期类型", // 显示的标题

      resizable: true, // 对应列是否可以通过拖动改变宽度

      visible: true, // 展示与隐藏

      sortable: true, // 对应列是否可以排序

      minWidth: "120px", //最小宽度%

      width: "", //宽度

      align: "",

      type: "dict",

      dictList: date_type,
    },

    {
      fieldIndex: "status", // 对应列内容的字段名

      label: "启用状态", // 显示的标题

      resizable: true, // 对应列是否可以通过拖动改变宽度

      visible: true, // 展示与隐藏

      sortable: true, // 对应列是否可以排序

      minWidth: "120px", //最小宽度%

      width: "", //宽度

      align: "",

      type: "dict",

      dictList: parking_rule_status,
    },

    {
    
    fieldIndex: "maxCarNumber", // 对应列内容的字段名  

      label: "最大可停车数量", // 显示的标题

      resizable: true, // 对应列是否可以通过拖动改变宽度

      visible: true, // 展示与隐藏

      sortable: true, // 对应列是否可以排序

      minWidth: "160px", //最小宽度%

      width: "", //宽度

      align: "",


    },

    {
      fieldIndex: "timeRestriction", // 对应列内容的字段名

      label: "规则分类", // 显示的标题

      resizable: true, // 对应列是否可以通过拖动改变宽度

      visible: true, // 展示与隐藏

      sortable: true, // 对应列是否可以排序

      minWidth: "120px", //最小宽度%

      width: "", //宽度

      align: "",

      type: "dict",

      dictList: time_restriction,
    },

    {
      fieldIndex: "timeRestriction1", // 对应列内容的字段名

      label: "限制时间1", // 显示的标题

      resizable: true, // 对应列是否可以通过拖动改变宽度

      visible: true, // 展示与隐藏

      sortable: true, // 对应列是否可以排序

      minWidth: "120px", //最小宽度%

      width: "", //宽度

      align: "",
    },

    {
      fieldIndex: "timeRestriction2", // 对应列内容的字段名

      label: "限制时间2", // 显示的标题

      resizable: true, // 对应列是否可以通过拖动改变宽度

      visible: true, // 展示与隐藏

      sortable: true, // 对应列是否可以排序

      minWidth: "120px", //最小宽度%

      width: "", //宽度

      align: "",
    },

    {
      fieldIndex: "timeRestriction3", // 对应列内容的字段名

      label: "限制时间3", // 显示的标题

      resizable: true, // 对应列是否可以通过拖动改变宽度

      visible: true, // 展示与隐藏

      sortable: true, // 对应列是否可以排序

      minWidth: "120px", //最小宽度%

      width: "", //宽度

      align: "",
    },

    {
      fieldIndex: "timeRestriction4", // 对应列内容的字段名

      label: "限制时间4", // 显示的标题

      resizable: true, // 对应列是否可以通过拖动改变宽度

      visible: true, // 展示与隐藏

      sortable: true, // 对应列是否可以排序

      minWidth: "120px", //最小宽度%

      width: "", //宽度

      align: "",
    },

    {
      label: "操作",

      slotname: "operation",

      width: "150",

      fixed: "right", //固定

      visible: true,
    },
  ],

  // 表格基础配置项，看个人需求添加

  tableConfig: {
    needPage: true, // 是否需要分页

    index: true, // 是否需要序号

    selection: false, // 是否需要多选

    reserveSelection: false, // 是否需要支持跨页选择

    indexFixed: false, // 序号列固定 left true right

    selectionFixed: false, //多选列固定left true right

    indexWidth: "50", //序号列宽度

    loading: false, //表格loading

    showSummary: false, //是否开启合计

    height: null, //表格固定高度 比如：300px
  },
});

// 获取停车场数据

const selectPark = async () => {
  try {
    const res = await screenIndex.findparkinglot({}, { status: "0" });

    parkingList.value = res.data.records || [];
  } catch (error) {
    console.error("获取停车场列表失败:", error);
  }
};

/** 查询用户列表 */

const getList = () => {
  tabelForm.tableConfig.loading = true;

  screenIndex
    .findParkingRule(queryParams.value, pageParams.value)
    .then((response) => {
      list.value = response.data.records;

      total.value = response.data.total;

      tabelForm.tableConfig.loading = false;
    });
};

/** 搜索按钮操作 */

const handleQuery = () => {
  pageParams.value.pageNum = 1;

  getList();
};

/** 重置按钮操作 */

const resetQuery = () => {
  queryParams.value = {};

  proxy.resetForm("queryForm");

  handleQuery();
};

/** 提交保存 */

const save = (val) => {
  editRef.value.saveForm();
};

/** 新增 */

const handleAdd = () => {
  dialogTitle.value = "新增停车规则";

  popupType.value = "add";

  dialogFooterBtn.value = true;

  rowData.value = {};

  dialogWidth.value = "50%";

  open1.value = true;
};

/** 修改 */

const handleUpate = (row) => {
  dialogTitle.value = "修改停车规则";

  popupType.value = "edit";

  dialogFooterBtn.value = true;

  rowData.value = row;

  dialogWidth.value = "50%";

  open1.value = true;
};

/** 查看 */

const handleView = (row) => {
  dialogTitle.value = "查看停车规则";

  popupType.value = "view";

  dialogFooterBtn.value = false;

  rowData.value = row;

  open1.value = true;
};

// 删除规则

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm("确认要删除吗？", "提示", {
      confirmButtonText: "确定",

      cancelButtonText: "取消",

      type: "warning",
    });

    const res = await screenIndex.deleteParkingRule(row);

    if (res.code == "1") {
      ElMessage.success("删除成功");

      await getList();
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    console.error("删除失败:", error);
  }
};

/** 点击取消保存 */

const cancellation = (val) => {
  close(false);
};

const cancellationRefresh = () => {
  close(false);

  getList();
};

/** 关闭弹窗 */

const close = (val) => {
  open1.value = val;
};

// 生命周期：组件挂载时调用

onMounted(() => {
  getList();

  selectPark();
});
</script>