<!-- 添加设置 -->

<template>
  <div
    class="dialog-box"
    :class="
      popupType === 'viewDevice' ? 'dialog-box-viewDevice' : 'dialog-box-edit'
    "
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="popupType !== 'viewDevice' ? rules : {}"
      label-width="80px"
    >
      <el-row>
        <el-col :span="24">
          <el-form-item label="设备名称" prop="deviceSn">
            <el-select
              v-if="popupType !== 'viewDevice'"
              v-model="form.deviceSn"
              collapse-tags
              placeholder="请选择设备名称"
              clearable
              filterable
              @change="selectDeviceInfo"
            >
              <el-option
                v-for="(item, index) in deviceList"
                :key="index"
                :label="item.deviceName"
                :value="item.deviceSn"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="设备编码" prop="deviceSn">
            <el-input
              disabled
              v-if="popupType !== 'viewDevice'"
              v-model="form.deviceSn"
              placeholder="请输入设备"
              maxlength="30"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="在线状态" prop="deviceStatus">
            <el-select
              disabled
              v-if="popupType !== 'viewDevice'"
              v-model="form.deviceStatus"
              placeholder="请选择设备"
            >
              <el-option
                v-for="dict in device_action_status"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="进出类型" prop="actionType">
            <el-select
              disabled
              v-if="popupType !== 'viewDevice'"
              v-model="form.actionType"
              placeholder="请选择设备"
            >
              <el-option
                v-for="dict in device_action_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="24">
          <el-form-item label="访客权限" prop="vistorSwitch">
            <el-radio-group
              v-if="popupType !== 'view'"
              v-model="form.vistorSwitch"
            >
              <el-radio-button
                v-for="dict in major_parking_lot_vistor_switch"
                :key="dict.value"
                :label="dict.value"
              >
                {{ dict.label }}
              </el-radio-button>
            </el-radio-group>
            <div v-if="popupType === 'view'">
              {{
                formatDict(form.vistorSwitch, major_parking_lot_vistor_switch)
              }}
            </div>
          </el-form-item>
        </el-col> -->
      </el-row>
    </el-form>

  </div>
</template>

<script setup>
import { ref, onMounted,getCurrentInstance } from "vue";
import { findDeviceForParking } from "@/api/majorParking/parkinglot";

// 字典数据
const { proxy } = getCurrentInstance();
const {
  device_action_status,
  device_action_type,
  major_parking_lot_vistor_switch,
} = proxy.useDict(
  "device_action_status",
  "device_action_type",
  "major_parking_lot_vistor_switch"
);
// 表单ref
const formRef = ref(null);

// 定义 props
const props = defineProps({
  rowData: {
    type: Object,
    default: () => ({}),
  },
  popupType: {
    type: String,
    default: "",
  },
});

// 定义 emits
const emit = defineEmits(["submitClose", "cancelClose"]);

// 表单数据
const form = ref({
});

// 设备列表
const deviceList = ref([]);

// 表单校验规则
const rules = ref({
  deviceSn: [{ required: true, message: "请选择设备", trigger: "blur" }],
  // vistorSwitch: [
  //   { required: true, message: "请选择访客权限", trigger: "blur" },
  // ],
});

// 选择设备信息
const selectDeviceInfo = () => {
  deviceList.value.forEach((item) => {
    if (item.deviceSn === form.value.deviceSn) {
      form.value.deviceName = item.deviceName;
      form.value.deviceStatus = item.deviceStatus;
      form.value.actionType = item.actionType;
    }
  });
};

// 获取停车场设备列表
const fetchDeviceForParking = async () => {
  const res = await findDeviceForParking({});
  deviceList.value = res.data;
};

// 提交表单
const submitForm = () => {
  formRef.value.validate((valid) => {
    if (valid) {
      form.value.parkingId = props.rowData.id;
      emit("submitClose", form.value);
    }
  });
};



// 字典方法
const formatDict = (value, dict) => {
  const item = dict.find((d) => d.value === value);
  return item ? item.label : "-";
};

// 组件挂载时获取设备列表
onMounted(() => {
  fetchDeviceForParking();
});

// 暴露方法
defineExpose({
  submitForm,
});
</script>

<style scoped>
</style>