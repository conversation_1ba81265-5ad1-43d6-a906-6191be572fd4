<template>
  <div class="app-container">
    <el-card style="height: 100%" shadow="never">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
        <el-form-item label="租户" prop="tenantId" v-if="userStore.userInfo.customParam.userType === 'admin'">
          <el-select v-model="queryParams.tenantId" placeholder="请选择租户" style="width: 180px" @change="handleQuery">
            <el-option v-for="item in tenantList" :key="item.tenantId" :label="item.tenantName" :value="item.tenantId"/>
          </el-select>
        </el-form-item>
        <el-form-item label="公告标题" prop="noticeTitle">
          <el-input v-model="queryParams.noticeTitle" placeholder="请输入公告标题" clearable style="width: 180px" @keyup.enter="handleQuery"/>
        </el-form-item>
        <el-form-item label="类型" prop="noticeType">
          <el-select v-model="queryParams.noticeType" placeholder="公告类型" style="width: 180px" clearable>
            <el-option v-for="dict in notice_type_list" :key="dict.value" :label="dict.label" :value="dict.value"/>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="Plus" v-hasPermi="['sys:release:notice:add']" @click="handleAdd">新增</el-button>
        </el-col>
        <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <!--  表格数据展示  -->
      <el-table v-loading="loading" :data="noticeList">
        <el-table-column label="序号" type="index" width="50" />
        <el-table-column label="公告标题" align="left" prop="noticeTitle" :show-overflow-tooltip="true">
          <template #default="scope">
            <a style="color: #3a7eb9" @click="detail(scope.row)">{{scope.row.noticeTitle}}</a>
          </template>
        </el-table-column>
        <el-table-column label="公告类型" align="center" prop="noticeTypeName" width="100">
          <template #default="scope">
            <dict-tag effect="plain" :options="notice_type_list" :value="scope.row.noticeType"/>
          </template>
        </el-table-column>
        <el-table-column label="通知类型" align="center" prop="notificationTypeName" width="80">
          <template #default="scope">
            <dict-tag effect="plain" :options="notice_notification_list" :value="scope.row.notificationType"/>
          </template>
        </el-table-column>
        <el-table-column label="创建者" align="center" prop="createByName" width="100"/>
        <el-table-column label="创建时间" align="center" prop="createDate" width="200">
          <template #default="scope">
            <span>{{parseTime(scope.row.createDate, "{y}-{m}-{d} {h}:{m}:{s}") }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" prop="noticeStatusName" width="100">
          <template #default="scope">
            <dict-tag :options="notice_status_list" :value="scope.row.noticeStatus"/>
          </template>
        </el-table-column>
        <el-table-column label="是否置顶" align="center" prop="isTop" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.isTop === 1 ? 'success' : 'info' ">{{ scope.row.isTop === 1 ? '是' : '否' }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="紧急程度" align="center" prop="priorityName" width="80">
          <template #default="scope">
            <dict-tag :options="notice_priority_list" :value="scope.row.priorityValue"/>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button link type="primary" icon="Position" v-hasPermi="['sys:release:notice:release']"
                       @click="updateStatus(scope.row,scope.row.noticeStatus !== 'notice_status_release')">
              {{scope.row.noticeStatus != "notice_status_release" ? "发布" : "取消发布" }}
            </el-button>
            <el-button link type="primary" icon="Upload" v-hasPermi="['sys:release:notice:top']" @click="updateTop(scope.row)">
              {{ scope.row.isTop == 0 ? "置顶" : "取消置顶" }}
            </el-button>
            <el-button v-if="scope.row.noticeStatus != 'notice_status_release'" link type="primary" icon="Edit"
                       v-hasPermi="['sys:release:notice:edit']" @click="handleUpdate(scope.row)">修改
            </el-button>
            <el-button
                v-if="scope.row.noticeStatus != 'notice_status_release'" link type="primary" icon="Delete"
                v-hasPermi="['sys:release:notice:remove']" @click="handleDelete(scope.row)">删除
            </el-button>
            <el-button link type="primary" icon="View" v-hasPermi="['sys:release:notice:view']" @click="detail(scope.row)">查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize"
                  @pagination="getList"/>
    </el-card>

    <!-- 查看/添加/修改公告对话框 -->
    <el-dialog :title="title" v-model="open" width="1000px" append-to-body>
      <el-form ref="formRef" :model="form" :rules="rules" label-width="140px" :disabled="formDisabled">
        <el-row>
          <el-col :span="12">
            <el-form-item label="公告标题" prop="noticeTitle">
              <el-input v-model="form.noticeTitle" placeholder="请输入公告标题"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="公告类型" prop="noticeType">
              <el-select v-model="form.noticeType" placeholder="请选择">
                <el-option v-for="dict in notice_type_list" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="紧急程度" prop="priorityValue">
              <el-select v-model="form.priorityValue" placeholder="请选择">
                <el-option v-for="dict in notice_priority_list" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="通知类型" prop="notificationType">
              <el-select v-model="form.notificationType" placeholder="请选择">
                <el-option v-for="dict in notice_notification_list" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="租户">
              <el-input v-model="tenantName" placeholder="租户" maxlength="50" disabled/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否全员可看" prop="isAllShow">
              <el-switch v-model="form.isAllShow" active-text="配置权限" inactive-text="全员可看" :active-value="0" :inactive-value="1" @change="setAuth" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-show="form.isAllShow === 0">
          <el-col :span="12">
            <el-form-item prop="visibleRange">
              <template #label>
                <span>
                  <el-tooltip content="当前用户所属租户下的组织" placement="top">
                    <el-icon><question-filled/></el-icon>
                  </el-tooltip>
                  当前租户组织
                </span>
              </template>
              <el-tree-select v-model="form.orgList" :data="allTree" :props="{ label: 'orgName', children:'children', value: 'orgId' }"
                              value-key="orgId" placeholder="请选择当前租户组织" multiple clearable show-checkbox :render-after-expand="false"
                              :default-expand-level="1" check-on-click-node check-strictly/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="visibleRange">
              <template #label>
                <span>
                  <el-tooltip content="选择的管辖租户下的所有组织" placement="top">
                    <el-icon><question-filled /></el-icon>
                  </el-tooltip>
                  管辖租户
                </span>
              </template>
              <el-select v-model="form.jurisdictionTenantIds" multiple collapse-tags placeholder="请选择管辖租户">
                <template v-if="jurisdictionTenantList && jurisdictionTenantList.length > 1">
                  <el-option v-for="dict in jurisdictionTenantList" :key="dict.tenantId" :label="dict.tenantName" :value="dict.tenantId"></el-option>
                </template>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="公告封面" prop="coverUrl">
              <el-upload :file-list="imageList" class="upload-demo" action="" :http-request="uploadImage"
                         :on-preview="handlePreview" :on-remove="handleRemove"
                         :on-exceed="handleExceed" :on-success="handleSuccess"
                         :limit="1">
                <el-button size="small" type="primary">点击上传<el-icon class="el-icon--right"><Upload /></el-icon></el-button>
              </el-upload>
              <el-dialog v-model="previewDialogVisible" append-to-body>
                <el-image width="100%" :src="previewImageUrl" />
              </el-dialog>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="内容" prop="noticeContent">
              <editor v-model="form.noticeContent" :min-height="192" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm" :disabled="formDisabled">确 定</el-button>
          <el-button @click="cancel" >取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="News">
import useUserStore from '@/store/modules/user';
import {getTenants} from "@/api/tenant/tenant";
import {
  addNotice,
  delNotice,
  listNotice,
  selectNoticeByNoticeId, updateNotice,
  updateStatusById,
  updateTopByNoticeId
} from "@/api/release/notice";
import { getJurisdictionTenantList } from "@/api/tenant/tenantJurisdiction";
import { treeAll } from "@/api/system/dept";
import {QuestionFilled} from "@element-plus/icons-vue";
import {ref} from "vue";
import {uploadSingle} from "@/api/tool/upload";

const { proxy } = getCurrentInstance();
const showSearch = ref(true);
const {
  notice_type_list,
  notice_priority_list,
  notice_notification_list,
  notice_status_list
} = proxy.useDict("notice_type_list", "notice_priority_list", "notice_notification_list", "notice_status_list");

/** 初始化租户数据 */
const userStore = useUserStore();
const tenantList = ref([]);
const tenantName = ref(userStore.userInfo.customParam.tenantName);
function getTenantList() {
  getTenants().then(res => {
    tenantList.value = res.data;
  }).catch(() => {
    tenantList.value = [];
  })
}
// 获取租户名
function getTenantName() {
  tenantList.value.forEach(item => {
    if (item.tenantId === queryParams.value.tenantId) {
      tenantName.value = item.tenantName;
    }
  })
}

/** 表格数据展示 */
const noticeList = ref([]);
const loading = ref(false);
const total = ref(0);
const queryRef = ref(null);
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  noticeTitle: undefined,
  createBy: undefined,
  status: undefined,
  showType: 1,
  programaType: "system_notice",
  tenantId: userStore.userInfo.customParam.tenantId,
})

function getList() {
  loading.value = true;
  listNotice(queryParams.value).then(res => {
    if (res.data) {
      noticeList.value = res.data.records;
      total.value = res.data.total;
    }
    loading.value = false;
  }).catch(() => {
    loading.value = false;
  })
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getTenantName();
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 发布/取消发布按钮操作 */
const updateStatus = (row, flag) => {
  const text = flag ? "确认" : "取消";
  proxy.$modal.confirm('是否'+text+'发布公告标题为"'+row.noticeTitle+'"的数据项？').then(() => {
    updateStatusById({
      noticeId: row.noticeId,
      noticeStatus: flag ? "notice_status_release" : "notice_status_draft",
    }).then(res => {
      if (res.data) {
        proxy.$modal.msgSuccess("操作成功！")
      } else {
        proxy.$modal.msgError("操作失败！")
      }
      getList();
    })
  }).catch(() => {
    proxy.$modal.msg("操作已取消")
  })
}

/** 置顶/取消置顶按钮操作 */
const updateTop = (row) => {
  updateTopByNoticeId({
    noticeId: row.noticeId,
    isTop: row.isTop === 0 ? "1" : "0"
  }).then(res => {
    if (res.data) {
      proxy.$modal.msgSuccess("操作成功！")
    } else {
      proxy.$modal.msgError("操作失败！")
    }
    getList();
  }).catch(() => {
    proxy.$modal.msg("取消操作！")
  })
}

/** 删除按钮操作 */
const handleDelete = (row) => {
  proxy.$modal.confirm('是否确认删除公告标题为"'+row.noticeTitle+'"的数据项？').then(() => {
    delNotice({ noticeId: row.noticeId }).then(res => {
      if (res.data) {
        proxy.$modal.msgSuccess("删除成功");
      } else {
        proxy.$modal.msgError("删除失败");
      }
      getList();
    })
  }).catch(() => {
    proxy.$modal.msg("取消操作");
  })
}

/**
 * 新增 && 修改
 */
const visibleRangeValidate = (rule, value, callback) => {
  if (form.value.isAllShow === 0) {
    // 租户组织的数据长度
    const orgListLength = form.value.orgList.length
    // 管辖租户组织的数据长度
    const jurisdictionLength = form.value.jurisdictionTenantIds.length
    if (orgListLength === 0 && jurisdictionLength === 0) {
      callback(new Error("请选择当前租户组织或管辖租户"));
    } else if (orgListLength === 0 || jurisdictionLength !== 0) {
      callback();
    } else {
      callback();
    }
  } else {
    callback();
  }
}
const formRef = ref(null);
const open = ref(false);
const title = ref("");
const formDisabled = ref(false);
const allTree = ref([]);
const jurisdictionTenantList = ref([]);
const form = ref({
  isAllShow: 1,
  noticeContentText: "",
  notificationType: "general",
  jurisdictionTenantList: [],
});
const rules = {
  noticeTitle: [{ required: true, message: "公告标题不能为空", trigger: "blur" },],
  noticeType: [{ required: true, message: "公告类型不能为空", trigger: "change" },],
  visibleRange: [{ required: true, validator: visibleRangeValidate, trigger: "blur" },],
  priorityValue: [{ required: true, message: "紧急程度不能为空", trigger: "change" },],
  notificationType: [{ required: true, message: "通知类型不能为空", trigger: "change" },]
}

/** 表单重置 */
function reset() {
  form.value = {
    noticeId: undefined,
    noticeTitle: undefined,
    noticeType: undefined,
    noticeContent: undefined,
    noticeContentText: undefined,
    notificationType: "general",
    isAllShow: 1,
    tenantName: undefined,
    orgList: [],
    jurisdictionTenantIds: [],
  };
  proxy.resetForm("formRef");
  imageList.value = [];
}

const cancel = () => {
  open.value = false;
  reset();
}

/** 新增按钮操作 */
const handleAdd = () => {
  formDisabled.value = false;
  reset();
  form.value = {
    ...form.value,
    notificationType: "general",
  };
  open.value = true;
  title.value = "添加公告";
  form.value.tenantId = queryParams.value.tenantId
}

/** 修改按钮操作 */
const handleUpdate = (row) => {
  formDisabled.value = false;
  reset();
  selectNoticeByNoticeId({noticeId: row.noticeId}).then(res => {
    if (res.data) {
      form.value = {
        coverUrl:res.data.coverUrl,
        isAllShow:res.data.isAllShow,
        isTop:res.data.isTop,
        jurisdictionTenantIds:res.data.jurisdictionTenantIds,
        noticeContent:res.data.noticeContent,
        noticeContentText:res.data.noticeContentText,
        noticeId:res.data.noticeId,
        noticeStatus: res.data.noticeStatus,
        noticeTitle: res.data.noticeTitle,
        noticeType: res.data.noticeType,
        notificationType: res.data.notificationType,
        orgList: res.data.orgList,
        programaType: res.data.programaType,
        priorityValue: res.data.priorityValue,
        tenantId:res.data.tenantId
      }
      open.value = true;
      title.value = "修改公告";
      form.value.coverUrl && imageList.value.push({url: form.value.coverUrl});
    } else {
      proxy.$modal.msgError("数据异常");
    }
  })
}
const setAuth = () => {
  getTreeAll();
}

/** 提交新增/修改 */
const submitForm = () => {
  proxy.$refs["formRef"].validate(valid => {
    if (valid) {
      if (form.value.noticeId !== undefined) {
        if (form.value.noticeContent) {
          form.value.noticeContentText = form.value.noticeContent.replace(
              /<[^<>]+>/g,
              ""
          );
        }
        updateNotice(form.value).then(res => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        })
      } else {
        //提取富文本框内的纯文本  并赋值给空字符串
        if (form.value.noticeContent) {
          form.value.noticeContentText = form.value.noticeContent.replace(
              /<.*?>/g,
              ""
          );
        }
        addNotice({
          ...form.value,
          programaType: "system_notice",
          noticeStatus: "notice_status_draft"
        }).then(res => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        })
      }
    }
  })
}

/** 详情按钮操作 */
const detail = (row) => {
  reset();
  selectNoticeByNoticeId({noticeId: row.noticeId}).then(res => {
    formDisabled.value = true;
    if (res.data) {
      form.value = res.data;
      open.value = true;
      title.value = '查看公告';
      form.value.coverUrl && imageList.value.push({url: form.value.coverUrl});
    } else {
      proxy.$modal.msgError("数据异常！");
    }
  })
}

/** 上传公告封面按钮操作 */
const imageList = ref([])
const previewImageUrl = ref('')
const previewDialogVisible = ref(false)

const uploadImage = (content) => {
  let formData = new FormData();
  //content.file 	文件file对象
  formData.append("multipartFile", content.file);
  formData.append("appCode", "portal");
  uploadSingle(formData).then((res) => {
    form.value.coverUrl = res.data.url;
    imageList.value = [];
    imageList.value.push({url: res.data.url});
  });
}
const handleRemove = (file, fileList) => {
  form.value.coverUrl = "";
  imageList.value = [];
}
const handlePreview = (file) => {
  previewImageUrl.value = form.value.coverUrl;
  previewDialogVisible.value = true;
}
const handleExceed = (files, fileList) => {
  proxy.$modal.msgWarning("只能上传一张图片,请先移除前一张");
}
const handleSuccess = (res, file, fileList) => {
  console.log("res", res);
}

function getTreeAll() {
  treeAll({
    orgId: userStore.userInfo.orgId,
    tenantId: queryParams.value.tenantId
  }).then(res => {
    allTree.value = res.data;
  })
}

function getJurisTenantList() {
  getJurisdictionTenantList({}).then(res => {
    if (res.data) {
      jurisdictionTenantList.value = res.data;
    }
  })
}

getTreeAll();
getTenantList();
getJurisTenantList();
getList();
</script>
