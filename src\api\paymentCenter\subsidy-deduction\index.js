import request from '@/utils/request'
import { apiUrl } from '@/utils/config'; 



export class screenIndex {


    // 查询补贴补扣列表
    static subsidyDeductionList(data,parasms) {
      return request({
        url: `pay${apiUrl}/pay/subsidy/deduction/list`,
        method: 'post',
        data:{...data,...parasms}
      })
    }

    // 供应商下拉列表
    static providerTree(data) {
      return request({
        url: `pay${apiUrl}/pay/subsidy/deduction/providerTree`,
        method: 'post',
        data
      })
    }

    // 支付场景下拉列表
    static paySceneTree(data) {
      return request({
        url: `pay${apiUrl}/pay/subsidy/deduction/paySceneTree`,
        method: 'post',
        data
      })
    }

    // 查询支付类型下拉列表
    static payTypeTree(data) {
      return request({
        url: `pay${apiUrl}/pay/subsidy/deduction/payTypeTree`,
        method: 'post',
        data
      })
    }

    // 查询价格类型
    static isFactsAmount(data) {
      return request({
        url: `pay${apiUrl}/pay/subsidy/deduction/isFactsAmount`,
        method: 'post',
        data
      })
    }

    // 查询人员
    static selectUserList(data) {
      return request({
        url: `pay${apiUrl}/pay/subsidy/deduction/selectUserList`,
        method: 'post',
        data
      })
    }

    // 查询账户列表
    static accountList(data) {
      return request({
        url: `pay${apiUrl}/pay/subsidy/deduction/accountList`,
        method: 'post',
        data
      })
    }

    // 扣款
    static deductAmount(data) {
      return request({
        url: `pay${apiUrl}/pay/subsidy/deduction/deductAmount`,
        method: 'post',
        data
      })
    }

    // 退款
    static refundAmount(data) {
      return request({
        url: `pay${apiUrl}/pay/subsidy/deduction/refundAmount`,
        method: 'post',
        data
      })
    }
}