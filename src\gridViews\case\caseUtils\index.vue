<template>
  <el-card class="line-card" v-loading="caseUtils" shadow="never" style="border: 0px">
    <div class="content">
      <div class="wordBench">
        <div class="title" @click="goWordBench">
          <h3>工作台快捷入口</h3>
          <p>WORKBENCH ENTRANCE</p>
        </div>
      </div>

      <div class="address">
        <div class="title" @click="goAddress">
          <h3>通讯录</h3>
          <p>ADDRESS LIST</p>
        </div>
      </div>

      <div class="know">
        <div class="title" @click="goKnow">
          <h3>知识库</h3>
          <p>KNOWLEDGE BASE</p>
        </div>
      </div>

      <div class="otherTool">
        <div class="title" @click="goOther">
          <h3>首页生成器</h3>
          <p>OTHER TOOL ENTRANCE</p>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script setup name="CaseUtils">
import {useRouter} from "vue-router";

const caseUtils = ref(false)
const router = useRouter();

const goWordBench = () => {
  // let windowObjectReference = window.open();
  // windowObjectReference.location.href = "http://unifast.com:19555/show/homePage";
  router.push({ path: '/system/index' })
}

const goAddress = () => {
  router.push({path: "addressList"});
}

const goKnow = () => {
  router.push({path: "documentLib"});
}

const goOther = () => {
  // let windowObjectReference = window.open();
  // windowObjectReference.location.href = "http://unifast.com:19555/landing/digital-industry-cloud";
  router.push({ path: '/system/extend/pageDesigner' })
}
</script>

<style scoped lang="less">
.line-card {
  border: 0px;
  :deep(.el-card__body) {
    height: 130px;
  }
  .content {
    padding: 0px;
    width: 100%;
    display: flex;
    .wordBench {
      width: 25%;
      height: 108px;
      border-radius: 0px;
      background-image: url("../../../assets/images/pageDesign/case/caseUtils1.jpg");
      background-size: 100% 100%;
      position: relative;
      cursor: pointer;
      .title {
        position: absolute;
        top: 50%;
        left: 40%;
        transform: translate(-50%, -50%);
        h3 {
          margin: 0;
          font-size: 20px;
          font-family: Microsoft YaHei;
          font-weight: bold;
          color: #ffffff;
        }
        p {
          margin: 10px 0 0 0;
          font-size: 12px;
          font-family: Microsoft YaHei;
          font-weight: 300;
          color: #ffffff;
        }
      }
    }
    .address {
      width: 25%;
      height: 108px;
      border-radius: 0px;
      background-image: url("../../../assets/images/pageDesign/case/caseUtils2.jpg");
      background-size: 100% 100%;
      position: relative;
      cursor: pointer;
      .title {
        position: absolute;
        top: 50%;
        left: 40%;
        transform: translate(-50%, -50%);
        h3 {
          margin: 0;
          font-size: 20px;
          font-family: Microsoft YaHei;
          font-weight: bold;
          color: #ffffff;
        }
        p {
          margin: 10px 0 0 0;
          font-size: 12px;
          font-family: Microsoft YaHei;
          font-weight: 300;
          color: #ffffff;
        }
      }
    }
    .know {
      width: 25%;
      height: 108px;
      border-radius: 0px;
      background-image: url("../../../assets/images/pageDesign/case/caseUtils3.jpg");
      background-size: 100% 100%;
      position: relative;
      cursor: pointer;
      .title {
        position: absolute;
        top: 50%;
        left: 40%;
        transform: translate(-50%, -50%);
        h3 {
          margin: 0;
          font-size: 20px;
          font-family: Microsoft YaHei;
          font-weight: bold;
          color: #ffffff;
        }
        p {
          margin: 10px 0 0 0;
          font-size: 12px;
          font-family: Microsoft YaHei;
          font-weight: 300;
          color: #ffffff;
        }
      }
    }
    .otherTool {
      width: 25%;
      height: 108px;
      border-radius: 0px;
      background-image: url("../../../assets/images/pageDesign/case/caseUtils4.jpg");
      background-size: 100% 100%;
      position: relative;
      cursor: pointer;
      .title {
        position: absolute;
        top: 50%;
        left: 40%;
        transform: translate(-50%, -50%);
        h3 {
          margin: 0;
          font-size: 20px;
          font-family: Microsoft YaHei;
          font-weight: bold;
          color: #ffffff;
        }
        p {
          margin: 10px 0 0 0;
          font-size: 12px;
          font-family: Microsoft YaHei;
          font-weight: 300;
          color: #ffffff;
        }
      }
    }
  }
}
</style>
