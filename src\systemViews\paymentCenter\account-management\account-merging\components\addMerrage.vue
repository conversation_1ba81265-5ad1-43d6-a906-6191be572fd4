<!-- 新增合并关系 -->

<template>
  <div
    class="dialog-box"
    :class="popupType === 'view' ? 'dialog-box-view' : 'dialog-box-edit'"
    style="height: 600px"
  >
    <el-row style="height: 100%">
      <Splitpanes class="default-theme">
        <Pane :size="18" :min-size="30">
          <el-card class="dep-card" style="height: 100%">
            <el-scrollbar style="height: 100%">
              <div style="font-size: 20px; color: #282828">请选择账户:</div>
              <el-form ref="form" label-width="40px">
                <el-form-item label="租户" prop="tenantId">
                  <TenantSelect v-model="queryTreeParams.tenantId" @change="handleChange"></TenantSelect>
                </el-form-item>
                <el-form-item>
                  <el-tree
                  :class="{ 'disabled-tree': popupType === 'edit' }"  
                    style="margin: 20px 0px;width: 100%;"
                    :check-strictly="true"
                    :data="areaOptions"
                    show-checkbox
                    default-expand-all
                    node-key="id"
                    ref="areaTree"
                    highlight-current
                    :props="treeProps"
                    @check="handleTreeCheck"
                  />
                </el-form-item>
              </el-form>
            </el-scrollbar>
          </el-card>
        </Pane>
        <Pane :size="82" :min-size="95">
          <el-card class="dep-card">
            <dialog-search
              @getList="getList"
              formRowNumber="2"
              :columns="tabelForm.columns"
            >
              <template v-slot:formList>
                <el-form
                  :model="queryParams"
                  ref="queryForm"
                  :inline="true"
                  label-width="75px"
                >
                  <el-form-item label="账号名称">
                    <el-input
                      v-model="queryParams.accountName"
                      placeholder="请输入账号名称"
                      clearable
                    ></el-input>
                  </el-form-item>
                  <el-form-item label="账号编码">
                    <el-input
                      v-model="queryParams.accountCode"
                      placeholder="请输入账号编码"
                      clearable
                    ></el-input>
                  </el-form-item>
                </el-form>
              </template>
              <template v-slot:searchList>
                <el-button type="primary" icon="Search" @click="handleQuery"
                  >搜索</el-button
                >
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              </template>
            </dialog-search>

            <public-table
              ref="publictable"
              :rowKey="tabelForm.tableKey"
              @handleSelectionChange="handleSelectionChange"
              :tableData="list"
              :columns="tabelForm.columns"
              :configFlag="tabelForm.tableConfig"
              :getList="getList"
            >
            </public-table>
          </el-card>
        </Pane>
      </Splitpanes>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, watch, getCurrentInstance, defineProps } from "vue";

import { ElMessage } from "element-plus";
import { screenIndex } from "@/api/paymentCenter/account-management/account-merging/index";
import useUserStore from "@/store/modules/user";
// 定义组件 props

const props = defineProps({
  closeBtn: {
    type: Function,

    default: null,
  },

  popupType: {
    type: String,

    default: "",
  },

  rowData: {
    type: Object,
    default: () => ({}),
  },
});
// 定义 emits
const emit = defineEmits(["closeBtn"]);
// 字典
const { proxy } = getCurrentInstance();
// const {  } = proxy.useDict("");
const userStore = useUserStore();
// 树形结构
const publictable = ref()
const accountTypeList = ref([])
const areaOptions = ref([]);
const areaTree = ref(null);
const treeProps = reactive({
  multiple: false,
  emitPath: false,
  value: "id",
  label: "accountName",
});

const selectedList = ref([]);
const queryParams = ref({
  id:'',
  tenantId: userStore.userInfo.tenantId,
});
const queryTreeParams = ref({
  tenantId: userStore.userInfo.tenantId,
});
const list = ref([]);
const total = ref(0);
const tabelForm = reactive({
  tableKey: "1", //表格key值
  isShowRightToolbar: true, //是否显示右侧显示和隐藏
  showSearch: true,
  // 表格表格数据
  columns: [
    {
      fieldIndex: "accountName", // 对应列内容的字段名
      label: "账号名称", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "120", //最小宽度%
      width: "", //宽度
      align: "left", //表格对齐方式
    },
    {
      fieldIndex: "accountCode", // 对应列内容的字段名
      label: "账号编码", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "120", //最小宽度%
      width: "", //宽度
      align: "", //表格对齐方式
    },
    {
      fieldIndex: "typeName", // 对应列内容的字段名
      label: "账号类型", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "120", //最小宽度%
      width: "", //宽度
      align: "", //表格对齐方式
  
    },
    {
      fieldIndex: "typeCode", // 对应列内容的字段名
      label: "类型编码", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "120", //最小宽度%
      width: "", //宽度
      align: "", //表格对齐方式
    },
  ],
  // 表格基础配置项，看个人需求添加
  tableConfig: {
    needPage: false, // 是否需要分页
    index: true, // 是否需要序号
    selection: true, // 是否需要多选
    reserveSelection: false, // 是否需要支持跨页选择
    indexFixed: false, // 序号列固定 left true right
    selectionFixed: false, //多选列固定left true right
    indexWidth: "50", //序号列宽度
    loading: false, //表格loading
    showSummary: false, //是否开启合计
    height: "459px", //表格固定高度 比如：300px
  },
});
const handleSelectionChange = (values)=>{
  selectedList.value = values
}
// 账户类型
const payAccountTypePageList = async () => {
  const res = await screenIndex.payAccountTypePageList({  });

  accountTypeList.value = res.data.records.map(item => ({
      value: item.typeCode,    // 对应 typeCode
      label: item.typeName,    // 对应 typeName
      // 保留原始数据（根据需要可选）
      ...item
    }));
    tabelForm.columns[2].dictList =  accountTypeList.value

};
/** 账户树 */
const PayAccountManageList = () => {
  screenIndex.PayAccountManageList(queryTreeParams.value).then((response) => {
    areaOptions.value = response.data;
      // 确保树数据加载完成后设置默认选中
      nextTick(() => {
      if (props.rowData.accountManageId) {
        areaTree.value.setCheckedKeys([props.rowData.accountManageId]);
        queryParams.value.id = props.rowData.accountManageId;
        getList(); // 触发右侧数据加载
      }
    });
  });
};

// 租户切换
const handleChange = () =>{
  queryParams.value.tenantId = queryTreeParams.value.tenantId;
  PayAccountManageList();
}

// 树节点选择事件处理
const handleTreeCheck = (currentNode, checkedState) => {
  // 获取当前选中节点
  const checkedKeys = checkedState.checkedKeys
  
  // 实现单选逻辑
  if (checkedKeys.length > 1) {
    const lastKey = currentNode.id;//checkedKeys[checkedKeys.length - 1]
    areaTree.value.setCheckedKeys([lastKey])
    queryParams.value.id = lastKey
  } else if (checkedKeys.length === 1) {
    queryParams.value.id = checkedKeys[0]
  } else {
    queryParams.value.id = null
  }
  
  // 当选中节点时刷新表格
  if (queryParams.value.id) {
    getList()
  }
}
/** 查询列表 */
const getList = () => {
  tabelForm.tableConfig.loading = true;
// 使用三元运算符选择接口
const apiCall = props.popupType === 'edit' 
    ? screenIndex.payAccountConsolidationFindById(queryParams.value)
    : screenIndex.mergeAbleList(queryParams.value);
    apiCall.then((response) => {
    list.value = response.data
    tabelForm.tableConfig.loading = false;

      // 设置表格默认选中
      nextTick(() => {
      list.value.forEach(row => {
        if (row.mark == '1') {
          publictable.value.selected([row]);
        }
      });
    });
  });
};

/** 搜索按钮操作 */
const handleQuery = () => {
  getList();
};
/** 重置按钮操作 */
const resetQuery = () => {
  proxy.resetForm("queryForm");
  queryParams.value.accountCode = '';
  queryParams.value.accountName = '';
  handleQuery();
};

// 修改保存方法
const saveForm = async () => {
  try {
    // 校验左侧树选择
    if (!queryParams.value.id) {
      ElMessage.error("请先选择左侧账户节点")
      return
    }
    
    // 校验右侧表格选择
    if (selectedList.value.length === 0) {
      ElMessage.error("请选择要合并的账号")
      return
    }

    // 构造符合接口要求的参数
    const requestData = {
      accountManageId: queryParams.value.id, // 左侧选中主账户ID
      mergeAccountIds: selectedList.value.map(item => ({
        mergeAccountId: item.id // 右侧选中的子账户ID
      }))
    }

    if (props.popupType === "edit") {
      const res = await screenIndex.update(requestData);

      if (res.success) {
        ElMessage.success("合并成功");

        emit("closeBtn");
      }
    } else if (props.popupType === "add") {
      const res = await screenIndex.insert(requestData);

      if (res.success) {
        ElMessage.success("修改成功");

        emit("closeBtn");
      }
    }

   } catch (error) {
    console.error("合并操作失败:", error)
    // ElMessage.error("合并失败，请检查数据")
  }
}
// 初始化数据

onMounted(() => {
  PayAccountManageList();
  payAccountTypePageList()
});

defineExpose({
  saveForm,
});
</script>

<style scoped lang="scss">
/* 修改模式下禁用树交互 */
.disabled-tree {
  pointer-events: none;   /* 禁用所有鼠标事件 */
  opacity: 0.9;          /* 可选：降低透明度表示禁用状态 */
}

/* 如果需要单独禁用复选框（保留展开功能） */
.disabled-tree :deep(.el-checkbox) {
  pointer-events: none;
  cursor: not-allowed;
}
</style>
