import request from '@/utils/request'
import { apiUrl } from '@/utils/config';

export class screenIndex {
  // 查询单位授权列表
  static deptAccreditList(data, params) {
    return request({
      url: `wisdomaccess${apiUrl}/admittance/accredit/deptAccreditList`,
      method: "post",
      data: data,
      params: params
    })
  }
  // 查询人员授权列表
  static userAccreditList(data, params) {
    return request({
      url: `wisdomaccess${apiUrl}/admittance/accredit/userAccreditList`,
      method: "post",
      data: data,
      params: params
    })
  }
  // 查询所有人员列表
  static userAllList(data, params) {
    return request({
      url: `wisdomaccess${apiUrl}/admittance/accredit/userAllList`,
      method: "post",
      data: data,
      params: params
    })
  }
  // 根据id删除部门区域权限
  static deleteDeptAreaById(id) {
    return request({
      url: `wisdomaccess${apiUrl}/admittance/accredit/deleteDeptAreaById/${id}`,
      method: 'get',
    })
  }
  // 根据id删除人员区域权限
  static deleteUserAreaById(id) {
    return request({
      url: `wisdomaccess${apiUrl}/admittance/accredit/deleteUserAreaById/${id}`,
      method: 'get',
    })
  }
  // 获取部门级联选择
  static deptTree() {
    return request({
      url: `system${apiUrl}/dept/treeselect`,
      method: 'get',
    })
  }
  // 获取单位树
  static unitTreeSelect(data) {
    return request({
      url: `system${apiUrl}/dept/unitTreeSelect`,
      method: 'post',
      data:data,
    })
  }
  // 获取区域级联选择
  static areaTree() {
    return request({
      url: `wisdomaccess${apiUrl}/admittance/tree/areaTreeSelect`,
      method: 'get',
    })
  }
  // 获取区域级联选择
  static areaTreeSelectByType(data) {
    return request({
      url: `wisdomaccess${apiUrl}/admittance/tree/areaTreeSelectByType/${data}`,
      method: 'get',
    })
  }
  // 根据部门ids查询部门名称
  static selectDeptNames(data) {
    return request({
      url: `wisdomaccess${apiUrl}/admittance/accredit/selectDeptNames`,
      method: 'post',
      data:data,
    })
  }
  // 根据部门ids查询区域名称
  static selectAreaNames(data) {
    return request({
      url: `wisdomaccess${apiUrl}/admittance/accredit/selectAreaNames`,
      method: 'post',
      data:data,
    })
  }
  // 保存部门授权
  static addDeptAccredit(data) {
    return request({
      url: `wisdomaccess${apiUrl}/admittance/accredit/addDeptAccredit`,
      method: 'post',
      data: data
    })
  }
  // 保存人员授权
  static addUserAccredit(data) {
    return request({
      url: `wisdomaccess${apiUrl}/admittance/accredit/addUserAccredit`,
      method: 'post',
      data: data
    })
  }
  // 查询人员(用户)选择列表
  static userList(query) {
    return request({
      url: `system${apiUrl}/user/userList`,
      method: 'get',
      params: query
    })
  }
  // 根据区域id和用户id删除人员区域权限
  static deleteUserArea(data) {
    return request({
      url: `wisdomaccess${apiUrl}/admittance/accredit/deleteUserArea`,
      method: 'post',
      data: data
    })
  }
  // 根据区域id和部门id删除部门区域权限
  static deleteDeptArea(data) {
    return request({
      url: `wisdomaccess${apiUrl}/admittance/accredit/deleteDeptArea`,
      method: 'post',
      data: data
    })
  }
  // 查询区域树
  static areaTreeSelect(data) {
    return request({
      url: `sys${apiUrl}/areaManage/queryLazyAreaTree`,
      method: 'post',
      data: data
    })
  }
  // 根据区域id顺序查找某区域的所有父节点
  static getAreaPath(areaId) {
    return request({
      url: `sys${apiUrl}/areaManage/findFatherPathById/`,
      method: 'get',
      params: areaId
    })
  }
  // 单位区域授权树展示
  static deptAreaTree(data, params) {
    return request({
      url: `admittance${apiUrl}/tree/deptAreaTree`,
      method: "post",
      data: data,
      params: params
    })
  }
  // 人员区域授权树展示
  static userAreaTree(data, params) {
    return request({
      url: `admittance${apiUrl}/tree/userAreaTree`,
      method: "post",
      data: data,
      params: params
    })
  }
  // 所有人员区域展示
  static allUserAreaTree(data, params) {
    return request({
      url: `admittance${apiUrl}/tree/allUserAreaTree`,
      method: "post",
      data: data,
      params: params
    })
  }
  // 查询后端构建区域树
  static getAllAreaTree(params) {
    return request({
      url: `sys${apiUrl}/areaManage/getAllAreaTree`,
      method: 'get',
      params: params
    })
  }
  // 修改到期日期
  static updateExpirationTime(data){
    return request({
      url: `wisdomaccess${apiUrl}/admittance/accredit/updateExpirationTime`,
      method: 'post',
      data: data
    })
  }
}

// //查询单位授权列表
// export function deptAccreditList(data, params) {
//   return request({
//     url: `wisdomaccess${apiUrl}/admittance/accredit/deptAccreditList`,
//     method: "post",
//     data: data,
//     params: params
//   })
// }l
// //查询人员授权列表
// export function userAccreditList(data, params) {
//   return request({
//     url: `wisdomaccess${apiUrl}/admittance/accredit/userAccreditList`,
//     method: "post",
//     data: data,
//     params: params
//   })
// }
// //查询所有人员列表
// export function userAllList(data, params) {
//   return request({
//     url: `wisdomaccess${apiUrl}/admittance/accredit/userAllList`,
//     method: "post",
//     data: data,
//     params: params
//   })
// }
// //根据id删除部门区域权限
// export function deleteDeptAreaById(id) {
//   return request({
//     url: `wisdomaccess${apiUrl}/admittance/accredit/deleteDeptAreaById/${id}`,
//     method: 'get',
//   })
// }
// //根据id删除人员区域权限
// export function deleteUserAreaById(id) {
//   return request({
//     url: `wisdomaccess${apiUrl}/admittance/accredit/deleteUserAreaById/${id}`,
//     method: 'get',
//   })
// }
// //获取部门级联选择
// export function deptTree() {
//   return request({
//     url: `system${apiUrl}/dept/treeselect`,
//     method: 'get',
//   })
// }
// //获取单位树
// export function unitTreeSelect(data) {
//   return request({
//     url: `system${apiUrl}/dept/unitTreeSelect`,
//     method: 'post',
//     data:data,
//   })
// }
// //获取区域级联选择
// export function areaTree() {
//   return request({
//     url: `wisdomaccess${apiUrl}/admittance/tree/areaTreeSelect`,
//     method: 'get',
//   })
// }
// //获取区域级联选择
// export function areaTreeSelectByType(data) {
//   return request({
//     url: `wisdomaccess${apiUrl}/admittance/tree/areaTreeSelectByType/${data}`,
//     method: 'get',
//   })
// }
// //根据部门ids查询部门名称
// export function selectDeptNames(data) {
//   return request({
//     url: `wisdomaccess${apiUrl}/admittance/accredit/selectDeptNames`,
//     method: 'post',
//     data:data,
//   })
// }
// //根据部门ids查询区域名称
// export function selectAreaNames(data) {
//   return request({
//     url: `wisdomaccess${apiUrl}/admittance/accredit/selectAreaNames`,
//     method: 'post',
//     data:data,
//   })
// }
// //保存部门授权
// export function addDeptAccredit(data) {
//   return request({
//     url: `wisdomaccess${apiUrl}/admittance/accredit/addDeptAccredit`,
//     method: 'post',
//     data: data
//   })
// }
// //保存人员授权
// export function addUserAccredit(data) {
//   return request({
//     url: `wisdomaccess${apiUrl}/admittance/accredit/addUserAccredit`,
//     method: 'post',
//     data: data
//   })
// }
// //查询人员(用户)选择列表
// export function userList(query) {
//   return request({
//     url: `system${apiUrl}/user/userList`,
//     method: 'get',
//     params: query
//   })
// }
// //根据区域id和用户id删除人员区域权限
// export function deleteUserArea(data) {
//   return request({
//     url: `wisdomaccess${apiUrl}/admittance/accredit/deleteUserArea`,
//     method: 'post',
//     data: data
//   })
// }
// //根据区域id和部门id删除部门区域权限
// export function deleteDeptArea(data) {
//   return request({
//     url: `wisdomaccess${apiUrl}/admittance/accredit/deleteDeptArea`,
//     method: 'post',
//     data: data
//   })
// }
// //查询区域树
// export function areaTreeSelect(data) {
//   return request({
//     url: `sys${apiUrl}/areaManage/queryLazyAreaTree`,
//     method: 'post',
//     data: data
//   })
// }

// //根据区域id顺序查找某区域的所有父节点
// export function getAreaPath(areaId) {
//   return request({
//     url: `sys${apiUrl}/areaManage/findFatherPathById/`,
//     method: 'get',
//     params: areaId
//   })
// }
// //单位区域授权树展示
// export function deptAreaTree(data, params) {
//   return request({
//     url: `admittance${apiUrl}/tree/deptAreaTree`,
//     method: "post",
//     data: data,
//     params: params
//   })
// }
// //人员区域授权树展示
// export function userAreaTree(data, params) {
//   return request({
//     url: `admittance${apiUrl}/tree/userAreaTree`,
//     method: "post",
//     data: data,
//     params: params
//   })
// }
// //所有人员区域展示
// export function allUserAreaTree(data, params) {
//   return request({
//     url: `admittance${apiUrl}/tree/allUserAreaTree`,
//     method: "post",
//     data: data,
//     params: params
//   })
// }
// //查询后端构建区域树
// export function getAllAreaTree(params) {
//   return request({
//     url: `sys${apiUrl}/areaManage/getAllAreaTree`,
//     method: 'get',
//     params: params
//   })
// }
// //修改到期日期
// export function updateExpirationTime(data){
//   return request({
//     url: `wisdomaccess${apiUrl}/admittance/accredit/updateExpirationTime`,
//     method: 'post',
//     data: data
//   })
// }
