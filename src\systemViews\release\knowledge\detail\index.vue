<template>
  <div class="app-container">
    <Splitpanes class="default-theme">
      <!-- 左侧树结构 -->
      <Pane :size="30" :min-size="10" :max-size="90">
        <el-card class="dep-card" shadow="never" style="height: 100%" v-model="treeLoading" :key="currentDocumentLibId">
          <template #header>
            <div class="card-header">
              <span>{{ currentDocumentLibName }}</span>
              <el-button type="primary" style="float: right; padding: 3px 0" link icon="Home" @click="goBack">返回</el-button>
              <el-button type="primary" style="float: right; padding: 3px 0" link icon="Refresh" @click="reloadTree">刷新</el-button>
            </div>
          </template>
          <el-tabs v-model="activeName" type="card"  @tab-click="handleTabClick">
            <el-tab-pane label="页面" name="publish"></el-tab-pane>
            <el-tab-pane label="草稿" name="draft"></el-tab-pane>
          </el-tabs>
          <el-tree :props="{label:'name',children:'children',isLeaf:'leaf'}"
                   :load="loadNode" lazy default-expand-all :expand-on-click-node="false"
                   ref="asyncTree" @node-click="handleNodeClick" node-key="id" highlight-current>
            <template #default="{ node, data }">
              <span class="custom-tree-node" >
                <el-tooltip :content="node.label" placement="top">
                  <span class="label">{{ node.label }}</span>
                </el-tooltip>
                <span>
                  <el-button link type="primary" size="small" v-if="isPublish === '1'"
                             @click="append(data)">新增</el-button>
                  <el-button link type="primary" size="small" @click="remove(node,data)"
                      v-if="data.parent !== '0' &&data.createBy === currentLoginUser.staffId">删除</el-button>
                </span>
              </span>
            </template>
          </el-tree>
        </el-card>
      </Pane>
      <!-- 右侧内容 -->
      <Pane :size="70" :min-size="10">
        <el-card class="dep-card" shadow="never" style="height: 100%">
          <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
              <el-button type="primary" plain icon="Edit" @click="handleEdit"
                         v-if="currentDocCreateBy === currentLoginUser.staffId">编辑
              </el-button>
            </el-col>
          </el-row>
          <v-md-preview :text="viewContent" height="400px"></v-md-preview>
          <div v-if="docInfo.createByName && docInfo.createDate" class="styx-entity-creator">
            <span class="creator">
              <div class="avatar-name ng-star-inserted">{{ docInfo.createByName }} 创建于 {{ docInfo.createDate }}</div>
            </span>
            <span class="divide">
              <svg viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg" fit height="1em" width="1em"
                  preserveAspectRatio="xMidYMid meet" focusable="false">
                <g id="hwnormal/eye" stroke-width="1" fill-rule="evenodd">
                  <path
                      d="M8 3.4c2.823 0 5.04 1.367 7.38 3.637a2.037 2.037 0 0 1 0 2.925C13.04 12.232 10.824 13.6 8 13.6c-2.823 0-5.032-1.364-7.376-3.637a2.037 2.037 0 0 1 0-2.925C2.967 4.764 5.176 3.4 8 3.4zm0 1.2c-2.417 0-4.405 1.228-6.542 3.3a.837.837 0 0 0 0 1.2C3.596 11.173 5.584 12.4 8 12.4s4.413-1.231 6.546-3.3a.837.837 0 0 0 0-1.2C12.412 5.83 10.415 4.6 8 4.6zM8 11a2.5 2.5 0 1 1 0-5 2.5 2.5 0 0 1 0 5zm0-1.2a1.3 1.3 0 1 0 0-2.6 1.3 1.3 0 0 0 0 2.6z"
                      id="hw形状结合"
                  />
                </g>
              </svg>
              {{ docInfo.counter }}
            </span>
          </div>
        </el-card>
      </Pane>
    </Splitpanes>

    <!-- 文档编辑 -->
    <el-drawer title="添加文章" v-model="visible" direction="btt" size="100%" :with-header="false">
      <el-card class="rich-text-main" shadow="never">
        <div>
          <el-row :gutter="20" type="flex" align="middle" style="height: 40px">
            <el-col :span="6" :offset="8">
              <el-input v-model="docInfo.name" placeholder="请输入标题(最多50个字)"
                        maxlength="50" style="width:520px" :disabled="useDisabled"
              />
            </el-col>
            <el-col :span="6" :offset="4">
              <el-button style="float: right; padding: 3px 0" link type="primary" @click="cancel">取消</el-button>
              <el-button style="float: right; padding: 3px 0; margin-right: 10px" link type="primary" @click="draftForm">保存草稿</el-button>
              <el-button style="float: right; padding: 3px 0; margin-right: 10px" link type="primary" @click="submitForm">发布</el-button>
            </el-col>
          </el-row>
        </div>
        <v-md-editor v-model="editorContent" />
      </el-card>
    </el-drawer>
  </div>
</template>

<script setup name="KnowledgeDetail">
import {useRoute} from 'vue-router';
import {
  createDocuments,
  deleteDocuments,
  documentsTree,
  getDocumentInfoById,
  updateDocument
} from "@/api/release/knowledge";
import useUserStore from "@/store/modules/user";

const {proxy} = getCurrentInstance();
const route = useRoute();
const userStore = useUserStore();
const currentLoginUser = computed(() => {
  return userStore.$state.userInfo
})

const currentDocumentLibName = ref(""); // 知识库名称
const currentDocumentLibId = ref(""); // 知识库id
// 获取知识库信息
watch(route, (newRoute, oldRoute) => {
  if (route.fullPath.includes('/system/notice/knowledgeDetail')) {
    if (route.query.documentLibId) {
      currentDocumentLibName.value = route.query.documentLibName;
      currentDocumentLibId.value = route.query.documentLibId;
    }
  }
}, {immediate: true});

const treeLoading = ref(false);
const isPublish = ref("1"); // 默认展示已发布文档，0代表草稿
const isAppend = ref(false)
const activeName = ref("publish");
const viewContent = ref("加载中，请稍后...")
const currentDocId = ref("0") // 当前选中文档id
const currentDocCreateBy = ref("0") // 当前选中的文档创建者id
const selectNodeName = ref("主页")
const selectNode = ref(undefined)
const visible = ref(false)
const docInfo = ref({})
const useDisabled = ref(false)
const editorContent = ref("加载中，请稍后...")

// 切换标签
const handleTabClick = (tab, event) => {
  if (tab.props.name === "draft") {
    isPublish.value = "0";
  }
  if (tab.props.name === "publish") {
    isPublish.value = "1";
  }
  currentDocCreateBy.value = "";
  viewContent.value = "";
  reset();
  reloadTree();
}

// 刷新加载树形结构
const reloadTree = () => {
  proxy.$refs.asyncTree.root.loaded = false;
  proxy.$refs.asyncTree.root.expand();
}

// 懒加载树形结构
const loadNode = (node, resolve) => {
  treeLoading.value = true;
  let params = {}
  if (node.level === 0) {
    params = { id: 0, documentLibId: currentDocumentLibId.value, type: "current", publish: isPublish.value }
  } else {
    params = { id: node.data.id, documentLibId: currentDocumentLibId.value, type: "down", publish: isPublish.value }
  }
  documentsTree(params).then(res => {
    if (res.data.length > 0) {
      if (isPublish === "0") {
        viewContent.value = res.data[0].draftContent;
      }
      if (res.data[0].parent === "0") {
        viewContent.value = res.data[0].content;
        currentDocId.value = res.data[0].id;
        docInfo.value = res.data[0];
        currentDocCreateBy.value = res.data[0].createBy;
      }
    }
    resolve(res.data)
    treeLoading.value = false;
  })
}

// 节点点击事件
const handleNodeClick = (data) => {
  selectNode.value = data;
  selectNodeName.value = data.name;
  currentDocId.value = data.id;
  currentDocCreateBy.value = data.createBy;
  if (isAppend.value) {
    visible.value = true;
  } else {
    getDocumentInfoById({
      id: currentDocId.value,
      documentLibId: currentDocumentLibId.value,
      publish: isPublish.value
    }).then(res => {
      viewContent.value = isPublish.value === "0" ? res.data.draftContent : res.data.content;
      docInfo.value = res.data;
    })
  }
}

// 删除按钮操作
const remove = (node, data) => {
  if (data.parent === "0") {
    proxy.$modal.msgError("根节点文档无法删除")
  } else {
    if (node.childNodes !== undefined && node.childNodes.length > 0) {
      proxy.$modal.msgError("当前文档存在下级文档，不允许删除")
      return;
    }
    deleteDocuments({id: data.id, documentLibId: data.documentLibId, publish: isPublish.value}).then(res => {
      if (res.success) {
        proxy.$modal.confirm("是否确认删除名称为（" + data.name + "）的知识库?").then(() => {
          setTimeout(() => {
            proxy.$modal.msgSuccess("删除成功");
            reset();
            reloadTree();
            viewContent.value = "";
            currentDocCreateBy.value = "";
          }, 700);
        })
      } else {
        proxy.$modal.msgError("根节点文档无法删除")
      }
    })
  }
}

// 表单重置
function reset() {
  docInfo.value = {
    name: undefined,
    parent: undefined,
    content: undefined,
    contentText: undefined,
    publish: undefined,
    sort: undefined,
  }
  isAppend.value = false;
}

// 新增按钮操作
const append = (data) => {
  docInfo.value = {
    id: undefined,
    name: "",
    parent: data.id,
    content: undefined,
    contentText: undefined,
    publish: undefined,
    sort: undefined,
  }
  editorContent.value = "";
  isAppend.value = true;
  useDisabled.value = false;
}

// 编辑按钮操作
const handleEdit = () => {
  console.log('doc1',docInfo.value)
  if (docInfo.value.draftContent !== null && docInfo.value.publish === "0") {
    useDisabled.value = false;
    editorContent.value = docInfo.value.draftContent;
  } else if (docInfo.value.publish === "1" && docInfo.value.content !== null ) {
    useDisabled.value = true;
    editorContent.value = docInfo.value.content;
  }
  visible.value = true;
}

// 发布按钮操作
const submitForm = () => {
  isPublish.value = "1";
  realSubmitCheck();
}

// 保存草稿
const draftForm = () => {
  isPublish.value = "0";
  if (docInfo.value.publish === "1") {
    proxy.$modal.msgError("已发布的内容不允许保存为草稿")
    return;
  }
  realSubmitCheck();
}

function realSubmitCheck() {
  if (docInfo.value.name === "") {
    proxy.$modal.msgError("文档标题不能为空！");
    return;
  }
  realSubmit();
}

function realSubmit() {
  if (docInfo.value.id !== undefined) {
    docInfo.value.content = editorContent.value;
    docInfo.value.contentText = docInfo.value.content.replace(/<.*?>/g, "");
    const request = {
      id: docInfo.value.id,
      name: docInfo.value.name,
      parent: docInfo.value.parent,
      content: docInfo.value.content,
      contentText: docInfo.value.contentText,
      publish: docInfo.value.publish,
      sort: docInfo.value.sort,
      counter: docInfo.value.counter,
      documentLibId: docInfo.value.documentLibId,
      attachmentList: docInfo.value.attachmentList,
    }
    updateDocument(request).then(res => {
      proxy.$modal.msgSuccess("修改成功");
      getDocumentInfoById({
        id: currentDocId.value,
        documentLibId: currentDocumentLibId.value,
        publish: isPublish.value,
      }).then(res => {
        viewContent.value = isPublish.value === "0" ? res.data.draftContent : res.data.content;
        docInfo.value = res.data;
      })
    })
  } else {
    //提取富文本框内的纯文本  并赋值给空字符串
    docInfo.value.content = editorContent.value;
    docInfo.value.contentText = docInfo.value.content.replace(/<.*?>/g, "");
    createDocuments({
      ...docInfo.value,
      parent: currentDocId.value,
      documentLibId: currentDocumentLibId.value,
      attachmentList: [],
      publish: isPublish.value,
    }).then(res => {
      if (res.code === "1") {
        proxy.$modal.msgSuccess("新增成功");
        visible.value = false;
        isAppend.value = false;
      }
    })
  }
  setTimeout(() => {
    if (isPublish.value === "1") {
      activeName.value = "publish";
    }
    if (isPublish.value === "0") {
      activeName.value = "draft";
    }
    cancel();
    reloadTree();
  }, 700);
}

// 取消
const cancel = () => {
  isAppend.value = false;
  visible.value = false;
}

// 返回按钮操作
const goBack = () => {
  window.history.back();
}

</script>

<style scoped>
.dep-card {
  min-height: calc(100vh - 120px);
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
  .label {
    display: block;
    width: 150px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}

.styx-entity-creator {
  display: flex;
  align-items: center;
  color: #aaa;
  font-size: 0.75rem;
  padding-bottom: 20px;
}

.creator {
  display: flex;
  align-items: center;
}

.divide {
  margin-left: 30px;
  position: relative;
}
</style>
