<template>
  <el-card :class="bgColor === true? 'notice-buisness-card': 'unshow-notice-buisness-card'" v-loading="loading" shadow="never">
    <template #header>
      <span class="card-title">
        <el-menu
            :default-active="activeIndex"
            :class="bgColor === true ?'el-menu-demo':'unshow-el-menu-demo'"
            mode="horizontal"
            @select="handleSelect"
            style="width: 100%;"
        >
          <el-menu-item index="system_notice">
            <el-icon class="icon iconfont icon-gonggao"></el-icon>通知公告</el-menu-item>
          <el-menu-item index="business_news">
            <el-icon class="icon iconfont icon-yewudongtai"></el-icon>新闻动态</el-menu-item>
        </el-menu>
        <el-button class="exec" link type="primary" @click="moreExt()">更多<el-icon><DArrowRight /></el-icon></el-button>
      </span>
    </template>
    <div class="notice-buisness-body">
      <el-carousel v-if="notice.length > 0" height="200px" style="padding-bottom: 10px">
        <el-carousel-item v-for="item in notice" :key="item.id">
          <router-link :to="portalIndex+`/detail/${item.programaType}/${item.noticeId}`" target="_blank">
            <el-image style="width: 100%; height: 100%" :src="item.coverUrl"></el-image>
          </router-link>
        </el-carousel-item>
      </el-carousel>
      <div style="overflow-y: auto; height: calc(100% - 200px)">
        <router-link v-for="item in notice" :key="item.id" class="notice-buisness"
            :to="portalIndex+`/detail/${item.programaType}/${item.noticeId}`" target="_blank"
        >
          <div class="notice-title" :title="item.noticeTitle">「<span style="color: red">{{ item.noticeTypeName }}</span>」{{ item.noticeTitle }}</div>
          <div class="notice-time">{{ parseTime(item.createDate, "{y}-{m}-{d}") }}</div>
        </router-link>
      </div>
      <el-empty style="padding: 20px 0" v-if="notice.length <= 0" :image-size="50" :description="text">
        <el-link icon="Refresh" @click="getNotice(activeIndex)" :underline="false" type="primary">刷新</el-link>
      </el-empty>
    </div>
  </el-card>
</template>

<script setup name="UseNoticeAndBusiness">
import {selectTop5Page} from "@/api/release/notice";
import {useRouter} from "vue-router";

const {proxy} = getCurrentInstance();
const portalIndex = proxy.portalIndex;
const router = useRouter();
const props = defineProps({
  showTitle: Boolean,
  bgColor: Boolean
})

const loading = ref(false);
const activeIndex = ref("system_notice");
const notice = ref([]);
const text = ref("暂无通知公告");

const handleSelect = (e) => {
  activeIndex.value = e;
  getNotice(e);
}

// 更多按钮操作
const moreExt = () => {
  router.push(`/portal/list/${this.activeIndex}`);
}

const getNotice = (activeIndex) => {
  loading.value = true;
  selectTop5Page({programaType: activeIndex}).then((res) => {
    notice.value = res.data.records;
    loading.value = false;
    if (activeIndex.value === "system_notice" && notice.value.length >= 0) {
      text.value = "暂无通知公告";
    } else if (activeIndex.value === "business_news" && notice.value.length >= 0) {
      text.value = "暂无新闻动态";
    }
  });
}

getNotice(activeIndex.value);
</script>

<style scoped lang="scss">
.notice-buisness-card {
  height: 100%;
  background: #fff;
  overflow: auto;
  :deep(.el-card__header) {
    padding: 0px;
    border-bottom: 0px solid #f5f9fa;
  }
  :deep(.el-menu--horizontal > .el-menu-item) {
    height: 58px;
  }
  :deep(.el-card__body) {
    padding: 10px 20px 20px 20px;
  }
  .card-title {
    display: flex;
    .icon {
      font-size: 18px;
      color: #419eee;
      margin-right: 5px;
      -webkit-transition: font-size 0.25s linear, width 0.25s linear;
      -moz-transition: font-size 0.25s linear, width 0.25s linear;
      transition: font-size 0.25s linear, width 0.25s linear;
    }
    .exec {
      padding: 3px 20px;
      border-bottom: 1px solid #f5f9fa;
      border-radius: 0px;
    }
  }
  .notice-buisness-body {
    height: 100%;
    .notice-buisness {
      display: flex;
      justify-content: space-between;
      flex-direction: row;
      font-size: 14px;
      padding: 4px 0;
      .notice-title {
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-all;
        white-space: nowrap;
        flex-basis: 70%;
      }
      .notice-time {
        flex-basis: 20%;
        text-align: right;
      }
    }
  }
}
.unshow-notice-buisness-card {
  height: 100%;
  border: 0px;
  background: #fff;
  :deep(.el-card__header) {
    padding: 0px;
    border-bottom: 0px solid #f5f9fa;
  }
  :deep(.el-menu--horizontal > .el-menu-item) {
    height: 58px;
  }
  :deep(.el-card__body) {
    padding: 10px 20px 20px 20px;
  }
  .el-menu-demo {
    background: #fff;
  }
  .unshow-el-menu-demo {
    background: #fff;
  }
  .card-title {
    display: flex;
    .icon {
      font-size: 18px;
      color: #419eee;
      margin-right: 5px;
      -webkit-transition: font-size 0.25s linear, width 0.25s linear;
      -moz-transition: font-size 0.25s linear, width 0.25s linear;
      transition: font-size 0.25s linear, width 0.25s linear;
    }
    .exec {
      padding: 3px 20px;
      border-bottom: 1px solid #f5f9fa;
      border-radius: 0px;
    }
  }
  .notice-buisness-body {
    height: 100%;
    .notice-buisness {
      display: flex;
      justify-content: space-between;
      flex-direction: row;
      font-size: 14px;
      padding: 4px 0;
      .notice-title {
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-all;
        white-space: nowrap;
        flex-basis: 70%;
      }
      .notice-time {
        flex-basis: 20%;
        text-align: right;
      }
    }
  }
}
</style>
