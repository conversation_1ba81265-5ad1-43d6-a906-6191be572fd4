import request from '@/utils/request'
import { apiUrl } from '@/utils/config'; 
//查询停车场列表
export function findparkinglot(data) {
  return request({
    url: `park${apiUrl}/parking/accredit/findparkinglot`,
    method: "post",
    data: data
  })
}

//查询车辆类型授权列表
export function typeAccreditList(data, params) {
  return request({
    url: `park${apiUrl}/parking/accredit/typeAccreditList`,
    method: "post",
    data: {...data,...params},
    params: params
  })
}
//保存车辆类型授权
export function addTypeAccredit(data) {
  return request({
    url: `park${apiUrl}/parking/accredit/addTypeAccredit`,
    method: 'post',
    data: data
  })
}
//修改车辆类型授权过期日期
export function updateTypeAccredit(data) {
  return request({
    url: `park${apiUrl}/parking/accredit/updateTypeAccredit`,
    method: 'post',
    data: data
  })
}
//根据id删除车辆类型授权
export function deleteTypeAccreditById(id) {
  return request({
    url: `park${apiUrl}/parking/accredit/deleteTypeAccreditById/` + id,
    method: 'get',
  })
}
//查询车牌号码授权列表
export function numberAccreditList(data, params) {
  return request({
    url: `park${apiUrl}/parking/accredit/numberAccreditList`,
    method: "post",
    data: {...data,...params},
    params: params
  })
}
//查询车牌列表
export function selectPlantNoList(data) {
  return request({
    url:  `park${apiUrl}/parking/accredit/selectPlantNoList`,
    method: "post",
    data: data,
  })
}
//保存车辆授权
export function addNumberAccredit(data) {
  return request({
    url:  `park${apiUrl}/parking/accredit/addNumberAccredit`,
    method: 'post',
    data: data
  })
}
//修改车牌授权过期日期
export function updateNumberAccredit(data) {
  return request({
    url:   `park${apiUrl}/parking/accredit/updateNumberAccredit`,
    method: 'post',
    data: data
  })
}
//根据id删除人员区域权限
export function deleteNumberAccreditById(id) {
  return request({
    url:  `park${apiUrl}/parking/accredit/deleteNumberAccreditById/` + id,
    method: 'get',
  })
}
//所有类型授权列表
export function allAccreditList(data, params) {
  return request({
    url:  `park${apiUrl}/parking/accredit/allAccreditList`,
    method: "post",
    data: {...data,...params},
    params: params
  })
}
//查询所有人员列表
export function userAllList(data, params) {
  return request({
    url: `park${apiUrl}/admittance/accredit/userAllList`,
    method: "post",
    data: {...data,...params},
    params: params
  })
}
//根据id删除部门区域权限
export function deleteDeptAreaById(id) {
  return request({
    url:  `park${apiUrl}/admittance/accredit/deleteDeptAreaById/` + id,
    method: 'get',
  })
}
//根据id删除人员区域权限
export function deleteUserAreaById(id) {
  return request({
    url:  `park${apiUrl}/admittance/accredit/deleteUserAreaById/` + id,
    method: 'get',
  })
}
//获取部门级联选择
export function deptTree() {
  return request({
    url: '/system/dept/treeselect',
    method: 'get',
  })
}
//获取单位树
export function unitTreeSelect(data) {
  return request({
    url: "/system/dept/unitTreeSelect" ,
    method: 'post',
    data:data,
  })
}
//获取区域级联选择
export function areaTree() {
  return request({
    url:  `park${apiUrl}/admittance/tree/areaTreeSelect`,
    method: 'get',
  })
}
//根据部门ids查询部门名称
export function selectDeptNames(data) {
  return request({
    url:  `park${apiUrl}/admittance/accredit/selectDeptNames` ,
    method: 'post',
    data:data,
  })
}
//根据部门ids查询区域名称
export function selectAreaNames(data) {
  return request({
    url:  `park${apiUrl}/admittance/accredit/selectAreaNames` ,
    method: 'post',
    data:data,
  })
}
//保存部门授权
export function addDeptAccredit(data) {
  return request({
    url:  `park${apiUrl}/admittance/accredit/addDeptAccredit`,
    method: 'post',
    data: data
  })
}
//保存人员授权
export function addUserAccredit(data) {
  return request({
    url:  `park${apiUrl}/admittance/accredit/addUserAccredit`,
    method: 'post',
    data: data
  })
}
//查询人员(用户)选择列表
export function userList(query) {
  return request({
    url: '/system/user/userList',
    method: 'get',
    params: query
  })
}
//根据区域id和用户id删除人员区域权限
export function deleteUserArea(data) {
  return request({
    url:  `park${apiUrl}/admittance/accredit/deleteUserArea`,
    method: 'post',
    data: data
  })
}
//根据区域id和部门id删除部门区域权限
export function deleteDeptArea(data) {
  return request({
    url:  `park${apiUrl}/admittance/accredit/deleteDeptArea`,
    method: 'post',
    data: data
  })
}
//查询区域树
export function areaTreeSelect(data) {
  return request({
    url: '/sys/areaManage/queryLazyAreaTree',
    method: 'post',
    data: data
  })
}
//根据区域id顺序查找某区域的所有父节点
export function getAreaPath(areaId) {
  return request({
    url: '/sys/areaManage/findFatherPathById/',
    method: 'get',
    params: areaId
  })
}
//单位区域授权树展示
export function deptAreaTree(data, params) {
  return request({
    url:  `park${apiUrl}/admittance/tree/deptAreaTree`,
    method: "post",
    data: {...data,...params},
    params: params
  })
}
//人员区域授权树展示
export function userAreaTree(data, params) {
  return request({
    url:  `park${apiUrl}/admittance/tree/userAreaTree`,
    method: "post",
    data: {...data,...params},
    params: params
  })
}
//所有人员区域展示
export function allUserAreaTree(data, params) {
  return request({
    url:  `park${apiUrl}/admittance/tree/allUserAreaTree`,
    method: "post",
    data: {...data,...params},
    params: params
  })
}
//查询后端构建区域树
export function getAllAreaTree(params) {
  return request({
    url: '/sys/areaManage/getAllAreaTree',
    method: 'get',
    params: params
  })
}
//修改到期日期
export function updateExpirationTime(data){
  return request({
    url:  `park${apiUrl}/admittance/accredit/updateExpirationTime`,
    method: 'post',
    data: data
  })
}
//查询停车场树结构
export function findparkinglotTree(data) {
  return request({
    url: `park${apiUrl}/majorParking/parkingLot/findparkinglotTree`,
    method: 'post',
    data:data
  })
}