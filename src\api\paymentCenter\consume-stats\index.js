import request from '@/utils/request'
import { apiUrl } from '@/utils/config'; 



export class screenIndex {
    //消费统计列表
    static pageList(data,params) {
      return request({
        url: `pay${apiUrl}/pay/stats/list`,
        method: 'post',
        data: {...data,...params},
      })
    }

    // 供应商树
    static providerTree(data) {
      return request({
        url: `pay${apiUrl}/pay/stats/providerTree`,
        method: 'post',
        data
      })
    }

    // 支付场景树
    static paySceneTree(data) {
      return request({
        url: `pay${apiUrl}/pay/stats/paySceneTree`,
        method: 'post',
        data
      })
    }

    // 支付类型树
    static payTypeTree(data) {
      return request({
        url: `pay${apiUrl}/pay/stats/payTypeTree`,
        method: 'post',
        data
      })
    }
    
    // 账户树
    static accountTree(data) {
      return request({
        url: `pay${apiUrl}/pay/stats/accountTree`,
        method: 'post',
        data
      })
    }

}