<template>
  <div  :class="popupType === 'view' ? 'dialog-box-view' : 'dialog-box-edit'">
        <el-form
          ref="ruleform"
          :model="formData"
          label-width="80px"
          :rules="popupType !== 'view' ? rules : {}"
        >
          <!-- 停车场信息 -->
          <el-row>
            <el-col :span="popupType !== 'view'?24:12">
              <el-form-item label="停车场" prop="parkingName">
                <div>{{ rowData.parkingName || "" }}</div>
              </el-form-item>
            </el-col>

          <!-- 车辆类型 -->
            <el-col :span="popupType !== 'view'?24:12">
              <el-form-item label="车辆类型" prop="vehicleType">
                <div>{{ $formatDictLabel(rowData.vehicleType, parking_rule_vehicle_type) }}</div>
              </el-form-item>
            </el-col>

          <!-- 授权类型（仅查看模式下显示） -->
            <el-col :span="12" v-if="popupType === 'view'">
              <el-form-item label="授权类型" prop="accreditType">
                <div>{{ $formatDictLabel(rowData.accreditType, parking_accredit_type) }}</div>
              </el-form-item>
            </el-col>

          <!-- 有效状态（仅查看模式下显示） -->
            <el-col :span="12" v-if="popupType === 'view'">
              <el-form-item label="有效状态" prop="accreditStatus">
                <div>{{ $formatDictLabel(rowData.accreditStatus, accredit_status) }}</div>
              </el-form-item>
            </el-col>

          <!-- 创建时间（仅查看模式下显示） -->
            <el-col :span="12" v-if="popupType === 'view'">
              <el-form-item label="创建时间" prop="createDate">
                <div>{{ rowData.createDate || "" }}</div>
              </el-form-item>
            </el-col>

          <!-- 操作人（仅查看模式下显示） -->
            <el-col :span="12" v-if="popupType === 'view'">
              <el-form-item label="操作人" prop="updateName">
                <div>{{ rowData.updateName || "" }}</div>
              </el-form-item>
            </el-col>

          <!-- 有效期限 -->
            <el-col :span="popupType !== 'view'?24:12">
              <el-form-item label="有效期限" prop="expirationTime">
                <!-- 非查看模式下显示日期选择器 -->
                <el-date-picker
                  v-if="popupType !== 'view'"
                  v-model="formData.expirationTime"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD 23:59:59"
                  type="date"
                   :disabled-date="disabledDate"
                  placeholder="默认长期有效"
                />
                <!-- 查看模式下显示文本 -->
                <div v-else>{{ rowData.expirationTime || "长期有效" }}</div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

    
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { updateTypeAccredit } from '@/api/majorParking/accredit/accredit';
import { getCurrentInstance } from 'vue';
// 获取当前组件实例
const { proxy } = getCurrentInstance();

// 定义组件的属性
const props = defineProps({
  closeBtn: {
    type: Function,
    default: null,
  },
  popupType: {
    type: String,
    default: '',
  },
  rowData: {
    type: Object,
    default: () => ({}),
  },
});

// 定义组件的事件
const emit = defineEmits(['close']);

// 表单数据
const formData = reactive({
  id: '',
  expirationTime: '',
});

// 时间选择器的禁用日期配置

const disabledDate = (time) => {
  const today = new Date();
    today.setHours(0, 0, 0, 0);
    return time.getTime() < today.getTime();
};

// 获取字典数据


const { parking_rule_vehicle_type, parking_accredit_type, accredit_status } =
  proxy.useDict(
    "parking_rule_vehicle_type",
    "parking_accredit_type",
    "accredit_status"
  );


// 保存按钮点击事件
const saveBtn = async () => {
  try {
    const res = await updateTypeAccredit({ ...formData });
    if (res.code == '1') {
      ElMessage.success('保存成功');
      emit('close');
    }
  } catch (error) {
    console.error('保存失败:', error);
  }
};


// 生命周期：组件挂载时调用
onMounted(() => {
  formData.id = props.rowData.id;
  if (props.rowData.expirationTime && props.rowData.expirationTime !== "长期有效") {
    formData.expirationTime = props.rowData.expirationTime + ' 23:59:59';
  } else {
    formData.expirationTime = '';
  }
});

defineExpose({
  saveBtn
})
</script>

<style scoped lang="scss">

</style>