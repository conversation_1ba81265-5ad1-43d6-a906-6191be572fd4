<template>
  <div class="app-container">
    <el-breadcrumb separator="/" style="margin: 5px 0 10px 0">
      <el-breadcrumb-item :to="{ path: portalIndex }" replace>智慧园区平台</el-breadcrumb-item>
<!--      <el-breadcrumb-item :to="{path: 'mailList', query: { code: '1' }}">-->
<!--        <a href>{{title }}</a>-->
<!--      </el-breadcrumb-item>-->
      <el-breadcrumb-item :to="{ path: '/portal/mailList'}"><a>{{ title }}</a></el-breadcrumb-item>
      <el-breadcrumb-item>
        <a>站内信内容</a>
      </el-breadcrumb-item>
      <el-breadcrumb-item>
        <a>{{mailTitle}}</a>
      </el-breadcrumb-item>
    </el-breadcrumb>
    <el-card class="d-card" shadow="never">
      <div class="contentMail">
        <h4 class="titleMail">[{{ mailTitle }}]</h4>
        <p class="content" v-html="mailInfo.content"></p>
      </div>
    </el-card>
  </div>
</template>

<script setup>

import {seeNoticeAdd} from "@/api/release/notice";

const {proxy} = getCurrentInstance()
const query = proxy.$route.query
const title = ref(query.title)
const mailTitle = ref(query.mailTitle)
const info = ref({
  noticeId: query.noticeId
})
const mailInfo = ref({
  time: query.time,
  content: query.content
})

function getAddInfo() {
  seeNoticeAdd(info.value).then(res => {
  });
}

getAddInfo();
</script>

<style scoped lang="scss">
.d-card {
  width: 60%;
  margin: 0 auto;
  border: 0px;
  min-height: calc(100vh - 150px);
  padding: 0px 50px;
  .d-title {
    text-align: center;
  }
  .d-desc {
    text-align: right;
    margin: 5px 10px;
    .d-type {
      padding: 0px 20px;
    }
  }
}
.content {
  ::v-deep img {
    display: block;
    margin: 0 auto;
  }
}
.titleMail {
  font-size: 20px;
}
.contentMail {
  // width: 50%;
  margin: 0 auto;
}
</style>
