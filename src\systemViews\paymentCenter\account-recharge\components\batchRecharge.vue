<!-- 批量充值 -->
<template>
  <div class="dialog-box dialog-box-edit" style="height: 600px">
    <el-row style="height: 100%">
      <Splitpanes class="default-theme">
        <Pane :size="100" :min-size="62">
          <el-card class="dep-card" style="height: 100%">
            <el-form ref="ruleform" :model="formData" :rules="rules">
              <el-row>
                <el-col :span="12">
                  <el-form-item label="充值月份" prop="month">
                    <el-date-picker
                      v-model="formData.month"
                      type="month"
                      format="YYYYMM"
                      value-format="YYYYMM"
                      placeholder="请选择"
                      clearable
                    />
                  </el-form-item>
                </el-col>

                <el-col :span="12">
                  <div class="flex">
                    <div class="flex-1"></div>

                    <el-button
                      plain
                      type="primary"
                      icon="Download"
                      @click="importTemplate"
                    >
                      模板下载
                    </el-button>
                    <el-button
                      icon="Upload"
                      plain
                      type="primary"
                      @click="handleImport"
                    >
                      导入开通
                    </el-button>
                  </div>
                </el-col>
              </el-row>
            </el-form>
            <public-table
              ref="publictable"
              :rowKey="tabelForm.tableKey"
              :tableData="list"
              :columns="tabelForm.columns"
              :configFlag="tabelForm.tableConfig"
            >
            </public-table>
          </el-card>
        </Pane>
      </Splitpanes>
    </el-row>

    <DialogBox
      :visible="diaWindow.open1"
      :dialogWidth="diaWindow.dialogWidth"
      :dialogFooterBtn="diaWindow.dialogFooterBtn"
      @save="save"
      @cancellation="cancellation"
      @close="close"
      :dialogTitle="diaWindow.headerTitle"
    >
      <template #content>
        <UploadFile
          ref="uploadRef"
          :uploadData="extraUploadFileData"
          :action="upload.url"
          :limit="1"
          :accept="'.xlsx, .xls'"
          :disabled="upload.isUploading"
          :auto-upload="false"
          tip="提示：仅允许导入“xls”或“xlsx”格式文件！<br>"
          @file-success="handleFileSuccess"
          @file-error="handleFileError"
        />
      </template>
    </DialogBox>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted,getCurrentInstance } from "vue";
import { ElMessageBox, ElMessage } from "element-plus";
import { screenIndex } from "@/api/paymentCenter/account-recharge/index";
import UploadFile from "@/components/UploadFile/index";
const emit = defineEmits(["dialogResult"]);
import { apiUrl } from "@/utils/config";
const { proxy } = getCurrentInstance();
const props = defineProps({
  closeBtn: {
    type: Function,
    default: () => {},
  },
  popupType: {
    type: String,
    default: "",
  },
  rowData: {
    type: Object,
    default: () => ({}),
  },
});

// 响应式数据
const list = ref([]);
const extraUploadFileData = ref({
  validDate: "",
});
// 用户导入参数
const upload = reactive({
  title: "",
  isUploading: false,
  updateSupport: 0,
  url: `${
    import.meta.env.VITE_APP_BASE_API
  }/park${apiUrl}/majorParking/userCar/importData`,
});
const diaWindow = reactive({
  open1: false,
  headerTitle: "",
  popupType: "",
  rowData: "",
  dialogWidth: "30%",
  dialogFooterBtn: false,
  customClass: "my_height_1",
});

const formData = reactive({
  staffInfos: [],
  areaIds: [],
});
const pageParams = ref({
  pageNum: 1,
  pageSize: 10,
});

const tabelForm = reactive({
  tableKey: "1", //表格key值
  isShowRightToolbar: true, //是否显示右侧显示和隐藏
  showSearch: true,
  // 表格表格数据
  columns: [
    {
      prop: "staffName",
      label: "员工姓名",
      show: true,
      sortable: true,
      visible: true, // 展示与隐藏
      minWidth: "120px",
    },
    {
      prop: "loginName",
      label: "登录账号",
      show: true,
      sortable: true,
      visible: true, // 展示与隐藏
      minWidth: "120px",
    },
    {
      prop: "cellPhone",
      label: "联系方式",
      show: true,
      sortable: true,
      visible: true, // 展示与隐藏
      minWidth: "120px",
    },

    {
      prop: "deptName",
      label: "员工部门",
      show: true,
      sortable: true,
      visible: true, // 展示与隐藏
      minWidth: "120px",
    },

    {
      prop: "staffType",
      label: "人员类型",
      show: true,
      visible: true, // 展示与隐藏
      sortable: true,
      minWidth: "120px",
      type:'dict',
      dictList:staff_type
    },
    {
      prop: "accountName",
      label: "账号名称",
      show: true,
      visible: true, // 展示与隐藏
      sortable: true,
      minWidth: "120px",
    },

    {
      prop: "accountCode",
      label: "账号编码",
      show: true,
      visible: true, // 展示与隐藏
      sortable: true,
      minWidth: "120px",
    },

    {
      fieldIndex: "rechargeDollor", // 对应列内容的字段名
      label: "充值金额", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "120", //最小宽度%
      width: "", //宽度
      type: "dollor",
    },
  ],
  // 表格基础配置项，看个人需求添加
  tableConfig: {
    needPage: true, // 是否需要分页
    index: true, // 是否需要序号
    selection: false, // 是否需要多选
    reserveSelection: false, // 是否需要支持跨页选择
    indexFixed: false, // 序号列固定 left true right
    selectionFixed: false, //多选列固定left true right
    indexWidth: "50", //序号列宽度
    loading: false, //表格loading
    showSummary: false, //是否开启合计
    height: null, //表格固定高度 比如：300px
  },
});

// 模板导出
const importTemplate = () => {
  proxy.download(
    `pay${apiUrl}/pay/payRechargeDetails/templateDownload`,

    {
      ...formData.value,
    },
    `批量充值导入模版_${formatMinuteTime(new Date())}.xlsx`
  );
};

// 导入
const handleImport = () => {
  diaWindow.popupType = "upload";
  diaWindow.headerTitle = "用户导入";
  diaWindow.dialogWidth = "400px";
  diaWindow.open1 = true;
};
// 文件上传成功处理
const handleFileSuccess = (response) => {
  if (response.response.code == "1") {
    ElMessage.success("导入成功");
    setTimeout(() => {
      upload.isUploading = false;
      diaWindow.open1 = false;
      getList();
    }, 800);
  } else {
    ElMessage.error(response.response.message);
  }
};

// 文件上传失败处理
const handleFileError = (res) => {
  ElMessage.error("导入失败");
};
const saveBtn = async () => {
  if (formData.areaIds.length < 1) {
    ElMessage.warning("请选择区域");
    return;
  }

  if (formData.staffInfos.length < 1) {
    ElMessage.warning("请选择人员");
    return;
  }

  try {
    await saveAreaAdmin(formData);
    ElMessage.success("添加成功");
    emit("dialogResult");
  } catch (error) {
    console.error("保存失败:", error);
  }
};

const cancelBtn = () => {
  props.closeBtn();
};

/** 点击确定后刷新 */
const cancellationRefsh = () => {
  close(false);
  // getList();
};
/** 点击取消保存 */
const cancellation = (val) => {
  close(false);
};

/** 关闭弹窗 */
const close = (val) => {
  diaWindow.open1 = val;
};

/** 查询列表 */
const getList = () => {
  tabelForm.tableConfig.loading = true;
  screenIndex.payAccountTypePageList(formData.value, pageParams.value).then((response) => {
    list.value = response.data.records;
    total.value = response.data.total;
    tabelForm.tableConfig.loading = false;
  });
};

// 生命周期
onMounted(() => {
    getList()
});
defineExpose({
  saveBtn,
});
</script>

<style scoped lang="scss">
.limit-cell-table {
  :deep(.el-table__cell) {
    padding: 8px 0;
  }
}

.text-left {
  text-align: left;
}

::v-deep .el-tree-node__content {
  color: #282828;
  font-weight: normal;
}
</style>
