import request from '@/utils/request'

// 查询团体访客邀约信息
export function findVistorGroup(data,params) {
  return request({
    url: '/visitor/group/findVistorGroup',
    method: 'post',
    data: data,
    params: params,
  })
}

// 新增团体访客邀约信息
export function addVistorGroup(data) {
  return request({
    url: '/visitor/group/saveVistorGroup',
    method: 'post',
    data: data,
  })
}
// 查看团体访客邀约二维码
export function findVistorGroupQr(data) {
  return request({
    url: '/visitor/group/createInvitationCode',
    method: 'post',
    data: data,
  })
}
// 删除团体访客邀约信息
export function deleteGroup(data) {
  return request({
    url: '/visitor/group/deleteVistorGroup',
    method: 'post',
    data: data,
  })
}
