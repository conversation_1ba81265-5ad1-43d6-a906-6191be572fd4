<template>
  <el-sub-menu v-if="item.children && item.children.length && item.children.some(i=>i.permissionVisible !== 'hide')" :index="props.menuIndex">
    <template #title>
      <svg-icon v-if="item.icon" :icon-class="item.icon" style="margin-right: 7px;" />
      <span class="menu-title" :title="hasTitle(item.permissionName)" @click="onClickMenu(item)">
        {{ item.permissionName }}
      </span>
    </template>
    <menu-item v-for="(child, index) in item.children" :menu-index="`${props.menuIndex}-${index + 1}`" :item="child" />
  </el-sub-menu>
  <el-menu-item v-else v-if="item.permissionVisible !== 'hide'" v-bind="menuItemProps(item)" @click="onClickMenu(item)">
    <svg-icon v-if="item.icon" :icon-class="item.icon" style="margin-right: 7px;" />
    <template #title>
      <span class="menu-title" :title="hasTitle(item.permissionName)">{{ item.permissionName }}</span>
    </template>
  </el-menu-item>
</template>

<script setup>

const router = useRouter()

const props = defineProps({
  item: {
    type: Object,
    required: true
  },
  menuIndex: {
    type: String,
    default: ''
  }
})

function menuItemProps(item) {
  if (item.feedback === 'view') {
    return {
      index: item.path
    }
  }
  return {
    index: `${props.menuIndex}-0`
  }
}

function onClickMenu(item) {
  if (item.feedback === 'view') {
    router.push({ path: item.path })
  } else if (item.feedback === 'blank') {
    window.open(item.path)
  }
}

function hasTitle(title) {
  if (title.length > 5) {
    return title;
  } else {
    return "";
  }
}
</script>
