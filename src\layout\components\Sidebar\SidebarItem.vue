<template>
  <el-sub-menu v-show="item.permissionVisible == 'show'" v-if="item.children && item.children.length" :index="props.menuIndex">
    <template #title>
      <svg-icon :icon-class="item.icon" />
      <span class="menu-title" :title="hasTitle(item.permissionName)" @click="onClickMenu(item)">
        {{ item.permissionName }}
      </span>
    </template>
    <sidebar-item v-for="(child, index) in item.children" :menu-index="`${props.menuIndex}-${index + 1}`" :item="child" />
  </el-sub-menu>
  <el-menu-item v-show="item.permissionVisible == 'show'" v-else v-bind="menuItemProps(item)" @click="onClickMenu(item)">
    <svg-icon :icon-class="item.icon" />
    <template #title>
      <span class="menu-title" :title="hasTitle(item.permissionName)">{{ item.permissionName }}</span>
    </template>
  </el-menu-item>
</template>

<script setup>

const router = useRouter()

const props = defineProps({
  item: {
    type: Object,
    required: true
  },
  menuIndex: {
    type: String,
    default: ''
  }
})

function menuItemProps(item) {
  if (item.feedback === 'view') {
    return {
      index: item.path
    }
  }
  return {
    index: `${props.menuIndex}-0`
  }
}

function onClickMenu(item) {
  if (item.feedback === 'view') {
    router.push({ path: item.path })
  } else if (item.feedback === 'blank') {
    window.open(item.path)
  }
}

function hasTitle(title) {
  if (title.length > 5) {
    return title;
  } else {
    return "";
  }
}
</script>
