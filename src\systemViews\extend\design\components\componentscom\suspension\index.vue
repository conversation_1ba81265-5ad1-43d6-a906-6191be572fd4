<template>
  <div id="suspension">
    <div className="suspension-wrap">
      <el-icon  class="suspension-logo"><Connection /></el-icon>
    </div>
    <!-- 删除组件 -->
    <slot name="deles"/>
  </div>
</template>
<script>
import {Back} from "@element-plus/icons-vue";

export default {
  name: 'suspension',
  components: {Back},
  props: {
    datas: Object,
  },
}
</script>
<style lang="less" scoped>
#suspension {
  width: 100%;
  height: 36px;
  position: relative;
  position: absolute;
  right: 0;
  bottom: 10%;
  z-index: 999;
  border: none;
  box-sizing: border-box;

  .suspension-wrap {
    width: 36px;
    height: 36px;
    position: absolute;
    right: 30px;
    z-index: 1001;

    .suspension-logo {
      width: 36px;
      height: 36px;
      border-radius: 50%;
    }
  }
}
</style>
