import request from "@/utils/request";


// 查询侧边栏
export function findDeptTree(data) {
    return request({
        url: "/user/orgStaff/sync/findDeptTree",
        method: "post",
        data: data,
    });
}


// 查询租户部门列表
export function tenantDeptList(data) {
    return request({
        url: "/user/tenantDept/list",
        method: "post",
        data: data,
    });
}


// 查询租户部门详细信息
export function getTenantDeptInfo(data) {
    return request({
        url: "/user/tenantDept/getInfo",
        method: "post",
        data: data,
    });
}


// 新增租户部门
export function addTenantDept(data) {
    return request({
        url: "/user/tenantDept/add",
        method: "post",
        data: data,
    });
}


// 修改租户部门
export function editTenantDept(data) {
    return request({
        url: "/user/tenantDept/edit",
        method: "post",
        data: data,
    });
}


// 删除租户部门
export function delTenantDept(data) {
    return request({
        url: "/user/tenantDept/delete",
        method: "post",
        data: data,
    });
}
