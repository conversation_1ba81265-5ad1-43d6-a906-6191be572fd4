<template>
  <div class="app-container" style="height: 100%">
    <Splitpanes class="default-theme">
      <Pane
          size="3.5"
          min-size="3.5"
          max-size="3.5"
          v-if="!showSider"
      >

        <el-card
            class="inquiry-center-card"
            shadow="never"
            body-style=""
        >
      <span class="inquiry-center-fold">
    <el-icon @click="showSider=true"><ArrowRightBold/></el-icon>点击可展开
      </span>
        </el-card>
      </Pane>
      <Pane
          min-size="15"
          max-size="20"
          :size="15"
          v-if="showSider"
      >
        <el-card
            class="inquiry-center-card"
            shadow="never"
            body-style=""
        >
          <template #header class="clearfix">
            <span>
          <el-icon @click="showSider=false" class="inquiry-center-fold"><ArrowLeftBold/></el-icon>
              {{ leftTitle }}</span
            >
            <el-button
                style="float: right; padding: 3px 0"
                link
                type="primary"
                icon="Refresh"
                @click="getLeftData"
                :loading="treeLoading"
            >刷新
            </el-button
            >
          </template>
          <el-tree
              :data="list"
              :props="{label:'permissionName',children:'children'}"
              @node-click="handleNodeClick"
              node-key="permissionId"
              ref="asyncTree"
              :default-expanded-keys="expandedId"
          >
            <template #default="{ node, data }">
              <span>
                <span style="margin-right: 4px;" class="ext-text">
            <svg-icon :icon-class="data.icon"/>
          </span>
          {{ node.label }}
        </span>
            </template>
          </el-tree>
        </el-card>
      </Pane>
      <Pane min-size="80">
        <el-card class="inquiry-center-card-iframe" shadow="never">
          <el-tabs
              v-model="activeTab"
              type="card"
              closable
              style="height: 100%"
              @edit="handleTabsEdit"
          >
            <el-tab-pane
                v-for="item in tabs"
                :key="item.id"
                :label="item.title"
                :name="item.id"
                style="height: 100%"
            >
              <iframe
                  :key="item.id + 'iframe'"
                  :src="item.menuUrl"
                  frameborder="0"
                  class="inquiry-center-iframe"
              ></iframe>
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </Pane>
    </Splitpanes>
  </div>
</template>

<script setup name="inquiryCenter">
import {getRouters} from "@/api/login";
import {arrayToTree} from "@/utils";
import useInquiryCenterStore from "@/store/modules/inquiryCenter";
import {ElMessage} from "element-plus";

const inquiryCenterStore = useInquiryCenterStore()
const showSider = ref(true)
const defaultProps = defineProps({
  children: {
    type: String,
    default: "children"
  },
  label: {
    type: String,
    default: "permissionName"
  },
})
const paneSize = ref()
const list = ref([])
const treeLoading = ref(false)
const leftTitle = ref("数据加载中")
const icon = ref("")
const expandedId = ref([])
const percent = ref(15)
const extPercent = ref(15)
const splitPane = ref()

const tabs = computed(() => inquiryCenterStore.tabs)
const activeTab = computed({
  get() {
    return inquiryCenterStore.activeTab
  },
  set(val) {
    inquiryCenterStore.setActiveTab(val)
  }

})


function handleNodeClick(data) {
  inquiryCenterStore.addTab({
    title: data.permissionName,
    name: data.permissionId,
    id: data.permissionId,
    menuUrl: data.uri,
  })
}

function handleTabsEdit(targetName) {
  if (tabs.value.size <= 1) {
    ElMessage.error("标签不允许删除完！")
    return
  }
  inquiryCenterStore.delTab(targetName)
}

function getLeftData() {
  treeLoading.value = true
  getRouters({
    permissionScope: "portalQuery",
  }).then((res) => {
    let resList = res.data
    resList.forEach((element, index) => {
      if (element.permissionVisible === 'hide') {
        resList.splice(index, 1)
      }
    })
    const listD = arrayToTree(resList, "children", "permissionId", "parentId");
    if (listD && listD.length > 0) {
      leftTitle.value = listD[0].permissionName;
      list.value = listD[0].children;
      expandedId.value = list.value.map((v) => v.permissionId)
      icon.value = listD[0].icon
    } else {
      leftTitle.value = "暂无数据"

    }
    console.log("55556")
    console.log(list.value)
    if (list.value.length > 0) {
      if (list.value[0].children !== null) {
        inquiryCenterStore.addTab({
          title: list.value[0].children[0].permissionName,
          name: list.value[0].children[0].permissionId,
          id: list.value[0].children[0].permissionId,
          menuUrl: list.value[0].children[0].uri,
        })
      }
    }
    treeLoading.value = false
    console.log(treeLoading.value)
  })
}

function renderContent(h, {node, data, store}) {
  // return (
  //     <span>
  //         <span style="margin-right: 4px;" class="ext-text">
  //           {data.icon && <svg-icon icon-class={data.icon} />}
  //         </span>
  //       {node.label}
  //       </span>
  // );
}

function resize(e) {
  extPercent.value = e < 15 ? 15 : e;
  splitPane.value.percent = e < 15 ? 15 : e;
}

function closeLeft() {
  if (splitPane.value.percent === 3) {
    splitPane.value.percent = percent;
    extPercent.value = percent.value;
  } else {
    percent.value = splitPane.value.percent;
    splitPane.value.percent = 3;
    extPercent.value = 3;
  }
}

getLeftData();
</script>

<style scoped lang="scss">
.inquiry-center-card {
  height: 100%;
}

.inquiry-center-fold {
  cursor: pointer;
}

.inquiry-center-card-iframe {
  height: 100%;

  :deep(.el-card__body){
    padding: 4px;
  }

  :deep(.el-card__body) {
    height: 100%;
  }

  :deep(.el-tabs__content){
    height: 100%;
  }

  :deep(.el-tabs__header) {
    margin: 0px;
  }

  .inquiry-center-iframe {
    width: 100%;
    height: 100%;
  }
}
</style>
