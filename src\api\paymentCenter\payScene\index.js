import request from '@/utils/request'
import { apiUrl } from '@/utils/config'; 



export class screenIndex {
//账户设置列表
  static payAccountTypePageList(data,params) {
    return request({
      url: `pay${apiUrl}/pay/PayAccountManage/accountTypeTree`,
      method: 'post',
      data: {...data,...params},
    })
  }
//账户设置列表
  static paySceneList(data,params) {
    return request({
      url: `pay${apiUrl}/pay/payScene/list`,
      method: 'post',
      data: {...data,...params},
    })
  }

// 删除
static paySceneDelete(data) {
    return request({
      url: `pay${apiUrl}/pay/payScene/delete`,
      method: 'post',
      data
    })
  }

// 查询

static paySceneGetInfo(data) {
    return request({
      url: `pay${apiUrl}/pay/payScene/getInfo`,
      method: 'post',
      data
    })
  }
// 添加
  static paySceneAdd(data) {
    return request({
      url: `pay${apiUrl}/pay/payScene/add`,
      method: 'post',
      data
    })
  }

//修改
static paySceneEdit(data) {
    return request({
      url: `pay${apiUrl}/pay/payScene/edit`,
      method: 'post',
      data
    })
  }

  // 管理员选择

  static selectUserList(data) {
    return request({
      url: `pay${apiUrl}/pay/payScene/adminTree`,
      method: 'post',
      data
    })
  }
  // 供应商
  static payProviderPageList(param,data) {
    return request({
      url: `pay${apiUrl}/pay/payScene/providerTree`,
      method: 'post',
      data: {...param,...data}
    })
  }
  static juniorList(param) {
    return request({
      url: `/user/tenants/juniorList`,
      method: 'get',
      data:param
    })
  }



  // 支付类型分页查询
  static pagePayType(data) {
    return request({
      url: `pay${apiUrl}/pay/payType/pagePayType`,
      method: 'post',
      data
    })
  }

  // 根据id查询支付类型
  static findPayTypeById(data) {
    return request({
      url: `pay${apiUrl}/pay/payType/findById`,
      method: 'post',
      data
    })
  }

  // 新增支付类型
  static savePayType(data) {
    return request({
      url: `pay${apiUrl}/pay/payType/savePayType`,
      method: 'post',
      data
    })
  }

  // 修改支付类型
  static updatePayType(data) {
    return request({
      url: `pay${apiUrl}/pay/payType/updatePayType`,
      method: 'post',
      data
    })
  }

  // 删除支付类型
  static deletePayType(data) {
    return request({
      url: `pay${apiUrl}/pay/payType/deletePayType`,
      method: 'post',
      data
    })
  }

  // 查询支付场景树
  static paySceneTree(data) {
    return request({
      url: `pay${apiUrl}/pay/payType/paySceneTree`,
      method: 'post',
      data
    })
  }


}