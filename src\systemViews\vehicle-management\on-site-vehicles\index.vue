

<template>
    <div>
        <TabContainers :tabList="tabList" :activeName="activeName"></TabContainers>
    </div> 

</template>

<script setup name="ParkingAccredit">
// tab切换
import TabContainers from '@/components/TabContainer/index';
import tab1 from '@/systemViews/vehicle-management/on-site-vehicles/tab1.vue'
import tab2 from '@/systemViews/vehicle-management/on-site-vehicles/tab2.vue'

import { ref } from "vue";
// 定义 tab 列表
const tabList = ref([
  {
    label: '已授权车辆',
    value: '01',
    component: tab1
  },
  {
    label: '未授权车辆',
    value: '02',
    component: tab2
  },
])
// 默认激活的 tab
const activeName = ref('01')
</script>

<style scoped lang="scss">
:deep(.el-tabs .el-tabs__content .content) {
    height: calc(100vh - 250px);
}
</style>