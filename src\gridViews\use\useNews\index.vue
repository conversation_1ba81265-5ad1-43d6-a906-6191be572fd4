<template>
  <el-card :class="bgColor === true ?'news-card':'unshow-news-card'" v-loading="loading" shadow="never">
    <template #header v-if="showTitle">
      <span class="card-title" >
        <span> <el-icon class="icon iconfont"></el-icon>新闻 </span>
      </span>
    </template>
    <div class="hot home-card-body">
      <ul>
        <li v-for="(item, index) in newList" :key="index">
          <a :href="'https://www.163.com/dy/article/' + item.docid + '.html'" target="_blank">{{ item.title }}</a>
        </li>
        <li v-for="(item, index) in newList" :key="index">
          <a :href="'https://www.163.com/dy/article/' + item.docid + '.html'" target="_blank">{{ item.title }}</a>
        </li>
      </ul>
    </div>
  </el-card>
</template>

<script setup name="UseNews">
import { getDate } from "@/api/grid/useNews";

const loading = ref(false);
const list = ref([]);
const newList = ref([]);
const props = defineProps({
  showTitle: Boolean,
  bgColor: Boolean
})

const getList = () => {
  getDate().then((res) => {
    list.value= res.data.T1348647853363;
    newList.value = list.value.splice(0, 10);
  })
}

getList();
</script>

<style scoped lang="scss">
.news-card {
  height: 100%;
  background: #fff;
  overflow: auto;
}
.unshow-news-card {
  height: 100%;
  border: 0px;
  background: #fff;
}
ul li {
  margin-bottom: 10px;
}
ul li::marker {
  color: gray;
}
ul li:hover {
  color: red;
}
</style>
