<template>
<div class="app-container">
  <el-card shadow="never">
  <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
    <el-form-item label="申请单单号" prop="tenantApplyNum">
      <el-input v-model="queryParams.tenantApplyNum" placeholder="请输入申请单单号" clearable style="width: 180px"
                @keyup.enter="handleQuery"/>
    </el-form-item>
    <el-form-item label="租户名称" prop="tenantName">
      <el-input v-model="queryParams.tenantName" placeholder="请输入租户名称" clearable style="width: 180px"
                @keyup.enter="handleQuery">
      </el-input>
    </el-form-item>
    <el-form-item label="租户登录名" prop="tenantLoginName">
      <el-input v-model="queryParams.tenantLoginName" placeholder="请输入租户登录名" clearable style="width: 180px"
                @keyup.enter="handleQuery">
      </el-input>
    </el-form-item>


    <el-form-item label="申请单状态" prop="tenantApplyStatus">
      <el-select
          v-model="queryParams.tenantApplyStatus"
          placeholder="申请单状态"
          clearable
          style="width: 180px;"
      >
        <el-option
            v-for="dict in tenant_apply_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
        />
      </el-select>
    </el-form-item>
    <el-form-item>
      <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
      <el-button icon="Refresh" @click="resetQuery">重置</el-button>
    </el-form-item>
  </el-form>
  <!--  刷新按钮-->
  <el-row :gutter="10" class="mb8">
    <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
  </el-row>
  <!--  展示-->
  <el-table v-loading="loading" :data="dataList">
    <el-table-column prop="tenantApplyNum" label="申请单单号" :show-overflow-tooltip="true"  align="center"></el-table-column>
    <el-table-column prop="tenantName" label="租户名称" :show-overflow-tooltip="true"  align="center"></el-table-column>
    <el-table-column prop="tenantLoginName" label="租户登录名" :show-overflow-tooltip="true"  align="center"></el-table-column>
    <el-table-column prop="displayName" label="租户管理员" :show-overflow-tooltip="true"  align="center"></el-table-column>
    <el-table-column prop="tenantDomain" label="租户域名" :show-overflow-tooltip="true" align="center"></el-table-column>
    <el-table-column prop="tenantHtmlPath" label="租户前台位置" :show-overflow-tooltip="true"  align="center"></el-table-column>
    <el-table-column prop="maxStaff" label="最大用户数" :show-overflow-tooltip="true" align="center"></el-table-column>
    <el-table-column prop="effectiveDate" label="有效截止时间" :show-overflow-tooltip="true"  align="center"></el-table-column>
    <el-table-column prop="tenantApplyStatus" label="申请状态"  width="180" align="center">
      <template #default="scope">
        <dict-tag :options="tenant_apply_status" :value="scope.row.tenantApplyStatus"/>
      </template>
    </el-table-column>
    <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="350">
      <template #default="scope">
        <el-button link type="primary" icon="View" @click="handleView(scope.row)"
                   :loading="submitButton==='view' && scope.row.tenantApplyId === loadingId"
                   v-hasPermi="['sys:tenant:tenant-audit:freeze']"
                   >查看
        </el-button>
        <el-button link type="primary" icon="Edit" @click="handleEdit(scope.row)"
                   :loading="submitButton==='submit' && scope.row.tenantApplyId === loadingId"
                   v-hasPermi="['sys:tenant:tenant-audit:freeze']"
                   :disabled="scope.row.tenantApplyStatus!='init'">修改
        </el-button>
        <el-button link type="primary" icon="Check" @click="handleAuditOk(scope.row, 'valid')"
                   :loading="submitButton==='ok' && scope.row.tenantApplyId === loadingId"
                   v-hasPermi="['sys:tenant:tenant-audit:pass']"
                    :disabled="scope.row.tenantApplyStatus!='init'"
                    >通过
        </el-button>
        <el-button link type="primary" icon="Close" @click="handleAuditReject(scope.row, 'reject')"
                   :loading="submitButton==='reject' && scope.row.tenantApplyId === loadingId"
                   v-hasPermi="['sys:tenant:tenant-audit:pass']"
                   :disabled="scope.row.tenantApplyStatus!='init'">拒绝
        </el-button>
      </template>
    </el-table-column>
  </el-table>

  <!--    分页器-->
  <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
  />
  </el-card>
<!--  修改相关-->

  <el-dialog
      :title="title"
      v-model="open"
      width="1200px"
      append-to-body
      @close="cancel"
      :close-on-press-escape="false"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="150px" :disabled="readonly">
      <el-row :gutter="12">
        <el-col :span="12">
          <el-form-item label="申请单单号" prop="tenantApplyNum">
            <el-input
                v-model="form.tenantApplyNum" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="申请状态" prop="tenantApplyStatus">
            <el-select
                v-model="form.tenantApplyStatus"
                placeholder="申请单状态"
                clearable
                style="width: 100%"
            >
              <el-option
                  v-for="dict in tenant_apply_status"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"/>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="12">
        <el-col :span="12">
          <el-form-item label="租户名称" prop="tenantName">
            <el-input
                v-model="form.tenantName"
                placeholder="请输入租户名称"
                maxlength="64"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="租户登录名" prop="tenantLoginName">
            <el-input
                v-model="form.tenantLoginName"
                placeholder="请输入租户登录名"
                maxlength="64"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="12">
        <el-col :span="12">
          <el-form-item label="管理员名称" prop="tenantAdminName">
            <el-input
                v-model="form.tenantAdminName"
                placeholder="请输入管理员名称"
                maxlength="64"
            />
          </el-form-item>
        </el-col>
        <el-col  :span="12">
          <el-form-item label="管理员登录名" prop="tenantAdminLoginName">
            <el-input
                v-model="form.tenantAdminLoginName"
                placeholder="请输入管理员登录名"
                maxlength="64"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="12">
      <el-col :span="12">
          <el-form-item label="组织名称" prop="orgName">
            <el-input
                v-model="form.orgName"
                placeholder="请输入组织名称"
                maxlength="64"
            />
          </el-form-item>
        </el-col>
      <el-col :span="12">
          <el-form-item label="组织编码" prop="orgCode">
            <el-input
                v-model="form.orgCode"
                placeholder="请输入组织编码"
                maxlength="64"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="12">
        <el-col :span="12">
          <el-form-item label="有效截止时间" prop="effectiveDate">
            <el-date-picker
                v-model="form.effectiveDate"
                style="width: 100%"
                type="datetime"
                value-format="YYYY-MM-DD HH:mm:ss"
            ></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="最大用户数" prop="maxStaff">
            <el-input-number
                v-model="form.maxStaff"
                style="width: 100%"
                :min="1"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="12">
        <el-col :span="12">
          <el-form-item label="域名" prop="tenantDomain">
            <el-input
                v-model="form.tenantDomain"
                placeholder="请输入组织编码"
                maxlength="64"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="前台位置" prop="tenantHtmlPath">
            <el-input
                v-model="form.tenantHtmlPath"
                placeholder="请输入前台位置"
                maxlength="11"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="业务主键" prop="businessId">
            <el-input
                v-model="form.businessId"
                placeholder="请输入业务主键"
                maxlength="50"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-form  :model="form"  label-width="150px">
    <el-row :gutter="12" v-show="readonly||submitButton === 'view'" >
          <el-col :span="24">
            <el-form-item label="审批意见" prop="auditInfo">
              <el-input
                  type="textarea"
                  v-model="form.auditInfo"
                  :disabled="submitButton === 'view'"
              />
            </el-form-item>
          </el-col>
        </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button v-show="submitButton === 'submit'" type="primary" @click="submitForm" :loading="saveLoading">确
          定
        </el-button>
        <el-button v-show="submitButton === 'ok'" type="primary" @click="submitOK" :loading="saveLoading">通 过
        </el-button>
        <el-button v-show="submitButton === 'reject'" type="primary" @click="submitReject" :loading="saveLoading">拒
          绝
        </el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>

  </el-dialog>
</div>
</template>

<script setup name="TenantAudit">
import {page, auditOk, auditReject, getById, update} from "@/api/tenant/tenantAudit";
import {ElMessage, ElMessageBox} from "element-plus";
import useUserStore from "@/store/modules/user";

const {proxy} = getCurrentInstance();
const userStore = useUserStore();
const {tenant_apply_status} = proxy.useDict("tenant_apply_status")

//查询列表
const total = ref(0)
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  tenantApplyNum: undefined,
  tenantName: undefined,
  tenantLoginName: undefined,
  tenantApplyStatus: undefined
})
const showSearch = ref(true);
const dataList = ref([])
const loading = ref(true)

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList();
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}
/** 查询列表 */
function getList() {
  page(queryParams.value).then((response)=>{
    if (response.success) {
      total.value = response.data.total
      dataList.value = response.data.records
    }
    loading.value = false;
  }).catch(() => {
    loading.value = false;
  })
}
getList()

//修改相关
const open=ref(false)
const formRef = ref();
const form = ref({});
const rules = ref({
})
const title=ref("")
const submitButton=ref('submit')
const loadingId=ref()
const readonly=ref(false)
const saveLoading = ref(false)
function reset(){
  form.value={}
}
function handleEdit(row) {
    reset();
    loadingId.value=row.tenantApplyId;
    readonly.value = false;
    submitButton.value = 'submit';
    title.value="修改租户申请"
    getInfoByTenantApplyId(row)
}
function handleView(row){
  reset();
  loadingId.value=row.tenantApplyId;
  readonly.value = true;
  submitButton.value = 'view';
  title.value="查看租户申请"
  getInfoByTenantApplyId(row)
}
function cancel(){
  open.value=false;
  loadingId.value=undefined;
  reset()
}
async function getInfoByTenantApplyId(row){
  await getById(row.tenantApplyId).then(response=>{
    if (response.data){
      form.value=response.data
      open.value=true;
    }else {
      ElMessage.error("数据异常")
    }
  })
}
function submitForm(){
  saveLoading.value=true;
  update(form.value).then((res)=>{
    if (res.data){
      ElMessage.success("修改成功！")
      getList();
      saveLoading.value=false;
      open.value=false
    }else {
      ElMessage.error("修改失败！")
      saveLoading.value=false;
      open.value=false
    }
  }).catch(() => {
    saveLoading.value=false;

  })
}
//审批相关
function handleAuditOk(row) {
  reset();
  loadingId.value=row.tenantApplyId;
  readonly.value = true;
  submitButton.value = 'ok';
  title.value="通过租户申请"
  getInfoByTenantApplyId(row)
}
function submitOK(){
  saveLoading.value=true;
  auditOk(form.value).then(res =>{
    if (res.data){
      ElMessage.success("审批成功！")
      getList();
      saveLoading.value=false;
      open.value=false
    }else {
      ElMessage.error("审批失败！")
      saveLoading.value=false;
      open.value=false
    }
  }).catch(() => {
    saveLoading.value=false;

  })

}
//审批相关
function handleAuditReject(row) {
  reset();
  loadingId.value=row.tenantApplyId;
  readonly.value = true;
  submitButton.value = 'reject';
  title.value="拒绝租户申请"
  getInfoByTenantApplyId(row)
}
function submitReject() {
  saveLoading.value=true;
  auditReject(form.value).then(res =>{
    if (res.data){
      ElMessage.success("审批成功！")
      getList();
      saveLoading.value=false;
      open.value=false
    }else {
      ElMessage.error("审批失败！")
      saveLoading.value=false;
      open.value=false
    }
  }).catch(() => {
    saveLoading.value=false;

  })
}


</script>

<style scoped>
:deep(input:disabled::-webkit-input-placeholder){
  -webkit-text-fill-color: rgba(255,255,255,0);
}

</style>
