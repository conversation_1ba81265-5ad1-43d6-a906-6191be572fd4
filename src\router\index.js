import { createWebHistory, createRouter, createWebHashHistory } from "vue-router";
/* Layout */
import Layout from "@/layout";
import PortalContainer from "@/portal";

/**
 * Note: 路由配置项
 * query: '{"id": 1, "name": "ry"}' // 访问路由的默认传递参数
 * permissions: ['a:a:a', 'b:b:b']  // 访问路由的菜单权限
 * meta : {
    noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg
  }
 */

// 管理系统首页
export const systemHome = {
  path: "/system/index",
  component: Layout,
  children: [
    {
      path: "",
      component: () => import("@/systemViews/index"),
      name: "systemHome",
      meta: { title: "首页", icon: "dashboard", affix: true },
    },
  ],
};

// 智慧园区平台
export const portalHome = {
  path: "/portal/index",
  component: PortalContainer,
  children: [
    {
      path: "",
      component: () => import("@/portalViews/index"),
      name: "portalHome",
      meta: { title: "首页", icon: "dashboard" },
    },
  ],
};

// 公共路由
export const constantRoutes = [
  systemHome,
  portalHome,
  {
    path: "/redirect",
    component: Layout,
    children: [
      {
        path: "/redirect/:path(.*)",
        component: () => import("@/views/redirect/index.vue"),
      },
    ],
  },
  {
    path: "/login",
    component: () => import("@/portalViews/login"),
  },
  {
    path: "/:pathMatch(.*)*",
    component: () => import("@/views/error/404"),
  },
  {
    path: "/401",
    component: () => import("@/views/error/401"),
  },
  {
    path: "",
    redirect: portalHome.path,
  },
  {
    path: "/system",
    component: Layout,
    hidden: true,
    children: [
      {
        // 后台 个人中心
        path: "user/profile",
        component: () => import("@/systemViews/system/user/profile/index.vue"),
        hidden: true
      },
    ]
  },
  {
    path: "/portal",
    component: PortalContainer,
    hidden: true,
    children: [
      {
        // 前台 个人中心
        path: "userInfo/index",
        component: () => import("@/views/userInfo/index.vue"),
        hidden: true
      },
    ]
  }
];

// 不在导航栏中显示的路由配置
export const dynamicRoutes = [
  {
    path: "/page/grid",
    permissions: ['system:extend:page-designer:design'],
    component: () => import("@/grid/edit"),
  },
  {
    path: "/page/grid/preview",
    permissions: ['system:extend:page-designer:design'],
    component: () => import("@/grid/designPage"),
  },
  {
    path: "/home/<USER>/custom",
    permissions: ['sys:index:portal'],
    component: () => import("@/grid/edit"),
  },
  {
    path: "/home/<USER>/org",
    permissions: ['index:home:org:edit'],
    component: () => import("@/grid/edit"),
  },
  {
    path: "/home/<USER>/tenant",
    permissions: ['index:home:tenant:edit'],
    component: () => import("@/grid/edit"),
  },
];

const router = createRouter({
  // history: createWebHistory(),
  history: createWebHashHistory(),
  routes: constantRoutes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition;
    } else {
      return { top: 0 };
    }
  },
});

export default router;
