<!--新增权限 -->

<script setup>
import { ref, reactive, onMounted, getCurrentInstance, defineProps, defineEmits, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { screenIndex } from "@/api/admittance/accredit";

const props = defineProps({
    popupType: {
        type: String,
        default: ''
    },
    rowData: {
        type: Object,
        default: () => ({})
    }
})
const emit = defineEmits(['submitClose'])

const ruleform = ref()
const formData = reactive({
    id: '',
    deptName: '',
    expirationTime: null
})

// 表单验证规则
const rules = reactive({
    expirationTime: [
        {
            validator: (rule, value, callback) => {
                if (value === null || value === '') {
                    callback() // 允许为空，表示长期有效
                } else {
                    const selectedDate = new Date(value)
                    const today = new Date()
                    today.setHours(0, 0, 0, 0)
                    
                    if (selectedDate < today) {
                        callback(new Error('到期日期不能早于今天'))
                    } else {
                        callback()
                    }
                }
            },
            trigger: 'change'
        }
    ]
})

const timepickerOptions = {
    disabledDate(time) {
        const today = new Date()
        today.setHours(0, 0, 0, 0)
        return time.getTime() < today.getTime()
    }
}

// 监听props.rowData变化，初始化formData
watch(() => props.rowData, (newVal) => {
    if (newVal && Object.keys(newVal).length > 0) {
        formData.id = newVal.id
        formData.deptName = newVal.deptName + '-' + newVal.areaName
        formData.expirationTime = null
        if (newVal.expirationTime != null && newVal.expirationTime !== '') {
            formData.expirationTime = newVal.expirationTime + ' 23:59:59'
        }
    }
}, { immediate: true, deep: true })

// 保存授权日期修改
const updateExpirationTime = () => {


    try {
        ruleform.value.validate((valid) => {
            if (!valid) return;
           
            screenIndex.updateExpirationTime(formData).then((res) => {
                if (res.success) {
                    ElMessage.success('修改成功')
                    emit('submitClose');
                }
            });
        });
    } catch (error) {
        console.error(error);
    }
}



defineExpose({
    updateExpirationTime
})

onMounted(() => {
    // getDeptTree()
    // getAreaTree()
})
</script>
<template>
    <div class="dialog-box dialog-box-edit">
        <el-form ref="ruleform" :model="formData" :rules="rules" :inline="true">
            <el-row>
                <el-col :span="24">
                    <el-form-item label="部门名称">
                        <span>{{ formData.deptName }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item label="到期日期" prop="expirationTime">
                        <el-date-picker v-model="formData.expirationTime" format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD 23:59:59" type="date"
                            :disabled-date="timepickerOptions.disabledDate" placeholder="默认长期有效" />
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
    </div>
</template>
