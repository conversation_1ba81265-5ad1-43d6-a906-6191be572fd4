<template>
  <div class="header">
    <div class="box">
      <div class="content" :style="contentWidth()">
        <div class="logo-box">
          <img v-if="logo" :src="logo" class="logo" />
          <span class="title">{{ permissionStore.portalName || title }}</span>
        </div>
        <div class="menu">
          <el-menu :default-active="route.path" mode="horizontal" style="border-bottom:1px">
            <menu-item v-for="(item, index) in menusData" :key="`${item.permissionId}-${index}`"
              :menu-index="index + ''" :item="item"></menu-item>
          </el-menu>
        </div>
        <div class="right-menu">
          <div id="adminbutton" class="right-menu-item hover-effect" v-hasPermi="['sys:base:menu:edit']" @click="goAdmin">
            <el-icon>
              <Setting></Setting>
            </el-icon>
            <span style="margin-left: 3px;">系统管理</span>
          </div>
          <screenfull id="screenfull" class="right-menu-item hover-effect"></screenfull>
          <el-dropdown @command="handleCommand" class="right-menu-item hover-effect" trigger="click">
            <div class="avatar-wrapper">
              <img :src="userStore.headImg || defaultAvatar" class="user-avatar" />
              <el-icon style="align-self: flex-end;"><caret-bottom /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <router-link to="/portal/userInfo/index">
                  <el-dropdown-item>个人中心</el-dropdown-item>
                </router-link>
                <el-dropdown-item command="homeLayout" v-if="route.path === '/portal/index'">
                  <span>主页配置</span>
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <span>退出登录</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>
<!--    <div class="bread" v-if="levelList && levelList.length">-->
<!--      <div class="content" :style="contentWidth()">-->
<!--        <el-breadcrumb class="app-breadcrumb" separator=">">-->
<!--          <transition-group name="breadcrumb">-->
<!--            <el-breadcrumb-item v-for="item in levelList" :key="item.path">-->
<!--              <a v-if="item.feedback" @click.prevent="handleLink(item)">{{ item.title }}</a>-->
<!--              <span v-else class="no-redirect">{{ item.title }}</span>-->
<!--            </el-breadcrumb-item>-->
<!--          </transition-group>-->
<!--        </el-breadcrumb>-->
<!--      </div>-->
<!--    </div>-->
  </div>
  <div id="portal-container" :class="[{ 'special': levelList && levelList.length }, 'comp-container']">
    <router-view v-slot="{ Component, route }">
      <component :is="Component" :key="route.path" />
    </router-view>
    <el-backtop target="#portal-container" :right="100" :bottom="100" />
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
import logo from '@/assets/logo/logo.png'
import MenuItem from './MenuItem'
import Screenfull from '@/components/Screenfull'
import usePermissionStore from '@/store/modules/permission'
import useUserStore from '@/store/modules/user'
import useAppStore from "@/store/modules/app"
import { portalHome } from "@/router";
import defaultAvatar from '@/assets/images/avatar.png'

const title = import.meta.env.VITE_APP_TITLE;
const permissionStore = usePermissionStore()
const userStore = useUserStore()
const appStore = useAppStore()
const route = useRoute();
const router = useRouter();

const constantMenus = [
  {
    permissionId: "1",
    permissionType: "menu",
    openType: "tab",
    permissionName: "首页",
    path: portalHome.path,
    icon: "dashboard",
    permissionSort: 0,
    feedback: "view",
  },
];

const menusData = computed(() => {
  return [...constantMenus, ...permissionStore.portalMenus]
});

const levelList = computed(() => {
  const { indexPath } = route.meta
  const newLevelList = [{ title: '首页', path: portalHome.path, feedback: "view" }]

  if (indexPath && indexPath.length) {
    let tempMenus = permissionStore.portalMenus
    for (let layer = 0; layer < indexPath.length; layer++) {
      const layerIndex = indexPath[layer];
      if (tempMenus && tempMenus[layerIndex]) {
        newLevelList.push({
          title: tempMenus[layerIndex].permissionName,
          path: tempMenus[layerIndex].path,
          feedback: tempMenus[layerIndex].feedback
        })
      }
      tempMenus = tempMenus[layerIndex].children
    }
    return newLevelList
  }

  return []
});


function handleLink(item) {
  const { feedback, path } = item

  if (feedback === 'view') {
    router.push({ path })
  } else if (feedback === 'blank') {
    window.open(path)
  }
}

function handleCommand(command) {
  if (command === 'homeLayout') {
    appStore.openHomeDrawer()
  } else if (command === 'logout') {
    userStore.logOut()
  }
}

const goAdmin = () => {
  router.push({ path: '/system/index' })
}

function contentWidth() {
  if (appStore.homeLayoutSize.width === '100%') {
    return {
      width: '100%'
    }
  }
  return {
    width: '100%',
    maxWidth: appStore.homeLayoutSize.width
  }
}

</script>

<style lang="scss" scoped>
.header {
  position: fixed;
  top: 0px;
  left: 0px;
  z-index: 999;
  width: 100%;
}

.box {
  width: 100%;
  height: 78px;
  display: flex;
  justify-content: center;
  background-color: #ffffff;
  border-bottom: 1px solid #e6e6e6;
}

.logo-box {
  height: 30px;
  width: 250px;
  display: flex;
  align-items: center;
  flex-shrink: 0;

  .logo {
    height: 30px;
    width: 30px;
    margin-right: 10px;
  }

  .title {
    font-size: 20px;
    font-weight: bold;
  }
}

.menu {
  width: calc(100% - 475px);
}

.right-menu {
  height: 60px;
  width: 225px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex-shrink: 0;

  .right-menu-item {
    display: flex;
    margin: 0px 8px;
    font-size: 18px;
    color: #5a5e66;
    align-items: center;

    &.hover-effect {
      cursor: pointer;

      &:hover {
        color: var(--el-menu-active-color);
      }
    }
  }

  .avatar-wrapper {
    height: 36px;
    display: flex;
    align-items: center;

    .user-avatar {
      width: 36px;
      height: 36px;
    }
  }
}

.content {
  width: 1366px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0px 20px;
}

.bread {
  width: 100%;
  height: 35px;
  display: flex;
  justify-content: center;
  background-color: #f6f6f6;
}

.comp-container {
  position: fixed;
  top: 94px;
  width: 100%;
  height: calc(100vh - 94px);
  overflow-y: auto;
}

.special {
  top: 93px;
  height: calc(100vh - 128px);

  .iframe-container {
    height: calc(100vh - 113px);
  }
}

@media screen and (max-width: 768px) {
  .logo-box {
    width: 120px;
  }

  .menu {
    width: calc(100% - 190px);
  }

  .right-menu {
    width: 70px;
  }

  #adminbutton {
    display: none
  }

  #screenfull {
    display: none;
  }
}
</style>
