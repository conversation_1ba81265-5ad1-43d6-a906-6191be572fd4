import request from "@/utils/request";

// 查询分页
export function page(params) {
  return request({
    url: "/user/layout-design",
    method: "get",
    params: params
  });
}

// 查询详细
export function get(data) {
  return request({
    url: "/user/layout-design/get",
    method: "post",
    data: data,
  });
}

export function getEnableLayoutDesign(data) {
  return request({
    url: "/user/layout-design/getEnableLayoutDesign",
    method: "post",
    data: data,
  });
}

// 新增
export function add(data) {
  return request({
    url: "/user/layout-design/add",
    method: "post",
    data: data
  });
}

// 修改
export function update(data) {
  return request({
    url: "/user/layout-design/update",
    method: "post",
    data: data
  });
}

// 删除
// export function del(id) {
// //   return request({
// //     url: "/user/layout-design/" + id,
// //     method: "delete"
// //   });
// // }
export function del(id) {
  return request({
    url: "/user/layout-design/delete/" + id,
    method: "post"
  });
}
