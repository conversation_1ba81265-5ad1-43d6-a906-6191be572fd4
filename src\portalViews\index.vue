<template>
  <div id="1.23452123412415384164.123412415">
    <GridContainer :layout="appStore.homeLayout" :size="appStore.homeLayoutSize" />
    <el-drawer v-model="appStore.showHomeDrawer" title="主页配置" direction="rtl" append-to-body>
      <el-form v-loading="changeLoading">
        <el-form-item label="主题选择：">
          <el-radio-group :model-value="appStore.homeThemeMode" @change="themeChange">
            <el-radio v-for="(theme, index) in Themes" border :label="theme.scope" class="theme-radio">
              {{ theme.name }}
              <el-icon v-if="theme.scope !== 'system_default'" @click="editTheme(theme.scope, $event)" class="edit">
                <Edit />
              </el-icon>
            </el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </el-drawer>
    <el-dialog v-model="editThemeType" :title="dialogTitle" append-to-body destroy-on-close>
      <div class="template-header">
        <div style="display: flex;align-items: center;">
          <template v-if="editThemeType === 'tenant_config'">
            租户选择：
            <el-select v-model="cTenantId" placeholder="选择租户" style="width: 180px" @change="tenantChange">
              <el-option v-for="t in tenantList" :key="t.tenantId" :label="t.tenantName" :value="t.tenantId" />
            </el-select>
          </template>
        </div>
        <el-button v-if="hasEditPermi" type="primary" :icon="Plus" @click="addTemplate">添加模板</el-button>
      </div>
      <el-table :data="templateData" v-loading="editLoading">
        <el-table-column prop="personalId" label="模板ID" />
        <el-table-column prop="staffName" label="创建人" />
        <el-table-column label="模板启用" width="100">
          <template #default="scope">
            <el-checkbox v-if="editThemeType === 'custom'" :model-value="scope.row.configStatus === 0" size="large"
                         @change="activeTemplate(scope.row)" />
            <el-checkbox v-else-if="editThemeType === 'org_config'" :model-value="scope.row.orgConfigStatus === 0"
                         size="large" @change="activeTemplate(scope.row)" />
            <el-checkbox v-else="editThemeType==='tenant_config'" :model-value="scope.row.tenantConfigStatus === 0"
                         size="large" @change="activeTemplate(scope.row)" />
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="200">
          <template #default="scope">
            <el-button v-if="hasDeletePermi" link type="danger" icon="Delete"
                       @click="goDeleteTemplate(scope)">删除</el-button>
            <el-button v-if="hasEditPermi" link type="primary" icon="Edit" @click="editTemplate(scope.row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog></div>

</template>

<script setup name="Index">
import { ref, computed } from 'vue'
import { ElMessageBox, ElMessage, ElRadioGroup, ElRadio } from "element-plus";
import { Plus, Edit } from '@element-plus/icons-vue'
import {
  updateStatus,
  updatePersonalThemeMode,
  deletePersonalId,
  updateTenantStatus,
  addLayout,
  getAllLayoutList,
  updateOrgStatus,
} from "@/api/system/config";
import { getTenants } from "@/api/tenant/tenant";
import { useRouter } from "vue-router";
import useAppStore from "@/store/modules/app";
import useUserStore from "@/store/modules/user";
import { configCode } from "@/grid/config"
import GridContainer from "@/grid/container"
import { checkPermi } from "@/utils/permission"

const appStore = useAppStore()
const userStore = useUserStore()

const router = useRouter();

const Themes = [
  {
    name: '系统默认',
    scope: 'system_default'
  },
  {
    name: '自定义',
    scope: 'custom'
  },
  {
    name: '组织配置',
    scope: 'org_config'
  },
  {
    name: '租户配置',
    scope: 'tenant_config'
  }
]

const changeLoading = ref(false)

const editThemeType = ref(false)

const tenantList = ref([])

const cTenantId = ref(userStore.userInfo.tenantId)

const templateData = ref([])

const editLoading = ref(false)

const dialogTitle = computed(() => {
  if (editThemeType.value === 'custom') {
    return '自定义模板管理'
  }
  if (editThemeType.value === 'org_config') {
    return '组织模板管理'
  }
  if (editThemeType.value === 'tenant_config') {
    return '租户模板管理'
  }
  return '模板管理'
})

const hasEditPermi = computed(() => {
  if (editThemeType.value === 'custom') {
    return true
  }
  if (editThemeType.value === 'org_config' && checkPermi(['index:home:org:edit'])) {
    return true
  }
  if (editThemeType.value === 'tenant_config' && checkPermi(['index:home:tenant:edit'])) {
    return true
  }
  return false
})

const hasDeletePermi = computed(() => {
  if (editThemeType.value === 'custom') {
    return true
  }
  if (editThemeType.value === 'org_config' && checkPermi(['index:home:org:remove'])) {
    return true
  }
  if (editThemeType.value === 'tenant_config' && checkPermi(['index:home:tenant:remove'])) {
    return true
  }
  return false
})

let isModifying = false

const editTheme = (theme, e) => {
  e.preventDefault()
  openTemplate(theme)
}

function openTemplate(theme) {
  if (theme === 'tenant_config') {
    initTenantList()
  }
  updateTemplateData(theme)
  editThemeType.value = theme
}

function updateTemplateData(theme) {
  const params = {
    configScope: theme
  }
  if (theme === 'tenant_config') {
    params.tenantId = cTenantId.value
  }
  getAllLayoutList(params).then((res) => {
    if (res.success && res.data) {
      templateData.value = res.data
    }
    editLoading.value = false
  }).catch(() => {
    editLoading.value = false
  })
}

const themeChange = (theme) => {
  changeLoading.value = true
  const params = {
    configCode: "personal_theme_mode",
    personalValue: theme,
    staffOrgId: userStore.userInfo.staffOrgId,
    tenantId: userStore.userInfo.tenantId
  }
  updatePersonalThemeMode(params).then(async (res) => {
    if (res.success) {
      await appStore.updateHomeDesign()
    }
    changeLoading.value = false
  }).catch((err) => {
    changeLoading.value = false
  })
}

const addTemplate = () => {
  editLoading.value = true
  addLayout({
    configCode,
    configId: "1",
    personalValue: "[]",
    tenantId: userStore.userInfo.tenantId,
    configScope: editThemeType.value,
  }).then((res) => {
    if (res.success) {
      ElMessage.success('添加成功')
      updateTemplateData(editThemeType.value)
    } else {
      ElMessage.error('添加失败')
      editLoading.value = false
    }
  }).catch((err) => {
    ElMessage.error('添加失败')
    editLoading.value = false
  })
}

const tenantChange = () => {
  editLoading.value = true
  updateTemplateData(editThemeType.value)
}

const activeTemplate = (item) => {
  if (item.configStatus === 0 || item.orgConfigStatus === 0 || item.tenantConfigStatus === 0) {
    return
  }
  editLoading.value = true

  let params = {
    configCode: item.configCode,
    configId: item.configId,
    configStatus: 0,
    personalId: item.personalId,
    staffOrgId: item.staffOrgId,
  }
  let request = updateStatus

  if (editThemeType.value === 'org_config') {
    params = {
      configCode: item.configCode,
      tenantId: item.tenantId,
      configId: item.configId,
      orgId: item.orgId,
      orgConfigStatus: 0,
      personalId: item.personalId,
    }
    request = updateOrgStatus
  } else if (editThemeType.value === 'tenant_config') {
    params = {
      configCode: item.configCode,
      tenantId: item.tenantId,
      configId: item.configId,
      tenantConfigStatus: 0,
      personalId: item.personalId,
    }
    request = updateTenantStatus
  }

  request(params).then((res) => {
    if (res.success) {
      ElMessage.success('启用模板成功')
      if (editThemeType.value === appStore.homeThemeMode) {
        appStore.updateHomeDesign()
      }
      updateTemplateData(editThemeType.value)
    } else {
      ElMessage.error('启用模板失败')
      editLoading.value = false
    }
  }).catch((err) => {
    ElMessage.error('启用模板失败')
    editLoading.value = false
  })
}

const goDeleteTemplate = (scope) => {
  if (templateData.value.length === 1) {
    ElMessage.warning('至少需要保留一套模板！')
    return
  }
  ElMessageBox.confirm(
    '是否确认删除此模板配置？',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(() => {
      deleteTemplate(scope)
    })
    .catch(() => {
    })
}

function deleteTemplate(scope) {
  const { row, $index } = scope
  editLoading.value = true
  deletePersonalId(row.personalId).then((res) => {
    if (res.success) {
      ElMessage.success('删除成功')
      if (row.configStatus === 0 || row.orgConfigStatus === 0 || row.tenantConfigStatus === 0) {
        autoActiveTemplate($index)
        templateData.value.splice($index, 1)
      } else {
        updateTemplateData(editThemeType.value)
      }
    } else {
      ElMessage.error('删除失败')
      editLoading.value = false
    }
  }).catch(() => {
    ElMessage.error('删除失败')
    editLoading.value = false
  })
}

function autoActiveTemplate($index) {
  const activeIndex = $index === 0 ? 1 : $index - 1
  const activeItem = templateData.value[activeIndex]
  activeTemplate(activeItem)
}

const editTemplate = (item) => {
  let path = '/home/<USER>/custom'
  let query = { id: item.personalId }

  if (editThemeType.value === 'org_config') {
    path = '/home/<USER>/org'
  } else if (editThemeType.value === 'tenant_config') {
    path = '/home/<USER>/tenant'
    query = { id: item.personalId, tenantId: item.tenantId }
  }
  const routeUrl = router.resolve({
    path,
    query
  });
  isModifying = true
  window.open(routeUrl.href, '_blank');
}

/** 初始化租户数据 */
function initTenantList() {
  getTenants()
    .then((response) => {
      tenantList.value = response.data;
    }).catch((err) => {
      tenantList.value = []
    })
}

document.addEventListener("visibilitychange", () => {
  if (document.visibilityState === 'visible' && isModifying) {
    appStore.updateHomeDesign()
  }
});
//水印

import {setBase64Watermark, setH5Watermark, setWatermark} from "@/utils/warterMark";
import { getPersonalConfig, statSpeed } from "@/api/system/config";
////watermark
onMounted(()=>{
  if (userStore.userInfo.userid){
    getPersonalConfig({
      configCode: "Watermark",
    }).then((r)=>{
      if (r.success){
        try {
          let obj = JSON.parse(r.data.attra);
          Object.keys(userStore.userInfo).map((v) => {
            obj.content = obj.content.replace(`$${v}$`, userStore.userInfo[v]);
          });

          if (obj.status) {
            setWatermark(obj);
            // 移动端可启用
            // setH5Watermark(obj);
          }
        }
        catch (error) { console.error(error)}
      }else {
        ElMessage.error(r.message)
      }
    });
    statSpeed({}).then((r)=>{
      if (r.success){
        try {
          setBase64Watermark(r.data);
        } catch (error) {
          console.error(error)
        }
      }
    })
  }
})
</script>

<style scoped lang="scss">
.app-container {
  width: 100%;
  height: 2000px;
  overflow: hidden;
}

.theme-radio {
  width: 300px;
  margin-bottom: 5px;

  :deep(.el-radio__label) {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .edit {
    font-size: 25px;
  }

  .edit:hover {
    font-size: 32px;
  }
}

.template-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}
</style>
