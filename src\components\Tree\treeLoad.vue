<template>
  <div style="height: 100%">
    <el-input placeholder="输入关键字进行查询" v-model="filterText" clearable style="margin-bottom: 8px" v-if="isShowSearch"
      @clear="handleClearInput">
      <template #append>
        <el-button icon="search" @click="searchFiler(filterText)"></el-button>
      </template>
    </el-input>

    <el-tree highlight-current :default-expanded-keys="defaultExpandedKeys" :default-expand-all="idDefaultExpandAll"
      v-loading="loading" :props="defaultProps" id="tree" ref="tree" :key="treeindex" :lazy="isLazy" :load="loadnode"
      :data="data" :node-key="nodeKey" class="tree-line content" :indent="0" :expand-on-click-node="false"
      :filter-node-method="filterNode">
      <template #default="{ node, data }">
        <span class="custom-tree-node" :data-current="data[nodeKey] === isChooseId">
          <span style="display: flex; align-items: center; width: 100%">
            <!-- 叶子节点 -->
            <img v-if="node.isLeaf" src="@/assets/images/tree/empty.png" style="width: 16px" />

            <!-- 有子节点且已展开 -->
            <img v-else-if="node.expanded" src="@/assets/images/tree/folderGreenOpen.png" style="width: 18px"
              @click="() => openTree(node, data)" />

            <!-- 有子节点未展开 -->
            <img v-else src="@/assets/images/tree/folderBlue.png" style="width: 16px"
              @click="() => openTree(node, data)" />
            <span :style="fontSize(node, data)" :class="data[nodeKey] == isChooseId ? 'nodeLabel-isChoose' : 'nodeLabel'
              " @click="() => handleNodeClick(node, data)">
              {{ data[treeName] }}
            </span>
          </span>
          <span class="custom-tree-node-button">
            <img v-if="treeBtnEdit" src="@/assets/images/tree/editor.png"
              style="width: 14px; height: 14px; margin-left: 6px" @click.stop="() => editNodes(node, data)" />
            <img v-if="treeBtnDelete" src="@/assets/images/tree/delete.png"
              style="width: 14px; height: 16px; margin-left: 6px" @click.stop="() => delNodes(node, data)" />
          </span>
        </span>
      </template>
    </el-tree>
  </div>
</template>

<script>
import { defineComponent, ref, watch } from "vue";

export default defineComponent({
  props: {
    // 保持原有 props 定义不变
    defaultProps: {
      type: Object,
      default: () => ({}),
    },
    treeData: {
      type: Array,
      default: () => [],
    },
    defaultExpandedKeys: {
      type: Array,
      default: () => [],
    },
    defaultExpandAll: {
      type: Boolean,
      default: true,
    },
    treeBtnEdit: {
      type: Boolean,
      default: false,
    },
    treeBtnDelete: {
      type: Boolean,
      default: false,
    },
    nodeKey: {
      type: String,
      default: "id",
    },
    isShowSearch: {
      type: Boolean,
      default: false,
    },
    isLazy: {
      type: Boolean,
      default: false,
    },
    idDefaultExpandAll: {
      type: Boolean,
      default: true,
    },
    treeindex: {
      type: Number,
      default: 0,
    },
    treeName: {
      type: String,
      default: "name",
    },
    // ...其他 props
    defaultSelectedKey: {
      type: [String, Number],
      default: ''
    }
  },
  setup(props, { emit }) {
    const loading = ref(false);
    const data = ref(props.treeData);
    const filterText = ref("");
    const isChooseId = ref("");
    const tree = ref(null);

    watch(
      () => props.treeData,
      (newVal) => {
        data.value = newVal;
      }
    );
    // 监听 treeindex 的变化
    watch(props.treeindex, (newValue) => {
      console.log("treeindex changed:", newValue);
    });

    // 监听默认选中值变化
    // watch(() => props.defaultSelectedKey, (newVal) => {
    //   if (tree.value) {
    //     // 
    //     let currentNode =
    //       tree.value && tree.value.getNode(newVal);
    //     // 触发高亮节点的点击事件，吧这个标签的详情展示在右边
       
    //     isChooseId.value = newVal;
    //     nextTick(() => {
    //       tree.value.setCurrentKey(newVal)
    //       handleNodeClick(currentNode,currentNode.data)
    //     })
    //   }
    // });

    const searchFiler = (val) => {
      const searchValue = val?.trim() || "";
      tree.value?.filter(searchValue);
      // 如果是懒加载模式且搜索值为空，可以重新加载根节点
      if (props.isLazy && !searchValue) {
        data.value = [];
        nextTick(() => {
          tree.value?.load([]); // 触发重新加载
        });
      }
    };

    const handleClearInput = () => {
      if (props.isLazy) {
        data.value = [];
        nextTick(() => {
          tree.value?.load([]); // 触发重新加载
        });
      } else {
        const searchValue = filterText.value?.trim() || "";
        tree.value?.filter(searchValue);
      }
    };

    const filterNode = (value, data) => {
      if (!value) return true;
      return data[props.treeName].includes(value);
    };

    const fontSize = () => {
      // return "font-size:13px";
    };
    const handleNodeClick = (node, data) => {
      console.log(node)
      if (props.isLazy) {
        node.loaded = false;
        node.expand();
      }
   
      isChooseId.value = data[props.nodeKey];
      emit("checkedKeys", data, node);
    };

    const openTree = (node, data) => {
      // node.expanded = !node.expanded;
      if (props.isLazy && !node.isLeaf && !node.loaded) {
        // 触发懒加载
        node.loadData((children) => {
          // 加载完成后自动展开
          node.expanded = true;
        });
      } else {
        // 直接切换展开状态
        node.expanded = !node.expanded;
      }
    };

    const loadnode = (node, resolve) => {
      loading.value = true;
      if (node.level === 0) {
        emit("loadFirstNodeFather", resolve);
      }
      if (node.level >= 1) {
        emit("loadChildNodeFather", node, resolve);
      }
      loading.value = false;
    };

    const editNodes = (node, data) => {
      emit("editNodes", node, data);
    };

    const delNodes = (node, data) => {
      emit("delNodes", node, data);
    };

    const setCurrentKeyFun = () => {
      tree.value.setCurrentKey(null);
    };

    const reloadTree = () => {
      if (isChooseId.value) {
        const node = tree.value.getNode(isChooseId.value);
        if (node) {
          // 清空子节点并标记为未加载
          node.childNodes = [];
          node.loaded = false;
          // 重新展开节点以触发懒加载
          node.expand();
        }
      }
    };

    return {
      loading,
      data,
      filterText,
      isChooseId,
      tree,
      searchFiler,
      filterNode,
      handleNodeClick,
      fontSize,
      openTree,
      loadnode,
      editNodes,
      delNodes,
      setCurrentKeyFun,
      reloadTree,
      handleClearInput,
    };
  },
});
</script>

<style lang="scss" scoped>
/* 保持原有样式不变，注意深度选择器修改 */
:deep(.el-tree-node) {
  position: relative;
  padding-left: 14px;
}

:deep(.el-tree-node__children) {
  padding-left: 14px;
  transition: all 0.5s;
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;

  .custom-tree-node-button {
    display: none;
  }
}

.custom-tree-node:hover .custom-tree-node-button {
  display: block;
  background-color: rgb(245, 247, 250);
  padding: 0 5px;
  position: absolute;
  right: 0px;
  z-index: 666;
}

.custom-tree-node i {
  margin-left: 8px;
}

.nodeLabel {
  margin-left: 8px;
  flex: 1;
  font-family: SYPXZT;
  font-size: 13px;
}

.nodeLabel-isChoose {
  margin-left: 8px;
  color: #1979d8;
}

.dialog-title {
  color: #222222;
  font-weight: 500;
  font-size: 20px;
  display: flex;
  align-items: center;

  span {
    margin-left: 10px;
  }
}

.dialog-content {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 20vh;
  font-size: 24px;
}
</style>



<style lang="scss">
// 以下为scss，记得去掉scoped，或者使用/deep/
.tree-line {
  .el-tree-node {
    position: relative;
    padding-left: 14px; // 缩进量
  }

  .el-tree-node__children {
    padding-left: 14px; // 缩进量
    transition: all 0.5s; // 关键动画！！！
  }

  // 竖线
  .el-tree-node::before {
    content: "";
    height: 100%;
    width: 1px;
    position: absolute;
    left: -3px;
    top: -26px;
    border-width: 1px;
    border-left: 1px solid #e9e9e9;
  }

  // 当前层最后一个节点的竖线高度固定
  .el-tree-node:last-child::before {
    height: 48px; // 可以自己调节到合适数值
  }

  // 横线
  .el-tree-node::after {
    content: "";
    width: 18px;
    height: 20px;
    position: absolute;
    left: -3px;
    top: 20px;
    border-width: 1px;
    border-top: 1px solid #e9e9e9;
  }

  // 去掉最顶层的虚线，放最下面样式才不会被上面的覆盖了
  &>.el-tree-node::after {
    border-top: none;
  }

  &>.el-tree-node::before {
    border-left: none;
  }

  // 展开关闭的icon
  .el-tree-node__expand-icon {
    font-size: 16px;
    display: none; // 也可以去掉

    // 叶子节点（无子节点）
    &.is-leaf {
      color: transparent;
      display: none; // 也可以去掉
    }
  }

  .el-tree-node__content {
    height: 40px;
  }

  .el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
    background-color: #f0f7ff;
    border: 1px solid #f0f7ff;
  }

  .el-tree-node__content:hover {
    background-color: #f0f7ff;
  }
}

.content {
  overflow: hidden;
  overflow-y: scroll;
  height: calc(100vh - 200px);
}

#portal-container .content {
  height: calc(100vh - 235px);
}

.el-tabs .el-tabs__content .content {
  height: calc(100vh - 215px);
}

#portal-container .el-tabs .el-tabs__content .content {
  height: calc(100vh - 275px);
}

#portal-container .treeLoadHight .content {
  height: calc(100vh - 260px);
}

.content::-webkit-scrollbar {
  // display: none;
  width: 2px;
  height: 1px;
}

.content::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 10px;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: rgba(0, 0, 0, 0.02);
}

.content::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  // box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  background: transparent;
}

@media (max-width: 1680px) and (min-width: 1442px) {
  .nodeLabel {
    font-size: 12px !important;
  }

  .custom-tree-node img {
    width: 14px !important;
  }

  .tree-line .el-tree-node__content {
    height: 35px;
  }
}




</style>s