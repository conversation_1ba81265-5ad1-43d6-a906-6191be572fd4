<!-- 封装table search -->
<template>
  <div class="public-box">
    <div class="dialog-search" :key="indexId" v-if="isShowSearch">
      <div class="dialog-search-top" ref="dialogSearchFatherTop">
        <div
          class="dialog-search-left"
          ref="dialogSearchTop"
          :class="'formRowNumber_' + formRowNumber"
        >
          <slot name="formList"></slot>
        </div>
        <div
          class="dialog-search-right search-commom-color"
          :style="{
            width: dialogSearchRightWidth,
          }"
        >
          <div
            class="searchList vertical-btn"
            :class="btnNum == '1' ? 'vertical-btnBtn1' : ''"
          >
            <slot name="searchList" v-if="isShowMainBtn"></slot>
            <el-button
              v-if="isExpend && !isExpendShow"
              type="primary"
              plain
              icon="ArrowDown"
              size="small"
              @click="openSearchBtn"
              >展开</el-button
            >

            <el-button
              v-if="isExpend && isExpendShow"
              type="primary"
              plain
              icon="ArrowDown"
              size="small"
              @click="closeSearchBtn"
              >收起</el-button
            >
          </div>
        </div>
      </div>
    </div>

    <div class="search-common-btn-list">
      <div class="searchBtnList search-commom-color">
        <slot name="searchBtnList"></slot>
      </div>
      <div class="rightToolbar" v-if="isShowRightBtn">
        <right-toolbar
          v-model:showSearch="isShowSearch"
          @queryTable="getList"
          :columns="columns"
          :tableBtnShow="tableBtnShow"
          v-model:isTable="isTable"
          @update:isTable="handleIsTableChange"
          :pageSizes="pageSizes"
        ></right-toolbar>
      </div>
    </div>
  </div>
</template>
  
  <script setup>
import { ref, computed, onMounted, onBeforeUnmount } from "vue";

const props = defineProps({
  columns: {
    type: Array,
    default: () => null,
  },
  isExpend: {
    type: Boolean,
    default: false,
  },
  isExpendShow: {
    type: Boolean,
    default: true,
  },
  isShowMainBtn: {
    type: Boolean,
    default: true,
  },
  isShowRightBtn: {
    type: Boolean,
    default: true,
  },

  isShowSearchFather: {
    type: Boolean,
    default: true,
  },

  formRowNumber: {
    type: Number,
    default: 3,
  },
  isTable: {
    type: Boolean,
    default: true,
  },
  tableBtnShow: {
    type: Boolean,
    default: false,
  },

  pageSizes: {
    type: Array,
    default() {
      return [10, 20, 30, 50];
    },
   
  },

  weightChange: {
      type: Boolean,
      default: false,
    },
});
const emits = defineEmits(["getList", "update:isTable"]);
const isTable = ref(props.isTable); // 本地状态
const indexId = ref(0);
const isShowSearch = ref(true);
const btnNum = ref(3);
const dialogSearchFatherTop = ref(null);
watch(isShowSearch, (newValue) => {
  if (newValue && props.isExpend && !props.isExpendShow) {
    props.isShowMainBtn = true;
    props.isExpendShow = true;
  }
});

onMounted(() => {
  isShowSearch.value = JSON.parse(JSON.stringify(props.isShowSearchFather));
  indexId.value++;
  $_initResizeEvent();
  resizeView();
});

onBeforeUnmount(() => {
  window.removeEventListener("resize", handleResize);
  // PubSub.clearAllSubscriptions();
});
// 监听子组件 right-toolbar 的 isTable 变化
const handleIsTableChange = (value) => {
  isTable.value = value;
  emits("update:isTable", value); // 通知上层
};
const dialogSearchRightWidth = computed(() => {
  if (btnNum.value == 1) {
    return "180px";
  } else {
    return "80px";
  }
});

const resizeView = () => {
  var parent = "";
  var child = "";
  var height = 0;
  var forms = "";
  nextTick(() => {
    parent = document.querySelector(".dialog-search-left");
    child = parent.children[0];
    forms = child.children;
    height = child.clientHeight;
    btnNum.value = forms.length;
    if (props.weightChange) {
      props.isExpend = false;
      props.isShowMainBtn = true;
      dialogSearchFatherTop.value.style.height = "auto";
      dialogSearchFatherTop.value.style.overflow = "visible";
      btnNum.value = 1;
      return
    }
    if (height > 92) {
      props.isExpend = true;
      btnNum.value = 3;
    } else if (height > 40 && height <= 92) {
      props.isExpend = false;
      props.isShowMainBtn = true;
      dialogSearchFatherTop.value.style.height = "auto";
      dialogSearchFatherTop.value.style.overflow = "visible";
      btnNum.value = 2;
    } else {
      props.isExpend = false;
      props.isShowMainBtn = true;
      dialogSearchFatherTop.value.style.height = "auto";
      dialogSearchFatherTop.value.style.overflow = "visible";
      btnNum.value = 1;
    }
  });
};

const closeSearchBtn = () => {
  props.isExpendShow = !props.isExpendShow;
  props.isShowMainBtn = false;
  dialogSearchFatherTop.value.style.height = "40px";
  dialogSearchFatherTop.value.style.overflow = "hidden";
};

const openSearchBtn = () => {
  props.isExpendShow = !props.isExpendShow;
  props.isShowMainBtn = true;
  dialogSearchFatherTop.value.style.height = "auto";
  dialogSearchFatherTop.value.style.overflow = "visible";
};

const $_initResizeEvent = () => {
  window.addEventListener("resize", handleResize, true);
};

const handleResize = () => {
  resizeView();
};

const getList = () => {
  emits("getList");
};

const handleDrop = () => {
  //订阅消息
  // PubSub.publish('handleDrop')
};
</script>
  
  
  <style lang="scss" scoped>
.public-box {
  background: #fff;
}
.dialog-search-left {
  border-right: 1px solid #e5e5e5;
  padding-right: 10px;
  margin-right: 20px;
}

.dialog-search-top {
  padding-bottom: 15px;
}

:deep(.dialog-search .el-form-item) {
  margin-bottom: 0px !important;
  margin-top: 15px;
}

// 一行 1 个
:deep(.dialog-search .el-form-item:nth-child(1)) {
  margin-top: 0px;
}
// 一行 2 个
:deep(.dialog-search .formRowNumber_2 .el-form-item:nth-child(2)) {
  margin-top: 0px;
}

// 一行 3 个
:deep(.dialog-search .formRowNumber_3 .el-form-item:nth-child(2)) {
  margin-top: 0px;
}

::v-deep(.dialog-search .formRowNumber_3 .el-form-item:nth-child(3)) {
  margin-top: 0px;
}
// 一行 4 个
:deep(.dialog-search .formRowNumber_4 .el-form-item:nth-child(2)) {
  margin-top: 0px;
}

:deep(.dialog-search .formRowNumber_4 .el-form-item:nth-child(3)) {
  margin-top: 0px;
}

:deep(.dialog-search .formRowNumber_4 .el-form-item:nth-child(4)) {
  margin-top: 0px;
}

// 一行 5 个
:deep(.dialog-search .formRowNumber_5 .el-form-item:nth-child(2)) {
  margin-top: 0px;
}

:deep(.dialog-search .formRowNumber_5 .el-form-item:nth-child(3)) {
  margin-top: 0px;
}

:deep(.dialog-search .formRowNumber_5 .el-form-item:nth-child(4)) {
  margin-top: 0px;
}

:deep(.dialog-search .formRowNumber_5 .el-form-item:nth-child(5)) {
  margin-top: 0px;
}

// 一行 6 个
:deep(.dialog-search .formRowNumber_6 .el-form-item:nth-child(2)) {
  margin-top: 0px;
}

:deep(.dialog-search .formRowNumber_6 .el-form-item:nth-child(3)) {
  margin-top: 0px;
}

:deep(.dialog-search .formRowNumber_6 .el-form-item:nth-child(4)) {
  margin-top: 0px;
}

:deep(.dialog-search .formRowNumber_6 .el-form-item:nth-child(5)) {
  margin-top: 0px;
}
:deep(.dialog-search .formRowNumber_6 .el-form-item:nth-child(6)) {
  margin-top: 0px;
}

// 一行 7 个
:deep(.dialog-search .formRowNumber_7 .el-form-item:nth-child(2)) {
  margin-top: 0px;
}

:deep(.dialog-search .formRowNumber_7 .el-form-item:nth-child(3)) {
  margin-top: 0px;
}

:deep(.dialog-search .formRowNumber_7 .el-form-item:nth-child(4)) {
  margin-top: 0px;
}

:deep(.dialog-search .formRowNumber_7 .el-form-item:nth-child(5)) {
  margin-top: 0px;
}
:deep(.dialog-search .formRowNumber_7 .el-form-item:nth-child(6)) {
  margin-top: 0px;
}
:deep(.dialog-search .formRowNumber_7 .el-form-item:nth-child(7)) {
  margin-top: 0px;
}

:deep(.dialog-search-left .el-input--medium .el-input__inner) {
  height: 32px;
  line-height: 32px;
}

:deep(.dialog-search .formRowNumber_1 .el-form--inline .el-form-item) {
  width: calc((100% - 10px) / 1);
  display: flex;
  align-items: center;
}

:deep(.dialog-search .formRowNumber_2 .el-form--inline .el-form-item) {
  width: calc((100% - 20px) / 2);
  display: flex;
  align-items: center;
}

:deep(.dialog-search .formRowNumber_3 .el-form--inline .el-form-item) {
  width: calc((100% - 30px) / 3);
  display: flex;
  align-items: center;
}

:deep(.dialog-search .formRowNumber_4 .el-form--inline .el-form-item) {
  width: calc((100% - 40px) / 4);
  display: flex;
  align-items: center;
}

:deep(.dialog-search .formRowNumber_5 .el-form--inline .el-form-item) {
  width: calc((100% - 50px) / 5);
  display: flex;
  align-items: center;
}

:deep(.dialog-search .formRowNumber_6 .el-form--inline .el-form-item) {
  width: calc((100% - 60px) / 6);
  display: flex;
  align-items: center;
}

:deep(.dialog-search .formRowNumber_7 .el-form--inline .el-form-item) {
  width: calc((100% - 70px) / 7);
  display: flex;
  align-items: center;
}

:deep(.dialog-search .el-form--inline .el-form-item) {
  margin-right: 10px;
  margin-bottom: 8px;
  vertical-align: top;
}

:deep(.dialog-search .el-form-item__content) {
  flex: 1;
}
:deep(.dialog-search .el-form) {
  display: flex;
  flex-wrap: wrap;
}
:deep(.dialog-search .el-button--primary.is-plain.is-disabled),
:deep(.dialog-search .el-button--primary.is-plain.is-disabled:active),
:deep(.dialog-search .el-button--primary.is-plain.is-disabled:focus),
:deep(.dialog-search .el-button--primary.is-plain.is-disabled:hover) {
  color: #bcbec2;
  background-color: #f4f4f5;
  border-color: #e9e9eb;
}

:deep(.dialog-search .el-date-editor.el-input),
:deep(.dialog-search .el-date-editor.el-input__inner) {
  width: 100%;
}

:deep(.dialog-search) {
  // background: #f7f9fa;
  padding: 10px;
  padding-bottom: 0px;
  padding-top: 0px;
  padding-right: 0px;
}
.dialog-search-top {
  display: flex;
  justify-content: flex-end;
  transition: height 0.5s;
  border-bottom: 1px solid #e5e5e5;
}

.dialog-search-bottom {
  padding-top: 5px;
  padding-bottom: 15px;
  position: relative;
}

.dialog-search .dialog-search-left {
  flex: 1;
}

.dialog-search .dialog-search-right {
  position: relative;
  // z-index: 10;
}
.dialog-search .noShowRightToolbar {
  min-height: 30px !important;
  top: 5px;
}

.dialog-search .searchList {
  display: flex;
  position: relative;
  flex-wrap: wrap;
}

.vertical-flex-btn {
  justify-content: space-between;
}

.vertical-flex-btn .el-button {
  margin-left: 0px !important;
  width: 80px;
  display: flex;
  align-items: center;
}

.vertical-flex-btn .el-button span {
  flex: 1;
  display: block;
  overflow: hidden; //超出的文本隐藏
  text-overflow: ellipsis; //用省略号显示
  white-space: nowrap; //不换行（文字不允许换行，单行文本
}

.rightToolbar {
  display: flex;
  justify-content: flex-end;
  position: absolute;
  bottom: 8px;
  right: 0px;
}

.rightToolbar .top-right-btn {
  float: none;
}

.vertical-btn {
  display: block;
}

:deep(.vertical-btn button) {
  display: block !important;
  margin-left: 0px !important;
  height: 32px;
}

.vertical-btnBtn1 {
  display: flex;
  align-items: center;
}

:deep(.vertical-btn button:not(:last-child)) {
  margin-bottom: 15px;
}

:deep(.vertical-btnBtn1 button) {
  margin-bottom: 0px !important;
}

:deep(.vertical-btnBtn1 button:not(:last-child)) {
  margin-right: 15px !important;
}

:deep(.vertical-flex-btn button:first-child) {
  margin-top: 0px;
}

:deep(.vertical-flex-btn button:nth-child(2)) {
  margin-top: 0px;
}

:deep(.vertical-flex-btn button) {
  margin-top: 8px;
}
.search-common-btn-list {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-top: 12px;
  padding-bottom: 12px;
}
.search-common-btn-list .searchBtnList {
  flex: 1;
}

:deep(.searchBtnList button) {
  height: 32px !important;
  border-radius: 4px;
}
// ::v-deep .searchBtnList button:first-child {
//   background: #c20000;
//   color: #ffffff;
// }

.search-common-btn-list {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.search-common-btn-list .rightToolbar {
  position: static;
  right: 15px;
  bottom: 5px;
}

:deep(.table-popver-btn .el-icon-more) {
  color: #4794e0;
  margin-left: 10px;
  cursor: pointer;
}

.table-popver {
  min-width: auto !important;
  padding: 8px 20px !important;
}

.table-popver .el-button {
  font-size: 16px !important;
}

.search-popver-btn {
  margin-top: 8px;
}
.table-common .rightToolbar {
  position: static;
}
.table-common .el-table .el-button {
  padding: 0px 5px;
}

.table-common .el-table .el-button + .el-button {
  margin-left: 0px;
  padding: 0px 5px;
}
// .table-common .el-table__body-wrapper .el-table__cell .cell {
//   color: #606266;
// }
.table-common .click-text {
  color: #c20000;
  cursor: pointer;
}

:deep(.dialog-search .el-form-item__label) {
  color: #595959 !important;
  font-weight: 400 !important;
  font-family: SYPXZT;
  font-size: 14px;
}
:deep(.dialog-search .el-input__inner) {
  color: #282828 !important;
  font-weight: 400 !important;
  font-family: SYPXZT;
  font-size: 14px;
}

:deep(.dialog-search .el-select__input) {
  color: #282828 !important;
  font-weight: 400 !important;
  font-family: SYPXZT;
  font-size: 14px;
}

@media screen and (max-width: 768px) {
  :deep(.dialog-search .dialog-search-left .el-form-item) {
    width: 100% !important;
  }
}
</style>
  