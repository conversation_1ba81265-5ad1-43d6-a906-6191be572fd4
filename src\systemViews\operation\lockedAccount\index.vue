<template>
  <div class="app-container">
    <el-card style="height: 100%" shadow="never">
      <el-form ref="queryRef" :inline="true" v-show="showSearch">
        <el-form-item label="租户" prop="tenantId" v-if="userStore.userInfo.customParam.userType === 'admin'" >
          <el-select v-model="queryParams.tenantId" placeholder="请选择租户" style="width: 180px" @change="handleQuery">
            <el-option v-for="item in tenantList" :key="item.tenantId" :label="item.tenantName" :value="item.tenantId"/>
          </el-select>
        </el-form-item>
        <el-form-item label="登录账号" prop="loginName">
          <el-input v-model="queryParams.loginName" placeholder="请输入登录账号" clearable style="width: 180px;"
                    @keyup.enter="handleQuery"/>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search"  @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <el-table v-loading="loading" :data="lockedAccountList" >
        <el-table-column label="序号" type="index" width="50" align="center"/>
        <el-table-column label="租户" align="center" prop="user.customParam.tenantName" :show-overflow-tooltip="true"  />
        <el-table-column label="登录账号" align="center" prop="user.loginName" :show-overflow-tooltip="true" width="180"/>
        <el-table-column label="所在组织" align="center" prop="user.orgName" :show-overflow-tooltip="true" />
        <el-table-column label="用户姓名" align="center" prop="user.staffName" :show-overflow-tooltip="true" width="180"/>
        <el-table-column label="登录失败次数" align="center" prop="errorNumber" :show-overflow-tooltip="true" width="180"/>
        <el-table-column label="解锁剩余时间" align="center" prop="expire" :show-overflow-tooltip="true" width="180"/>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right" width="100">
          <template #default="scope">
            <el-button link type="primary" icon="Key" @click="unlockUserLogin(scope.row)"
                       v-if="scope.row.tenantId === userStore.userInfo.customParam.tenantId || userStore.userInfo.customParam.userType === 'admin'"
            >解锁</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup name="LockedAccount">
import {getLockedAccountList, unLockAccount} from "@/api/operation/lockedAccount";
import useUserStore from '@/store/modules/user';
import {getTenants} from "@/api/tenant/tenant";

const { proxy } = getCurrentInstance();
const showSearch = ref(true);

/** 初始化租户数据 */
const userStore = useUserStore();
const tenantList = ref([]);
function getTenantList() {
  getTenants().then(res => {
    tenantList.value = res.data;
  }).catch(() => {
    tenantList.value = [];
  })
}

/** 表格数据显示 */
const lockedAccountList = ref([]);
const loading = ref(true);
const queryRef = ref(null)
const queryParams = ref({
  tenantId: userStore.userInfo.customParam.tenantId,
  loginName: ""
})
function getList() {
  loading.value = true;
  getLockedAccountList(queryParams.value).then(res => {
    if (res.success) {
      lockedAccountList.value = res.data;
    }
    loading.value = false;
  }).catch(() => {
    loading.value = false;
  })
}

/** 搜索按钮操作 */
const handleQuery = () => {
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 解锁按钮操作 */
const unlockUserLogin = (row) => {
  proxy.$modal.confirm('此操作将解锁（'+row.user.staffName+'）登录等待时间，是否继续？').then(() => {
    unLockAccount(row.redisKey).then(res => {
      if(res.data) {
        proxy.$modal.msgSuccess("解锁成功！")
      } else {
        proxy.$modal.msgError("解锁失败！")
      }
      getList();
    }).catch(() => {
      proxy.$modal.msg("操作已取消")
    })
  })
}

getTenantList();
getList();
</script>
