<template>
  <div
    style="min-height: 500px"
    class="dialog-box"
    :class="popupType === 'view' ? 'dialog-box-view' : 'dialog-box-edit'"
  >
    <el-form
      ref="ruleform"
      :model="formData"
      :rules="rules"
      inline
      label-width="80px"
    >
      <!-- 原有模板结构保持不变 -->
      <el-row>
        <el-col :span="10">
          <el-form-item label="有效日期">
            <el-date-picker
              v-model="formData.expirationTime"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD 23:59:59"
              type="date"
              :disabled-date="disabledDate"
              placeholder="默认长期有效"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-row>
      <Splitpanes class="default-theme">
        <Pane :size="35" :min-size="10">
          <el-card class="dep-card dep-card1">
            <el-form
              ref="ruleform"
              :model="formData"
              :rules="rules"
              inline
              label-width="60px"
            >
              <el-form-item label="停车场">
                <el-tree
                  default-expand-all
                  style="margin: 20px auto"
                  :data="parkingOptions"
                  show-checkbox
                  node-key="id"
                  ref="deptTreeRef"
                  :props="parkingProps"
                />
              </el-form-item>
            </el-form>
          </el-card>
        </Pane>
        <Pane :size="65" :min-size="30">
          <el-card class="dep-card dep-card2">
            <el-form
              ref="ruleform"
              :model="formData"
              :rules="rules"
              inline
              label-width="80px"
            >
              <!-- 原有模板结构保持不变 -->

              <el-row>
                <el-col :span="15">
                  <el-form-item label="租户" prop="tenantId" class="otherLength">
                    <TenantSelect v-model="tenantId"></TenantSelect>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="15">
                  <el-form-item label="车牌号码" prop="nameList" style="width: 100%;">
                    <el-select
                      v-model="selectedPlateNo"
                      class="select-width"
                      multiple
                      collapse-tags
                      filterable
                      remote
                      reserve-keyword
                      placeholder="请输入车牌号进行选择"
                      :remote-method="getPlantNoList"
                      @change="handlePlateNoChange"
                    >
                      <el-option
                        v-for="item in plateNoList"
                        :key="item.plateNo"
                        :label="`${item.plateNo} (${item.nickName}/${item.deptName})`"
                        :value="item.plateNo"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
            <el-table :data="plantNoInfo" border style="width: 95%" max-height="300px">
              <el-table-column
                type="index"
                label="#"
                width="50"
                align="center"
                sortable
              />
              <template v-for="(item, index2) in userColumns" :key="item.prop">
                <el-table-column
                  show-overflow-tooltip
                  :prop="item.prop"
                  align="center"
                  :label="item.label"
                  :min-width="item.minWidth"
                  :resizable="item.resizable"
                >
                  <template #default="scope">
                    <template v-if="item.prop === 'vehicleType'">
                      {{
                        formatVehicleType(
                          scope.row.vehicleType,
                          parking_rule_vehicle_type
                        )
                      }}
                    </template>
                    <template v-else>
                      {{ scope.row[item.prop] }}
                    </template>
                  </template>
                </el-table-column>
              </template>
              <el-table-column label="操作" align="center" fixed="right">
                <template #default="scope">
                  <el-button
                    size="mini"
                    type="text"
                    icon="Delete"
                    @click="handleDelete(scope.row)"
                  />
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </Pane>
      </Splitpanes>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import useUserStore from "@/store/modules/user";
import { ElMessage } from "element-plus";
import {
  findparkinglot,
  addNumberAccredit,
  selectPlantNoList,
} from "@/api/majorParking/accredit/accredit.js";
const { proxy } = getCurrentInstance();
const userStore = useUserStore();
// 响应式数据
const formData = reactive({
  expirationTime: "",
  parkingIds: [],
  plateNoList: [],

});
// 定义组件的事件
const emit = defineEmits(["close"]);
const tenantId  = ref(userStore.userInfo.tenantId)

const parkingOptions = ref([]);
const plateNoList = ref([]);
const plantNoInfo = ref([]);
const selectedPlateNo = ref([]);
const deptTreeRef = ref(null);

const { parking_rule_vehicle_type } = proxy.useDict(
  "parking_rule_vehicle_type"
);

// 表格列配置（必须声明）
const userColumns = ref([
  {
    prop: "plateNo",
    label: "车牌号码",
    minWidth: "80px",
    resizable: true,
  },
  {
    prop: "vehicleType",
    label: "车辆类型",
    minWidth: "80px",
    resizable: true,
  },
  {
    prop: "nickName",
    label: "人员姓名",
    minWidth: "80px",
    resizable: true,
  },
  {
    prop: "deptName",
    label: "人员部门",
    minWidth: "90px",
    resizable: true,
  },
]);
// 生命周期钩子
onMounted(async () => {
  await getParking();
});

// 方法声明
const disabledDate = (time) => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  return time.getTime() < today.getTime();
};

const getParking = async () => {
  try {
    const res = await findparkinglot({ status: "0" });
    parkingOptions.value = res.data;
  } catch (error) {
    ElMessage.error("获取停车场失败");
  }
};

const getPlantNoList = async (query) => {
  if (!query) return;
  try {
    const res = await selectPlantNoList({
      accreditStatus: "0",
      plateNo: query,
      tenantId: tenantId.value
    });
    plateNoList.value = res.data;
  } catch (error) {
    ElMessage.error("获取车牌列表失败");
  }
};

const handlePlateNoChange = (values) => {
  plantNoInfo.value = plateNoList.value.filter((item) =>
    values.includes(item.plateNo)
  );
};

const handleDelete = (row) => {
  plantNoInfo.value = plantNoInfo.value.filter(
    (item) => item.plateNo !== row.plateNo
  );
  selectedPlateNo.value = selectedPlateNo.value.filter(
    (v) => v !== row.plateNo
  );
};

// 格式化方法代替过滤器
const formatVehicleType = (value, parkingRuleVehicleType) => {
  const type = parkingRuleVehicleType.find((item) => item.value === value);
  return type?.label || "";
};

const saveBtn = async () => {
  // 获取选中的节点
  const checkedKeys = deptTreeRef.value.getCheckedKeys();

  // 获取所有父级节点
  const parentKeys = [];
  checkedKeys.forEach((key) => {
    const parents = getParentKeys(parkingOptions.value, key);
    if (parents) {
      parentKeys.push(...parents);
    }
  });

  // 合并选中节点和父级节点
  formData.parkingIds = [...new Set([...checkedKeys, ...parentKeys])]; // 去重
  formData.plateNoList = plantNoInfo.value;

  // 校验是否选择了停车场
  if (!formData.parkingIds.length) {
    ElMessage.warning("请选择停车场");
    return;
  }

  try {
    const res = await addNumberAccredit(formData);
    if (res.code == '1') {
      ElMessage.success(res.message);
      emit("close");
    }
  } catch (error) {
    ElMessage.error("保存失败");
  }
};

// 递归获取父级节点
const getParentKeys = (treeData, key, path = []) => {
  for (const node of treeData) {
    if (node.id === key) {
      return path;
    }
    if (node.children) {
      const result = getParentKeys(node.children, key, [...path, node.id]);
      if (result) return result;
    }
  }
  return null;
};
// const saveBtn = async () => {

//   formData.parkingIds = deptTreeRef.value.getCheckedKeys();
//   formData.plateNoList = plantNoInfo.value;

//   if (!formData.parkingIds.length) {
//     ElMessage.warning("请选择停车场");
//     return;
//   }

//   try {
//     const res = await addNumberAccredit(formData);
//     if (res.code == '1') {
//       ElMessage.success(res.message);
//       emit("close");
//     }
//   } catch (error) {
//     // ElMessage.error("保存失败");
//   }
// };
defineExpose({
  saveBtn,
});
</script>

<style scoped>
.otherLength{
  width: 99%;
}

.dep-card1{
  height: calc(100vh - 400px);
  overflow-y: auto;
}
.dep-card2{
  height: calc(100vh - 400px);
  overflow-y: auto;
}
.select-width{
  /* width: 350px; */
}
</style>
