<!-- 借调人员详情 -->
<template>
    <div class="container-table-box-p">
      <Splitpanes class="default-theme">
        <Pane :size="100" :min-size="65">
          <el-card class="dep-card">
            <dialog-search
              @getList="getList"
              formRowNumber="4"
              :columns="tabelForm.columns"
            >
              <template v-slot:formList>
                <el-form
                  :model="queryParams"
                  ref="queryForm"
                  :inline="true"
                  label-width="75px"
                >
                  <el-form-item label="员工姓名">
                    <el-input
                      v-model="queryParams.staffName"
                      placeholder="请输入员工姓名"
                      clearable
                    ></el-input>
                  </el-form-item>
                  <el-form-item label="人员类型">
                    <el-select v-model="queryParams.staffType" placeholder="借调人员" disabled>
                      <el-option
                        v-for="item in staff_type"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-form>
              </template>
              <template v-slot:searchList>
                <el-button type="primary" icon="Search" @click="handleQuery"
                  >搜索</el-button
                >
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              </template>
  
              <template v-slot:searchBtnList>
                <el-button type="primary" size="mini" icon="Download" @click="handleExport" v-hasPermi="['sys:base:user:export']">导出</el-button>
              </template>
            </dialog-search>
  
            <public-table
              ref="publictable"
              :rowKey="tabelForm.tableKey"
              :tableData="list"
              :columns="tabelForm.columns"
              :configFlag="tabelForm.tableConfig"
              :pageValue="pageParams"
              :total="total"
              :getList="getList"
            >
            </public-table>
          </el-card>
        </Pane>
      </Splitpanes>
    </div>
  </template>
      
<script setup name="loanStaffDetail">
import { ElMessage, ElMessageBox } from "element-plus";
import { ref, reactive, getCurrentInstance, onMounted } from "vue";
import { listLoanSysUser } from "@/api/system/user";
import { apiUrl } from "@/utils/config";
import { formatMinuteTime } from "@/utils";
const { proxy } = getCurrentInstance();
const { staff_type } = proxy.useDict(
  "staff_type"
);
const props = defineProps({
closeBtn: {
  type: Function,
  default: () => {},
},
popupType: {
  type: String,
  default: "",
},
rowData: {
  type: Object,
  default: () => ({}),
},
});

const pageParams = ref({
  pageNum: 1,
  pageSize: 10,
});
const queryParams = ref({
  staffName: '',
  staffType: '2' // 锁定为借调人员
});
const list = ref([{}]);
const total = ref(0);
const tabelForm = reactive({
tableKey: "1", //表格key值
isShowRightToolbar: true, //是否显示右侧显示和隐藏
showSearch: true,
columns: [
{
    fieldIndex: "staffName",
    label: "员工姓名",
    show: true,
    sortable: true,
    visible: true, // 展示与隐藏
    minWidth: "120px",
  },
  {
    fieldIndex: "loginName",
    label: "登录账号",
    show: true,
    sortable: true,
    visible: true, // 展示与隐藏
    minWidth: "120px",
  },
  {
    fieldIndex: "cellphone",
    label: "联系方式",
    show: true,
    sortable: true,
    visible: true, // 展示与隐藏
    minWidth: "120px",
  },
  {
    fieldIndex: "orgName",
    label: "员工部门",
    show: true,
    sortable: true,
    visible: true, // 展示与隐藏
    minWidth: "120px",
  },
  {
    fieldIndex: "staffType",
    label: "人员类型",
    show: true,
    visible: true, // 展示与隐藏
    sortable: true,
    minWidth: "120px",
    type:'dict',
    dictList: staff_type.filter(item => item.value === '2') // 只显示借调人员
  },
  {
    fieldIndex: "post",
    label: "岗位",
    show: true,
    visible: true, // 展示与隐藏
    sortable: true,
    minWidth: "120px",
  },
  {
    fieldIndex: "idCard",
    label: "身份证号",
    show: true,
    visible: true, // 展示与隐藏
    sortable: true,
    minWidth: "150px",
  },
],
// 表格基础配置项，看个人需求添加
tableConfig: {
  needPage: true, // 是否需要分页
  index: true, // 是否需要序号
  selection: false, // 是否需要多选
  reserveSelection: false, // 是否需要支持跨页选择
  indexFixed: false, // 序号列固定 left true right
  selectionFixed: false, //多选列固定left true right
  indexWidth: "50", //序号列宽度
  loading: false, //表格loading
  showSummary: false, //是否开启合计
  height: null, //表格固定高度 比如：300px
},
});

/** 查询列表 */
const getList = () => {
  tabelForm.tableConfig.loading = true;
  listLoanSysUser(queryParams.value, pageParams.value).then((response) => {
    list.value = response.data.records;
    total.value = response.data.total;
    tabelForm.tableConfig.loading = false;
  });
};

/** 搜索按钮操作 */
const handleQuery = () => {
  pageParams.value.pageNum = 1;
  getList();
};
/** 重置按钮操作 */
const resetQuery = () => {
  proxy.resetForm("queryForm");
  queryParams.value = {
    staffName: '',
    staffType: '2' // 重置时也保持借调人员类型
  };
  handleQuery();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy.download(
    `/user/sysUser/export`,
    {
      ...queryParams.value,
    },
    `借调人员列表_${formatMinuteTime(new Date())}.xlsx`
  );
};

onMounted(() => {
  getList();
});
</script>
      
<style scoped>
</style>
