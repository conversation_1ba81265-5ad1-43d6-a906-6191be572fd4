<template>
  <div
    class="dialog-box"
    :class="popupType === 'view' ? 'dialog-box-view' : 'dialog-box-edit'"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="popupType !== 'view' ? rules : {}"
      label-width="130px"
    >
      <el-row>
        <div class="common-box-one">
          <div class="common-header">
            <div class="common-header-line"></div>
            <div class="common-header-text">基础信息</div>
          </div>
        </div>

        <el-col :span="12">
          <el-form-item
            v-if="rootNode === 'false'"
            label="上级区域"
            prop="parkingAreaId"
          >
            <el-select
              v-if="popupType !== 'view'"
              filterable
              :filter-method="dataFilter"
              popper-class="opperView"
              :popper-append-to-body="true"
              class="main-select-tree"
              ref="selectTreeRef"
              v-model="formData.parentId"
              placeholder="请选择上级区域"
            >
              <el-option
                :label="formData.parentName"
                :value="formData.parentId"
                style="display: none"
              />
              <el-tree
                :check-strictly="true"
                :data="areaTreeData"
                :filter-node-method="filterNode"
                @node-click="handleNodeClick"
                default-expand-all
                node-key="id"
                ref="areaTreeRef"
                highlight-current
                :props="treeProps"
              />
            </el-select>
            <div v-if="popupType === 'view'">
              {{ formData.parkingAreaName || "" }}
            </div>
          </el-form-item>
        </el-col>
    
        <el-col :span="12">
          <el-form-item label="出入口名称" prop="parkingName">
            <el-input
              v-if="popupType !== 'view'"
              v-model="formData.parkingName"
              placeholder="请输入出入口名称"
              maxlength="30"
            />
            <div v-if="popupType === 'view'">
              {{ formData.parkingName || "-" }}
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="负责人" prop="managerUserId">
            <el-select
              v-if="popupType !== 'view'"
              v-model="formData.managerUserId"
              @change="changeUser"
              collapse-tags
              placeholder="请输入负责人姓名进行选择"
              clearable
              filterable
              remote
              :remote-method="getUserList"
            >
              <el-option
                v-for="(item, index) in applyUserList"
                :key="index"
                :label="item.staffName"
                :value="item.staffId"
              >
                <div>
                  {{
                    item.staffName +
                    "(" +
                    item.orgName +
                    "/" +
                    item.loginName +
                    ")"
                  }}
                </div>
              </el-option>
            </el-select>
            <div v-if="popupType === 'view'">
              {{ formData.managerNickName || "-" }}
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="手机号码" prop="managerPhone">
            <el-input
              :disabled="true"
              v-if="popupType !== 'view'"
              v-model="formData.managerPhone"
              placeholder="请选择负责人"
              maxlength="30"
            />
            <div v-if="popupType === 'view'">
              {{ formData.managerPhone || "-" }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="访客权限" prop="vistorSwitch">
            <el-radio-group
              v-if="popupType !== 'view'"
              v-model="formData.vistorSwitch"
            >
              <el-radio-button
                v-for="dict in major_parking_lot_vistor_switch"
                :key="dict.value"
                :label="dict.value"
                >{{ dict.label }}</el-radio-button
              >
            </el-radio-group>
            <div v-if="popupType === 'view'">
              {{ $formatDictLabel(formData.status, major_parking_lot_status) }}
            </div>
          </el-form-item>
        </el-col>
        <!-- 
        <el-col :span="12">
          <el-form-item label="总车位数" prop="totalSpaceNum">
            <el-input
              v-if="popupType !== 'view'"
              v-model.number="formData.totalSpaceNum"
              placeholder="请输入总车位数"
              maxlength="30"
              oninput="value=value.replace(/[^\d]/g,'')"
            />
            <div v-if="popupType === 'view'">
              {{ formData.totalSpaceNum || "-" }}
            </div>
          </el-form-item>
        </el-col> -->

        <el-col :span="12">
          <el-form-item label="启用状态" prop="status">
            <el-radio-group
              v-if="popupType !== 'view'"
              v-model="formData.status"
            >
              <el-radio-button
                v-for="dict in major_parking_lot_status"
                :key="dict.value"
                :label="dict.value"
                >{{ dict.label }}</el-radio-button
              >
            </el-radio-group>
            <div v-if="popupType === 'view'">
              {{ $formatDictLabel(formData.status, major_parking_lot_status) }}
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="12" v-if="popupType === 'view'">
          <el-form-item label="已停车位数" prop="stopSpaceNum">
            <el-input
              v-if="popupType !== 'view'"
              v-model.number="formData.stopSpaceNum"
              placeholder="请输入已停车位数"
              maxlength="30"
              oninput="value=value.replace(/[^\d]/g,'')"
            />
            <div v-if="popupType === 'view'">
              {{ formData.stopSpaceNum || "-" }}
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="排序" prop="orderNum">
            <el-input
              v-if="popupType !== 'view'"
              v-model.number="formData.orderNum"
              placeholder="请输入排序"
              maxlength="30"
              oninput="value=value.replace(/[^\d]/g,'')"
            />
            <div v-if="popupType === 'view'">
              {{ formData.orderNum || "-" }}
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="12" v-if="popupType === 'view'">
          <el-form-item label="剩余车位数" prop="remainingSpaceNum">
            <el-input
              v-if="popupType !== 'view'"
              v-model.number="formData.remainingSpaceNum"
              placeholder="请输入剩余车位数"
              maxlength="30"
              oninput="value=value.replace(/[^\d]/g,'')"
            />
            <div v-if="popupType === 'view'">
              {{ formData.remainingSpaceNum || "-" }}
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="详细地址" prop="addr">
            <el-input
              v-if="popupType !== 'view'"
              type="textarea"
              v-model="formData.addr"
              placeholder="请输入详细地址"
              maxlength="300"
              resize="none"
              :rows="3"
              show-word-limit
            />
            <span v-if="popupType === 'view'" class="dialog-text">{{
              formData.addr
            }}</span>
          </el-form-item>
        </el-col>
      </el-row>
      <div class="common-box-one">
        <div class="common-header">
          <div class="common-header-line"></div>
          <div class="common-header-text">配置中心</div>
        </div>
      </div>
      <el-row>
        <el-col :span="12">
          <el-form-item label="出入配置中心" prop="outOrIn">
            <el-radio-group v-model="formData.outOrIn">
              <el-radio   v-for="dict in parking_lot_out_in"
                :key="dict.value"
                :label="dict.value">{{ dict.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- <el-row>
        <div class="common-box-one">
          <div class="common-header">
            <div class="common-header-line"></div>
            <div class="common-header-text">配置中心</div>
          </div>
        </div>

        <el-col :span="24">
          <el-form-item label="余位为零时" prop="remainderIsZero">
            <el-radio-group
              :disabled="popupType === 'view'"
              v-model="formData.remainderIsZero"
              @change="changeRemainderIsZero"
            >
              <el-radio
                v-for="dict in remainderIsZeroOptions"
                :key="dict.value"
                :label="dict.value"
                >{{ dict.label }}</el-radio
              >
            </el-radio-group>
            <el-button
              v-if="formData.remainderIsZero === '2'"
              style="margin-left: 10px"
              class="add"
              type="primary"
              plain
              size="small"
              @click="addRules"
              >停车规则</el-button
            >
          </el-form-item>
        </el-col>
      </el-row> -->

      <el-row>
        <div class="common-box-one">
          <div class="common-header">
            <div class="common-header-line"></div>
            <div class="common-header-text">设备配置</div>
          </div>
        </div>

        <el-col :span="24">
          <div class="dialog-footer" v-if="popupType !== 'view'">
            <div class="flex-1"></div>
            <div class="dialog-footer-btn">
              <el-button
                class="add"
                type="primary"
                plain
                size="small"
                @click="addDevice"
              >
                添加设备
              </el-button>
            </div>
          </div>
        </el-col>

        <el-col :span="24">
          <el-table
            class="limit-cell-table"
            :data="deviceInfo"
            style="width: 100%; margin-top: 10px; margin-bottom: 10px"
            border
          >
            <el-table-column
              type="index"
              label="序号"
              width="80"
              align="center"
              sortable
            />
            <template v-for="item in deviceColumns" :key="item.prop">
              <el-table-column
                :prop="item.prop"
                :label="item.label"
                :min-width="item.minWidth"
                :resizable="item.resizable"
                align="center"
                show-overflow-tooltip
              >
                <template #default="{ row }">
                  <div v-if="item.prop === 'deviceName'">
                    {{ row.deviceName }}
                  </div>
                  <div v-if="item.prop === 'deviceSn'">{{ row.deviceSn }}</div>
                  <div v-if="item.prop === 'deviceStatus'">
                    {{
                      $formatDictLabel(row.deviceStatus, deviceStatusOptions)
                    }}
                  </div>
                  <div v-if="item.prop === 'actionType'">
                    {{ $formatDictLabel(row.actionType, actionTypeOptions) }}
                  </div>
                  <div v-if="item.prop === 'vistorSwitch'">
                    {{
                      $formatDictLabel(row.vistorSwitch, vistorSwitchOptions)
                    }}
                  </div>
                </template>
              </el-table-column>
            </template>
            <el-table-column
              v-if="popupType !== 'view'"
              label="操作"
              align="center"
              fixed="right"
            >
              <template #default="{ row }">
                <el-button
                  size="small"
                  type="text"
                  @click="handleDeleteDevice(row)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
    </el-form>

    <DialogBox
      :visible="dialog.visible"
      :dialogWidth="dialog.width"
      @save="save"
      @cancellation="cancellation"
      @close="close"
      :dialogFooterBtn="dialog.dialogFooterBtn"
      :CloseSubmitText="dialog.CloseSubmitText"
      :SaveSubmitText="dialog.SaveSubmitText"
      :dialogTitle="dialog.title"
    >
      <template #content>
        <AddDevice
          ref="addDeviceRef"
          v-if="dialog.type === 'addDevice'"
          @submitClose="submitDevice"
        />
        <Addrulestransfer
          ref="AddRulesRef"
          v-if="dialog.type === 'addRules' || dialog.type === 'viewRules'"
          :ruleInfo="ruleInfo"
          :parkingId="parkingId"
          @submitForm="submitRuleClose"
        />
      </template>
    </DialogBox>
  </div>
</template>

<script setup>
import {
  ref,
  reactive,
  onMounted,
  watch,
  computed,
  getCurrentInstance,
} from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  selectApplyUserList,
  findpassagewayById,
  getParkingId,
  selectByUserId,
  addPassageway,
  updatePassagewayById,
  findparkinglotTree,
} from "@/api/majorParking/exit";
import AddDevice from "@/systemViews/vehicle-management/parking-management/components/AddDevice.vue";
import Addrulestransfer from "@/systemViews/vehicle-management/parking-management/components/addrulestransfer.vue";

const addDeviceRef = ref(null);
const AddRulesRef = ref(null);
const props = defineProps({
  popupType: {
    type: String,
    default: "",
  },

  rowData: {
    type: Object,
    default: () => {},
  },
});

// 字典数据
const { proxy } = getCurrentInstance();
const {
  major_parking_lot_status,
  major_parking_lot_remainder_is_zero,
  device_action_status,
  device_action_type,
  major_parking_lot_vistor_switch,
  parking_lot_out_in
} = proxy.useDict(
  "major_parking_lot_status",
  "major_parking_lot_remainder_is_zero",
  "device_action_status",
  "device_action_type",
  "major_parking_lot_vistor_switch",
  "parking_lot_out_in"
);
const emits = defineEmits(["cancellationRefresh"]);
// 响应式数据
const formRef = ref(null);
const selectTreeRef = ref(null);
const areaTreeRef = ref(null);
const dialog = reactive({
  visible: false,
  width: "30%",
  title: "",
  type: "",
  dialogFooterBtn: true,
  CloseSubmitText: "取消",
  SaveSubmitText: "确定",
});

const formData = reactive({
  status: "0",
  orderNum: 1,
  vistorSwitch: "0",
  // remainderIsZero: "0",
});

const areaTreeData = ref([]);
const applyUserList = ref([]);
const deviceInfo = ref([]);
const ruleInfo = ref([]);
const parkingId = ref("");
const rootNode = ref("false");

const treeProps = {
  children: "children",
  label: "label",
  value: "id",
};

const deviceColumns = [
  { prop: "deviceName", label: "设备名称", minWidth: "80%" },
  { prop: "deviceSn", label: "设备编码", minWidth: "100%" },
  { prop: "deviceStatus", label: "在线状态", minWidth: "150%" },
  { prop: "actionType", label: "进出类型", minWidth: "150%" },
  // { prop: "vistorSwitch", label: "访客权限", minWidth: "150%" },
];

const rules = {
  parkingAreaId: [
    { required: true, message: "请选择所属区域", trigger: "blur" },
  ],
  parkingName: [
    { required: true, message: "请输入出入口名称", trigger: "blur" },
  ],
  managerUserId: [{ required: true, message: "请选择负责人", trigger: "blur" }],
  managerPhone: [{ required: true, message: "请选择负责人", trigger: "blur" }],
  // totalSpaceNum: [
  //   { required: true, message: "请输入车位总数", trigger: "blur" },
  // ],
  status: [{ required: true, message: "请选择启用状态", trigger: "blur" }],
  vistorSwitch: [
    { required: true, message: "请选择访客权限", trigger: "blur" },
  ],
  outOrIn: [
    { required: true, message: "请选择出入配置中心", trigger: "blur" },
  ],
};

// 计算属性
const statusOptions = computed(() => major_parking_lot_status.value);
const remainderIsZeroOptions = computed(
  () => major_parking_lot_remainder_is_zero.value
);
const deviceStatusOptions = computed(() => device_action_status.value);
const actionTypeOptions = computed(() => device_action_type.value);
const vistorSwitchOptions = computed(
  () => major_parking_lot_vistor_switch.value
);

const dataFilter = (val) => {
  if (val) {
    areaTreeRef.value.filter(val);
  }
};

const filterNode = (value, data) => {
  if (!value) return true;
  return data.label.indexOf(value) !== -1;
};

const handleNodeClick = (data) => {
  if (data.type != "parking") return ElMessage.error("请选择停车场！");
  formData.parkingAreaId = data.parkingAreaId;
  formData.parkingAreaName = data.parkingAreaName;
  formData.parentId = data.id;
  formData.parentName = data.label;
  formData.ancestors = data.ancestors;
  formData.isChild = data.type === "area" ? "1" : "0";
  selectTreeRef.value.blur();
};

const changeUser = () => {
  const user = applyUserList.value.find(
    (o) => o.staffId === formData.managerUserId
  );
  if (user) {
    formData.managerNickName = user.staffName;
    formData.managerPhone = user.cellphone;
  }
};

const getUserList = (value) => {

  if (!value || value.trim().length < 2) {
    applyUserList.value = []; // 立即清空列表
    return;
  }

  if (value.length > 0) {
    selectApplyUserList({ staffName: value }).then((res) => {
      applyUserList.value = res.data;
    });
  } else {
    applyUserList.value = [];
  }
};

const selectAreaTree = () => {
  findparkinglotTree({ excludeParentId: formData.id }).then((res) => {
    areaTreeData.value = res.data;
  });
};

const extractParkingNodes = (data) => {
  const result = [];

  // 递归遍历函数
  function traverse(node) {
    if (node.type === "parking") {
      // 如果是 parking 类型，添加到结果中
      result.push(node);
    }

    // 如果有子节点，递归遍历
    if (node.children && node.children.length > 0) {
      node.children.forEach((child) => traverse(child));
    }
  }

  // 遍历原始数据
  data.forEach((item) => traverse(item));

  return result;
};

const findParkinRulesByParkingLotIdFun = () => {
  findParkinRulesByParkingLotId({ parkingId: parkingId.value }).then((res) => {
    if (res.code === 200) {
      ruleInfo.value = res.data.map((item) => ({
        id: item.parkingRuleId,
        parkingId: item.parkingId,
        ruleName: item.ruleName,
        applyType: item.applyType,
      }));
    }
  });
};

const changeRemainderIsZero = () => {
  if (formData.remainderIsZero === "2") {
    verifyRules(formData.id).then((res) => {
      if (res.code === 200 && !res.data) {
        ElMessage.warning(res.msg);
        formData.remainderIsZero = "0";
        ruleInfo.value = [];
      }
    });
  }
};

const handleDeleteDevice = (row) => {
  ElMessageBox.confirm(
    "确定删除设备名称为【" + row.deviceName + "】的设备吗？",
    "提示",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }
  )
    .then(() => {
      deviceInfo.value = deviceInfo.value.filter(
        (item) => item.deviceSn !== row.deviceSn
      );
    })
    .catch(() => {
      // 用户点击了取消
    });
};

const addDevice = () => {
  dialog.visible = true;
  dialog.width = "30%";
  dialog.title = "新增设备";
  dialog.type = "addDevice";
  dialog.CloseSubmitText = "取消";
  dialog.SaveSubmitText = "确定";
  dialog.dialogFooterBtn = true;
};

const addRules = () => {
  dialog.visible = true;
  dialog.width = "60%";
  dialog.title = props.popupType === "view" ? "查看配置" : "修改配置";
  dialog.type = props.popupType === "view" ? "viewRules" : "addRules";
};

const submitDevice = (info) => {
  dialog.visible = false;
  if (info.deviceSn) {
    const exists = deviceInfo.value.some(
      (item) => item.deviceSn === info.deviceSn
    );
    if (!exists) {
      deviceInfo.value.push(info);
    }
  }
};

const submitRuleClose = (info) => {
  dialog.visible = false;
  if (info) {
    ruleInfo.value = info.map((item) => ({
      id: item.id,
      parkingId: item.parkingId,
      ruleName: item.ruleName,
      applyType: item.applyType,
    }));
  } else {
    ruleInfo.value = [];
  }
};

const submitForm = () => {
  formRef.value.validate((valid) => {
    if (valid) {
      formData.lotDeviceList = deviceInfo.value;
      formData.lotRuleList = ruleInfo.value.map((item) => ({
        parkingRuleId: item.id,
        parkingId: item.parkingId,
        ruleName: item.ruleName,
        applyType: item.applyType,
      }));

      // if (formData.remainderIsZero === "2" && ruleInfo.value.length < 1) {
      //   ElMessage.warning("请添加停车规则");
      //   return;
      // }

      const api =
        props.popupType === "add" ? addPassageway : updatePassagewayById;
      api(formData).then((res) => {
        if (res.code == "1") {
          ElMessage.success(
            props.popupType === "add" ? "新增成功" : "修改成功"
          );
          emits("cancellationRefresh");
        }
      });
    }
  });
};

const cancel = () => {
  emit("cancelClose");
};

//弹窗方法

/** 提交保存 */
const save = (val) => {
  if (dialog.type === "addDevice") {
    addDeviceRef.value.submitForm();
  }

  if (dialog.type === "addRules") {
    AddRulesRef.value.submitForm();
  }
};

/** 点击取消保存 */
const cancellation = (val) => {
  close(false);
};

/** 关闭弹窗 */
const close = (val) => {
  dialog.visible = val;
};
// 生命周期钩子
onMounted(() => {
  if (props.popupType === "add") {
    getParkingId().then((res) => {
      formData.id = res.data;
      selectAreaTree();
    });
  } else {
    findpassagewayById(props.rowData.id).then((res) => {
      Object.assign(formData, res.data);
      if (!formData.orderNum) formData.orderNum = 1;
      deviceInfo.value = res.data.lotDeviceList;
      selectByUserId(formData.managerUserId).then((res) => {
        const user = res.data;
        user.staffId = user.staffId.toString();
        applyUserList.value = [user];
        selectAreaTree();
      });
    });
  }
});

// 监听 formData.id 变化
watch(
  () => formData.id,
  (newValue) => {
    if (newValue) {
      parkingId.value = newValue;
    }
  },
  { immediate: true }
);

// 暴露方法
defineExpose({
  submitForm,
  cancel,
});
</script>

<style scoped>
</style>