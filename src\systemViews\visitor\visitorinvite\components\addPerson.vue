<template>
  <div    class="dialog-box"
  :class="popupType === 'view' ? 'dialog-box-view' : 'dialog-box-edit'">
        <el-form
          ref="ruleFormRef"
          :model="formData"
          label-width="100px"
          :rules="rules"
        >
          <el-row>
            <el-col :span="24">
              <el-form-item label="访客姓名" prop="name">
                
                  <el-input
                    v-if="popupType!='view'"
                    placeholder="请输入访客姓名"
                    v-model="formData.name"
                    clearable
                  />
                  <div v-else>{{ formData.name }}</div>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="24">
              <el-form-item label="手机号码" prop="telephone">
                <el-input
                  v-if="popupType!='view'"
                  placeholder="请输入手机号码"
                  v-model="formData.telephone"
                  clearable
                />
                <div v-else>{{ formData.telephone }}</div>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row v-if="isIdCard">
            <el-col :span="24">
              <el-form-item label="证件类型" prop="licenseType">
                <el-select 
                 v-if="popupType!='view'"
                  v-model="formData.licenseType" 
                  placeholder="请选择"
                >
                  <el-option
                    v-for="dict in id_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
                <div v-else>{{ $formatDictLabel(formData.licenseType, id_type) }}</div>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row v-if="isIdCard">
            <el-col :span="24">
              <el-form-item label="证件号码" prop="idNumber">
                <el-input
                 v-if="popupType!='view'"
                  placeholder="请输入证件号码"
                  v-model="formData.idNumber"
                  clearable
                />
                <div v-else>{{ formData.idNumber }}</div>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="24">
              <el-form-item label="访客单位" prop="personnelUnit">
                <el-input
                 v-if="popupType!='view'"
                  placeholder="请输入访客单位"
                  v-model="formData.personnelUnit"
                  clearable
                />
                <div v-else>{{ formData.personnelUnit }}</div>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="24">
              <el-form-item label="照片" prop="pictures">

                <ImageUpload
                      v-if="popupType!='view'"
              v-model="fileList"
              limit="1"
              fileSize="10"
              :paramsData="imageExtraData"
              :uploadImgUrl="
                '/park' + apiUrls + '/visitorinvite/personnel/uploadImage'
              "
            />
            <template v-if="popupType == 'view'">
            <!-- 查看模式显示图片 -->
            <PreviewImage
              style="width: 150px"
              :photo-id="formData.faceImg"
            ></PreviewImage>
          </template>
               
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

      
  </div>
</template>

<script setup>
import { ref, reactive, onMounted,getCurrentInstance} from 'vue'
import { ElMessage } from 'element-plus'
import { save, findById, getUUid } from '@/api/visitor/visitorinvite/components/addPerson'
import { getConfigInfo } from '@/api/visitor/visitorParameterConfig'
const emit = defineEmits(["submitClose"]); // 定义事件
const props = defineProps({
  popupType: {
    type: String,
    default: ''
  },
  rowData: {
    type: Object,
    default: () => ({})
  }
})

const { proxy } = getCurrentInstance();
const { id_type } = proxy.useDict("id_type");
const ruleFormRef = ref()
const isIdCard = ref(false)
const fileList = ref([])

const imageExtraData = reactive({
  businessId: ''
})

const formData = reactive({
  id: '',
  inviteId: '',
  licenseType: '1',
  name: '',
  telephone: '',
  idNumber: '',
  personnelUnit: '',
  faceImg: ''
})

const idNumberValidator = (rule, value, callback) => {
  const re = /^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/
  if (!value) {
    callback(new Error('请输入证件证号'))
  } else if (formData.licenseType === '1' && !re.test(value)) {
    callback(new Error('请输入正确的证件号码'))
  } else {
    callback()
  }
}

const rules = reactive({
  name: [{ required: true, message: '请输入访客姓名', trigger: 'blur' }],
  telephone: [{ required: true, message: '请输入手机号码', trigger: 'blur' }],
  idNumber: [{ required: true, validator: idNumberValidator, trigger: 'blur' }],
  personnelUnit: [{ required: true, message: '请输入访客单位', trigger: 'blur' }]
})

onMounted(() => {
  formData.inviteId = props.rowData.id
  if (props.popupType === 'addPerson') {
    getUUid().then(res => {
      formData.id = res.id
      imageExtraData.businessId = res.id
    })
  }
  if (['editPerson', 'viewPerson'].includes(props.popupType)) {
    findByIdFun(props.rowData.id)
    imageExtraData.businessId = props.rowData.id
  }
  getConfig()
})

const getConfig = async () => {
  const res = await getConfigInfo()
  isIdCard.value = res.rows[0].isIdcard === '0'
}

const findByIdFun = async (id) => {
  const res = await findById(id)
  Object.assign(formData, res.data)
  if (formData.encodeToBase64) {
    fileList.value = [{ name: '', url: `data:image/png;base64,${formData.encodeToBase64}`,fileId:formData.faceImg }]
  }
}


const saveBtn = async () => {
  if (!ruleFormRef.value) return
  
  try {
    // 表单验证
    const valid = await ruleFormRef.value.validate()
    if (!valid) return

    // 处理文件列表
    if (fileList.value.length > 0) {
      formData.faceImg = fileList.value[0].res.attachmentId  // 根据实际接口字段调整
    } else {
      formData.faceImg = ''
    }

    // 设置默认类型
    formData.personnelType = '1'

    // 根据操作类型调用不同API
    let apiResponse
    apiResponse = await save(formData)

    if (apiResponse.code == '1') {
      ElMessage.success({
        message: props.popupType === 'add' ? '新增成功' : '修改成功',
        type: 'success'
      })
      emit('submitClose')  // 触发父组件关闭弹窗/刷新数据
    }
  } catch (error) {
    console.error('保存失败:', error)
  }
}

defineExpose({
  saveBtn
})

</script>

<style scoped lang="scss">

</style>