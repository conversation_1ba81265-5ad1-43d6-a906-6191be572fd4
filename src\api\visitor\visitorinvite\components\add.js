import request from '@/utils/request'

//添加/修改邀约信息
export function save(data) {
  return request({
    url: '/visitorinvite/invite/save',
    method: 'post',
    data:data
  })
}
//获取主键
export function getUUid() {
  return request({
    url: "/visitorinvite/invite/inviteId",
    method: 'get',
  })
}

export function findById(id) {
  return request({
    url: '/visitorinvite/invite/query/'+id,
    method: 'post'
  })
}

export function queryListByInviteId(id) {
  return request({
    url: '/visitorinvite/personnel/queryListByInviteId/'+id,
    method: 'get',
  })
}

export function deletePersonnelById(id) {
  return request({
    url: '/visitorinvite/personnel/deleteById/'+id,
    method: 'get'
  })
}

//查询权限区域集合
export function areaList(data) {
  return request({
    url: '/visitorinvite/invite/areaList',
    method: 'post',
    data: data,
  })
}
//查询访问区域集合
export function selectAreaList() {
  return request({
    url: '/visitorinvite/invite/selectareaList',
    method: 'post',
  })
}
// 查询用户个人信息
export function getUserProfile() {
  return request({
    url: '/system/user/profile',
    method: 'get'
  })
}


//根据电话查询访客信息
export function findVisitorInfoByTel(params) {
  return request({
    url: '/visitor/visitorInfo/tel/' + params,
    method: 'get',
  })
}
// 根据附件主键删除文件接口
export function delAttachmentById(id){
  return request({
    url: '/sys/attachment/delAttachmentById/' + id,
    method: 'post',
  })
}

// 获取配置信息
export function getConfigInfo() {
  return request({
    url: '/visitor/visitorParameterConfig/list',
    method: 'get'
  })
}