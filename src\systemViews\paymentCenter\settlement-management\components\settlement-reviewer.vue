<!-- 结算审核员 -->

<template>
    <div class="app-container-other">
      <Splitpanes class="default-theme">
        <Pane :size="100" :min-size="65">
          <el-card class="dep-card">
            <dialog-search @getList="getList" formRowNumber="3" :columns="tabelForm.columns"  :isShowRightBtn="$checkPermi(['pay:settleManage:audit:list'])">
              <template v-slot:formList>
                <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="80px">
                  <el-form-item label="人员名称">
                    <el-input v-model="queryParams.staffName" placeholder="请输入人员名称" clearable></el-input>
                  </el-form-item>
                </el-form>
              </template>
              <template v-slot:searchList>
                <el-button type="primary" icon="Search" @click="handleQuery" v-hasPermi="['pay:settleManage:audit:list']">搜索</el-button>
                <el-button icon="Refresh" @click="resetQuery" v-hasPermi="['pay:settleManage:audit:list']">重置</el-button>
              </template>
  
              <template v-slot:searchBtnList>
                <el-button type="primary" icon="Plus" size="mini" @click="handleAdd" v-hasPermi="['pay:settleManage:audit:add']">新增结算审核员</el-button>
              </template>
            </dialog-search>
  
            <public-table ref="publictable" :rowKey="tabelForm.tableKey" :tableData="list" :columns="tabelForm.columns"
              :configFlag="tabelForm.tableConfig" :pageValue="pageParams" :total="total" :getList="getList">
  
              <template #statusType="{ scope }">
                <el-radio-group v-model="scope.row.status" @change="radioChange(scope.row)" v-hasPermi="['pay:settleManage:audit:edit']">
                  <el-radio-button :label="item.value" v-for="(item, index) of account_type_status"
                    :key="index">{{ item.label }}</el-radio-button>
                </el-radio-group>
              </template>
              <template #operation="{ scope }">
                <!-- <el-button size="mini" type="text" title="查看" icon="View" @click="handleView(scope.row)" v-hasPermi="['pay:settleManage:audit:edit']">
                </el-button> -->
                <el-button size="mini" type="text" title="修改" icon="Edit" @click="handleEdit(scope.row)" v-hasPermi="['pay:settleManage:audit:info']">
                </el-button>
  
                <el-button size="mini" type="text" title="删除" icon="Delete" @click="handleDelete(scope.row)" v-if="scope.row.status!='1'" v-hasPermi="['pay:settleManage:audit:remove']">
                </el-button>
              </template>
            </public-table>
          </el-card>
        </Pane>
      </Splitpanes>
  
      <DialogBox :visible="open1" :dialogWidth="diaWindow.dialogWidth" :dialogFooterBtn="diaWindow.dialogFooterBtn"
        @save="save" @cancellation="cancellation" :custom-class="diaWindow.customClass" @close="close"
        :dialogTitle="diaWindow.headerTitle">
        <template #content>
          <addSettlementOfficer ref="settlementOfficerRef" :rowData="diaWindow.rowData" :popupType="diaWindow.popupType"
            @closeBtn="cancellationRefsh"></addSettlementOfficer>
        </template>
      </DialogBox>
    </div>
  </template>
  
  <script setup name="User">
  import { ElMessage, ElMessageBox } from "element-plus";
  import { ref, reactive, getCurrentInstance } from "vue";
  import { screenIndex } from "@/api/paymentCenter/settlement-management/index";
  import { apiUrl } from "@/utils/config";
  import { formatMinuteTime } from "@/utils";
  import addSettlementOfficer from "./add-settlement-reviewer.vue";
  import useUserStore from "@/store/modules/user";
  const userStore = useUserStore();
  const { proxy } = getCurrentInstance();
  const { account_type_status } = proxy.useDict(
    "account_type_status",
  );
  
  const settlementOfficerRef = ref(null);
  const open1 = ref(false);
  const diaWindow = reactive({
    headerTitle: "",
    popupType: "",
    rowData: "",
    dialogWidth: "20%",
    dialogFooterBtn: false,
    customClass: 'my_height_1'
  });
  
  const pageParams = ref({
    pageNum: 1,
    pageSize: 10,
  });
  const queryParams = ref({
    staffName:'',
    tenantId:userStore.userInfo.tenantId
  });
  const list = ref([]);
  const total = ref(0);
  const tabelForm = reactive({
    tableKey: "1", //表格key值
    isShowRightToolbar: true, //是否显示右侧显示和隐藏
    showSearch: true,
    // 表格表格数据
    columns: [
      {
        fieldIndex: "staffName", // 对应列内容的字段名
        label: "人员姓名", // 显示的标题
        resizable: true, // 对应列是否可以通过拖动改变宽度
        visible: true, // 展示与隐藏
        sortable: true, // 对应列是否可以排序
        fixed: "", //固定
        minWidth: "150", //最小宽度%
        width: "", //宽度
      },
      {
        fieldIndex: "tenantName", // 对应列内容的字段名
        label: "租户名称", // 显示的标题
        resizable: true, // 对应列是否可以通过拖动改变宽度
        visible: true, // 展示与隐藏
        sortable: true, // 对应列是否可以排序
        fixed: "", //固定
        minWidth: "150", //最小宽度%
        width: "", //宽度
        align: "", //表格对齐方式
      },
      {
        fieldIndex: "roleName", // 对应列内容的字段名
        label: "角色名称", // 显示的标题
        resizable: true, // 对应列是否可以通过拖动改变宽度
        visible: true, // 展示与隐藏
        sortable: true, // 对应列是否可以排序
        fixed: "", //固定
        minWidth: "150", //最小宽度%
        width: "", //宽度
        align: "", //表格对齐方式
      },
  
      {
        label: "操作",
        slotname: "operation",
        width: "120",
        fixed: "right", //固定
        headerAlign:'center',
        visible: true,
      },
    ],
    // 表格基础配置项，看个人需求添加
    tableConfig: {
      needPage: true, // 是否需要分页
      index: true, // 是否需要序号
      selection: false, // 是否需要多选
      reserveSelection: false, // 是否需要支持跨页选择
      indexFixed: false, // 序号列固定 left true right
      selectionFixed: false, //多选列固定left true right
      indexWidth: "50", //序号列宽度
      loading: false, //表格loading
      showSummary: false, //是否开启合计
      height: null, //表格固定高度 比如：300px
    },
  });
  
  /** 查询列表 */
  const getList = () => {
    tabelForm.tableConfig.loading = true;
    screenIndex.auditorList(queryParams.value, pageParams.value).then((response) => {
      response.data.records.forEach((item) => {
        item.roleName = "结算审核员";
      })
      list.value = response.data.records;
      total.value = response.data.total;
      tabelForm.tableConfig.loading = false;
    });
  };
  
  /** 搜索按钮操作 */
  const handleQuery = () => {
    pageParams.value.pageNum = 1;
    getList();
  };
  /** 重置按钮操作 */
  const resetQuery = () => {
    proxy.resetForm("queryForm");
    queryParams.value = {
      tenantId:userStore.userInfo.tenantId
    };
    handleQuery();
  };
  
  /** 导出按钮操作 */
  
  const handleExport = () => {
    proxy.download(
      `pay${apiUrl}/pay/payAccountType/export`,
      {
        ...queryParams.value,
      },
      `账户类型列表_${formatMinuteTime(new Date())}.xlsx`
    );
  };
  /** 删除 */
  const handleDelete = async (row) => {
    try {
      await ElMessageBox.confirm("确认要删除吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      });
  
      const res = await screenIndex.deleteSettleManage({ id: row.id });
      if (res.code == "1") {
        ElMessage.success("删除成功");
        await getList();
      } else {
        ElMessage.error(res.msg);
      }
    } catch (error) {
      console.error("删除失败:", error);
      // 用户取消删除或其他错误
    }
  };
  
  /** 查看 */
  const handleView = (data) => {
    diaWindow.headerTitle = "查看账户类型";
    diaWindow.popupType = "view";
    diaWindow.rowData = data;
    diaWindow.dialogWidth = "28%";
    diaWindow.dialogFooterBtn = false;
    open1.value = true;
  };
  
  // 新增
  const handleAdd = () => {
    diaWindow.headerTitle = "新增账户类型";
    diaWindow.popupType = "add";
    diaWindow.rowData = {}; // 如果需要传递数据到弹窗
    diaWindow.dialogFooterBtn = true;
    diaWindow.dialogWidth = "28%";
    open1.value = true;
  };
  
  // 修改
  const handleEdit = (row) => {
    diaWindow.headerTitle = "修改账户类型";
    diaWindow.popupType = "edit";
    diaWindow.rowData = row; // 如果需要传递数据到弹窗
    diaWindow.dialogFooterBtn = true;
    diaWindow.dialogWidth = "28%";
    open1.value = true;
  };
  
  
  
  // 状态变更处理
  const radioChange = async (row) => {
    let text  = ''
    if(row.chargeStatus == '1'){
        text = `确定要禁用类型名称【 ${row.typeName} 】的状态吗？`
    }else{
         text = `确定要启用类型名称【 ${row.typeName} 】的状态吗？`
    }
   
    try {
      // 添加确认对话框
      await ElMessageBox.confirm(
        text,
        "操作确认",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      );
      const res = await screenIndex.payAccountTypeUpdate(row);
      if (res.code == "1") {
        ElMessage.success("状态更新成功");
        emit("closeBtn");
      }else{
        getList();
      }
    } catch (error) {
      getList();
    }
  };
  /** 点击确定保存 */
  const save = () => {
    if (diaWindow.popupType == "add") {
      settlementOfficerRef.value.saveForm();
    }
  
    if (diaWindow.popupType == "edit") {
      settlementOfficerRef.value.saveForm();
    }
  };
  /** 点击确定后刷新 */
  const cancellationRefsh = () => {
    close(false);
    getList();
  };
  /** 点击取消保存 */
  const cancellation = (val) => {
    close(false);
  };
  
  /** 关闭弹窗 */
  const close = (val) => {
    open1.value = val;
  };
  
  
  
  getList();
  </script>
  
  <style scoped>
  .dep-card {
    min-height: calc(100vh - 160px);
  }
  </style>