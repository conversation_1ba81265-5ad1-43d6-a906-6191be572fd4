<template>
  <div class="app-container">
    <el-card shadow="never">
      <!-- 搜索栏  -->
      <el-form :inline="true" label-width="68px">
        <el-form-item label="主机地址" prop="host">
          <el-input v-model="host" placeholder="请输入主机地址" clearable style="width: 240px" @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item label="数据库" prop="host">
          <el-input v-model="databaseName" placeholder="请输入数据库名称" clearable style="width: 240px" @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <!-- 新增  -->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
        </el-col>
      </el-row>
      <!-- 表格数据  -->
      <el-table v-loading="loading" :data="datasourceList">
        <el-table-column label="数据库" prop="databaseName" :show-overflow-tooltip="true" min-width="200" />
        <el-table-column label="驱动" prop="driverName" :show-overflow-tooltip="true" min-width="200" />
        <el-table-column label="主机" prop="host" :show-overflow-tooltip="true" min-width="180" />
        <el-table-column label="端口" prop="port" :show-overflow-tooltip="true" min-width="150" />
        <el-table-column label="用户名" prop="username" :show-overflow-tooltip="true" min-width="180" />
        <el-table-column label="参数" prop="suffix" :show-overflow-tooltip="true" min-width="100" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right" min-width="200">
          <template #default="scope">
            <el-button link icon="Edit" type="primary" @click="handleUpdate(scope.row)">编辑</el-button>
            <el-button link icon="Delete" type="primary" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页  -->
      <pagination v-show="total > 0" :total="total" v-model:page="pageParams.pageNum" v-model:limit="pageParams.pageSize"
                  @pagination="getList" />
      <!-- 新增&编辑对话框  -->
      <el-dialog :title="editTitle" v-model="editOpen" width="600px" append-to-body destroy-on-close :close-on-click-modal="false">
        <el-form ref="editForm" :model="editData" :rules="editRules" label-width="60px" :inline="true">
          <el-form-item label="驱动" prop="driverName">
            <el-select v-model="editData.driverName" placeholder="请选择驱动" style="width: 180px">
              <el-option label="com.mysql.cj.jdbc.Driver" value="com.mysql.cj.jdbc.Driver"></el-option>
              <el-option label="com.mysql.jdbc.Driver" value="com.mysql.jdbc.Driver"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="库名" prop="databaseName">
            <el-input v-model="editData.databaseName" style="width: 180px"></el-input>
          </el-form-item>
          <el-form-item label="主机" prop="host">
            <el-input v-model="editData.host" style="width: 180px"></el-input>
          </el-form-item>
          <el-form-item label="端口" prop="port">
            <el-input v-model="editData.port" style="width: 180px"></el-input>
          </el-form-item>
          <el-form-item label="用户" prop="username">
            <el-input v-model="editData.username" style="width: 180px"></el-input>
          </el-form-item>
          <el-form-item label="密码" prop="password">
            <el-input v-model="editData.password" type="password" style="width: 180px"></el-input>
          </el-form-item>
          <el-form-item label="参数" prop="suffix">
            <el-input v-model="editData.suffix" style="width: 450px"></el-input>
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="success" @click="testConnection()">测 试</el-button>
            <el-button type="primary" @click="submitEdit(editForm)">确 定</el-button>
            <el-button @click="cancelEdit">取 消</el-button>
          </div>
        </template>
      </el-dialog>

    </el-card>
  </div>
</template>

<script setup name="Datasource">
import {ElMessage, ElMessageBox} from "element-plus";
import {findOne, addDatasource, delDatasource, findPage, updateDatasource, testConn} from "@/api/extend/generator/datasource";
import {doCrypt} from "@/utils/sm2Encrypt";

// 表格数据显示
const loading = ref(true);
const datasourceList = ref([]);
const host = ref('')
const databaseName = ref('')
const total = ref(0);
const notTest = ref(true)
const pageParams = ref({
  pageNum: 1,
  pageSize: 10
})

function getList() {
  loading.value = true;
  const params = {
    ...pageParams.value,
    host: host.value,
    databaseName: databaseName.value,
  }
  findPage(params).then(res => {
    if (res.data && res.data.records) {
      datasourceList.value = res.data.records
      total.value = res.data.total
    }
    loading.value = false;
  }).catch(() => {
    loading.value = false;
  })
}

const handleQuery = () => {
  pageParams.value.pageNum = 1;
  getList();
}

const resetQuery = () => {
  host.value = '';
  databaseName.value = '';
  handleQuery();
}

// 新增&编辑数据源
const editTitle = ref('')
const editOpen = ref(false)
const editForm = ref(null)
const editData = ref({})
const editRules = {
  driverName: [{ required: true, message: "驱动不能为空", trigger: "blur" }],
  databaseName: [{ required: true, message: "数据库名称不能为空", trigger: "blur" }],
  host: [{ required: true, message: "主机地址不能为空", trigger: "blur" }],
  port: [{ required: true, message: "端口不能为空", trigger: "blur" }],
  username: [{ required: true, message: "用户名称不能为空", trigger: "blur" }],
  password: [{ required: true, message: "用户密码不能为空", trigger: "blur" }]
}

const handleAdd = () => {
  editTitle.value = '新增'
  editData.value = {}
  editOpen.value = true
}

const handleUpdate = (row) => {
  editTitle.value = '修改'
  findOne(row.id).then(res => {
    if (res.data){
      editData.value = res.data;
      editOpen.value = true;
    }
  })
}

const submitEdit = (ref) => {
  ref.validate(async (valid, fields) => {
    if (valid) {
      if (notTest.value) {
        ElMessage.warning("请先通过数据库连接测试")
      } else {
        const pwd = editData.value.password;
        const cryptPassword = await doCrypt(pwd, 1)
        let param = Object.assign({}, editData.value);
        param.password = cryptPassword;
        let submitRequest = addDatasource
        if (editTitle.value === '修改') {
          submitRequest = updateDatasource
        }
        submitRequest(param).then(() => {
          ElMessage.success(`${editTitle.value}成功`)
          editOpen.value = false
          notTest.value = true
          getList()
        })
      }
    } else {
      console.log('validate fail!')
    }
  })
}

const cancelEdit = () => {
  editOpen.value = false
}

// 删除数据源
const handleDelete = (row) => {
  ElMessageBox.confirm(h('span', null, [
    h('span', null, '确定删除 '),
    h('span', { style: 'color: red' }, row.databaseName),
    h('span', null, ' 吗？'),
  ]), '删除警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    delDatasource(row.id).then(() => {
      ElMessage.success('删除成功')
      getList()
    })
  })
}

// 测试数据源连接
const testConnection = async () => {
  const pwd = editData.value.password;
  const cryptPassword = await doCrypt(pwd, 1)
  let param = Object.assign({}, editData.value);
  param.password = cryptPassword;
  testConn(param).then((res) => {
    if (res.data) {
      ElMessage.success('数据库连接成功！')
      notTest.value = false;
    } else {
      ElMessage.error('数据库连接异常！')
    }
  })
}

getList()
</script>

<style scoped>

</style>
