<template>
  <div class="container-table-box">
    <dialog-search @getList="getList" formRowNumber="4" :columns="tabelForm.columns">
      <template v-slot:formList>
        <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          label-width="68px"
        >
          <el-form-item label="用户名称" prop="staffName">
            <el-input
              v-model="queryParams.staffName"
              placeholder="请输入用户名称"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="手机号" prop="cellphone">
            <el-input
              v-model="queryParams.cellphone"
              placeholder="请输入手机号"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="邮箱" prop="email">
            <el-input
              v-model="queryParams.email"
              placeholder="请输入邮箱"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
        </el-form>
      </template>
      <template v-slot:searchList>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </template>

      <template v-slot:searchBtnList>
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['sys:base:user:add']"
          >新增
        </el-button>
      </template>
    </dialog-search>

    <public-table
      ref="publictable"
      :rowKey="tabelForm.tableKey"
      :tableData="userList"
      :columns="tabelForm.columns"
      :configFlag="tabelForm.tableConfig"
      :pageValue="queryParams"
      :total="total"
      :getList="getList"
    >
      <template #operation="{ scope }">
        <div v-if="scope.row.staffStatus === 'valid'">
          <el-button
            link
            icon="Edit"
            type="primary"
            title="修改"
            :loading="getUserLoading && scope.row.staffId === loadUserId"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['sys:base:user:edit']"
          >
          </el-button>
          <el-tooltip
            v-if="scope.row.staffId !== 1"
            content="租户管理员不允许删除"
            :disabled="scope.row.staffId !== queryParams.tenantAdminId"
            placement="top"
          >
            <el-button
              link
              icon="Delete"
              type="primary"
              @click="handleDelete(scope.row)"
              v-hasPermi="['sys:base:user:remove']"
              :disabled="scope.row.staffId === queryParams.tenantAdminId"
            >
            </el-button>
          </el-tooltip>
          <el-button
            link
            icon="Refresh"
            type="primary"
            title="重置"
            @click="handleResetPwd(scope.row)"
            v-hasPermi="['sys:base:user:resetPwd']"
          >
          </el-button>
        </div>
      </template>
    </public-table>

    <DialogBox :visible="open1" @save="save" @cancellation="cancellation" @close="close"></DialogBox>
  </div>
</template>

<script setup>
import { listUser } from "@/api/system/user";
import { ref, reactive, nextTick } from "vue";

import useUserStore from "@/store/modules/user";
const userStore = useUserStore();
const userList = ref([]);

const publictable = ref(null);
const queryParams = ref({
  tenantId: userStore.userInfo.tenantId,
  pageNum: 1,
  pageSize: 10,
  email: undefined,
  cellphone: undefined,
  orgId: userStore.userInfo.orgId,
  staffOrgType: "F",
  tenantAdminId: userStore.userInfo.customParam.tenantAdminId,
  tenantName: userStore.userInfo.customParam.tenantName,
});
const total = ref(0);

const open1 = ref(false)

// 字典
const { proxy } = getCurrentInstance();
const { staff_kind, user_status, sys_user_sex } = proxy.useDict(
  "staff_kind",
  "user_status",
  "sys_user_sex"
);

const tabelForm = reactive({
  tableKey: "1", //表格key值
  isShowRightToolbar: true, //是否显示右侧显示和隐藏
  showSearch: true,
  // 表格表格数据
  columns: [
    {
      fieldIndex: "staffId", // 对应列内容的字段名
      label: "用户编号", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible:true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      minWidth: "120px", //最小宽度%
      width: "", //宽度
      align: "left",
      
    },

    {
      fieldIndex: "orgName", // 对应列内容的字段名
      label: "组织", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible:true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      minWidth: "80px", //最小宽度%
      width: "", //宽度
    },

    {
      fieldIndex: "staffName", // 对应列内容的字段名
      label: "用户名称", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible:true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      minWidth: "120px", //最小宽度%
      align: "left",
      width: "", //宽度
    },
    {
      fieldIndex: "loginName", // 对应列内容的字段名
      label: "登录名称", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible:true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      minWidth: "80px", //最小宽度%
      align: "left",
      width: "", //宽度
    },

    {
      fieldIndex: "staffKind", // 对应列内容的字段名
      label: "用户类型", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible:true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      minWidth: "80px", //最小宽度%
      width: "", //宽度
      type: "dict",
      dictList: staff_kind,
    },

    {
      fieldIndex: "staffStatus", // 对应列内容的字段名
      label: "用户状态", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible:true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      minWidth: "", //最小宽度%
      minWidth: "80px", //最小宽度%
      width: "", //宽度
      type: "dict",
      dictList: user_status,
    },

    {
      fieldIndex: "cellphone", // 对应列内容的字段名
      label: "手机号码", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible:true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      minWidth: "80px", //最小宽度%
      width: "", //宽度
    },

    {
      fieldIndex: "employeeCode", // 对应列内容的字段名
      label: "身份证号", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible:true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      minWidth: "100px", //最小宽度%
      width: "", //宽度
    },

    {
      fieldIndex: "email", // 对应列内容的字段名
      label: "邮箱", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible:true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      minWidth: "100px", //最小宽度%
      align: "left",
      width: "", //宽度
    },

    {
      label: "操作",
      slotname: "operation",
      width: "150",
      fixed: "right", //固定
      visible:true,
    },
  ],
  // 表格基础配置项，看个人需求添加
  tableConfig: {
    needPage: true, // 是否需要分页
    index: true, // 是否需要序号
    selection: false, // 是否需要多选
    reserveSelection: false, // 是否需要支持跨页选择
    indexFixed: false, // 序号列固定 left true right
    selectionFixed: false, //多选列固定left true right
    indexWidth: "50", //序号列宽度
    loading: false, //表格loading
    showSummary: false, //是否开启合计
    height: null, //表格固定高度 比如：300px
  },
});

/** 查询用户列表 */
const getList = () => {
  tabelForm.tableConfig.loading = true;
  listUser(queryParams.value).then((response) => {
    userList.value = response.data.records;
    total.value = response.data.total;
    tabelForm.tableConfig.loading = false;
  });
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};
/** 重置按钮操作 */
const resetQuery = () => {
  proxy.resetForm("queryForm");
  handleQuery();
};

/** 提交保存 */
const save = (val)=>{
}

/** 新增 */
const handleAdd = ()=>{
  open1.value = true
}
/** 点击取消保存 */
const cancellation = (val) =>{
  close(false)
}

/** 关闭弹窗 */
const close = (val) =>{
  open1.value = val
}
getList();
</script>