<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
      <el-form-item label="租户" prop="tenantId">
        <el-select v-model="queryParams.tenantId" placeholder="选择租户" style="width: 180px" @change="handleQuery">
          <el-option v-for="t in tenantList" :key="t.tenantId" :label="t.tenantName" :value="t.tenantId"/>
        </el-select>
      </el-form-item>
      <el-form-item label="菜单名称" prop="permissionName">
        <el-input v-model="queryParams.permissionName" placeholder="请输入菜单名称" clearable style="width: 180px"
                  @keyup.enter="handleQuery"/>
      </el-form-item>
      <el-form-item label="状态" prop="permissionStatus">
        <el-select v-model="queryParams.permissionStatus" placeholder="菜单状态" clearable style="width: 150px"
                   @change="handleQuery">
          <el-option v-for="dict in menu_status" :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="菜单归属" prop="permissionScope">
        <el-select v-model="queryParams.permissionScope" placeholder="菜单归属" clearable style="width: 150px"
                   @change="handleQuery">
          <el-option v-for="dict in scope" :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd"
                   v-hasPermi="['sys:base:menu:add']">新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="Sort" @click="toggleExpandAll">展开/折叠</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-if="refreshTable" v-loading="loading" :data="menuList" row-key="permissionId"
              :default-expand-all="isExpandAll" :tree-props="{ children: 'children' }">
      <el-table-column prop="permissionName" label="菜单名称" :show-overflow-tooltip="true"
                       width="180"></el-table-column>
      <el-table-column prop="icon" label="图标" align="center" width="70">
        <template #default="scope">
          <img v-if="scope.row.icon.includes('base64')" style="width: 16px;height: 16px" :src="scope.row.icon">
          <svg-icon v-else :icon-class="scope.row.icon"/>
        </template>
      </el-table-column>
      <el-table-column prop="permissionType" label="类型" width="70">
        <template #default="scope">
          <dict-tag :options="menu_type" :value="scope.row.permissionType"/>
        </template>
      </el-table-column>
      <el-table-column prop="permissionSort" label="排序" width="70"></el-table-column>
      <el-table-column prop="checkCode" label="权限标识" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column prop="uri" label="路由标识" :show-overflow-tooltip="true" width="130"></el-table-column>
      <el-table-column prop="code" label="组件路径" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column prop="permissionStatus" label="状态" width="70">
        <template #default="scope">
          <dict-tag :options="menu_status" :value="scope.row.permissionStatus"/>
        </template>
      </el-table-column>
      <el-table-column prop="permissionVisible" label="是否显示" width="90">
        <template #default="scope">
          <dict-tag :options="menu_visible" :value="scope.row.permissionVisible"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="210" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                     v-hasPermi="['sys:base:menu:edit']">修改
          </el-button>
          <el-button link type="primary" icon="Plus" @click="handleAdd(scope.row)"
                     v-hasPermi="['sys:base:menu:add']">新增
          </el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
                     v-hasPermi="['sys:base:menu:remove']">删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加或修改菜单对话框 -->
    <el-dialog :title="title" v-model="open" width="750px" append-to-body>
      <el-form ref="menuRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="租户">
              <el-input v-model="form.tenantName" placeholder="租户" maxlength="50" disabled/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="上级菜单">
              <el-tree-select v-model="form.parentId" :data="menuOptions"
                              :props="{ value: 'permissionId', label: 'permissionName', children: 'children' }"
                              value-key="permissionId" placeholder="选择上级菜单" check-strictly
                              @node-click="onParentChange"/>
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="form.parentId === 0">
            <el-form-item label="菜单归属" prop="permissionScope" required>
              <el-radio-group v-model="form.permissionScope">
                <el-radio v-for="dict in scope" :key="dict.value" :label="dict.value">
                  {{ dict.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="菜单类型" prop="permissionType">
              <el-radio-group v-model="form.permissionType">
                <el-radio v-for="dict in menu_type" :key="dict.value" :label="dict.value">
                  {{ dict.label }}
                </el-radio>
                <el-radio v-if="form.permissionScope === 'mb_app'" v-for="dict in mobile_jump_mode"
                          :key="dict.value" :label="dict.value">
                  {{ dict.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="菜单名称" prop="permissionName">
              <el-input v-model="form.permissionName" placeholder="请输入菜单名称"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="菜单描述" prop="description">
              <el-input type="textarea" v-model="form.description" placeholder="请输入菜单描述"/>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.permissionType != 'operation'">
            <el-form-item label="菜单图标" prop="icon">
              <el-popover placement="bottom-start" :width="540" trigger="click">
                <template #reference>
                  <el-input v-model="form.icon" placeholder="点击选择图标" @blur="showSelectIcon" readonly>
                    <template #prefix>
                      <svg-icon v-if="form.icon && !base64Img" :icon-class="form.icon" class="el-input__icon"
                                style="height: 32px;width: 16px;"/>
                      <img v-else-if="form.icon && base64Img" style="height: 32px;width: 32px;" :src="form.icon">
                      <el-icon v-else style="height: 32px;width: 16px;">
                        <search/>
                      </el-icon>
                    </template>
                  </el-input>
                </template>
                <icon-select ref="iconSelectRef" @selected="selected" :active-icon="form.icon"/>
              </el-popover>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.permissionType !== 'operation'">
            <el-form-item label="显示排序" prop="permissionSort">
              <el-input-number v-model="form.permissionSort" controls-position="right" :min="0"/>
            </el-form-item>
          </el-col>
          <el-col :span="16" v-if="form.permissionType !== 'operation'">
            <el-form-item prop="code">
              <template #label>
                        <span>
                           <el-tooltip placement="top">
                              <template #content>
                                 组件路径：以'systemViews'或者'portalViews'目录开头
                                 <br/>
                                 链接地址：以http://、https://、inner://开头
                                 <br/>
                                 设计页面：以pageView://开头
                              </template>
                              <el-icon><question-filled/></el-icon>
                           </el-tooltip>
                           组件路径
                        </span>
              </template>
              <el-input v-model="form.code" placeholder="请输入组件路径"/>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="form.permissionType !== 'operation'">
            <el-form-item prop="permissionFrame">
              <template #label>
                        <span>
                           <el-tooltip content="当菜单地址为外部链接时,是否需要拼接单点登录的 mmy 参数" placement="top">
                              <el-icon><question-filled/></el-icon>
                           </el-tooltip>
                           单点登录
                        </span>
              </template>
              <el-radio-group v-model="form.permissionFrame">
                <el-radio label="Y">是</el-radio>
                <el-radio label="N">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.permissionType != 'operation'">
            <el-form-item prop="uri" required>
              <template #label>
                        <span>
                           <el-tooltip content="由各层级路由标识，拼接最终的路由路径" placement="top">
                              <el-icon><question-filled/></el-icon>
                           </el-tooltip>
                           路由标识
                        </span>
              </template>
              <el-input v-model="form.uri" placeholder="请输入路由标识"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="checkCode">
              <el-input v-model="form.checkCode" placeholder="请输入权限标识" maxlength="100"/>
              <template #label>
                        <span>
                           <el-tooltip content="权限控制器中使用的权限字符" placement="top">
                              <el-icon><question-filled/></el-icon>
                           </el-tooltip>
                           权限字符
                        </span>
              </template>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item>
              <template #label>
                        <span>
                           <el-tooltip content="选择停用则路由将不会出现在侧边栏，也不能被访问，子菜单状态同步更改"
                                       placement="top">
                              <el-icon><question-filled/></el-icon>
                           </el-tooltip>
                           菜单状态
                        </span>
              </template>
              <el-radio-group v-model="form.permissionStatus">
                <el-radio v-for="dict in menu_status" :key="dict.value" :label="dict.value">
                  {{ dict.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.permissionType != 'operation'">
            <el-form-item>
              <template #label>
                        <span>
                           <el-tooltip content="选择隐藏则路由将不会出现在侧边栏，但仍然可以访问" placement="top">
                              <el-icon><question-filled/></el-icon>
                           </el-tooltip>
                           显示状态
                        </span>
              </template>
              <el-radio-group v-model="form.permissionVisible">
                <el-radio v-for="dict in menu_visible" :key="dict.value" :label="dict.value">
                  {{ dict.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Menu">
import {addMenu, delMenu, getMenuDetail, listMenu, updateMenu} from "@/api/system/menu";
import {getTenants} from "@/api/tenant/tenant";
import SvgIcon from "@/components/SvgIcon";
import IconSelect from "@/components/IconSelect";
import useUserStore from '@/store/modules/user'

const {proxy} = getCurrentInstance();
const {
  menu_status,
  scope,
  menu_visible,
  menu_type,
  mobile_jump_mode
} = proxy.useDict("menu_status", "scope", "menu_visible", "menu_type", "mobile_jump_mode");
const userStore = useUserStore()

const tenantList = ref([])


const menuList = ref([]);
const open = ref(false);
const loading = ref(true);
const title = ref("");
const menuOptions = ref([]);
const isExpandAll = ref(false);
const refreshTable = ref(true);
const iconSelectRef = ref(null);
const showSearch = ref(true);
const base64Img = ref(false);

const queryParams = ref({
  tenantId: userStore.userInfo.tenantId,
  permissionName: undefined,
  permissionStatus: undefined,
  permissionScope: undefined
})

const form = ref({})

const validatorPermissionScope = (rule, value, callback) => {
  if (form.value.parentId === 0 && !value) {
    return callback(new Error('请选择菜单归属'))
  }
  if (form.value.parentId === 0) {
    const brotherMenus = menuOptions.value[0].children
    if(!brotherMenus){
      return callback(new Error('不存在菜单归属'))
    }
    const sameScopeMenu = brotherMenus.find(m => (m.permissionScope === value && m.permissionId !== form.value.permissionId))
    if (sameScopeMenu) {
      return callback(new Error('同一种归属，只能存在一个根目录'))
    }
    return callback()
  }
  return callback()
}

const validatorUri = (rule, value, callback) => {
  if (form.value.permissionType === 'folder' || form.value.permissionType === 'menu') {
    if (!value) {
      return callback(new Error('请输入路由标识'))
    }
    if (/^\/.*\/$/.test(value)) {
      return callback(new Error('不以“/”开头或者结尾'))
    }
    return callback()
  }
  return callback()
}

const validatorCode = (rule, value, callback) => {
  if (form.value.permissionType === 'folder' || form.value.permissionType === 'menu') {
    if (value && !/^(\/systemViews\/|\/portalViews\/|https:\/\/|http:\/\/|inner:\/\/|pageView:\/\/).+/.test(value)) {
      return callback(new Error('需以 /systemViews、/portalViews、https://、http://、inner://、pageView:// 开头'))
    }
    return callback()
  }
  return callback()
}
/** 校验规则 */
const rules = {
  permissionScope: [{validator: validatorPermissionScope, trigger: 'blur'}],
  permissionType: [{required: true, message: "菜单类型不能为空", trigger: "blur"}],
  permissionName: [{required: true, message: "菜单名称不能为空", trigger: "blur"}],
  uri: [{validator: validatorUri, trigger: "blur"}],
  code: [{validator: validatorCode, trigger: "blur"}]
}

/** 查询菜单列表 */
function getList() {
  loading.value = true;
  listMenu(queryParams.value).then(response => {
    if (response.success) {
      menuList.value = proxy.handleTree(response.data, "permissionId");
    }
    loading.value = false;
  }).catch(() => {
    loading.value = false;
  })
}

/** 查询菜单下拉树结构 */
function getTreeselect(permissionScope) {
  const params = {tenantId: queryParams.value.tenantId}
  if (permissionScope) {
    params.permissionScope = permissionScope
  }
  listMenu(params).then(response => {
    if (response.success) {
      const menu = {permissionId: 0, permissionName: "主类目", children: []};
      menu.children = proxy.handleTree(response.data, "permissionId");
      menuOptions.value = [menu];
    }
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    permissionId: undefined,
    parentId: 0,
    permissionName: undefined,
    icon: undefined,
    permissionType: undefined,
    permissionSort: undefined,
    permissionVisible: "show",
    permissionStatus: "valid",
    description: ""
  };
  proxy.resetForm("menuRef");
}

/** 展示下拉图标 */
function showSelectIcon() {
  iconSelectRef.value.reset();
}

/** 选择图标 */
function selected(name) {
  if (name.includes("base64")){
    base64Img.value = true;
  }else {
    base64Img.value = false;
  }
  form.value.icon = name;
}

/** 搜索按钮操作 */
function handleQuery() {
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 更新节点scope */
function onParentChange(item) {
  if (item.permissionScope) {
    form.value.permissionScope = item.permissionScope;
  }
}

/** 新增按钮操作 */
function handleAdd(row) {
  reset();
  getTreeselect();
  if (row && row.permissionId) {
    form.value.parentId = row.permissionId;
  } else {
    form.value.parentId = 0;
  }
  if (row.permissionScope) {
    form.value.permissionScope = row.permissionScope;
  }
  form.value.tenantId = queryParams.value.tenantId;
  form.value.tenantName = getTenantName(queryParams.value.tenantId)
  open.value = true;
  title.value = "添加菜单";
}

/** 获取租户名称 */
function getTenantName(id) {
  const tenant = tenantList.value.find(t => t.tenantId === id)
  return tenant && tenant.tenantName
}

/** 展开/折叠操作 */
function toggleExpandAll() {
  refreshTable.value = false;
  isExpandAll.value = !isExpandAll.value;
  nextTick(() => {
    refreshTable.value = true;
  });
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  getTreeselect(row.permissionScope);
  getMenuDetail(row.permissionId).then(response => {
    form.value = response.data;
    if(form.value.icon.includes("base64")){
      base64Img.value = true
    }else {
      base64Img.vlaue = false
    }
    if (form.value.parentId === '0') {
      form.value.parentId = 0
    }
    open.value = true;
    title.value = "修改菜单";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["menuRef"].validate(valid => {
    if (valid) {
      if (form.value.permissionId != undefined) {
        updateMenu(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addMenu(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$modal.confirm('是否确认删除名称为"' + row.permissionName + '"的数据项?').then(function () {
    return delMenu({permissionId: row.permissionId});
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {
  });
}

/** 初始化租户数据 */
function initTenantList() {
  getTenants()
      .then((response) => {
        tenantList.value = response.data;
      }).catch((err) => {
    tenantList.value = []
  })
}

initTenantList()
getList();
</script>
