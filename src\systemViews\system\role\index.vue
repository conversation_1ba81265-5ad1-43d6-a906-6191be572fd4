<template>
  <div class="app-container">
    <el-card>
      <el-form
          :model="queryParams"
          ref="queryForm"
          v-show="showSearch"
          :inline="true"
          @submit.native.prevent
      >
        <el-form-item label="租户" v-if="userType === 'admin'">
          <el-select
              v-model="queryParams.tenantId"
              style="width: 240px"
              remote
              :remote-method="getTenantList"
              :loading="getTenantLoading"
              @change="tenantChange"
          >
            <el-option
                v-for="item in tenantList"
                :key="item.tenantId"
                :label="item.tenantName"
                :value="item.tenantId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="角色名称" prop="roleName">
          <el-input
              v-model="queryParams.roleName"
              placeholder="请输入角色名称"
              clearable
              style="width: 240px"
              @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['sys:base:role:add']">新增
          </el-button>
        </el-col>
        <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table
          v-loading="loading"
          :data="roleList"
          row-key="roleId"
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
          default-expand-all
      >
        <el-table-column label="角色名称" prop="roleName" :show-overflow-tooltip="true"/>
        <el-table-column prop="roleSort" label="排序"/>
        <el-table-column label="创建时间" align="left" prop="createDateStr"/>
        <el-table-column label="角色描述" prop="description" :show-overflow-tooltip="true"/>
        <el-table-column label="是否分配角色" prop="description" :show-overflow-tooltip="true" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.distributeFlag === 'yes'?'success':''">
              {{ parseDistributeFlag(scope.row.distributeFlag) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <template v-if="!scope.row.isOp">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                         v-hasPermi="['sys:base:role:edit']"
                         v-if="scope.row.tenantId === userStore.userInfo.customParam.tenantId || userType === 'admin'">
                修改
              </el-button>
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
                         v-hasPermi="['sys:base:role:remove']"
                         v-if="scope.row.tenantId === userStore.userInfo.customParam.tenantId || userType === 'admin'">
                删除
              </el-button>
              <el-button type="primary" v-if="(scope.row.tenantId === userStore.userInfo.customParam.tenantId && userType === 'admin')
                && scope.row.distributeFlag === 'no' && scope.row.roleName !== '系统管理员'"
                         link icon="Unlock" @click="handleDistribute(scope.row)"
                         v-hasPermi="['sys:base:role:distribute']">分配
              </el-button>
              <el-button type="primary"
                         v-if="(scope.row.tenantId === userStore.userInfo.customParam.tenantId && userType === 'admin')
                && scope.row.distributeFlag === 'yes' && scope.row.roleName !== '系统管理员'"
                         link icon="Lock" @click="handleDistribute(scope.row)"
                         v-hasPermi="['sys:base:role:distribute']">不分配
              </el-button>
            </template>
          </template>
        </el-table-column>
      </el-table>

      <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
      />
    </el-card>

    <!-- 添加或修改角色配置对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="角色名称" prop="roleName">
          <el-input v-model="form.roleName" placeholder="请输入角色名称"/>
        </el-form-item>
        <el-form-item label="角色归属" prop="roleScope" placeholder="请选择角色归属">
          <el-select v-model="form.roleScope">
            <el-option
                v-for="item in scopeOptions"
                :key="item.dictValue"
                :label="item.dictLabel"
                :value="item.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="权限字符" prop="roleKey">
          <el-input v-model="form.roleKey" placeholder="请输入权限字符"/>
        </el-form-item>
        <el-form-item label="显示排序" prop="roleSort">
          <el-input-number v-model="form.roleSort" controls-position="right" :min="0"/>
        </el-form-item>
        <el-form-item label="租户">
          <el-input v-model="form.tenantName" placeholder="租户" maxlength="50" disabled/>
        </el-form-item>
        <el-form-item label="菜单权限">
          <el-checkbox v-model="menuExpand" @change="handleCheckedTreeExpand($event, 'menu')">展开/折叠
          </el-checkbox>
          <el-checkbox v-model="menuNodeAll" @change="handleCheckedTreeNodeAll($event, 'menu')">全选/全不选
          </el-checkbox>
          <el-checkbox v-model="menuCheckStrictly" @change="handleCheckedTreeConnect($event, 'menu')">父子联动
          </el-checkbox>
          <el-tree
              class="tree-border"
              :data="permissionOptions"
              show-checkbox
              ref="menu"
              :check-strictly=true
              @check="hanleCheck"
              node-key="permissionId"
              empty-text="暂无数据"
              :props="defaultProps"
          ></el-tree>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="form.description" type="textarea" placeholder="请输入内容"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm" :loading="submitLoading">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Role">
import {
  findRoleListByScope,
  getRole,
  delRole,
  addRole,
  updateRole,
  changeRoleStatus,
  queryRoleMenu
} from "@/api/system/role";
import {getTenants} from "@/api/tenant/tenant";
import {treeselect as menuTreeselect} from "@/api/system/menu";
import {arrayToTree} from "@/utils";
import useUserStore from "@/store/modules/user";
import {ref} from "vue";
import {getDictInfo} from "@/api/system/dict/data";
import {ElMessageBox} from "element-plus";

const {proxy} = getCurrentInstance();
const userStore = useUserStore();

const userType = userStore.userInfo.customParam.userType;
const menuCheckStrictly = ref(true);
// 遮罩层
const loading = ref(true);
const submitLoading = ref(false);
const getTenantLoading = ref(false);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 角色表格数据
const roleList = ref([]);
// 弹出层标题
const title = ref("");
// 是否显示弹出层
const open = ref(false);
const menuExpand = ref(false);
const menuNodeAll = ref(false);
// 菜单列表
const permissionOptions = ref([]);
const tenantList = ref([]);
const scopeOptions = ref([]);
// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  roleName: undefined,
  roleCode: undefined,
  roleStatus: undefined,
  tenantId: userStore.userInfo.customParam.tenantId,
  tenantName: userStore.userInfo.customParam.tenantName,
  permissionStatus: "valid"
});
// 表单参数
const form = ref({});
const defaultProps = ref({
  children: "children",
  label: "permissionName"
});
// 表单校验
const rules = {
  roleName: [
    {required: true, message: "角色名称不能为空", trigger: "blur"}
  ],
  roleCode: [
    {required: true, message: "权限字符不能为空", trigger: "blur"}
  ],
  roleScope: [
    {required: true, message: "请选择角色归属", trigger: "blur"}
  ],
  roleSort: [
    {required: true, message: "角色排序不能为空", trigger: "blur"},
  ],
  roleKey: [
    {required: true, message: "请输入权限字符", trigger: "blur"}
  ]
}

function getTenantList(tenantName) {
  getTenantLoading.value = true;
  let query = {}
  if (tenantName !== undefined && tenantName !== '') {
    query.tenantName = tenantName
    query.tenantId = undefined
  } else {
    query.tenantId = queryParams.value.tenantId
  }
  getTenants(query).then((response) => {
    tenantList.value = response.data;
  }).finally(() => {
    getTenantLoading.value = false;
  });
}

/** 查询角色列表 */
function getList() {
  loading.value = true;
  findRoleListByScope(queryParams.value).then(r => {
    getDictInfo("scope").then(response => {
      scopeOptions.value = response.data;
      const newData = response.data.map(v => {
        v["children"] = r.data.filter(i => i.roleScope === v.dictValue);
        v["roleName"] = v.dictLabel;
        v["roleId"] = v.dictDataId;
        v["isOp"] = true;
        v["roleSort"] = v.dictSort;
        return v;
      });
      roleList.value = [];
      roleList.value = newData;
      loading.value = false;
    });
    roleList.value = r.data.records;
    loading.value = false;
  });
}

/** 查询菜单树结构 */
function getMenuTreeselect() {
  menuTreeselect(queryParams.value).then(response => {
    permissionOptions.value = arrayToTree(
        response.data,
        "children",
        "permissionId",
        "parentId"
    );
  });
}

// 所有菜单节点数据
function getMenuAllCheckedKeys() {
  // 目前被选中的菜单节点
  let checkedKeys = proxy.$refs.menu.getCheckedKeys();
  // 半选中的菜单节点
  let halfCheckedKeys = proxy.$refs.menu.getHalfCheckedKeys();
  checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys);
  return checkedKeys;
}

/** 根据角色ID查询菜单树结构 */
function getRoleMenuTreeselect(roleId) {
  return queryRoleMenu(roleId).then(response => {
    return response;
  });
}

// 角色状态修改
function handleStatusChange(row) {
  let text = row.status === "0" ? "启用" : "停用";
  proxy.$modal.confirm('确认要"' + text + '""' + row.roleName + '"角色吗?')
      .then(function () {
        return changeRoleStatus(row.roleId, row.status);
      })
      .then(() => {
        proxy.$modal.msgSuccess(text + "成功");
      })
      .catch(function () {
        row.status = row.status === "0" ? "1" : "0";
      });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  if (proxy.$refs.menu !== undefined) {
    proxy.$refs.menu.setCheckedKeys([]);
  }
  menuExpand.value = false;
  menuNodeAll.value = false;
  form.value = {
    roleId: undefined,
    roleName: undefined,
    roleCode: undefined,
    roleDesc: undefined,
    roleSort: 0,
    roleStatus: "valid",
    permissionIds: [],
    roleScope: undefined,
    tenantName: undefined
  };
  proxy.resetForm("formRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryForm");
  handleQuery();
}

// 树权限（展开/折叠）
function handleCheckedTreeExpand(value, type) {
  let treeList = permissionOptions.value;
  for (let i = 0; i < treeList.length; i++) {
    proxy.$refs.menu.store.nodesMap[treeList[i].permissionId].expanded = value;
  }
}

// 树权限（全选/全不选）
function handleCheckedTreeNodeAll(value, type) {
  proxy.$refs.menu.setCheckedNodes(value ? permissionOptions.value : [])

}

// 树权限（父子联动）
function handleCheckedTreeConnect(value, type) {
  menuCheckStrictly.value = value ? true : false;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  getMenuTreeselect();
  open.value = true;
  title.value = "添加角色";
  form.value.tenantName = queryParams.value.tenantName;
  form.value.tenantId = queryParams.value.tenantId;
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  getMenuTreeselect();
  const roleId = row.roleId;
  const roleMenu = getRoleMenuTreeselect(roleId);
  getRole(roleId).then(response => {
    form.value = response.data;
    open.value = true;
    nextTick(() => {
      roleMenu.then(res => {
        let checkedKeys = res.data.permissions;
        proxy.$refs.menu.setCheckedKeys(checkedKeys.map(v => v.permissionId));
      });
    });
    title.value = "修改角色";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["formRef"].validate(valid => {
    if (valid) {
      submitLoading.value = true;
      if (form.value.roleId !== undefined) {
        form.value.permissionIds = getMenuAllCheckedKeys();
        updateRole(form.value).then(response => {
          submitLoading.value = false;
          if (response.success) {
            proxy.$modal.msgSuccess("修改成功");
            open.value = false;
            getList();
          } else {
            proxy.$modal.msgError(response.message)
            return;
          }
        });
      } else {
        form.value.permissionIds = getMenuAllCheckedKeys();
        addRole(form.value).then(response => {
          submitLoading.value = false;
          if (response.success) {
            proxy.$modal.msgSuccess("新增成功");
            open.value = false;
            getList();
          } else {
            proxy.$modal.msgError(response.message);
          }
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  if (row.roleName === '默认角色' && row.roleScope === 'system') {
    proxy.$modal.msgError("默认角色不允许被删除！");
    return;
  }
  proxy.$modal.confirm("是否确认删除角色名称为（" + row.roleName + "）的数据项?").then(function () {
    delRole({roleId: row.roleId}).then(res => {
      if (res.success) {
        proxy.$modal.msgSuccess("删除成功")
      } else {
        proxy.$modal.msgError(res.message)
      }
      getList();
    });
  });
}

function handleDistribute(row) {
  const btnText = row.distributeFlag === "yes" ? '不分配' : '分配';
  ElMessageBox.confirm(
      row.distributeFlag === "yes" ? '确认不分配' + row.roleName + '吗?' : '确认要分配' + row.roleName + '吗?',
      "警告",
      {
        confirmButtonText: btnText,
        cancelButtonText: "取消",
        type: "warning",
        beforeClose: (action, instance, done) => {
          if (action === "confirm") {
            instance.confirmButtonLoading = true;
            instance.confirmButtonText = btnText + "处理中...";
            updateRole({
              roleId: row.roleId,
              distributeFlag: row.distributeFlag === "yes" ? "no" : "yes",
            })
                .then((response) => {
                  done();
                  if (response.success) {
                    proxy.$modal.msgSuccess(btnText + "成功");
                    getList();
                  } else {
                    proxy.$modal.msgError(response.message);
                  }
                  instance.confirmButtonLoading = false;
                })
                .catch((e) => {
                  done();
                  instance.confirmButtonLoading = false;
                });
          } else {
            done();
          }
        },
      }
  ).catch(function () {
  });
}

function tenantChange(tenantId) {
  if (tenantId !== '') {
    queryParams.value.tenantName = tenantList.value.find(item => item.tenantId === tenantId).tenantName
  }
  handleQuery();
}

function parseDistributeFlag(val) {
  if (val === 'yes') {
    return '已分配'
  } else return '未分配'
}

function hanleCheck(data, node) {
  // 获取当前节点是否被选中
  const isChecked = proxy.$refs.menu.getNode(data).checked
  // 如果当前节点被选中，则遍历上级节点和下级子节点并选中，如果当前节点取消选中，则遍历下级节点并取消选中
  if (isChecked) {
    // 判断是否有上级节点，如果有那么遍历设置上级节点选中
    data.parentId && setParentChecked(data.parentId)
    // 判断该节点是否有下级节点，如果有那么遍历设置下级节点为选中
    if (menuCheckStrictly.value) {
      data.children && setChildreChecked(data.children, true)
    }
  } else {
    // 如果节点取消选中，则取消该节点下的子节点选中
    data.children && setChildreChecked(data.children, false)
  }

  function setParentChecked(parentId) {
    // 获取该id的父级node
    const parentNode = proxy.$refs.menu.getNode(parentId)
    // 如果该id的父级node存在父级id则继续遍历
    parentNode && parentNode.data && parentNode.data.parentId && setParentChecked(parentNode.data.parentId)
    //  设置该id的节点为选中状态
    proxy.$refs.menu.setChecked(parentId, true)
  }

  function setChildreChecked(node, isChecked) {
    node.forEach(item => {
      item.children && setChildreChecked(item.children, isChecked)
      proxy.$refs.menu.setChecked(item.permissionId, isChecked)
    })
  }
}

getTenantList();
getList();
</script>

<style scoped>

</style>
