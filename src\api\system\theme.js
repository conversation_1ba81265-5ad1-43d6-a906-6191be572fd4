import request from "@/utils/request";

// 查询分页
export function page(params) {
  return request({
    url: "/user/theme",
    method: "get",
    params: params
  });
}

// 查询详细
export function find(data) {
  return request({
    url: "/user/theme/find",
    method: "post",
    data: data,
  });
}

export function findEnableTheme(data) {
  return request({
    url: "/user/theme/findEnableTheme",
    method: "post",
    data: data,
  });
}

// 新增
export function add(data) {
  return request({
    url: "/user/theme/add",
    method: "post",
    data: data
  });
}

// 修改
export function update(data) {
  return request({
    url: "/user/theme/update",
    method: "post",
    data: data
  });
}

// 删除
// export function del(id) {
//   return request({
//     url: "/user/theme/" + id,
//     method: "delete"
//   });
// }

// 删除
export function del(id) {
  return request({
    url: "/user/theme/delete/" + id,
    method: "post"
  });
}
