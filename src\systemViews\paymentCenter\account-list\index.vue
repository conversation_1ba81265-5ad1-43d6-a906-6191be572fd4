<!-- 账户列表 -->
<template>
  <div class="container-table-box">
    <Splitpanes class="default-theme">
      <Pane :size="100" :min-size="65">
        <el-card class="dep-card">
          <dialog-search @getList="getList" formRowNumber="4" :columns="tabelForm.columns"  :isShowRightBtn="$checkPermi(['pay:accountList:list'])">
            <template v-slot:formList>
              <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="75px">
                 <!-- <el-form-item label="模糊搜索">
                  <el-input v-model="queryParams.params" placeholder="请输入人员姓名、登录名称或手机号" clearable></el-input>
                </el-form-item> -->
                <el-form-item label="模糊搜索">
                  <el-input v-model="queryParams.staffName" placeholder="请输入人员姓名、账号、手机号码进行搜索" clearable></el-input>
                </el-form-item>
                <!-- <el-form-item label="登录名称">
                  <el-input v-model="queryParams.loginName" placeholder="请输入登录名称" clearable></el-input>
                </el-form-item> -->
                <el-form-item label="租户" prop="tenantId" >
                  <TenantSelect v-model="queryParams.tenantId" :key="resetKey"></TenantSelect>
                </el-form-item>
                <el-form-item label="员工部门">
                  <el-input v-model="queryParams.department" placeholder="请输入员工部门" clearable></el-input>
                </el-form-item>

                <el-form-item label="人员类型" prop="type">
                  <el-select v-model="queryParams.type" placeholder="请选择人员类型" clearable>
                    <el-option v-for="(item, index) of staff_type" :key="index" :label="item.label"
                      :value="item.value" />
                  </el-select>
                </el-form-item>

                <el-form-item label="账户名称">
                  <el-input v-model="queryParams.accountName" placeholder="请输入账户名称" clearable></el-input>
                </el-form-item>

                <el-form-item label="账户编码">
                  <el-input v-model="queryParams.accountCode" placeholder="请输入账户编码" clearable></el-input>
                </el-form-item>

                <el-form-item label="开通状态" prop="state">
                  <el-select v-model="queryParams.state" placeholder="请选择开通状态" clearable>
                    <el-option v-for="(item, index) of activation_status" :key="index" :label="item.label"
                      :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-form>
            </template>
            <template v-slot:searchList>
              <el-button type="primary" icon="Search" @click="handleQuery"  v-hasPermi="['pay:accountList:list']">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery" v-hasPermi="['pay:accountList:list']">重置</el-button>
            </template>

            <template v-slot:searchBtnList>
              <el-button type="primary" icon="Plus" size="mini" @click="handleAdd" v-hasPermi="['pay:accountList:add']">开通账户</el-button>
              <el-button size="mini" @click="handleBatchAdd" v-hasPermi="['pay:accountList:add']">
                <i class="icon iconfont icon-zhanghuchongzhi icon-btn"></i>
                批量开通
              </el-button>
              <el-button size="mini" icon="Download" @click="handleExport" v-hasPermi="['pay:accountList:export']">导出
              </el-button>
            </template>
          </dialog-search>

          <public-table ref="publictable" :rowKey="tabelForm.tableKey" :tableData="list" :columns="tabelForm.columns"
            :configFlag="tabelForm.tableConfig" :pageValue="pageParams" :total="total" :getList="getList"       @clickTextDetail="clickTextDetail">
            <template #operation="{ scope }">
              <el-button size="mini" type="text" title="账户明细" @click="handleView(scope.row)" v-hasPermi="['pay:accountList:detail:list']">
                <i class="icon iconfont icon-zhangdanzhanghuliushui"></i>
              </el-button>
              <el-button v-if="scope.row.state == '1'" size="mini" type="text" title="禁用" v-hasPermi="['pay:accountList:edit']"
                @click="handleDisabled(scope.row)">
                <i class="icon iconfont icon-jinyong"></i>
              </el-button>
              <el-button v-if="scope.row.state == '2'" size="mini" type="text" icon="Select" title="启用"  v-hasPermi="['pay:accountList:edit']"
                @click="handleSelect(scope.row)">
              </el-button>
              <el-button size="mini" type="text" title="合并账户" @click="handleConsolidated(scope.row)"  v-hasPermi="['pay:accountList:merge:add']">
                <i class="icon iconfont icon-hebing1"></i>
              </el-button>

              <el-button size="mini" type="text" title="删除" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['pay:accountList:remove']">
              </el-button>
            </template>
          </public-table>
        </el-card>
      </Pane>
    </Splitpanes>

    <DialogBox :visible="diaWindow.open1" :dialogWidth="diaWindow.dialogWidth"
      :dialogFooterBtn="diaWindow.dialogFooterBtn" @save="save" @cancellation="cancellation" @close="close"
      :dialogTitle="diaWindow.headerTitle" :dialogTop="diaWindow.dialogTop">
      <template #content>
        <openAccount v-if="diaWindow.popupType == 'openAccount'" ref="accountAddRef" :rowData="diaWindow.rowData"
          :popupType="diaWindow.popupType" @closeBtn="cancellationRefsh"></openAccount>

        <consolidatedAccount v-if="diaWindow.popupType == 'consolidated'" ref="consolidatedRef"
          :rowData="diaWindow.rowData" :popupType="diaWindow.popupType" @closeBtn="cancellationRefsh">
        </consolidatedAccount>

        <accountDetail v-if="diaWindow.popupType == 'view'" ref="accountDetailRef" :rowData="diaWindow.rowData"
          :popupType="diaWindow.popupType"></accountDetail>

        <el-form v-if="diaWindow.popupType == 'batch'" :model="queryParams" ref="queryForm" :inline="true"
          label-width="82px">
          <el-form-item label="导入模版:" prop="type">
            <div class="flex a-link" @click="importTemplate">
              <el-icon>
                <UploadFilled />
              </el-icon>
              批量开通导入模版
            </div>
          </el-form-item>
          <el-form-item label="导入文件:" prop="type">
            <UploadFile ref="uploadRef" :isDrag="false" :uploadData="extraUploadFileData" :action="upload.url"
              :limit="1" :loading="upload.isUploading" :accept="'.xlsx, .xls'" :disabled="upload.isUploading"
              :auto-upload="false" tip="提示：仅允许导入“xls”或“xlsx”格式文件！<br>" @file-success="handleFileSuccess"
              @file-error="handleFileError" @update:file-list="handleFileListUpdate" />
          </el-form-item>
        </el-form>

        <uploadDetail v-if="diaWindow.popupType == 'uploadSuccessdetailList'" ref="uploadDetailRef"
          :detailList="detailList" :uoloadStatus="diaWindow.uoloadStatus" :redisLock="redisLock"
          @closeBtn="cancellationRefsh"></uploadDetail>

          <faceInfo v-if="diaWindow.popupType == 'faceInfo'"   :rowData="diaWindow.rowData" ></faceInfo>
      </template>
    </DialogBox>
  </div>
</template>

<script setup name="accountList">
import { ElMessage, ElMessageBox } from "element-plus";
import { ref, reactive, getCurrentInstance } from "vue";
import { screenIndex } from "@/api/paymentCenter/account-list/index";
import { apiUrl } from "@/utils/config";
import { formatMinuteTime } from "@/utils";
import openAccount from "./components/open-account";
import consolidatedAccount from "./components/consolidated-account";
import accountDetail from "./components/detail.vue";
import uploadDetail from "./components/uploadDetail.vue";
import UploadFile from "@/components/UploadFile/index";
import useUserStore from "@/store/modules/user";
const userStore = useUserStore();
//组件挂载时保存初始的租户ID
const defaultTenantId = ref(userStore.userInfo.tenantId);
const resetKey = ref(0)  // 添加重置标识
const { proxy } = getCurrentInstance();
const { activation_status, staff_type } = proxy.useDict(
  "activation_status",
  "staff_type"
);
const accountTypeList = ref([]);
const accountAddRef = ref(null);
const consolidatedRef = ref(null);
const accountDetailRef = ref(null);
const uploadDetailRef = ref(null);
const redisLock = ref()
// 用户导入参数
const uploadRef = ref(null);
// 用户导入参数
const extraUploadFileData = ref({});
const fileList = ref([])
// 用户导入参数
const upload = reactive({
  title: "",
  isUploading: false,
  updateSupport: 0,
  url: `${import.meta.env.VITE_APP_BASE_API
    }/pay${apiUrl}/pay/payAccountList/importAccount`,
});
const diaWindow = reactive({
  open1: false,
  headerTitle: "",
  popupType: "",
  rowData: "",
  dialogWidth: "30%",
  dialogFooterBtn: false,
  uoloadStatus: '',
  dialogTop:''
});

const pageParams = ref({
  pageNum: 1,
  pageSize: 10,
});
const queryParams = ref({
  tenantId: userStore.userInfo.tenantId,
});
const list = ref([]);
const total = ref(0);
const detailList = ref([]);
const tabelForm = reactive({
  tableKey: "1", //表格key值
  isShowRightToolbar: true, //是否显示右侧显示和隐藏
  showSearch: true,
  // 表格表格数据
  columns: [
    {
      fieldIndex: "staffName", // 对应列内容的字段名
      label: "人员姓名", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "100", //最小宽度%
      width: "", //宽度
      type:'clickText',
    },
    // {
    //   fieldIndex: "loginName", // 对应列内容的字段名
    //   label: "登录名称", // 显示的标题
    //   resizable: true, // 对应列是否可以通过拖动改变宽度
    //   visible: true, // 展示与隐藏
    //   sortable: true, // 对应列是否可以排序
    //   fixed: "", //固定
    //   minWidth: "120", //最小宽度%
    //   width: "", //宽度
    //   align: "", //表格对齐方式
    // },
    // {
    //   fieldIndex: "cellphone", // 对应列内容的字段名
    //   label: "联系方式", // 显示的标题
    //   resizable: true, // 对应列是否可以通过拖动改变宽度
    //   visible: true, // 展示与隐藏
    //   sortable: true, // 对应列是否可以排序
    //   fixed: "", //固定
    //   minWidth: "120", //最小宽度%
    //   width: "", //宽度
    //   type: "phoneHidden",
    // },
    // {
    //   fieldIndex: "department", // 对应列内容的字段名
    //   label: "员工部门", // 显示的标题
    //   resizable: true, // 对应列是否可以通过拖动改变宽度
    //   visible: true, // 展示与隐藏
    //   sortable: true, // 对应列是否可以排序
    //   fixed: "", //固定
    //   minWidth: "180", //最小宽度%
    //   width: "", //宽度
    //   align: "left",
    // },
    // {
    //   fieldIndex: "staffTypeName", // 对应列内容的字段名
    //   label: "人员类型", // 显示的标题
    //   resizable: true, // 对应列是否可以通过拖动改变宽度
    //   visible: true, // 展示与隐藏
    //   sortable: true, // 对应列是否可以排序
    //   fixed: "", //固定
    //   minWidth: "120", //最小宽度%
    //   width: "", //宽度
    // },

    {
      fieldIndex: "accountName", // 对应列内容的字段名
      label: "账户名称", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "160", //最小宽度%
      align: "left",
      width: "", //宽度
    },

    {
      fieldIndex: "accountCode", // 对应列内容的字段名
      label: "账户编码", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "120", //最小宽度%
      width: "", //宽度
    },

    {
      fieldIndex: "state", // 对应列内容的字段名
      label: "开通状态", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "100", //最小宽度%
      width: "", //宽度
      type: "dict",
      dictList: activation_status,
    },

    {
      fieldIndex: "balance", // 对应列内容的字段名
      label: "剩余余额(元)", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "140", //最小宽度%
      width: "", //宽度
      type: "dollor",
    },

    {
      fieldIndex: "createDate", // 对应列内容的字段名
      label: "创建时间", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "180", //最小宽度%
      width: "", //宽度
    },

    {
      label: "操作",
      slotname: "operation",
      width: "160",
      fixed: "right", //固定
      visible: true,
    },
  ],
  // 表格基础配置项，看个人需求添加
  tableConfig: {
    needPage: true, // 是否需要分页
    index: true, // 是否需要序号
    selection: false, // 是否需要多选
    reserveSelection: false, // 是否需要支持跨页选择
    indexFixed: false, // 序号列固定 left true right
    selectionFixed: false, //多选列固定left true right
    indexWidth: "50", //序号列宽度
    loading: false, //表格loading
    showSummary: false, //是否开启合计
    height: null, //表格固定高度 比如：300px
  },
});
// 账户类型
const payAccountTypePageList = async () => {
  const res = await screenIndex.payAccountTypePageList({});

  accountTypeList.value = res.data.records.map((item) => ({
    value: item.typeCode, // 对应 typeCode
    label: item.typeName, // 对应 typeName
    // 保留原始数据（根据需要可选）
    ...item,
  }));
  tabelForm.columns[4].dictList = accountTypeList.value;
};
/** 查询列表 */
const getList = () => {
  tabelForm.tableConfig.loading = true;
  screenIndex.pageList(queryParams.value, pageParams.value).then((response) => {
    list.value = response.data.records;
    total.value = response.data.total;
    tabelForm.tableConfig.loading = false;
  });
};

/** 搜索按钮操作 */
const handleQuery = () => {
  pageParams.value.pageNum = 1;
  getList();
};
/** 重置按钮操作 */
const resetQuery = () => {
  proxy.resetForm("queryForm");
  queryParams.value = {
    tenantId: userStore.userInfo.tenantId,
  };
    //设置当前租户为默认值
    queryParams.value.tenantId = defaultTenantId.value;
  resetKey.value++  // 改变 key 强制组件重建
  handleQuery();
};

/** 导出按钮操作 */

const handleExport = () => {
  proxy.download(
    `pay${apiUrl}/pay/payAccountList/export`,
    {
      ...queryParams.value,
    },
    `账户列表_${formatMinuteTime(new Date())}.xlsx`
  );
};
/** 删除 */
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm("确认要删除吗？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });

    const res = await screenIndex.delete({ id: row.id });
    if (res.code == "1") {
      ElMessage.success("删除成功");
      await getList();
    } else {
       ElMessage.error("删除失败");
    }
  } catch (error) {
    console.error("删除失败:", error);
    // 用户取消删除或其他错误
  }
};
// 启用
const handleSelect = (row) => {
  ElMessageBox.confirm("确认要启用该账户吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      // 调用禁用接口（假设接口为disableAccount，需根据实际API调整）
      screenIndex.update({ id: row.id, state: "1", unionId:row.unionId }).then((response) => {
        if (response.code === "1") {
          ElMessage.success("启用成功");
          getList(); // 刷新列表
        } else {
          ElMessage.error(response.msg);
        }
      });
    })
    .catch(() => { });
};
// 禁用按钮操作
const handleDisabled = (row) => {
  ElMessageBox.confirm("确认要禁用该账户吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      // 调用禁用接口（假设接口为disableAccount，需根据实际API调整）
      screenIndex.update({ id: row.id, state: "2" ,unionId:row.unionId }).then((response) => {
        if (response.code === "1") {
          ElMessage.success("禁用成功");
          getList(); // 刷新列表
        } else {
          ElMessage.error(response.msg);
        }
      });
    })
    .catch(() => { });
};

//账户明细
const handleView = (row) => {
  diaWindow.headerTitle = "账户明细";
  diaWindow.popupType = "view";
  diaWindow.rowData = row; // 如果需要传递数据到弹窗
  diaWindow.dialogFooterBtn = false;
  diaWindow.dialogWidth = "80%";
  diaWindow.dialogTop = ''
  diaWindow.open1 = true;
};
// 开通账户
const handleAdd = () => {
  diaWindow.headerTitle = "开通账户";
  diaWindow.popupType = "openAccount";
  diaWindow.rowData = {}; // 如果需要传递数据到弹窗
  diaWindow.dialogFooterBtn = true;
  diaWindow.dialogWidth = "70%";
  diaWindow.dialogTop = ''
  diaWindow.open1 = true;
};
// 批量开通
const handleBatchAdd = () => {
  diaWindow.headerTitle = "批量开通";
  diaWindow.popupType = "batch";
  diaWindow.rowData = {}; // 如果需要传递数据到弹窗
  diaWindow.dialogFooterBtn = true;
  diaWindow.dialogWidth = "30%";
  diaWindow.dialogTop = ''
  diaWindow.open1 = true;
};
// 批量开通成功列表
const uploadSuccessdetailListFun = () => {
  diaWindow.headerTitle = "批量开通成功列表";
  diaWindow.popupType = "uploadSuccessdetailList";
  diaWindow.dialogFooterBtn = true;
  diaWindow.dialogWidth = "75%";
  diaWindow.dialogTop = ''
  diaWindow.uoloadStatus = 'successed'
  diaWindow.open1 = true;
};

// 批量开通失败列表
const uploadSuccessdetailListFun2 = () => {
  diaWindow.headerTitle = "批量开通失败列表";
  diaWindow.popupType = "uploadSuccessdetailList";
  diaWindow.dialogFooterBtn = false;
  diaWindow.dialogWidth = "75%";
  diaWindow.uoloadStatus = 'failed'
  diaWindow.dialogTop = ''
  diaWindow.open1 = true;
};
// 合并账户
const handleConsolidated = (row) => {
  diaWindow.headerTitle = "合并账户";
  diaWindow.popupType = "consolidated";
  diaWindow.rowData = row; // 如果需要传递数据到弹窗
  diaWindow.dialogFooterBtn = true;
  diaWindow.dialogWidth = "60%";
  diaWindow.dialogTop = ''
  diaWindow.open1 = true;
};

// 人员信息
const clickTextDetail = (row) => {
  diaWindow.headerTitle = "人员信息";
  diaWindow.popupType = "faceInfo";
  diaWindow.rowData = row; // 如果需要传递数据到弹窗
  diaWindow.dialogFooterBtn = false;
  diaWindow.dialogWidth = "530px";
  diaWindow.dialogTop = '18vh'
  diaWindow.open1 = true;
};
const handleFileListUpdate = (files) => {
  fileList.value = files; // 存储文件列表
};
// 模板导出
const importTemplate = () => {
  proxy.download(
    `pay${apiUrl}/pay/payAccountList/templateDownload`,
    {
      ...queryParams.value,
    },
    `开通账户导入模版_${formatMinuteTime(new Date())}.xlsx`
  );
};

// 文件上传成功处理
const handleFileSuccess = (response) => {
  upload.isUploading = false;
  if (response.response.code == "1") {
    setTimeout(() => {
      upload.isUploading = false;
      getImportResult(response.response.data);
    }, 800);
  } else {
    ElMessage.error(response.response.message);
  }
};

// 导入校验 getImportResult
const getImportResult = async (data) => {
  const res = await screenIndex.getImportResult({ redisLock: data });
  redisLock.value = data
  if (res.code === "1") {
    // 0:导入执行中、1:导入失败、2:导入成功
    switch (res.data) {
      case "0":
        // 导入中，轮询检查状态
        setTimeout(() => getImportResult(data), 2000);
        break;
      case "1":
        const successData1 = []
        ElMessage.error(`导入失败`);
        // 清空导入文件和状态
        setTimeout(() => {
          upload.isUploading = false;
          uploadRef.value.clearFiles();
          uploadSuccessdetailListFun2();
          detailList.value = successData1.data;
          getList(); // 刷新列表
        }, 1000);


        // 修改为确认框只留下确定按钮
        // ElMessageBox.confirm("导入失败，是否下载失败数据？", "提示", {
        //   confirmButtonText: "下载",
        //   cancelButtonText: "取消",
        //   type: "error",
        //   closeOnClickModal: false, // 点击遮罩层不关闭
        // })
        //   .then(() => {
        //     proxy.download(
        //       `pay${apiUrl}/pay/payAccountList/exportErrorData`,
        //       {
        //         redisLock: data,
        //       },
        //       `批量开通导入失败列表_${formatMinuteTime(new Date())}.xlsx`
        //     );
        //     // 清空导入文件和状态
        //     upload.isUploading = false;
        //     uploadRef.value.clearFiles();
        //   })
        //   .catch(() => {
        //     // 清空导入文件和状态
        //     upload.isUploading = false;
        //     uploadRef.value.clearFiles();
        //   });

        // 处理失败数据...
        break;
      case "2":
        // 导入成功，获取成功数据
        const successData = await screenIndex.getImportData({
          redisLock: data,
        });
        ElMessage.success(`导入成功，成功${successData.data.length}条`);
        // 清空导入文件和状态
        setTimeout(() => {
          upload.isUploading = false;
          uploadRef.value.clearFiles();
          uploadSuccessdetailListFun();
          detailList.value = successData.data;
          getList(); // 刷新列表
        }, 1000);

        break;
    }
  }
  return res;
};
// 文件上传失败处理
const handleFileError = () => {
  upload.isUploading = false;
  ElMessage.error("导入失败");
};
/** 点击确定保存 */
const save = () => {
  if (diaWindow.popupType == "openAccount") {
    accountAddRef.value.saveBtn();
  }

  if (diaWindow.popupType == "consolidated") {
    consolidatedRef.value.submitForm();
  }

  if (diaWindow.popupType == "batch") {
    if (fileList.value.length === 0) {
      ElMessage.error("请选择文件");
      return false;
    }
    upload.isUploading = true;
    uploadRef.value.submitFileForm();
  }

  if (diaWindow.popupType == "uploadSuccessdetailList") {
    uploadDetailRef.value.saveBtn();
  }
};
/** 点击确定后刷新 */
const cancellationRefsh = () => {
  close(false);
  getList();
};
/** 点击取消保存 */
const cancellation = (val) => {
  close(false);
};

/** 关闭弹窗 */
const close = (val) => {
  diaWindow.open1 = val;
};

onMounted(() => {
  payAccountTypePageList();
  getList();
});
</script>

<style lang="scss" scoped>
.dep-card {
  min-height: calc(100vh - 160px);
}

.icon-btn {
  font-size: 16px;
  margin-right: 5px;
}

.a-link {
  color: #c20000;
  align-items: center;
  cursor: pointer;

  :deep(.el-icon) {
    margin-right: 8px;
  }
}
</style>
