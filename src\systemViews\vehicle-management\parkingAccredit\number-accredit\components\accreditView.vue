<template>
  <div class="dialog-box"  :class="popupType === 'view' ? 'dialog-box-view' : 'dialog-box-edit'">
    <el-form
      ref="formRef"
      :model="formData"
      label-width="80px"
      :rules="popupType !== 'view' ? rules : {}"
    >
      <!-- 各表单项 -->
      <el-row>
        <el-col :span="popupType == 'view'?12:24">
          <el-form-item label="停车场" prop="abbreviation">
            <div>{{ rowData.parkingName || "" }}</div>
          </el-form-item>
        </el-col>

      <!-- 其他表单项结构类似，保持原有模板结构 -->

        <el-col :span="popupType == 'view'?12:24">
          <el-form-item label="车牌号码" prop="abbreviation">
            <div>{{ rowData.plateNo || "" }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            v-if="popupType == 'view'"
            label="人员姓名"
            prop="abbreviation"
          >
            <div>{{ rowData.nickName || "" }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            v-if="popupType == 'view'"
            label="人员部门"
            prop="abbreviation"
          >
            <div>{{ rowData.deptName || "" }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="车辆类型" prop="abbreviation">
            <div>
              {{
                
                formatDictValue(rowData.vehicleType,parking_rule_vehicle_type)
              }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            v-if="popupType == 'view'"
            label="授权类型"
            prop="abbreviation"
          >
            <div>
              {{
                
                formatDictValue(rowData.accreditType,parking_accredit_type)
              }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            v-if="popupType == 'view'"
            label="有效状态"
            prop="abbreviation"
          >
            <div>
              {{
              
                formatDictValue(  rowData.accreditStatus,accredit_status)
              }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            v-if="popupType == 'view'"
            label="创建时间"
            prop="abbreviation"
          >
            <div>{{ rowData.createDate || "" }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            v-if="popupType == 'view'"
            label="操作人"
            prop="abbreviation"
          >
            <div>{{ rowData.updateName || "" }}</div>
          </el-form-item>
        </el-col>

        <el-col :span="popupType == 'view'?12:24">
          <el-form-item label="有效期限">
            <el-date-picker
              v-if="popupType !== 'view'"
              v-model="formData.expirationTime"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD 23:59:59"
              type="date"
              :disabled-date="disabledDate"
              placeholder="默认长期有效"
            />
            <div v-else>{{ rowData.expirationTime || "长期有效" }}</div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { updateNumberAccredit } from "@/api/majorParking/accredit/accredit.js";
const { proxy } = getCurrentInstance();
const { parking_rule_vehicle_type, parking_accredit_type, accredit_status } =
proxy.useDict(
"parking_rule_vehicle_type",
"parking_accredit_type",
"accredit_status"
);
const emit = defineEmits(["close"]);
// 组件属性
const props = defineProps({
closeBtn: {
type: Function,
default: () => {},
},
popupType: {
type: String,
default: "",
},
rowData: {
type: Object,
default: () => ({}),
},
});

// 表单引用
const formRef = ref(null);

// 响应式数据
const formData = reactive({
id: "",
expirationTime: "",
});



// 初始化表单数据
const initFormData = () => {
// formData.id = props.rowData.id;
// if (props.rowData.expirationTime) {
//   formData.expirationTime = props.rowData.expirationTime + " 23:59:59";
// }

formData.id = props.rowData.id;
if (props.rowData.expirationTime && props.rowData.expirationTime !== "长期有效") {
formData.expirationTime = props.rowData.expirationTime + ' 23:59:59';
} else {
formData.expirationTime = '';
}
};
/**
* 格式化字典值显示
* @param {string} value - 原始值
* @param {Array} dictList - 字典列表
* @returns {string} 格式化后的显示值
*/
const formatDictValue = (value, dictList) => {
const item = dictList.find((item) => item.value === value);
return item?.label || "";
};

/**
* 保存表单
*/
const saveBtn = async () => {
try {
// 表单验证
await formRef.value.validate();

// 调用API
const res = await updateNumberAccredit(formData);
if (res.code == '1') {
  ElMessage.success("保存成功");
  emit('close');
}
} catch (error) {
console.error("保存失败:", error);
}
};


/**
* 禁用日期判断
*/
const disabledDate = (time) => {
const today = new Date();
today.setHours(0, 0, 0, 0);
return time.getTime() < today.getTime();
};

// 组件挂载时初始化
onMounted(() => {
initFormData();
});

defineExpose({
saveBtn
})
</script>

<style scoped lang="scss">

</style>