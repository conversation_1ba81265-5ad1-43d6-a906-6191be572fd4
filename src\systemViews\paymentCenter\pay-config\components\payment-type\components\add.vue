<!-- 新增支付类型 -->

<template>
  <div class="dialog-box" :class="popupType === 'view' ? 'dialog-box-view' : 'dialog-box-edit'">
    <el-form ref="formRef" :model="formData" label-width="90px" :rules="popupType !== 'view' ? rules : ''">
      <el-row>
        <el-col :span="12">
          <el-form-item label="支付场景" prop="sceneId">
            <!-- 添加或编辑时显示输入框 -->
            <el-select v-if="popupType !== 'view'" v-model="formData.sceneId" placeholder="请选择支付场景" collapse-tags
              filterable clearable multiple>
              <el-option v-for="(item, index) in paySceneList" :key="index" :label="item.label" :value="item.value" />
            </el-select>
            <!-- 查看时显示文本 -->

            <div v-else>
              {{ formData.sceneName || "" }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="类型名称" prop="typeName">
            <el-input v-model="formData.typeName" :disabled="popupType === 'view'" placeholder="请输入类型名称" clearable
              v-if="popupType !== 'view'" />

            <div v-else>
              {{ formData.typeName || "" }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="类型编码" prop="typeCode">
            <el-input v-model="formData.typeCode" :disabled="popupType === 'view'" placeholder="请输入类型编码" clearable
              v-if="popupType !== 'view'" />

            <div v-else>
              {{ formData.typeCode || "" }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="结算类型" prop="settleType">
            <el-select v-if="popupType !== 'view'" v-model="formData.settleType" placeholder="请选择结算类型" collapse-tags
              filterable clearable>
              <el-option v-for="(item, index) in settle_type" :key="index" :label="item.label" :value="item.value" />
            </el-select>
            <div v-else>
              {{ formatCommonDict(formData.settleType, settle_type) }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="排序" prop="typeSort">
            <!-- 添加或编辑时显示选择器 -->
            <NumberInput v-if="popupType !== 'view'" v-model="formData.typeSort" customPlaceholder="请输入排序"
              input-type="integer" />

            <!-- 查看时显示文本 -->

            <div v-else>
              {{ formData.typeSort }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="支付状态" prop="status">
            <!-- 添加或编辑时显示单选按钮 -->

            <el-radio-group v-model="formData.status" v-if="popupType !== 'view'">
              <el-radio-button :label="item.value" v-for="(item, index) of type_status"
                :key="index">{{ item.label }}</el-radio-button>
            </el-radio-group>

            <!-- 查看时显示文本 -->

            <span class="dialog-text" v-else>
              {{ $formatDictLabel(formData.status, type_status) || "" }}
            </span>
          </el-form-item>
        </el-col>



      </el-row>


    </el-form>


  </div>
</template>



<script setup>
import { ref, reactive, getCurrentInstance, defineProps, onMounted } from "vue";

import { ElMessage } from "element-plus";

import {
  screenIndex
} from "@/api/paymentCenter/account-management/payment-type/index";

// 正确引入 payScene 的 screenIndex
import { screenIndex as paySceneScreenIndex } from "@/api/paymentCenter/payScene/index";

// 定义组件 props
const props = defineProps({
  closeBtn: {
    type: Function,

    default: null,
  },

  popupType: {
    type: String,
    default: "",
  },

  rowData: {
    type: Object,
    default: () => ({}),
  },
});

// 字典
const { proxy } = getCurrentInstance();
const {
  settle_type,
  type_status
} = proxy.useDict(
  "settle_type",
  "type_status"
);
// 定义 emits
const emit = defineEmits(["closeBtn"]);
// 定义状态
let formData = ref({
  status:'0',
  sceneId:[]
});

// 定义校验规则

const rules = reactive({
  typeName: [
    { required: true, message: '请输入类型名称', trigger: ['blur', 'change'] }
  ],
  typeCode: [
    { required: true, message: '请输入类型编码', trigger: ['blur', 'change'] },
    {
      pattern: /^[\x21-\x7E]+$/, // 允许所有可打印的 ASCII 字符（不包含空格）
      message: "类型编码只能包含字母、数字和特殊符号",
      trigger: ["blur", "change"]
    }
  ],
  sceneId: [
    { required: true, message: '请选择支付场景', trigger: ['blur', 'change'] }
  ],
  settleType: [
    { required: true, message: '请选择结算类型', trigger: ['blur', 'change'] }
  ],
  typeSort: [
    { required: true, message: '请输入支付顺序', trigger: ['blur', 'change'] }
  ],
  status: [
    { required: true, message: '请选择支付状态', trigger: ['blur', 'change'] }
  ],
})

// 表单引用

const formRef = ref(null);

const formatCommonDict = (value, dict) => {
  if (!value) return "";
  // 统一处理字符串，分割后映射标签
  const values = Array.isArray(value) ? value : value.split(",");
  const labels = values.map((v) => {
    const item = dict.find((item) => item.value === v);
    return item ? item.label : v;
  });
  return labels.join(", ");
};

// 支付场景列表
const paySceneList = ref([]);

// 定义方法


const initData = async () => {
  // 获取支付场景树
  try {
    const res = await paySceneScreenIndex.paySceneTree({});
    if (res && res.data) {
      // 处理为扁平列表（如有树结构可调整）
      paySceneList.value = Array.isArray(res.data) ? res.data.map(item => ({
        label: item.sceneName,
        value: item.id
      })) : [];
    }
  } catch (e) {
    paySceneList.value = [];
  }
  if (props.popupType === "add") {
  } else {
    formData.value = JSON.parse(JSON.stringify(props.rowData))
    // 将逗号分隔的sceneId转换为数组
    if (formData.value.sceneId && typeof formData.value.sceneId === 'string') {
      formData.value.sceneId = formData.value.sceneId.split(',');
    }
  }

};



const saveForm = async () => {
  try {
    await formRef.value.validate();
    // 只保留需要的字段
    const fields = [
      'id',
      'typeName',
      'typeCode',
      'sceneId',
      'settleType',
      'status',
      'typeSort',
      'remark',
    ];
    const submitData = {};
    fields.forEach(key => {
      if (formData.value[key] !== undefined) {
        if (key === 'sceneId' && Array.isArray(formData.value[key])) {
          // 如果是多选的支付场景，则将数组用逗号连接
          submitData[key] = formData.value[key].join(',');
        } else {
          submitData[key] = formData.value[key];
        }
      }
    });
    if (props.popupType === "edit") {
      const res = await screenIndex.updatePayType(submitData);
      if (res.code == "1") {
        ElMessage.success("修改成功");
        emit("closeBtn");
      }
    } else if (props.popupType === "add") {
      const res = await screenIndex.savePayType(submitData);
      if (res.code == "1") {
        ElMessage.success("新增成功");
        emit("closeBtn");
      }
    }
  } catch (error) {
    console.error("表单校验失败:", error);
  }
};



// 初始化数据

onMounted(() => {
  initData();
});

defineExpose({
  saveForm,
});
</script>



<style scoped lang="scss"></style>