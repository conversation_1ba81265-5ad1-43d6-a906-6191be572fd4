import request from "@/utils/request";

export function list(data) {
    return request({
        url: "/cms/v1.0/app/category/list",
        method: "post",
        data
    });
}

export function listAll(data) {
    return request({
        url: "/cms/v1.0/app/category/listAll",
        method: "post",
        data
    });
}

export function update(data) {
    return request({
        url: "/cms/v1.0/app/category/update",
        method: "post",
        data
    });
}

export function addCategory(data) {
    return request({
        url: "/cms/v1.0/app/category/add",
        method: "post",
        data
    });
}

// export function deleteCategory(data) {
//   return request({
//     url: "/cms/v1.0/app/category/delete",
//     method: "delete",
//     data
//   });
// }

export function deleteCategory(data) {
    return request({
        url: "/cms/v1.0/app/category/delete",
        method: "post",
        data
    });
}
export function addCenter(data) {
    return request({
        url: "/cms/v1.0/app/center/add",
        method: "post",
        data
    });
}

export function updCenter(data) {
    return request({
        url: "/cms/v1.0/app/center/update",
        method: "post",
        data
    });
}

// export function deleteCenter(data) {
//   return request({
//     url: "/cms/v1.0/app/center/delete",
//     method: "delete",
//     data
//   });
// }
export function deleteCenter(data) {
    return request({
        url: "/cms/v1.0/app/center/delete",
        method: "post",
        data
    });
}
export function getCenter(data) {
    return request({
        url: "/cms/v1.0/app/center/getCenterById",
        method: "post",
        data
    });
}

export function authApp(data) {
    return request({
        url: "/cms/v1.0/app/center/addTenantApp",
        method: "post",
        data
    })
}

export function selectTenantIds(data) {
    return request({
        url: "/cms/v1.0/app/center/selectTenantIds",
        method: "get",
        params: {
            appCenterId: data,
        }
    })
}

export function selectTenantApps(data) {
    return request({
        url: "/cms/v1.0/app/center/selectTenantApps",
        method: "get",
        params: {
            type: data,
        }
    })
}

export function selectRoleApps(data) {
    return request({
        url: "/cms/v1.0/app/center/selectRoleApps",
        method: "get",
        params: {
            type: data,
        }
    })
}

export function getHadRolesAndNoRoles(params) {
    return request({
        url: "/cms/v1.0/app/center/getRoles",
        method: "get",
        params
    });
}

export function addAppRole(appCenterId, roleId) {
    return request({
        url: "/cms/v1.0/app/center/updateRole/" + appCenterId + "/" + roleId,
        method: "post"
    });
}
export function delAppRole(appCenterId, roleId) {
    return request({
        url: "/cms/v1.0/app/center/deleteRole/" + appCenterId + "/" + roleId,
        method: "post"
    });
}

