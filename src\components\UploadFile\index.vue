<template>
  <div>
  <el-upload v-if="isDrag" ref="upload" :limit="limit" :accept="accept" :headers="headers" :action="action"
    :data="uploadData" :disabled="disabled" :on-progress="handleProgress" :on-success="handleSuccess"
    :on-error="handleError" :before-upload="beforeUpload" :auto-upload="autoUpload" drag
    @update:file-list="onFileListUpdate">
    <i class="el-icon-upload"></i>
    <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>

    <div v-if="loading" class="upload-loading">
      <el-icon class="is-loading">
        <Loading />
      </el-icon>
      上传中...
    </div>
    <slot name="tip">
      <div class="el-upload__tip" slot="tip" v-html="tip"></div>
    </slot>
  </el-upload>

  <el-upload v-if="!isDrag" ref="upload" :limit="limit" :accept="accept" :headers="headers" :action="action"
    :data="uploadData" :disabled="disabled" :on-progress="handleProgress" :on-success="handleSuccess"
    :on-error="handleError" :before-upload="beforeUpload" :auto-upload="autoUpload"
    @update:file-list="onFileListUpdate">
    <div class="el-upload__text-2">


      <el-button type="primary" icon="CirclePlus" @click="handleQuery">选取文件</el-button>
    </div>

   
    <slot name="tip">
      <div class="el-upload__tip-2" slot="tip" v-html="tip"></div>
    </slot>
    
  </el-upload>

  <div v-if="loading && !isDrag" class="upload-loading">
      <el-icon class="is-loading">
        <Loading />
      </el-icon>
      上传中...
    </div>
</div>
</template>

<script setup>
import { ref, defineEmits, defineProps } from "vue";
import { getToken } from "@/utils/auth";
const headers = ref({ Authorization: `Bearer ${getToken()}` });
// 定义组件的属性
defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
  limit: {
    type: Number,
    default: 1,
  },
  accept: {
    type: String,
    default: ".xlsx, .xls",
  },
  action: {
    type: String,
    required: true,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  autoUpload: {
    type: Boolean,
    default: false,
  },
  tip: {
    type: String,
    default: "提示：仅允许导入“xls”或“xlsx”格式文件！",
  },
  uploadData: {
    type: Object,
    default: () => { },
  },

  isDrag: {
    type: Boolean,
    default: true,
  },
});

// 定义组件的事件
const emit = defineEmits([
  "update:file-list",
  "file-progress",
  "file-success",
  "file-error",
  "before-upload",
  "submit",
]);

// 定义文件上传的逻辑
const upload = ref(null);

const handleProgress = (event, file, fileList) => {
  emit("file-progress", { event, file, fileList });
};

const handleSuccess = (response, file, fileList) => {
  emit("file-success", { response, file, fileList });
};

const handleError = (error, file, fileList) => {
  emit("file-error", { error, file, fileList });
};

const beforeUpload = (file) => {
  emit("before-upload", file);
  // 返回 true 继续上传，返回 false 阻止上传
  return true;
};

const submitFileForm = () => {
  upload.value.submit();
  emit("submit");
};

// 文件列表更新
const onFileListUpdate = (fileList) => {
  emit("update:file-list", fileList);
};

const clearFiles = () => {
  upload.value.clearFiles();
};
defineExpose({
  submitFileForm,
  clearFiles,
});
</script>

<style scoped>
.el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.el-upload:hover {
  border-color: #409eff;
}

.el-icon-upload {
  font-size: 28px;
  color: #8c939d;
  min-width: 50px;
  min-height: 50px;
  text-align: center;
  line-height: 50px;
}

.el-upload__text {
  margin: 16px 0 16px;
  text-align: center;
}

.el-upload__text-2 {
  margin: 0px;
  text-align: center;
}

.el-upload__tip {
  color: #c20000;
  text-align: center;
  line-height: 22px;
}

.el-upload__tip-2 {
  color: #8c939d;
  text-align: center;
  line-height: 22px;
  padding-left: 12px;
  font-size: 13px;
}

.upload-loading {
  margin-top: 10px;
  color: #409eff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 13px;
}

.upload-loading .el-icon {
  margin-right: 5px;
  animation: rotating 2s linear infinite;
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>