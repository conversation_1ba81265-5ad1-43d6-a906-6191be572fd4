<!-- 员工车辆 -->
<template>
    <div class="container-table-box" >
      <el-row :gutter="24">
        <el-col :span="24" :xs="24">
  
          <PublicTable ref="publictable" :rowKey="tabelForm.tableKey" :tableData="userList" :columns="tabelForm.columns"
            :configFlag="tabelForm.tableConfig" :pageValue="pageParams" :total="total" :getList="getList">
            <!-- 有效期限 -->
            <template #validDate="{ scope }">
              {{ scope.row.validDate || "长期有效" }}
            </template>

            <template #importTypeSlot="{ scope }">
            <div class="lv-block-2" > {{ scope.row.importType  }}</div> 
            </template>
            <template #tipsSlot="{ scope }">
            <div class="lv-block" v-if="scope.row.tips=='新增成功' || scope.row.tips=='更新成功'"> {{ scope.row.tips  }}</div> 
            <div class="red-block" v-else> {{ scope.row.tips  }}</div> 
            </template>
  
          </PublicTable>
  
            
  
  
        </el-col>
      </el-row>
    </div>
  </template>
  
  <script setup>
  import { reactive, ref, onMounted, getCurrentInstance } from "vue";
  const { proxy } = getCurrentInstance();
  // 字典数据
  const { parking_rule_vehicle_type,vehicle_specification,car_status,plate_color,car_type} = proxy.useDict(
    "parking_rule_vehicle_type",
    "vehicle_specification",
    "car_status",
      "plate_color",
      "car_type"
  );


  const props = defineProps({
  rowData: {
    type: Array,
    default: () => [],
  },
});


  // 表格配置
  const tabelForm = ref({
    tableKey: "1",
    isShowRightToolbar: true,
    showSearch: true,
    columns: [
      {
        fieldIndex: "plateNo",
        label: "车牌号码",
        resizable: true,
        visible: true,
        sortable: true,
        minWidth: "120px", //最小宽度%
      },
      {
        fieldIndex: "vehicleType",
        label: "车辆编组",
        resizable: true,
        visible: true,
        sortable: true,
        minWidth: "160px", //最小宽度%
        type: "dict",
        dictList: parking_rule_vehicle_type,
      },
      {
        fieldIndex: "carType",
        label: "车辆类型",
        resizable: true,
        visible: true,
        sortable: true,
        minWidth: "160px", //最小宽度%
        type: "dict",
        dictList: car_type,
      },
      {
        fieldIndex: "staffName",
        label: "人员姓名",
        resizable: true,
        minWidth: "120px", //最小宽度%
        visible: true,
        sortable: true,
      },
      {
        fieldIndex: "orgName",
        label: "人员部门",
        resizable: true,
        minWidth: "120px", //最小宽度%
        visible: true,
        sortable: true,
      },
      {
        fieldIndex: "cellphone",
        label: "手机号码",
        resizable: true,
        visible: true,
        minWidth: "120px", //最小宽度%
        sortable: true,
      },
      {
        fieldIndex: "carStatus",
        label: "车辆状态",
        resizable: true,
        visible: true,
        sortable: true,
        minWidth: "120px", //最小宽度%
        type: "dict",
        dictList: car_status,
      },
      {
        fieldIndex: "validDate",
        slotname: "validDate",
        label: "有效期限",
        resizable: true,
        minWidth: "120px", //最小宽度%
        visible: true,
        sortable: true,
      },
      {
        fieldIndex: "carBrand",
        label: "车辆品牌",
        resizable: true,
        minWidth: "120px", //最小宽度%
        visible: true,
        sortable: true,
      },
      {
        fieldIndex: "carModel",
        label: "品牌型号",
        resizable: true,
        visible: true,
        minWidth: "120px", //最小宽度%
        sortable: true,
      },
      {
        fieldIndex: "vehicleSpecification",
        label: "车辆规格",
        resizable: true,
        visible: true,
        sortable: true,
        minWidth: "120px", //最小宽度%
        type: "dict",
        dictList: vehicle_specification,
      },
      {
        fieldIndex: "carColor",
        label: "车辆颜色",
        resizable: true,
        minWidth: "120px", //最小宽度%
        visible: true,
        sortable: true,
      },
      {
        fieldIndex: "plateColor",
        label: "车牌颜色",
        resizable: true,
        minWidth: "110px", //最小宽度%
        visible: true,
        sortable: true,
        type: "dict",
        dictList: plate_color,
      },
      {
        fieldIndex: "remark",
        label: "车辆备注",
        resizable: true,
        minWidth: "120px", //最小宽度%
        visible: true,
        sortable: true,
        align: "left",
      },

      {
        fieldIndex: "importType",
        label: "导入类型",
        resizable: true,
        visible: true,
        sortable: true,
        minWidth: "120px", //最小宽度%
        slotname:'importTypeSlot',
        fixed: "right",
    
      },
      {
        fieldIndex: "tips",
        label: "导入提示",
        resizable: true,
        visible: true,
        sortable: true,
        minWidth: "200px", //最小宽度%
        slotname:'tipsSlot',
        fixed: "right",
    
      },
    ],
    tableConfig: {
      needPage: true,
      index: true,
      selection: false,
      reserveSelection: false,
      indexFixed: false,
      selectionFixed: false,
      indexWidth: "50",
      loading: false,
      showSummary: false,
      height: '600px',
    },
  });
  
  // 表格数据
  const userList = ref([]);
  
  // 生命周期钩子：组件挂载时调用
  onMounted(() => {
    userList.value = props.rowData
  });
  </script>
  
  <style scoped>
  /* 添加自定义样式 */
  .lv-block{
    background: #F0F9EB;
    color: #74C74B;
    width: 120px;
    margin: 0 auto;
  }
  .lv-block-2{
    background: #F0F9EB;
    color: #74C74B;
    margin: 0 auto;
  }

  .red-block{
    background: #c20000;
    color: #ffffff;
    padding: 0px 10px;
    margin: 0 auto;
  }
  </style>
  