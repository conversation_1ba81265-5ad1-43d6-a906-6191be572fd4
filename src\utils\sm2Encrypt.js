import { sm2 } from "sm-crypto-v2";
import { getPublic<PERSON>ey } from "@/api/login";

let publicKey;
/**
 * sm2加密方法
 */
export async function doCrypt(msgString, cipherMode) {
  if (!publicKey) {
    await initPublicKey();
  }
  const encryptData = sm2.doEncrypt(msgString, publicKey, cipherMode);
  return `04${encryptData}`;
}

async function initPublicKey() {
  return new Promise((resolve, reject) => {
    getPublicKey()
      .then((res) => {
        publicKey = res;
        resolve();
      })
      .catch((error) => {
        reject(error);
      });
  });
}
