<!-- 补贴管理 -->
<template>
  <div class="container-table-box">
    <Splitpanes class="default-theme">
      <Pane :size="100" :min-size="65">
        <el-card class="dep-card">
          <!-- 搜索区域 -->
          <dialog-search
            @getList="getList"
            formRowNumber="3"
            :columns="tabelForm.columns"
            :isShowRightBtn="$checkPermi(['pay:subsidy:list'])"
          >
            <template v-slot:formList>
              <el-form
                :model="queryParams"
                ref="queryForm"
                :inline="true"
                label-width="90px"
              >
                <el-form-item label="供应商名称">
                  <el-input
                    v-model="queryParams.providerName"
                    placeholder="请输入供应商名称"
                    clearable
                  />
                </el-form-item>
                <el-form-item label="支付场景">
                  <el-select
                    v-model="queryParams.payType" 
                    placeholder="请选择支付场景"
                    clearable
                  >
                    <el-option
                      v-for="item in orderSubclassOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="支付类型">
                  <el-select
                    v-model="queryParams.paySubclass"
                    placeholder="请选择支付类型"
                    clearable
                  >
                    <el-option
                      v-for="item in largeCategoryOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-form>
            </template>

            <template v-slot:searchList>
              <el-button type="primary" icon="Search" @click="handleQuery"  v-hasPermi="['pay:subsidy:list']"
                >搜索</el-button
              >
              <el-button icon="Refresh" @click="resetQuery"  v-hasPermi="['pay:subsidy:list']">重置</el-button>
            </template>

            <template v-slot:searchBtnList>
              <el-button type="primary" icon="Plus" @click="handleAdd"  v-hasPermi="['pay:subsidy:add']"
                >新增</el-button
              >
              <!-- <el-button size="mini" icon="Download" @click="handleExport"
                >导出</el-button
              > -->
            </template>
          </dialog-search>

          <!-- 表格区域 -->
          <public-table
            ref="publictable"
            :rowKey="tabelForm.tableKey"
            :tableData="list"
            :columns="tabelForm.columns"
            :configFlag="tabelForm.tableConfig"
            :pageValue="pageParams"
            :total="total"
            :getList="getList"
          >
            <!-- 默认价格 -->
            <template #defaultAmount="{ scope }">
              <div>
                {{
                  scope.row.priceType === '1' 
                    ? scope.row.defaultAmount
                    : "-"
                }}
              </div>
            </template>
            <template #operation="{ scope }">
              <el-button
                type="text"
                title="查看"
                    v-hasPermi="['pay:settle:info']"
                icon="View"
                @click="handleView(scope.row)"
              ></el-button>
              <el-button
                type="text"
                title="修改"
                icon="Edit"
                 v-hasPermi="['pay:subsidy:edit']"
                @click="handleEdit(scope.row)"
              ></el-button>
              <el-button
                type="text"
                title="删除"
                icon="Delete"
                  v-hasPermi="['pay:subsidy:remove']"
                @click="handleDelete(scope.row)"
              ></el-button>
            </template>
          </public-table>
        </el-card>
      </Pane>
    </Splitpanes>

    <DialogBox
      :visible="diaWindow.open1"
      :dialogWidth="diaWindow.dialogWidth"
      :dialogFooterBtn="diaWindow.dialogFooterBtn"
      @save="save"
      @cancellation="cancellation"
      :custom-class="diaWindow.customClass"
      @close="close"
      :dialogTitle="diaWindow.headerTitle"
      :dialogTop="diaWindow.dialogTop"
    >
      <template #content>
        <accountAdd
          ref="accountAddRef"
          :rowData="diaWindow.rowData"
          :popupType="diaWindow.popupType"
          @closeBtn="cancellationRefsh"
        ></accountAdd>
      </template>
    </DialogBox>
  </div>
</template>

<script setup>
import { screenIndex } from "@/api/paymentCenter/subsidy-management/index";
import { ref, reactive, getCurrentInstance, onMounted } from "vue";
import { formatMinuteTime } from "@/utils";
import accountAdd from "./components/add.vue";
import {ElMessageBox,ElMessage} from 'element-plus';
const { proxy } = getCurrentInstance();
const { 
       date_kind,
       first_subsidy,
       consume_type,
       consume_num,
       subsidy_status,
       price_type 
      } = proxy.useDict(
        "date_kind",
        "first_subsidy",
        "consume_type",
        "consume_num",
        "subsidy_status",
        "price_type"
      );
const accountAddRef = ref(null);
const diaWindow = reactive({
  open1: false,
  headerTitle: "",
  popupType: "",
  rowData: "",
  dialogWidth: "20%",
  dialogFooterBtn: false,
  customClass: "",
    dialogTop:'12%'
});
// 支付大类选项
const largeCategoryOptions = ref([
]);

// 支付小类选项
const orderSubclassOptions = ref([
]);

// 表格配置
const tabelForm = reactive({
  tableKey: "subsidy",
  columns: [
    {
      fieldIndex: "providerName",
      label: "供应商名称",
      minWidth: 150,
      sortable: true,
      visible: true,
      align: "left",
    },
    {
      fieldIndex: "payTypeName",
      label: "支付场景",
      minWidth: 140,
      sortable: true,
      type: "tag",
      visible: true,
    },
    {
      fieldIndex: "paySubclassName",
      label: "支付类型",
      minWidth: 140,
      sortable: true,
      type: "tag",
      visible: true,
    },
    {
      fieldIndex: "accountName",
      label: "账户名称",
      minWidth: 180,
      sortable: true,
      type: "tag",
      visible: true,
      align: "left",
    },
    {
      fieldIndex: "priceType",
      label: "价格类型",
      minWidth: 120,
      sortable: true,
      type: "dict",
      dictList:price_type,
      visible: true,
    },
    {
      fieldIndex: "defaultAmount",
      label: "默认价格(元)",
      minWidth: 150,
      sortable: true,
      slot: true,
      visible: true,
      slotname: "defaultAmount",
    },
    {
      fieldIndex: "dateType",
      label: "日期类型",
      minWidth: 150,
      sortable: true,
      type: "dicts",
      dictList:date_kind,
      visible: true,
    },
    {
      fieldIndex: "firstSubsidy",
      label: "首次补贴",
      minWidth: 120,
      sortable: true,
      type: "dict",
      dictList:first_subsidy,
      visible: true,
    },
    {
      fieldIndex: "consumeType",
      label: "消费类型",
      minWidth: 120,
      sortable: true,
      type: "dict",
      dictList:consume_type,
      visible: true,
    },
    {
      fieldIndex: "consumeNum",
      label: "消费次数",
      minWidth: 120,
      sortable: true,
      type: "dict",
      dictList:consume_num,
      visible: true,
    },
    {
      fieldIndex: "status",
      label: "补贴状态",
      minWidth: 120,
      sortable: true,
      type: "dict",
      dictList:subsidy_status,
      visible: true,
    },
    {
      label: "操作",
      slotname: "operation",
      minWidth: 120,
      fixed: "right",
      visible: true,
      slot: true,
    },
  ],
  tableConfig: {
    needPage: true,
    index: true,
    selection: false,
    indexWidth: "60",
    loading: false,
    height: null,
  
  },
});



// 模拟数据
const list = ref([
]);

// 查询参数
const queryParams = ref({});
const pageParams = ref({
  pageNum: 1,
  pageSize: 10,
});
const total = ref(1);
// 查询支付类型树
const payTypeTree = async (value,label) => {
  try {
    const res = await screenIndex.payTypeTree({
    "value": value||'',
    });
    
    if(value && label){
      orderSubclassOptions.value = res.data;
    }else{
      largeCategoryOptions.value = res.data;
    }
  } catch (error) {
    console.error('获取支付类型树失败:', error);
  }
};

const paySceneTree= async (value,label) => {
  try {
    const res = await screenIndex.paySceneTree({
    "value": '',
    });
    
    orderSubclassOptions.value = res.data;
  } catch (error) {
    console.error('获取支付类型树失败:', error);
  }
};

const handleChangePayType = async (value) => {
  queryParams.value.paySubclass = '';
  
  const selectedOption = largeCategoryOptions.value.find(item => item.value === value);
  
  if (selectedOption) {
    try {
      await payTypeTree(value, selectedOption.label);

    } catch (error) {
      console.error('获取支付类型失败:', error);
      orderSubclassOptions.value = [];
    }
  }
};
// 方法
const handleQuery = () => {
  pageParams.value.pageNum = 1;
  getList();
};

const resetQuery = () => {
  queryParams.value = {};
  handleQuery();
};

/** 查看 */
const handleView = (data) => {
  diaWindow.headerTitle = "查看补贴管理";
  diaWindow.popupType = "view";
  diaWindow.rowData = data;
  diaWindow.dialogWidth = "52%";
  diaWindow.dialogFooterBtn = false;
  diaWindow.open1 = true;
};

// 新增
const handleAdd = () => {
  diaWindow.headerTitle = "新增补贴管理";
  diaWindow.popupType = "add";
  diaWindow.rowData = {}; // 如果需要传递数据到弹窗
  diaWindow.dialogFooterBtn = true;
  diaWindow.dialogWidth = "55%";
  diaWindow.open1 = true;
};

// 修改
const handleEdit = (row) => {
  diaWindow.headerTitle = "修改补贴管理";
  diaWindow.popupType = "edit";
  diaWindow.rowData = row; // 如果需要传递数据到弹窗
  diaWindow.dialogFooterBtn = true;
  diaWindow.dialogWidth = "48%";
  diaWindow.open1 = true;
};

const handleDelete = async (row) => {
 try {
    await ElMessageBox.confirm("确认要删除吗？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });

    const res = await screenIndex.delete({ id: row.id });
    if (res.code == "1") {
      ElMessage.success("删除成功");
      await getList();
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    console.error("删除失败:", error);
    // 用户取消删除或其他错误
  }

};

const handleExport = () => {
  proxy.download(
    `pay${apiUrl}/pay/paySubsidy/export`,
    {
      ...queryParams.value,
    },
    `补贴管理列表_${formatMinuteTime(new Date())}.xlsx`
  );
};



  /** 点击确定保存 */
  const save = () => {
    if (diaWindow.popupType == "add") {
      accountAddRef.value.saveForm();
    }
  
    if (diaWindow.popupType == "edit") {
      accountAddRef.value.saveForm();
    }
  };
  /** 点击确定后刷新 */
  const cancellationRefsh = () => {
    close(false);
    getList();
  };
  /** 点击取消保存 */
  const cancellation = (val) => {
    close(false);
  };
  
  /** 关闭弹窗 */
  const close = (val) => {
    diaWindow.open1 = val;
  };
  

const getList = () => {
  tabelForm.tableConfig.loading = true;
  screenIndex.pageList(queryParams.value, pageParams.value).then((response) => {
    list.value = response.data.records;
    total.value = response.data.total;
    tabelForm.tableConfig.loading = false;
  });
};
onMounted(() => {
    payTypeTree()
    paySceneTree()
    getList();

});
</script>
