<!-- 订单结算 -->

<template>
  <div class="container-table-box">
    <Splitpanes class="default-theme">
      <Pane :size="100" :min-size="65">
        <el-card class="dep-card">
          <dialog-search
            @getList="getList"
            formRowNumber="3"
            :columns="tabelForm.columns"
             :isShowRightBtn="$checkPermi(['pay:settle:audit:list'])"
          >
            <template v-slot:formList>
              <el-form
                :model="queryParams"
                ref="queryForm"
                :inline="true"
                label-width="75px"
              >
                <el-form-item label="账户名称">
                  <el-input
                    v-model="queryParams.accountName"
                    placeholder="请输入账户名称"
                    clearable
                  ></el-input>
                </el-form-item>

                <el-form-item label="结算状态" prop="settleStatus">
                  <el-select
                    v-model="queryParams.settleStatus"
                    placeholder="请选择类型状态"
                    clearable
                  >
                    <el-option
                      v-for="(item, index) of settle_status"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>

                <el-form-item label="结算时间">
                  <el-date-picker
                    v-model="settleTimes"
                    type="daterange"
                    value-format="YYYY-MM-dd"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                  />
                </el-form-item>
              </el-form>
            </template>

            <template v-slot:searchList>
              <el-button type="primary" icon="Search" @click="handleQuery"  v-hasPermi="['pay:settle:audit:list']"
                >搜索</el-button
              >
              <el-button icon="Refresh" @click="resetQuery"  v-hasPermi="['pay:settle:audit:list']">重置</el-button>
            </template>

            <!-- <template v-slot:searchBtnList>
              <el-button
                type="primary"
                icon="Plus"
                size="mini"
                @click="handleAdd"
                >新增结算</el-button
              >
            </template> -->
          </dialog-search>

          <public-table
            ref="publictable"
            :rowKey="tabelForm.tableKey"
            :tableData="list"
            :columns="tabelForm.columns"
            :configFlag="tabelForm.tableConfig"
            :pageValue="pageParams"
            :total="total"
            :getList="getList"
          >
            <template #statusType="{ scope }">
              <el-radio-group
                v-model="scope.row.status"
                @change="radioChange(scope.row)"
              >
                <el-radio-button
                  :label="item.value"
                  v-for="(item, index) of settle_status"
                  :key="index"
                  >{{ item.label }}</el-radio-button
                >
              </el-radio-group>
            </template>
<template #settlementTime="{ scope }">
             <span>{{scope.row.startTime}}至{{scope.row.endTime}}</span> 
            </template>
            
            <template #operation="{ scope }">
              <el-button
                size="mini"
                type="text"
                title="查看"
                icon="View"
                @click="handleView(scope.row)"
              >
              </el-button>
              <el-button
               v-hasPermi="['pay:settle:audit:add']"
                  size="mini"
                  type="text"
                  title="审核"
                  @click="handleReview(scope.row)"
                >
                <i class="icon iconfont icon-shenhe1"></i>
                </el-button>
            </template>
          </public-table>
        </el-card>
      </Pane>
    </Splitpanes>

    <DialogBox
      :visible="open1"
      :dialogWidth="diaWindow.dialogWidth"
      :dialogFooterBtn="diaWindow.dialogFooterBtn"
      @save="save"
      @cancellation="cancellation"
      :custom-class="diaWindow.customClass"
      @close="close"
      :dialogTitle="diaWindow.headerTitle"
    >
      <template #content>
        <accountAdd
          ref="accountAddRef"
          :rowData="diaWindow.rowData"
          :popupType="diaWindow.popupType"
          @closeBtn="cancellationRefsh"
        ></accountAdd>
      </template>
    </DialogBox>
  </div>
</template>

<script setup name="User">
import { ElMessage, ElMessageBox } from "element-plus";
import { ref, reactive, getCurrentInstance } from "vue";
import { screenIndex } from "@/api/paymentCenter/order-settlement/index";
import accountAdd from "./add.vue";
const { proxy } = getCurrentInstance();
const { settle_status } = proxy.useDict("settle_status");
import useUserStore from "@/store/modules/user";

const userStore = useUserStore();
const accountAddRef = ref(null);
const open1 = ref(false);
const diaWindow = reactive({
  headerTitle: "",
  popupType: "",
  rowData: "",
  dialogWidth: "20%",
  dialogFooterBtn: false,
});

const pageParams = ref({
  pageNum: 1,
  pageSize: 10,
});
const queryParams = ref({
  staffId: userStore.userInfo.staffId
});
const settleTimes = [];
const list = ref([{}]);
const total = ref(0);
const tabelForm = reactive({
  tableKey: "1", //表格key值
  isShowRightToolbar: true, //是否显示右侧显示和隐藏
  showSearch: true,
  // 表格表格数据
  columns: [
    {
      fieldIndex: "providerName", // 对应列内容的字段名
      label: "供应商名称", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "140", //最小宽度%
      width: "", //宽度
      align: "left", //表格对齐方式
    },
    {
      fieldIndex: "accountName", // 对应列内容的字段名
      label: "账户名称", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "150", //最小宽度%
      width: "", //宽度
      align: "left", //表格对齐方式
    },

    {
      slotname: "settlementTime", // 对应列内容的字段名
      label: "结算时间", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "150", //最小宽度%
      width: "", //宽度
    },
    {
      fieldIndex: "settleStatus", // 对应列内容的字段名
      label: "结算状态", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "100", //最小宽度%
      width: "", //宽度
      align: "", //表格对齐方式
      type: "dict",
      dictList: settle_status,
    },

    {
      fieldIndex: "updateDate", // 对应列内容的字段名
      label: "操作时间", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "150", //最小宽度%
      width: "", //宽度
    },

    {
      label: "操作",
      slotname: "operation",
      width: "120",
      fixed: "right", //固定
      headerAlign: "center",
      visible: true,
    },
  ],
  // 表格基础配置项，看个人需求添加
  tableConfig: {
    needPage: true, // 是否需要分页
    index: true, // 是否需要序号
    selection: false, // 是否需要多选
    reserveSelection: false, // 是否需要支持跨页选择
    indexFixed: false, // 序号列固定 left true right
    selectionFixed: false, //多选列固定left true right
    indexWidth: "50", //序号列宽度
    loading: false, //表格loading
    showSummary: false, //是否开启合计
    height: null, //表格固定高度 比如：300px
  },
});

// 监听settleTimes变化
watch(settleTimes, (newVal) => {
  if (newVal && newVal.length === 2) {
    queryParams.value.startTime = newVal[0];
    queryParams.value.endTime = newVal[1];
  } else {
    queryParams.value.startTime = "";
    queryParams.value.endTime = "";
  }
});
/** 查询列表 */
const getList = () => {
  tabelForm.tableConfig.loading = true;
  screenIndex
    .settleAuditList(queryParams.value, pageParams.value)
    .then((response) => {
      list.value = response.data.records;
      total.value = response.data.total;
      tabelForm.tableConfig.loading = false;
    });
};

/** 搜索按钮操作 */
const handleQuery = () => {
  pageParams.value.pageNum = 1;
  getList();
};
/** 重置按钮操作 */
const resetQuery = () => {
  proxy.resetForm("queryForm");
  queryParams.value = {};
  handleQuery();
};

/** 删除 */
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm("确认要删除吗？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });

    const res = await screenIndex.delete({ id: row.id });
    if (res.code == "1") {
      ElMessage.success("删除成功");
      await getList();
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    console.error("删除失败:", error);
    // 用户取消删除或其他错误
  }
};

// 审核

const handleReview = (row) => {
    diaWindow.headerTitle = "审核结算";
    diaWindow.popupType = "review";
    diaWindow.rowData = row; // 如果需要传递数据到弹窗
    diaWindow.dialogFooterBtn = false;
    diaWindow.dialogWidth = "85%";
    open1.value = true;
  };/** 查看 */
const handleView = (data) => {
  diaWindow.headerTitle = "查看结算";
  diaWindow.popupType = "view";
  diaWindow.rowData = data;
  diaWindow.dialogWidth = "85%";
  diaWindow.dialogFooterBtn = false;
  open1.value = true;
};

// 新增
const handleAdd = () => {
  diaWindow.headerTitle = "新增结算";
  diaWindow.popupType = "add";
  diaWindow.rowData = {}; // 如果需要传递数据到弹窗
  diaWindow.dialogFooterBtn = false;
  diaWindow.dialogWidth = "75%";
  open1.value = true;
};

// 修改
const handleEdit = (row) => {
  diaWindow.headerTitle = "修改结算";
  diaWindow.popupType = "edit";
  diaWindow.rowData = row; // 如果需要传递数据到弹窗
  diaWindow.dialogFooterBtn = false;
  diaWindow.dialogWidth = "85%";
  open1.value = true;
};

/** 点击确定保存 */
const save = () => {
  if (diaWindow.popupType == "add") {
    accountAddRef.value.saveForm();
  }

  if (diaWindow.popupType == "edit") {
    accountAddRef.value.saveForm();
  }
};
/** 点击确定后刷新 */
const cancellationRefsh = () => {
  close(false);
  getList();
};
/** 点击取消保存 */
const cancellation = (val) => {
  close(false);
};

/** 关闭弹窗 */
const close = (val) => {
  open1.value = val;
};

getList();
</script>

<style scoped></style>
