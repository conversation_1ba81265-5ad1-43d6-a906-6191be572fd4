<template>
  <div class="app-container">
    <el-card style="height: 100%" shadow="never">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
        <el-form-item label="租户" prop="tenantId" v-if="userStore.userInfo.customParam.userType === 'admin'">
          <el-select v-model="queryParams.tenantId" placeholder="请选择租户" style="width: 180px" @change="handleQuery">
            <el-option v-for="item in tenantList" :key="item.tenantId" :label="item.tenantName" :value="item.tenantId"/>
          </el-select>
        </el-form-item>
        <el-form-item label="终端类型" prop="terminal">
          <el-select v-model="queryParams.terminal" placeholder="终端类型" style="width: 180px" clearable>
            <el-option v-for="dict in menu_terminal" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="广告标题" prop="title" >
          <el-input v-model="queryParams.title" placeholder="请输入广告标题" clearable style="width: 180px" @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item label="类型" prop="advertisingType">
          <el-select v-model="queryParams.advertisingType" placeholder="广告类型" style="width: 180px" clearable >
            <el-option v-for="dict in advertising_type_list" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :guitter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['sys:release:posters:add']">新增</el-button>
        </el-col>
        <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <!-- 表格数据展示 -->
      <el-table v-loading="loading" :data="noticeList">
        <el-table-column label="序号" type="index" width="50" />
        <el-table-column label="广告标题" align="left" prop="title" width="auto" :show-overflow-tooltip="true">
          <template #default="scope">
            <a style="color: #3a7eb9" @click="detail(scope.row)">{{scope.row.title}}</a>
          </template>
        </el-table-column>
        <el-table-column label="广告类型" align="center" prop="advertisingType" width="auto">
          <template #default="scope">
            <dict-tag effect="plain" :options="advertising_type_list" :value="scope.row.advertisingType" />
          </template>
        </el-table-column>
        <el-table-column label="客户端类型" align="center" prop="terminal" width="auto">
          <template #default="scope">
            <dict-tag effect="plain" :options="menu_terminal" :value="scope.row.terminal" />
          </template>
        </el-table-column>
        <el-table-column label="展示位置" align="center" prop="siteName" width="auto">
          <template #default="scope">
            <dict-tag effect="plain" :options="advertising_site" :value="scope.row.site" />
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" prop="statusName" width="auto">
          <template #default="scope">
            <dict-tag :options="advertising_status_list" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="排序" align="center" prop="sort" width="180px" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button link type="primary" icon="Position" v-hasPermi="['sys:release:posters:release']"
                       @click="updateStatus(scope.row, scope.row.status !== 'status_release')"
            >{{ scope.row.status !== "status_release" ? "发布" : "取消发布" }}</el-button>
            <el-button v-if="scope.row.status != 'status_release'" link type="primary" icon="Edit"
                       @click="handleUpdate(scope.row)" v-hasPermi="['sys:release:posters:edit']" >修改</el-button>
            <el-button v-if="scope.row.status != 'status_release'" link type="primary" icon="Delete"
                       @click="handleDelete(scope.row)" v-hasPermi="['sys:release:posters:remove']">删除</el-button>
            <el-button link type="primary" icon="View" v-hasPermi="['sys:release:posters:view']" @click="detail(scope.row)">查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize"
                  @pagination="getList"/>

      <el-dialog :title="title" v-model="open" width="850px" append-to-body>
        <el-form ref="formRef" :model="form" :rules="rules" label-width="90px" :disabled="formDisabled">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="广告标题" prop="title">
                <el-input v-model="form.title" placeholder="请输入广告标题"/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="广告类型" prop="advertisingType">
                <el-select v-model="form.advertisingType" placeholder="请选择">
                  <el-option v-for="dict in advertising_type_list" :key="dict.value" :label="dict.label" :value="dict.value" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="状态" prop="status">
                <el-select v-model="form.status" placeholder="请选择">
                  <el-option v-for="dict in advertising_status_list" :key="dict.value" :label="dict.label" :value="dict.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="排序" prop="sort">
                <el-input-number v-model="form.sort" controls-position="right" :min="1" :max="10" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="展示位置" prop="site">
                <el-select v-model="form.site" placeholder="请选择">
                  <el-option v-for="dict in advertising_site" :key="dict.value" :label="dict.label" :value="dict.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item v-if="userStore.userInfo.customParam.userType === 'admin'" label="租户ID" prop="tenantId">
                <el-input v-model="tenantName" placeholder="租户" maxlength="50" disabled />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="终端类型" prop="terminal">
                <el-select v-model="form.terminal" placeholder="请选择">
                  <el-option v-for="dict in menu_terminal" :key="dict.value" :label="dict.label" :value="dict.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="form.advertisingType !== 'carousel'">
              <el-form-item label="跳转地址" prop="jumpAddress">
                <el-input v-model="form.jumpAddress" placeholder="请输入跳转地址" style="width: 300px"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="图片地址" prop="imgList" v-if="form.advertisingType !== 'text'">
                <el-upload :file-list="imageList" class="upload-demo" action="" :http-request="uploadImage"
                           :on-preview="handlePreview" :on-remove="handleRemove"
                           :on-exceed="handleExceed" :on-success="handleSuccess"
                           :limit="form.advertisingType === 'carousel' ? 3 : 1">
                  <el-button type="primary">点击上传</el-button>
                </el-upload>
                <div>只能上传jpg/png文件，且不超过500kb</div>
                <el-dialog v-model="previewDialogVisible" append-to-body>
                  <el-image width="100%" :src="previewImageUrl" />
                </el-dialog>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="内容" prop="content" v-if="form.advertisingType === 'text'">
                <span>(文本广告设置内容时，用空格分隔每条广告)</span>
                <editor v-model="form.content" :min-height="192" :read-only="formDisabled"/>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel" >取 消</el-button>
          </div>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup name="Poster">
import useUserStore from '@/store/modules/user';
import {getTenants} from "@/api/tenant/tenant";
import {ref} from "vue";
import DictTag from "@/components/DictTag/index.vue";
import {addPoster, changeStatusPoster, delPoster, editPoster, lookPoster, posterList} from "@/api/release/poster";
import {uploadSingle} from "@/api/tool/upload";

const { proxy } = getCurrentInstance();
const showSearch = ref(true);
const news = ref([]);
const permissionTerminal = ref([])
const {
  menu_terminal,
  advertising_type_list,
  advertising_site,
  advertising_status_list
} = proxy.useDict("menu_terminal", "advertising_type_list", "advertising_site", "advertising_status_list") ;

/** 初始化租户数据 */
const userStore = useUserStore();
const tenantList = ref([]);
const tenantName = ref(userStore.userInfo.customParam.tenantName);
function getTenantList() {
  getTenants().then(res => {
    tenantList.value = res.data;
  }).catch(() => {
    tenantList.value = [];
  })
}
// 获取租户名
function getTenantName() {
  tenantList.value.forEach(item => {
    if (item.tenantId === queryParams.value.tenantId) {
      tenantName.value = item.tenantName;
    }
  })
}

/** 表格数据 */
const loading = ref(false);
const total = ref(0);
const noticeList = ref([]);
const queryRef = ref(null);
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  tenantId: userStore.userInfo.customParam.tenantId,
})
function getList() {
  loading.value = true;
  posterList(queryParams.value).then(res => {
    if (res.success) {
      noticeList.value = res.data.records;
      total.value = res.data.total;
      loading.value = false;
    }
  })
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getTenantName();
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 删除按钮操作 */
const handleDelete = (row) => {
  proxy.$modal.confirm('是否确认删除广告标题为"'+row.title+'"的数据项？').then(() => {
    delPoster({ id: row.id }).then(res => {
      if (res.success) {
        proxy.$modal.msgSuccess("删除成功");
      } else {
        proxy.$modal.msgError("删除失败");
      }
      getList();
    })
  }).catch(() => {
    proxy.$modal.msg("取消操作");
  })
}

const imageValidate = (rule, value, callback) => {
   if (form.value.advertisingType !== "text") {
     const imgListLength = form.value.imgList.length;
     if ( imgListLength === 0 ) {
       callback(new Error("图片地址不能为空"));
     } else if( imgListLength > 1 && form.value.advertisingType === "general") {
       callback(new Error("最多只能上传一张照片"));
     } else if (imgListLength > 3 && form.value.advertisingType === "carousel") {
       callback(new Error("最多只能上传三张照片"));
     } else {
       callback();
     }
   } else {
     callback();
   }
}
const title = ref("");
const open = ref(false);
const formRef = ref(null);
const form = ref({});
const formDisabled = ref(false);
const imageList = ref([]);
const previewImageUrl = ref('')
const previewDialogVisible = ref(false)
const rules = {
  title: [{required: true, message: "广告标题不能为空", trigger: "blur"},],
  advertisingType: [{required: true, message: "广告类型不能为空", trigger: "change"},],
  status: [{required: true, message: "状态不能为空", trigger: "change"},],
  sort: [{required: true, message: "排序不能为空", trigger: "blur"}],
  site: [{required: true, message: "展示位置不能为空", trigger: "blur"},],
  imgList: [{required: true, validator: imageValidate, trigger: "blur"},],
  tenantId: [{required: true, message: "租户ID不能为空", trigger: "blur"},],
}

/** 表单重置 */
function reset() {
  form.value = {
    id: undefined,
    title: undefined,
    site: undefined,
    content: undefined,
    status: "",
    sort: 1,
    contentText: undefined,
    advertisingType: "general",
    imgList: [],
    terminal: "PC",
    jumpAddress: "",
  };
  proxy.resetForm("formRef");
  imageList.value = [];
}

/** 新增按钮操作 */
const handleAdd = () => {
  formDisabled.value = false;
  reset();
  open.value = true;
  form.value.advertisingType = "general";
  form.value.tenantId = queryParams.value.tenantId;
  title.value = "添加广告";
}

/** 修改按钮操作 */
const handleUpdate = (row) => {
  formDisabled.value = false;
  reset();
  lookPoster({id: row.id, advertisingType: row.advertisingType}).then(res => {
    if (res.data) {
      form.value = {
        id: res.data.id,
        title: res.data.title,
        site: res.data.site,
        content: res.data.content,
        status: res.data.status,
        contentText: res.data.contentText,
        advertisingType: res.data.advertisingType,
        imgList: res.data.imgList,
        terminal: res.data.terminal,
        jumpAddress: res.data.jumpAddress,
        tenantId: res.data.tenantId,
        sort: res.data.sort
      }
      open.value = true;
      title.value = "修改广告";
      form.value.imgList.forEach(item => {
        imageList.value.push({url: item.coverUrl, name: item.coverName});
      })
    } else {
      proxy.$modal.msgError("数据异常！")
    }
  })
}

/** 查看按钮操作 */
const detail = (row) => {
  formDisabled.value = true;
  reset();
  lookPoster({id: row.id}).then(res => {
    if (res.data) {
      form.value = res.data;
      open.value = true;
      title.value = "查看广告详情";
      form.value.imgList.forEach(item => {
        imageList.value.push({url: item.coverUrl, name: item.coverName});
      })
    } else {
      proxy.$modal.msgError("数据异常！")
    }
  })
}

/** 确认按钮操作 */
const submitForm = () => {
  proxy.$refs["formRef"].validate(valid => {
    if (valid) {
      if (form.value.id !== undefined) {
        if (form.value.content) {
          form.value.contentText = form.value.content.replace(/<[^<>]+>/g, "")
        }
        editPoster(form.value).then(res => {
          if (res.success) {
            proxy.$modal.msgSuccess("修改成功");
            open.value = false;
            getList();
          } else {
            proxy.$modal.msgError(res.message);
          }
        })
      } else {
        //提取富文本框内的纯文本  并赋值给空字符串
        if (form.value.content) {
          form.value.contentText = form.value.content.replace(/<.*?>/g, "");
        }
        addPoster({...form.value, status: "status_draft"}).then(res => {
          if (res.success) {
            proxy.$modal.msgSuccess("新增成功");
            open.value = false;
            getList();
          } else {
            proxy.$modal.msgError(res.message);
          }
        })
      }
    }
  })
}

/** 取消按钮操作 */
const cancel = () => {
  open.value = false;
  reset();
}

/** 上传图片操作 */
const uploadImage = (content) => {
  let formData = new FormData();
  //content.file 	文件file对象
  formData.append("multipartFile", content.file);
  formData.append("appCode", "portal");
  uploadSingle(formData).then((res) => {
    form.value.imgList.push({
      coverUrl: res.data.url,
      coverName: res.data.originalName
    })
    imageList.value.push({
      url: res.data.url,
      name: res.data.originalName
    });
  });
}
const handlePreview = (file) => {
  previewImageUrl.value = file.url;
  previewDialogVisible.value = true;
}

const handleRemove = (file, fileList) => {
  form.value.imgList = [];
  imageList.value.forEach(item => {
    form.value.imgList.push({
          coverUrl: item.url,
          coverName: item.name
    })
  })
}
const handleExceed = (files, fileList) => {
  if (form.value.advertisingType === "general") {
    proxy.$modal.msgWarning(`只能最多上传一张图片`);
  } else {
    proxy.$modal.msgWarning(`只能最多上传三张图片`);
  }
}
const handleSuccess = (res, file, fileList) => {
  console.log("res", res);
}

/** 发布/取消发布按钮操作 */
const updateStatus = (row, flag) => {
  noticeList.value.forEach(item => {if (item.status === "status_release") news.value.push(item);});
  var terminalItem = '';
  var terminalName = '';
  if (row.terminal === "WX") {
    terminalItem = news.value.filter(item => item.terminal === "WX");
    terminalName = '微信小程序';
  } else if (row.terminal === "PC") {
    terminalItem = news.value.filter(item => item.terminal === "PC");
    terminalName = 'PC端';
  } else if (row.terminal === "APP") {
    terminalItem = news.value.filter(item => item.terminal === "APP");
    terminalName = '移动端';
  } else if (row.terminal === "DD") {
    terminalItem = news.value.filter(item => item.terminal === "DD");
    terminalName = '钉钉';
  }
  if (terminalItem.filter(item => item["advertisingType"] === row.advertisingType).length !== 0 && row.status !== "status_release") {
    proxy.$modal.msgError(terminalName + "已有已发布的" + row.advertisingTypeName + "广告");
  } else if (terminalItem.filter(item => item["advertisingType"] === row.advertisingType).length === 0 && row.status !== "status_release") {
    proxy.$modal.confirm('是否确认发布广告标题为"' + row.title + '"的数据项？').then(() => {
      changeStatusPoster({id: row.id, status: flag ? "status_release" : "status_draft"}).then(res => {
        if (res.success) {
          proxy.$modal.msgSuccess("操作成功");
        } else {
          proxy.$modal.msgError("操作失败");
        }
        getList();
      })
    }).catch(() => {
      proxy.$modal.msg("取消操作")
    })
  } else if (row.status === "status_release") {
    proxy.$modal.confirm('是否取消发布广告标题为"' + row.title + '"的数据项？').then(() => {
      changeStatusPoster({id: row.id, status: flag ? "status_release" : "status_draft"}).then(res => {
        if (res.success) {
          proxy.$modal.msgSuccess("操作成功");
        } else {
          proxy.$modal.msgError("操作失败");
        }
        getList();
      })
    }).catch(() => {
      proxy.$modal.msg("取消操作")
    })
  }
}

getTenantList();
getList();
</script>
