

<template>
    <div>
        <TabContainers :tabList="tabList" :activeName="activeName"></TabContainers>
    </div> 

</template>

<script setup name="ParkingAccredit">
// tab切换
import TabContainers from '@/components/TabContainer/index';
import AllAccredit from '@/systemViews/admittance/accredit/components/allAccredit/allAccredit.vue'
import deptAccredit from '@/systemViews/admittance/accredit/components/dept-accredit/deptAccredit.vue'
import peopleAccredit from '@/systemViews/admittance/accredit/components/user-accredit/peopleAccredit.vue'
import { ref } from "vue";
// 定义 tab 列表
const tabList = ref([
  {
    label: '所有人员',
    value: '01',
    component: AllAccredit
  },
  {
    label: '部门授权',
    value: '02',
    component: deptAccredit
  },
  {
    label: '人员授权',
    value: '03',
    component: peopleAccredit
  }
])
// 默认激活的 tab
const activeName = ref('01')
</script>

<style scoped lang="scss">
:deep(.el-tabs .el-tabs__content .content) {
    height: calc(100vh - 250px);
}
</style>