<!-- 新增停车规则组件 -->

<template>
  <div
    class="dialog-box"
    :class="popupType === 'view' ? 'dialog-box-view' : 'dialog-box-edit'"
  >
    <el-form
      ref="formRef"
      :model="formData"
      label-width="125px"
      :rules="popupType !== 'view' ? rules : ''"
    >
      <!-- 规则名称 -->

      <el-row>
        <el-col :span="12">
          <el-form-item label="规则名称" prop="ruleName">
            <!-- 添加或编辑时显示输入框 -->

            <el-input
              v-model="formData.ruleName"
              :disabled="popupType === 'view'"
              placeholder="请输入规则名称"
              clearable
              v-if="popupType !== 'view'"
            />

            <!-- 查看时显示文本 -->

            <div v-else>
              {{ formData.ruleName || "" }}
            </div>
          </el-form-item>
        </el-col>

        <!-- 停车场选择 -->

        <el-col :span="12">
          <el-form-item label="停车场" prop="parkingId">
            <!-- 添加时显示选择器 -->
            <TreeSelect
             v-if="popupType === 'add'"

                v-model:parkingId="formData.parkingId"
                v-model:parkingName="formData.parkingName"
            />
          

            <!-- 查看或编辑时显示停车场名称 -->

            <div v-else>
              {{ formData.parkingName || "" }}
            </div>
          </el-form-item>
        </el-col>
    
      <!-- 进出类型 -->
      <el-col :span="12">
          <el-form-item label="适用类型" prop="applyType">
            <!-- 添加或编辑时显示选择器 -->

            <el-select
            @change="applyTypeChange"
              v-model="formData.applyType"
              :disabled="popupType === 'view'"
              placeholder="请选择适用类型"
              v-if="popupType !== 'view'"
            >
              <el-option
                v-for="(item, index) in parking_rule_apply_type"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>

            <!-- 查看时显示文本 -->

            <div v-else>
              {{
                $formatDictLabel(
                  formData.applyType,

                  parking_rule_apply_type
                ) || ""
              }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="进出类型" prop="entryExitType">
            <!-- 添加或编辑时显示选择器 -->

            <el-select
              v-model="formData.entryExitType"
               :disabled="sylx_3"
              placeholder="请选择进出类型"
              v-if="popupType !== 'view'"
            >
              <el-option
                v-for="(item, index) in entry_exit_type"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>

            <!-- 查看时显示文本 -->

            <div v-else>
              {{
                $formatDictLabel(
                  formData.entryExitType,

                  entry_exit_type
                ) || ""
              }}
            </div>
          </el-form-item>
        </el-col>
      
        <!-- 通过类型 -->

        <el-col :span="12">
          <el-form-item label="通过类型" prop="restrictionType">
            <!-- 添加或编辑时显示选择器 -->

            <el-select
            v-if="popupType !== 'view'"
              v-model="formData.restrictionType"
              :disabled="sylx_3"
              placeholder="请选择通过类型"
              
            >
              <el-option
                v-for="(item, index) in parking_rule_restriction_type"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>

            <!-- 查看时显示文本 -->

            <div v-else>
              {{
                $formatDictLabel(
                  formData.restrictionType,

                  parking_rule_restriction_type
                ) || ""
              }}
            </div>
          </el-form-item>
        </el-col>

      <!-- 有效期起止 -->

      
<el-col :span="12">
          <el-form-item label="启用状态" prop="status">
            <!-- 添加或编辑时显示单选按钮 -->

            <el-radio-group
              v-model="formData.status"
              v-if="popupType !== 'view'"
            >
              <el-radio-button label="1">启用</el-radio-button>

              <el-radio-button label="2">禁用</el-radio-button>
            </el-radio-group>

            <!-- 查看时显示文本 -->

            <span class="dialog-text" v-else>
              {{ $formatDictLabel(rowData.status, parking_rule_status) || "" }}
            </span>
          </el-form-item>
        </el-col>
          <el-col :span="12">
          <el-form-item label="有效期起" prop="validFrom">
            <!-- 添加或编辑时显示日期选择器 -->

            <el-date-picker
              v-model="formData.validFrom"
              type="date"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              placeholder="默认永久有效"
              v-if="popupType !== 'view'"
            />

            <!-- 查看时显示文本 -->

            <div v-else>
              {{ formData.validFrom || "" }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="有效期止" prop="validTo">
            <!-- 添加或编辑时显示日期选择器 -->

            <el-date-picker
              v-model="formData.validTo"
              type="date"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              placeholder="默认永久有效"
              v-if="popupType !== 'view'"
            />

            <!-- 查看时显示文本 -->

            <div v-else>
              {{ formData.validTo || "" }}
            </div>
          </el-form-item>
        </el-col>

      <!-- 启用状态、日期类型、规则分类 -->



        

        <el-col :span="12">
          <el-form-item label="日期类型" prop="dateType">
            <!-- 添加或编辑时显示选择器 -->

            <el-select
              v-model="formData.dateType"
              :disabled="popupType === 'view'"
              placeholder="请选择日期类型"
              v-if="popupType !== 'view'"
            >
              <el-option
                v-for="(item, index) in date_type"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>

            <!-- 查看时显示文本 -->

            <div v-else>
              {{ $formatDictLabel(formData.dateType, date_type) || "" }}
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="12" v-if="formData.applyType!='3'">
          <el-form-item label="规则分类" prop="timeRestriction">
            <!-- 添加或编辑时显示选择器 -->

            <el-select
              v-model="formData.timeRestriction"
              :disabled="popupType === 'view'"
              placeholder="请选择规则分类"
              v-if="popupType !== 'view'"
            >
              <el-option
                v-for="(item, index) in time_restriction"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>

            <!-- 查看时显示文本 -->

            <div v-else>
              {{
                $formatDictLabel(
                  formData.timeRestriction,

                  time_restriction
                ) || ""
              }}
            </div>
          </el-form-item>
        </el-col>

      <!-- 车辆类型 -->

        <el-col :span="12" v-if="showVehicleType">
          <el-form-item label="车辆类型" prop="carType">
            <!-- 添加或编辑时显示选择器 -->

            <el-select
              v-model="formData.carType"
              :disabled="popupType === 'view'"
              placeholder="请选择车辆类型"
              v-if="popupType !== 'view'"
            >
              <el-option
                v-for="(item, index) in car_type"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>

            <!-- 查看时显示文本 -->

            <div v-else>
              {{
                $formatDictLabel(
                  formData.carType,

                  car_type
                ) || ""
              }}
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="12" v-if="isShowMaxCarNumber">
          <el-form-item label="停车数量" prop="maxCarNumber">
            <!-- 添加或编辑时显示选择器 -->
            <NumberInput 
              v-if="popupType !== 'view'"
    v-model="formData.maxCarNumber" 
    customPlaceholder="请输入停车数量"
    input-type="integer"
  />
           

            <!-- 查看时显示文本 -->

            <div v-else>
              {{
               formData.maxCarNumber
              }}
            </div>
          </el-form-item>
        </el-col>

 
      <!-- 限制时间 1-4 -->

        <el-col :span="12" v-if="showtimeRestriction">
          <el-form-item label="限制时间1" prop="timeRestriction1">
            <!-- 添加或编辑时显示时间选择器 -->

            <div
              v-if="popupType !== 'view'"
              style="display: flex; gap: 10px; width: 100%"
            >
              <el-time-select
                v-model="timeData.startTime1"
                placeholder="起始时间"
                start =  '08:30'
                step = '00:15'
                end = '23:45'
               
              />

              <el-time-select
                v-model="timeData.endTime1"
                placeholder="结束时间"
                start =  '08:30'
                step = '00:15'
                end = '23:45'
                :min-time="timeData.startTime1"
              />
            </div>

            <!-- 查看时显示文本 -->

            <div v-else>
              {{ formData.timeRestriction1 || "" }}
            </div>
          </el-form-item>
        </el-col>

        <!-- 其他时间限制的类似渲染逻辑，这里省略重复部分 -->

        <el-col :span="12">
          <el-form-item
            label="限制时间2"
            prop="timeRestriction2"
            v-if="showtimeRestriction"
          >
            <div
              v-if="popupType != 'view'"
              style="display: flex; gap: 10px; width: 100%"
            >
              <el-time-select
                v-if="popupType != 'view'"
                placeholder="起始时间"
                v-model="timeData.startTime2"
                :disabled="popupType == 'view'"
                start =  '08:30'
                step = '00:15'
                end = '23:45'
                
              >
              </el-time-select>

              <el-time-select
                placeholder="结束时间"
                v-model="timeData.endTime2"
                start =  '08:30'
                step = '00:15'
                end = '23:45'
                :min-time="timeData.startTime2"
           
              >
              </el-time-select>
            </div>

            <div v-if="popupType == 'view'">
              {{ formData.timeRestriction2 || "" }}
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item
            label="限制时间3"
            prop="timeRestriction3"
            v-if="showtimeRestriction"
          >
            <div
              v-if="popupType != 'view'"
              style="display: flex; gap: 10px; width: 100%"
            >
              <el-time-select
                v-if="popupType != 'view'"
                placeholder="起始时间"
                v-model="timeData.startTime3"
                :disabled="popupType == 'view'"

                start =  '08:30'
                step = '00:15'
                end = '23:45'
              
              >
              </el-time-select>

              <el-time-select
                placeholder="结束时间"
                v-model="timeData.endTime3"
                start =  '08:30'
                step = '00:15'
                end = '23:45'
                 :min-time="timeData.startTime3"
               
              >
              </el-time-select>
            </div>

            <div v-if="popupType == 'view'">
              {{ formData.timeRestriction3 || "" }}
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item
            label="限制时间4"
            prop="timeRestriction4"
            v-if="showtimeRestriction"
          >
            <div
              v-if="popupType != 'view'"
              style="display: flex; gap: 10px; width: 100%"
            >
              <el-time-select
                v-if="popupType != 'view'"
                placeholder="起始时间"
                v-model="timeData.startTime4"
                :disabled="popupType == 'view'"
                start =  '08:30'
                step = '00:15'
                end = '23:45'
              >
              </el-time-select>

              <el-time-select
                placeholder="结束时间"
                v-model="timeData.endTime4"
                start =  '08:30'
                step = '00:15'
                end = '23:45'
                :min-time="timeData.startTime4"
               
              >
              </el-time-select>
            </div>

            <div v-if="popupType == 'view'">
              {{ formData.timeRestriction4 || "" }}
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 车牌信息 -->

      <div v-if="showPlateNo">
        <el-row>
          <!-- 添加车牌按钮 -->

          <el-col :span="24">
            <div
              class="dialog-footer"
              v-if="popupType !== 'view'"
              style="text-align: right"
            >
              <div class="flex-1"></div>

              <div class="dialog-footer-btn">
                <el-button
                  class="add"
                  type="primary"
                  plain
                  icon="Plus"
                  size="mini"
                  @click="addplate"
                >
                  添加车牌
                </el-button>
              </div>
            </div>
          </el-col>

          <!-- 车牌列表 -->

          <el-col :span="24">
            <el-table
              :key="tableKey1"
              class="limit-cell-table"
              :data="plateInfo"
              border
              style="width: 100%; margin-top: 10px"
            >
              <el-table-column
                type="index"
                label="序号"
                width="80"
                align="center"
                sortable
              />

              <template v-for="(item, index) in plateColumns" :key="index">
                <el-table-column
                  show-overflow-tooltip
                  :prop="item.prop"
                  :label="item.label"
                  :min-width="item.minWidth"
                  :resizable="item.resizable"
                  align="center"
                >
                </el-table-column>
              </template>

              <el-table-column
                label="操作"
                align="center"
                fixed="right"
                v-if="popupType !== 'view'"
              >
                <template #default="scope">
                  <el-button
                    size="mini"
                    type="text"
                    icon="Delete"
                    @click="handleDelete(scope.row)"
                  />
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
      </div>
    </el-form>

    <!-- 对话框组件 -->

    <DialogBox
      :visible="diaWindow.open"
      :dialogWidth="diaWindow.width"
      @save="save"
      @cancellation="cancellation"
      @close="close"
      :dialogFooterBtn="diaWindow.dialogFooterBtn"
      :CloseSubmitText="diaWindow.CloseSubmitText"
      :SaveSubmitText="diaWindow.SaveSubmitText"
      :dialogTitle="diaWindow.titleName"
    >
      <template #content>
        <add-plate
          ref="addPlateRef"
          :popupType="diaWindow.popupType"
          :rowData="diaWindow.rowData"
          @submitClose="submitCloseFun"
        />
      </template>
    </DialogBox>
  </div>
</template>
  
    
  
  <script setup>
import { ref, reactive, watch, getCurrentInstance, defineProps } from "vue";

import { ElMessage } from "element-plus";

import addPlate from "@/systemViews/vehicle-management/parkingRule/components/addPlate.vue";

import { screenIndex } from "@/api/majorParking/parkingRule";

// 定义组件 props

const props = defineProps({
  closeBtn: {
    type: Function,

    default: null,
  },

  popupType: {
    type: String,

    default: "",
  },

  rowData: {
    type: Object,

    default: () => ({}),
  },
});

const plateColumns =  [
        {
          prop: 'plateNo',
          label: '车牌号码',
          show: true,
          sortable: true, //可排序
          minWidth: '100%'
        },
        {
          prop: 'staffName',
          label: '车主姓名',
          show: true,
          sortable: true, //可排序
          minWidth: '100%'
        },
        {
          prop: 'cellphone',
          label: '手机号码',
          show: true,
          sortable: true, //可排序
          minWidth: '110%'
        },
        {
          prop: 'orgName',
          label: '车主部门',
          show: true,
          sortable: true, //可排序
          minWidth: '100%'
        }
      ]
// 字典

const { proxy } = getCurrentInstance();

const {
  parking_rule_apply_type,
  entry_exit_type,
  parking_rule_restriction_type,
  time_restriction,
  parking_rule_vehicle_type,
  date_type,
  parking_rule_status,
  car_type
} = proxy.useDict(
  "parking_rule_apply_type",

  "entry_exit_type",

  "parking_rule_restriction_type",

  "time_restriction",

  "parking_rule_vehicle_type",

  "date_type",

  "parking_rule_status",
  "car_type"
);


// 定义 emits

const emit = defineEmits(["closeBtn"]);

// 定义状态

const formData = reactive({
  status: "1",
  parkingId: "", //所属车场id
  parkingName: "", //所属车场名称
  plateList: [],
});

const timeData = reactive({
  startTime1: "",

  endTime1: "",

  startTime2: "",

  endTime2: "",

  startTime3: "",

  endTime3: "",

  startTime4: "",

  endTime4: "",
});

const plateInfo = ref([]);

const parkingList = ref([]);

const diaWindow = ref({
  open: false,

  width: "30%",

  titleName: "修改",

  popupType: "edit",

  rowData: {},

  dialogFooterBtn: true,

  CloseSubmitText: "取消",

  SaveSubmitText: "确 定",
});

const showVehicleType = ref(false);

const showPlateNo = ref(false);

const showtimeRestriction = ref(false);

const isShowMaxCarNumber = ref(false);

const tableKey1 = ref(0);

// 定义校验规则

const rules = reactive({
  ruleName: [{ required: true, message: "请输入规则名称", trigger: "blur" }],

  parkingId: [{ required: true, message: "请选择停车场", trigger: "blur" }],

  entryExitType: [
    { required: true, message: "请选择进出类型", trigger: "blur" },
  ],

  restrictionType: [
    { required: true, message: "请选择通过类型", trigger: "blur" },
  ],

  applyType: [{ required: true, message: "请选择适用类型", trigger: "blur" }],

  timeRestriction: [
    { required: true, message: "请选择规则分类", trigger: "blur" },
  ],

  carType: [{ required: true, message: "请选择车辆类型", trigger: "blur" }],

  dateType: [{ required: true, message: "请选择日期类型", trigger: "blur" }],

  status: [{ required: true, message: "请选择启用状态", trigger: "blur" }],
  maxCarNumber:[{ required: true, message: "请输入停车数量", trigger: "blur" }],
});

// 表单引用

const formRef = ref(null);

// 组件定义

const addPlateRef = ref(null);

// 定义方法
const sylx_3 = ref(false)

const applyTypeChange = (val)=>{
  if(val=='3'){
    sylx_3.value = true
    formData.restrictionType = '1'
    formData.entryExitType = '1'
  }else{
     sylx_3.value = false
    formData.restrictionType = ''
     formData.entryExitType = ''
  }
}
const handleParkingChange = (value) => {
  const selectedParking = parkingList.value.find((item) => item.id === value);

  if (selectedParking) {
    formData.parkingName = selectedParking.parkingName;
  } else {
    formData.parkingName = ""; // 如果没有找到匹配项，清空 parkingName
  }
};

const handleDelete = (row) => {
  plateInfo.value = plateInfo.value.filter(
    (item) => item.plateNo !== row.plateNo
  );
};

const formatTimeRange = (start, end) => {
  if (!start || !end) {
    return "";
  }

  return `${start}-${end}`;
};

const splitTimeRange = (timeRange) => {
  if (!timeRange) return ["", ""];

  const [start, end] = timeRange.split("-");

  return [start, end];
};



const addplate = () => {
  diaWindow.value.titleName = "新增车牌";

  diaWindow.value.popupType = "add";

  diaWindow.value.rowData = formData;

  diaWindow.value.open = true;

  diaWindow.value.dialogFooterBtn = true;
};

const submitCloseFun = (info) => {
  diaWindow.value.open = false;

  if (info.plateNo) {
    setTimeout(() => {
      const existingPlate = plateInfo.value.find(
        (item) => item.plateNo === info.plateNo
      );

      if (!existingPlate) {
        plateInfo.value.push(info);
      }
    }, 100);
  }
};

const initData = async () => {
  if (props.popupType === "add") {
    const res = await screenIndex.getParkingRuleId();

    formData.id = res.data;
  } else {
    const res = await screenIndex.findParkingRuleById({ id: props.rowData.id });

    const data = res.data || {};

    formData.ruleName = data.ruleName ?? "";

    formData.parkingId = data.parkingId ?? null;

    formData.parkingName = data.parkingName ?? "";

    formData.entryExitType = data.entryExitType ?? null;

    formData.restrictionType = data.restrictionType ?? null;

    formData.validFrom = data.validFrom ?? null;

    formData.validTo = data.validTo ?? null;

    formData.applyType = data.applyType ?? null;

    formData.status = data.status ?? "1";

    formData.timeRestriction = data.timeRestriction ?? null;

    formData.dateType = data.dateType ?? null;

    formData.carType = data.carType ?? null;

    formData.timeRestriction1 = data.timeRestriction1 ?? "";

    formData.timeRestriction2 = data.timeRestriction2 ?? "";

    formData.timeRestriction3 = data.timeRestriction3 ?? "";

    formData.timeRestriction4 = data.timeRestriction4 ?? "";

    formData.plateList = data.plateList ?? [];

    formData.id = data.id ?? "";
    
    formData.maxCarNumber = data.maxCarNumber ?? '';

    timeData.startTime1 = splitTimeRange(formData.timeRestriction1)[0];

    timeData.endTime1 = splitTimeRange(formData.timeRestriction1)[1];

    timeData.startTime2 = splitTimeRange(formData.timeRestriction2)[0];

    timeData.endTime2 = splitTimeRange(formData.timeRestriction2)[1];

    timeData.startTime3 = splitTimeRange(formData.timeRestriction3)[0];

    timeData.endTime3 = splitTimeRange(formData.timeRestriction3)[1];

    timeData.startTime4 = splitTimeRange(formData.timeRestriction4)[0];

    timeData.endTime4 = splitTimeRange(formData.timeRestriction4)[1];

    showVehicleType.value = formData.applyType === "1" || formData.applyType === "3";

    isShowMaxCarNumber.value = formData.applyType === "3" 

    showPlateNo.value = formData.applyType === "2";

    showtimeRestriction.value = formData.timeRestriction === "2";
  }

  const res = await screenIndex.findparkinglot({}, { status: "0" });

  parkingList.value = res.data.records || [];

  console.log(parkingList.value.length);
};

const selectPark = async () => {
  const res = await screenIndex.findparkinglot({}, { status: "0" });

  parkingList.value == res.data.records || [];
};

const saveForm = async () => {
  try {
    await formRef.value.validate();

    const formData1 = {
      ...formData,

      plateList: plateInfo.value,
    };

    formData1.timeRestriction1 = formatTimeRange(
      timeData.startTime1,
      timeData.endTime1
    );

    formData1.timeRestriction2 = formatTimeRange(
      timeData.startTime2,
      timeData.endTime2
    );

    formData1.timeRestriction3 = formatTimeRange(
      timeData.startTime3,
      timeData.endTime3
    );

    formData1.timeRestriction4 = formatTimeRange(
      timeData.startTime4,
      timeData.endTime4
    );

    if (props.popupType === "edit") {
      const res = await screenIndex.updateParkingRule(formData1);

      if (res.code == "1") {
        ElMessage.success("修改成功");

        emit("closeBtn");
      } 
    } else if (props.popupType === "add") {
      const res = await screenIndex.saveParkingRule(formData1);

      if (res.code == "1") {
        ElMessage.success("新增成功");

        emit("closeBtn");
      }
    }
  } catch (error) {
    console.error("表单校验失败:", error);
  }
};

/** 弹窗提交保存 */

const save = (val) => {
  addPlateRef.value.submitForm();
};

/** 弹窗点击取消保存 */

const cancellation = (val) => {
  close(false);
};

/** 关闭弹窗 */

const close = (val) => {
  diaWindow.value.open = val;
};

// 监听amines类型变化

watch(
  () => formData.applyType,

  (newVal) => {
    showVehicleType.value = newVal === "1" || newVal === "3"; 
    isShowMaxCarNumber.value = newVal === "3"; 
    showPlateNo.value = newVal === "2";
  }
);

// 监听规则分类变化

watch(
  () => formData.timeRestriction,

  (newVal) => {
    showtimeRestriction.value = newVal === "2";
  }
);

// 初始化数据

onMounted(() => {
  initData();
});

defineExpose({
  saveForm,
});
</script>
  
    
  
    <style scoped lang="scss">
</style>