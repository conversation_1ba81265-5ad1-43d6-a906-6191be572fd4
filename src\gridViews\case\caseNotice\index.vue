<template>
  <el-card class="line-card" v-loading="caseNotice" style="border: 0">
    <div class="home-card-body">
      <div style="font-size: 18px;font-family: Microsoft YaHei;font-weight: bold;color: #606060;">通知公告：</div>
      <router-link
          v-if="notice[index]"
          :to="portalIndex+`/detail/${notice[index].programaType}/${notice[index].noticeId}`"
          target="_blank"
      >
        <div class="notice-title">
          「
          <span style="color: red">{{ notice[index].noticeTypeName }}</span>
          」{{ notice[index].noticeTitle }}
        </div>
      </router-link>
      <img
          src="../../../assets/images/pageDesign/case/left.png"
          alt
          style="margin:auto 16px auto 43px"
          @click="reduce"
      />
      <img src="../../../assets/images/pageDesign/case/right.png" alt @click="add" />
    </div>
  </el-card>
</template>

<script setup name="CaseNotice">
import { selectTopPage } from "@/api/release/notice";

const { proxy } = getCurrentInstance();
const portalIndex = ref(proxy.portalIndex);
const caseNotice = ref(false);
const notice = ref([]);
const index = ref(0);

function getNotice() {
  caseNotice.value = true;
  selectTopPage({
    programaType: "system_notice",
  }).then((res) => {
    notice.value = res.data.records;
    caseNotice.value = false;
  });
}

const add = () => {
  if (index.value < notice.value.length - 1) {
    index.value++;
  } else {
    proxy.$modal.msg("没有更多数据了！");
  }
}

const reduce = () => {
  if (index.value > 0) {
    index.value--;
  } else {
    proxy.$modal.msg("没有更多数据了！");
  }
}
getNotice();
</script>

<style scoped lang="less">
.line-card {
  width: 100%;
  border-radius: 0px;
  background: #ffffff;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
  border: 0px;
  cursor: pointer;
  .home-card-body {
    display: flex;
    height: 50px;
    line-height: 50px;
    padding-left: 20px;
    img {
      width: 9px;
      height: 15px;
      margin: auto 0;
    }
  }
  .notice {
    font-size: 14px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #333333;
  }
  :deep(.el-card__body) {
    padding: 0;
  }
}
</style>
