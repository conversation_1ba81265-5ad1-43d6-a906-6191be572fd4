import request from '@/utils/request'
import { apiUrl } from '@/utils/config'; 



export class screenIndex {
//账户设置列表
  static findPage(data,params) {
    return request({
      url: `pay${apiUrl}/pay/payAccountType/pageList`,
      method: 'post',
      data: {...data,...params},
    })
  }

// 删除  ttp://localhost:9001/pay/payAccountType/id?id=
static payAccountTypeDelete(data) {
    return request({
      url: `pay${apiUrl}/pay/payAccountType?id=${data.id}` ,
      method: 'post',
      data
    })
  }

// 查询

static payAccountTypeGet(data) {
    return request({
      url: `pay${apiUrl}/pay/payAccountType`,
      method: 'post',
      data
    })
  }
// 添加
  static payAccountTypeAdd(data) {
    return request({
      url: `pay${apiUrl}/pay/payAccountType/insert`,
      method: 'post',
      data
    })
  }

//修改
static payAccountTypeUpdate(data) {
    return request({
      url: `pay${apiUrl}/pay/payAccountType/update`,
      method: 'post',
      data
    })
  }


}