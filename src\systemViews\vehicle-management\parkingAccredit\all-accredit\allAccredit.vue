<!-- 停车场管理 -->

<template>
  <div class="app-container-other">
    <Splitpanes class="default-theme">
      <Pane :size="18" :min-size="18">
        <el-card class="dep-card">
  

          <treeLoad
            ref="myTree"
            :isShowSearch="true"
            :defaultProps="defaultProps"
            :treeData="treeData"
            :treeBtnEdit="false"
            :treeBtnDelete="false"
            treeName="label"
            :isLazy="false"
            @checkedKeys="handleNodeClick"
    />
        </el-card>
      </Pane>
      <Pane :size="82" :min-size="65">
        <el-card class="dep-card">
          <dialog-search
            @getList="getList"
            formRowNumber="4"
            :columns="tabelForm.columns"
            :isShowRightBtn="$checkPermi(['parking:accredit:list'])"
          >
            <template v-slot:formList>
              <el-form
                :model="queryParams"
                ref="queryForm"
                :inline="true"
                label-width="75px"
              >

              

              <el-form-item label="租户">
                  <TenantSelect
                    v-model="queryParams.tenantId"
                  ></TenantSelect>
                </el-form-item>
                <el-form-item label="人员姓名">
                  <el-input
                    v-model="queryParams.nickName"
                    placeholder="请输入人员姓名"
                    clearable
                  ></el-input>
                </el-form-item>
                <el-form-item label="车牌号码">
                  <el-input
                    v-model="queryParams.plateNo"
                    placeholder="请输入车牌号码"
                    clearable
                  ></el-input>
                </el-form-item>
                <el-form-item label="授权类型" prop="accreditType">
                  <el-select
                    v-model="queryParams.accreditType"
                    placeholder="请选择授权类型"
                    clearable
                  >
                    <el-option
                      v-for="(item, index) of parking_accredit_type"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="有效状态" prop="accreditStatus">
                  <el-select
                    v-model="queryParams.accreditStatus"
                    placeholder="请选择有效状态"
                    clearable
                  >
                    <el-option
                      v-for="(item, index) of accredit_status"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-form>
            </template>
            <template v-slot:searchList>
              <el-button type="primary" icon="Search" @click="handleQuery"  v-hasPermi="['parking:accredit:list']"
                >搜索</el-button
              >
              <el-button icon="Refresh" @click="resetQuery" v-hasPermi="['parking:accredit:list']">重置</el-button>
            </template>

            <template v-slot:searchBtnList>
              <el-button
              v-hasPermi="['parking:accredit:export']"
                type="primary"
                icon="Download"
                @click="handleExport"
                >导出
              </el-button>
            </template>
          </dialog-search>

          <public-table
            ref="publictable"
            :rowKey="tabelForm.tableKey"
            :tableData="list"
            :columns="tabelForm.columns"
            :configFlag="tabelForm.tableConfig"
            :pageValue="pageParams"
            :total="total"
            :getList="getList"
            @clickTextDetail="clickTextDetail"
          >
            <template #operation="{ scope }">
              <el-button
               v-hasPermi="['parking:accredit:list']"
                size="mini"
                type="text"
                icon="View"
                @click="handleView(scope.row)"
                title="查看"
              ></el-button>
            </template>
          </public-table>
        </el-card>
      </Pane>
    </Splitpanes>

    <DialogBox
      :visible="open1"
      :dialogWidth="diaWindow.dialogWidth"
      :dialogFooterBtn="false"
      :custom-class="diaWindow.customClass"
      @save="save"
      @cancellation="cancellation"
      @close="close"
      dialogTitle="查看"
    >
      <template #content> 
        <accreditView :rowData="diaWindow.rowData" :popupType="diaWindow.popupType" v-if="diaWindow.popupType=='view'"></accreditView>
        <personView   :rowData="diaWindow.rowData"  v-if="diaWindow.popupType=='personView'"></personView>
      </template>
    </DialogBox>
  </div>
</template>

<script setup name="User">
import useUserStore from "@/store/modules/user";
import { ref, reactive, getCurrentInstance } from "vue";
import {
  allAccreditList,
  findparkinglotTree
} from "@/api/majorParking/accredit/accredit";
import { formatMinuteTime } from "@/utils";
import { apiUrl } from '@/utils/config'; 
import accreditView from './components/accreditView.vue'
import personView from './components/face.vue'
import treeLoad from "@/components/Tree/treeLoad";
const userStore = useUserStore();
const { proxy } = getCurrentInstance();
const { parking_rule_vehicle_type, parking_accredit_type, accredit_status } =
  proxy.useDict(
    "parking_rule_vehicle_type",
    "parking_accredit_type",
    "accredit_status"
  );



const diaWindow = reactive({
  popupType: "",
  rowData: "",
  dialogWidth: "30%",
  customClass:''
});

const pageParams = ref({
  pageNum: 1,
  pageSize: 10,
});
const treeData = ref([]);
const queryParams = ref({
  tenantId:userStore.userInfo.tenantId,
  parentId:'',
  nickName: "",
  plateNo: "",
  accreditType: "",
  accreditStatus: "",
});
const open1 = ref(false);
const list = ref([]);
const total = ref(0);
const tabelForm = reactive({
  tableKey: "1", //表格key值
  isShowRightToolbar: true, //是否显示右侧显示和隐藏
  showSearch: true,
  // 表格表格数据
  columns: [
    {
      fieldIndex: "parkingName", // 对应列内容的字段名
      label: "停车场", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "150", //最小宽度%
      width: "", //宽度
      align: "left", //表格对齐方式
    },
    {
      fieldIndex: "plateNo", // 对应列内容的字段名
      label: "车牌号码", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "110", //最小宽度%
      width: "", //宽度
      align: "", //表格对齐方式
    },
    {
      fieldIndex: "vehicleType", // 对应列内容的字段名
      label: "车辆编组", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "160px", //最小宽度%
      width: "", //宽度
      align: "", //表格对齐方式
      type: "dict",
      dictList: parking_rule_vehicle_type,
    },
    {
      fieldIndex: "nickName", // 对应列内容的字段名
      label: "人员姓名", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "110", //最小宽度%
      width: "", //宽度
      align: "", //表格对齐方式
      type:'clickText'
    },
    // {
    //   fieldIndex: "deptName", // 对应列内容的字段名
    //   label: "人员部门", // 显示的标题
    //   resizable: true, // 对应列是否可以通过拖动改变宽度
    //   visible: true, // 展示与隐藏
    //   sortable: true, // 对应列是否可以排序
    //   fixed: "", //固定
    //   minWidth: "140", //最小宽度%
    //   width: "", //宽度
    //   align: "", //表格对齐方式
    // },
    {
      fieldIndex: "accreditType", // 对应列内容的字段名
      label: "授权类型", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "110", //最小宽度%
      width: "", //宽度
      align: "", //表格对齐方式
      type: "dict",
      dictList: parking_accredit_type,
    },
    {
      label: "有效期限",
      fieldIndex: "expirationTime",
      sortable: true, // 对应列
      width: "110",
      visible: true,
      defaultLabel:'长期有效'
    },
    {
      fieldIndex: "accreditStatus", // 对应列内容的字段名
      label: "有效状态", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "110", //最小宽度%
      width: "", //宽度
      align: "", //表格对齐方式
      type: "dict",
      dictList: accredit_status,
    },
    {
      fieldIndex: "createDate", // 对应列内容的字段名
      label: "创建时间", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "200", //最小宽度%
      width: "", //宽度
      align: "", //表格对齐方式
    },
    {
      fieldIndex: "updateName", // 对应列内容的字段名
      label: "操作人", // 显示的标题
      resizable: true, // 对应列是否可以通过拖动改变宽度
      visible: true, // 展示与隐藏
      sortable: true, // 对应列是否可以排序
      fixed: "", //固定
      minWidth: "110", //最小宽度%
      width: "", //宽度
      align: "", //表格对齐方式
    },

    {
      label: "操作",
      slotname: "operation",
      width: "100",
      fixed: "right", //固定
      visible: true,
    },
  ],
  // 表格基础配置项，看个人需求添加
  tableConfig: {
    needPage: true, // 是否需要分页
    index: true, // 是否需要序号
    selection: false, // 是否需要多选
    reserveSelection: false, // 是否需要支持跨页选择
    indexFixed: false, // 序号列固定 left true right
    selectionFixed: false, //多选列固定left true right
    indexWidth: "50", //序号列宽度
    loading: false, //表格loading
    showSummary: false, //是否开启合计
    height: null, //表格固定高度 比如：300px
  },
});

/** 查询用户列表 */
const getList = () => {
  tabelForm.tableConfig.loading = true;
  allAccreditList(queryParams.value, pageParams.value).then((response) => {
    list.value = response.data.records;
    total.value = response.data.total;
    tabelForm.tableConfig.loading = false;
  });
};

//获取停车场区域
const selectParkingTree = () => {
  findparkinglotTree({}).then((res) => {
    treeData.value = res.data;
  });
};

/** 搜索按钮操作 */
const handleQuery = () => {
  pageParams.value.pageNum = 1;
  getList();
};
/** 重置按钮操作 */
const resetQuery = () => {
  proxy.resetForm("queryForm");
   queryParams.value = {
  tenantId:userStore.userInfo.tenantId,
  parentId:'',
  nickName: "",
  plateNo: "",
  accreditType: "",
  accreditStatus: "",
};
  handleQuery();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy.download(
    `park${apiUrl}/parking/accredit/exportAllAccredit`,
    {
      ...queryParams.value,
    },
   `车辆授权汇总查询_${formatMinuteTime(new Date())}.xlsx`
  );
};

/** 查看 */
const handleView = (data) => {
  diaWindow.popupType = "view";
  diaWindow.rowData = data;
   diaWindow.customClass = 'my_height_1'
  diaWindow.dialogWidth = '30%'
  open1.value = true;
};

/** 点击取消保存 */
const cancellation = (val) => {
  close(false);
};

/** 关闭弹窗 */
const close = (val) => {
  open1.value = val;
};




/** 树结构数据默认 */
const defaultProps = reactive({
  children: "children",
  label: "label",
})


/** 树结构点击 */
const handleNodeClick = (data)=>{
  queryParams.value.parentId = data.id;
  getList();
}

const clickTextDetail = (row)=>{
  diaWindow.popupType = "personView";
  diaWindow.rowData = row;
  diaWindow.dialogWidth = '500px'
  diaWindow.customClass = 'my_height_1'
  open1.value = true;
}

selectParkingTree()
getList();
</script>

<style scoped>
.dep-card {
  min-height: calc(100vh - 160px);
}

.error-msg {
  color: #f56c6c;
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
}

</style>