<template>
  <el-card :class="bgColor === true ? 'notice-card' : 'unshow-notice-card'" v-loading="loading" shadow="never">
    <template #header v-if="showTitle">
      <span class="card-title">
        <div class="title">
          <div><el-icon class="icon iconfont icon-yewudongtai"></el-icon> 新闻动态</div>
        </div>
        <div class="moreButtom">
          <el-button class="exec" link type="primary" style="font-size: 12px"
              @click="moreExt('business_news')">更多<el-icon><DArrowRight /></el-icon>
          </el-button>
        </div>
      </span>
    </template>
    <div class="home-card-body">
      <router-link v-for="item in business" :key="item.id" class="notice"
                   :to="portalIndex + `/detail/${item.programaType}/${item.noticeId}`" target="_blank">
        <div class="notice-title" :title="item.noticeTitle">
          「<span style="color: red">{{ item.noticeTypeName }}</span>」{{ item.noticeTitle }}
        </div>
        <div class="notice-time">{{ parseTime(item.createDate, "{y}-{m}-{d}") }}</div>
      </router-link>
      <el-empty style="padding: 20px 0" v-if="business.length <= 0" :image-size="50" description="暂无新闻动态">
        <el-link icon="Refresh" @click="getBusiness" :underline="false" type="primary">刷新</el-link>
      </el-empty>
    </div>
  </el-card>
</template>

<script setup name="UseBusiness">
import {useRouter} from "vue-router";
import {selectTopPage} from "@/api/release/notice";

const {proxy} = getCurrentInstance()
const router = useRouter()
const props = defineProps({
  showTitle: Boolean,
  bgColor: Boolean
})

const loading = ref(true)
const business = ref([])
const portalIndex = ref(proxy.portalIndex)

const moreExt = (type) => {
  router.push(`/portal/list/${type}`);
}

const getBusiness = () => {
  loading.value = true;
  selectTopPage({
    programaType: "business_news",
    queryNum:5,
  }).then((res) => {
    loading.value = false;
    business.value = res.data.records;
  });
}

getBusiness();
</script>

<style scoped lang="scss">
.notice-card {
  background: #fff;
  height: 100%;
  overflow: auto;
  .card-title {
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .icon {
      font-size: 18px;
      color: #419eee;
      padding: 0px 5px;
      -webkit-transition: font-size 0.25s linear, width 0.25s linear;
      -moz-transition: font-size 0.25s linear, width 0.25s linear;
      transition: font-size 0.25s linear, width 0.25s linear;
    }
    .exec {
      padding: 3px 0;
    }
  }
  .notice {
    display: flex;
    justify-content: space-between;
    flex-direction: row;
    padding: 4px 0;
    font-size: 14px;
    .notice-title {
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-all;
      white-space: nowrap;
      flex-basis: 70%;
    }
    .notice-time {
      flex-basis: 20%;
      text-align: right;
    }
  }
}

.unshow-notice-card {
  border: 0px;
  height: 100%;
  background: #fff;
  .card-title {
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .icon {
      font-size: 18px;
      color: #419eee;
      padding: 0px 5px;
      -webkit-transition: font-size 0.25s linear, width 0.25s linear;
      -moz-transition: font-size 0.25s linear, width 0.25s linear;
      transition: font-size 0.25s linear, width 0.25s linear;
    }
    .exec {
      padding: 3px 0;
    }
  }
  .notice {
    display: flex;
    justify-content: space-between;
    flex-direction: row;
    padding: 4px 0;
    font-size: 14px;
    .notice-title {
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-all;
      white-space: nowrap;
      flex-basis: 70%;
    }
    .notice-time {
      flex-basis: 20%;
      text-align: right;
    }
  }
}

.moreButtom {
  display: flex;
  justify-content: flex-end;
}
.home-card-body {
  padding-top: 20px;
}
</style>
