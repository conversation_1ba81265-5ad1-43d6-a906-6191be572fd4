import request from '@/utils/request'
import { apiUrl } from '@/utils/config'; 



export class screenIndex {
//选择账户列表
  static PayAccountManageList(data) {
    return request({
      url: `pay${apiUrl}/pay/payAccountList/accountTree`,
      method: 'post',
      data: data
    })
  }
// 人员选择
static selectUserList(data) {
    return request({
      url: `pay${apiUrl}/pay/payAccountList/selectUserList` ,
      method: 'post',
      data
    })
  }

// 修改 （启用禁用）
static update(data) {
    return request({
      url: `pay${apiUrl}/pay/payAccountList/update` ,
      method: 'post',
      data
    })
  }

  //列表
  static pageList(data,params) {
    return request({
      url: `pay${apiUrl}/pay/payAccountList/pageList`,
      method: 'post',
      data: {...data,...params},
    })
  }
// 删除
static delete(data) {
    return request({
      url: `pay${apiUrl}/pay/payAccountList/delete` ,
      method: 'post',
      data
    })
  }
// 合并账户

static mergeAccountsList(data) {
    return request({
      url: `pay${apiUrl}/pay/payAccountConsolidation/mergeAccountsList` ,
      method: 'post',
      data
    })
  }

  //账户设置列表
    static payAccountTypePageList(data,params) {
      return request({
        url: `pay${apiUrl}/pay/payAccountType/pageList`,
        method: 'post',
        data: {...data,...params},
      })
    }
    static insertBatch(data) {
      return request({
        url: `pay${apiUrl}/pay/payAccountList/insertBatch` ,
        method: 'post',
        data
      })
    }
// 根据redis锁获取导入结果

static getImportResult(data) {
  return request({
    url: `pay${apiUrl}/pay/payAccountList/getImportResult` ,
    method: 'post',
    data
  })
}

// 获取导入校验成功的数据


static getImportData(data) {
  return request({
    url: `pay${apiUrl}/pay/payAccountList/getImportData` ,
    method: 'post',
    data
  })
}
// 查询可合并的账户列表

static queryMergeAccountList(data) {
  return request({
    url: `pay${apiUrl}/pay/payAccountList/queryMergeAccountList`,
    method: 'post',
    data
  })
}

// 账户合并保存


static listAccountMerge(data) {
  return request({
    url: `pay${apiUrl}/pay/payAccountList/listAccountMerge` ,
    method: 'post',
    data
  })
}

// 获取成功的数据
static getImportData(data) {
  return request({
    url: `pay${apiUrl}/pay/payAccountList/getImportData`,
    method: 'post',
    data
  })
}
// 获取失败的数据
static exportErrorData(data) {
  return request({
    url: `pay${apiUrl}/pay/payAccountList/exportErrorData`,
    method: 'post',
    data
  })
}

}