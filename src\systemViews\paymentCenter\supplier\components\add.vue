<!-- 新增账号设置 -->

<template>
  <div class="dialog-box" :class="popupType === 'view' ? 'dialog-box-view' : 'dialog-box-edit'">
    <el-form ref="formRef" :model="formData" label-width="125px" :rules="popupType !== 'view' ? rules : ''">
      <el-row>
        <el-col :span="12">
          <el-form-item label="供应商名称" prop="providerName">
            <!-- 添加或编辑时显示输入框 -->

            <el-input v-model="formData.providerName" :disabled="popupType === 'view'" placeholder="请输入供应商名称" clearable
              v-if="popupType !== 'view'" />

            <!-- 查看时显示文本 -->

            <div v-else>
              {{ formData.providerName || "" }}
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="供应商编码" prop="providerCode">
            <el-input v-model="formData.providerCode" placeholder="请输入供应商编码" clearable v-if="popupType == 'add'" />

            <div v-else>
              {{ formData.providerCode || "" }}
            </div>
          </el-form-item>
        </el-col>


        <el-col :span="12">
          <el-form-item label="供应商简称" prop="providerShortName">
            <el-input v-model="formData.providerShortName" placeholder="请输入供应商简称" clearable
              v-if="popupType !== 'view'" />

            <div v-else>
              {{ formData.providerShortName || "" }}
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="供应商负责人" prop="providerContact">
            <el-select v-if="popupType !== 'view'"  v-model="formData.providerContact" collapse-tags placeholder="请输入人员进行选择" clearable @change="changeUser"
              filterable remote reserve-keyword :remote-method="getUserList">
              <el-option v-for="(item, index) in applyUserList" :key="index" :label="item.staffName"
                :value="item.staffId">
                <div>
                  {{
                    item.staffName +
                    "(" +
                    item.orgName +
                    "/" +
                    item.cellphone +
                    ")"
                  }}
                </div>
              </el-option>
            </el-select>
            <div v-else>
              {{ formData.providerContact || "" }}
            </div>
          </el-form-item>
        </el-col>


        <el-col :span="12">
          <el-form-item label="联系电话" prop="providerPhone">
            <el-input v-model="formData.providerPhone" placeholder="请输入联系电话" clearable
              v-if="popupType !== 'view'" />

            <div v-else>
              {{ formData.providerPhone || "" }}
            </div>
          </el-form-item>
        </el-col>


        <el-col :span="12">
          <el-form-item label="适用账户" prop="applicableAccountsList">
            <el-select v-if="popupType !== 'view'" multiple  placeholder="请选择适用账户" clearable  collapse-tags
              filterable   v-model="applicableAccountsList" >
              <el-option v-for="(item, index) in applyAccountList" :key="index" :label="item.accountName"
                :value="item.id">
                
              </el-option>
            </el-select>
            <div v-else>
              {{ formData.applicableAccounts || "" }}
            </div>
          </el-form-item>
        </el-col>


        
        <el-col :span="12">
          <el-form-item label="联系人职务" prop="providerPost">
            <el-input v-model="formData.providerPost" placeholder="请输入联系人职务" clearable
              v-if="popupType !== 'view'" />

            <div v-else>
              {{ formData.providerPost || "" }}
            </div>
          </el-form-item>
        </el-col>


        <el-col :span="12">
          <el-form-item label="联系人邮箱" prop="providerEmail">
            <el-input v-model="formData.providerEmail" placeholder="请输入联系人邮箱" clearable
              v-if="popupType !== 'view'" />

            <div v-else>
              {{ formData.providerEmail || "" }}
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="客服电话" prop="kefuPhone">
            <el-input v-model="formData.kefuPhone" placeholder="请输入客服电话" clearable
              v-if="popupType !== 'view'" />

            <div v-else>
              {{ formData.kefuPhone || "" }}
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="统一社会信用代码" prop="unifiedSocialCode">
            <el-input v-model="formData.unifiedSocialCode" placeholder="请输入统一社会信用代码" clearable
              v-if="popupType !== 'view'" />

            <div v-else>
              {{ formData.unifiedSocialCode || "" }}
            </div>
          </el-form-item>
        </el-col>

   
        <el-col :span="12">
          <el-form-item label="供应商管理员"  prop="providerAdministrators">
            <el-select v-if="popupType !== 'view'" v-model="formData.providerAdministrators" multiple placeholder="请输入人员进行选择"
              clearable @change="changeUser" filterable remote reserve-keyword :remote-method="getUserList">
              <el-option v-for="(item, index) in providerAdministrators" :key="index" :label="item.staffName"
                :value="item.staffId">
                <div>
                  {{
                    item.staffName +
                    "(" +
                    item.orgName +
                    "/)" 
                  }}
                </div>
        
              </el-option>
            </el-select>
            <div v-else>
              {{ formData.providerAdministratorsName || "" }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="账户状态" prop="accountStatus">
            <!-- 添加或编辑时显示单选按钮 -->

            <el-radio-group v-model="formData.providerStatus" v-if="popupType !== 'view'">
              <el-radio-button :label="item.value" v-for="(item, index) of provider_state"
                :key="index">{{ item.label }}</el-radio-button>
            </el-radio-group>

            <!-- 查看时显示文本 -->

            <span class="dialog-text" v-else>
              {{ $formatDictLabel(formData.providerStatus, provider_state) || "" }}
            </span>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="供应商信息" prop="providerInfo">
            <el-input v-model="formData.providerInfo" placeholder="供应商信息" clearable type="textarea" 
              v-if="popupType !== 'view'" />

            <div v-else>
              {{ formData.unifiedSocialCode || "" }}
            </div>
          </el-form-item>
        </el-col>



        <el-col :span="24">
            <el-form-item label="供应商logo" prop="providerLogo">
              <!-- 自定义图片上传组件 -->
              <ImageUpload
                v-if="popupType !== 'view'"
                v-model="fileList"
                limit="1"
                fileSize="10"
                :paramsData="imageExtraData"
                :uploadImgUrl="
                  '/wisdompay' + apiUrls + '/pay/payProvider/uploadFile'
                "
              />
              <!-- 查看模式显示图片 -->
              <PreviewImage
                style="width: 150px"
                v-if="popupType === 'view' && formData.providerLogo"
                :photo-id="formData.providerLogo"
              ></PreviewImage>
             
            </el-form-item>
          </el-col>
      </el-row>


    </el-form>


  </div>
</template>



<script setup>
import { ref, reactive, watch, getCurrentInstance, defineProps } from "vue";

import { ElMessage } from "element-plus";
import {apiUrl} from "@/utils/config";
import { validateComplexPhone,validateEmail  } from '@/utils/validate'
import {
  screenIndex
} from "@/api/paymentCenter/supplier/index";


// 定义组件 props
const props = defineProps({
  closeBtn: {
    type: Function,

    default: null,
  },

  popupType: {
    type: String,
    default: "",
  },

  rowData: {
    type: Object,
    default: () => ({}),
  },
});
const apiUrls = ref(apiUrl);
// 字典
const { proxy } = getCurrentInstance();
const {
  account_type_status,
  provider_state
} = proxy.useDict(
  "account_type_status",
  "provider_state"
);

// 供应商联系人
const applyUserList = ref([]);

//适用账户
const applyAccountList = ref([])
//供应商管理员id列表
const providerAdministrators = ref([])
const applicableAccountsList = ref([])
const administratorsIds = ref([])

// 定义 emits
const emit = defineEmits(["closeBtn"]);


let formData = ref({
  providerStatus:'1'
});

// 定义校验规则

const rules = reactive({
  providerName: [
    { required: true, message: '请输入供应商名称', trigger: ['blur', 'change'] }
  ],
  providerCode: [
    { required: true, message: '请输入供应商编码', trigger: ['blur', 'change'] },
    {
      pattern: /^[\x21-\x7E]+$/, // 允许所有可打印的 ASCII 字符（不包含空格）
      message: "类型编码只能包含字母、数字和特殊符号",
      trigger: ["blur", "change"]
    }
  ],
  providerShortName: [
    { required: true, message: '请输入供应商简称', trigger: ['blur', 'change'] }
  ],
  staffName: [
    { required: true, message: '请选择供应商负责人', trigger: ['blur', 'change'] }
  ],
  providerPhone: [
    { required: true, message: '请输入联系电话', trigger: ['blur', 'change'] },
    { 
      validator: (_, value, callback) => {
        // 允许为空（由 required 规则处理）
        if (!value) return callback();
        return validateComplexPhone(_, value, callback)
      }, 
      trigger: ['blur', 'change'] 
    }
  ],
  kefuPhone: [
    { 
      validator: (_, value, callback) => {
        // 允许为空（由 required 规则处理）
        if (!value) return callback();
        return validateComplexPhone(_, value, callback)
      }, 
      trigger: ['blur', 'change'] 
    }
  ],
  providerAdministrators:[
    {
      required: true,
      message: '请选择供应商管理员',
      trigger: []
    }
  ],
  providerEmail:[
  { 
      validator: (_, value, callback) => {
        // 允许为空（由 required 规则处理）
        if (!value) return callback();
        return validateEmail(_, value, callback)
      }, 
      trigger: ['blur', 'change'] 
    }
  ]
})

// 表单引用

const formRef = ref(null);



// 定义方法

const getUserList = async (name) => {
  if (!name || name.trim().length < 2) {
    applyUserList.value = []; // 立即清空列表
    // 不清空providerAdministrators以保留已选管理员
    return;
  }
  try {
    const res = await screenIndex.selectUserList({
        staffName: name,
    });
    applyUserList.value = res.data;
    
    // 保留现有已选的管理员，只添加新的搜索结果
    const existingAdminIds = formData.value.providerAdministrators || [];
    const currentAdmins = providerAdministrators.value || [];
    
    // 创建现有管理员ID的Set，用于快速查找
    const existingAdminIdSet = new Set(currentAdmins.map(admin => admin.staffId));
    
    // 合并现有管理员和新搜索结果，避免重复
    providerAdministrators.value = [
      ...currentAdmins,
      ...res.data.filter(item => !existingAdminIdSet.has(item.staffId))
    ];
  } catch (error) {
    console.error("获取用户列表失败:", error);
  }
};
// 选择
const changeUser = (staffId) => {
  const selectedUser = applyUserList.value.find(
    (item) => item.staffId === staffId
  );


  if (selectedUser) {
    // formData.value.providerContact = selectedUser.staffId
    // formData.value.staffName = selectedUser.staffName
    // formData.value.providerPhone = selectedUser.cellphone
    formData.value.staffId = selectedUser.staffId
    formData.value.providerContact = selectedUser.staffName
    formData.value.providerPhone = selectedUser.cellphone
  }
};

// 监听供应商管理员选择变化
watch(() => formData.value.providerAdministrators, (newVal, oldVal) => {
  if (newVal && Array.isArray(newVal)) {
    // 确保所有已选管理员都在providerAdministrators列表中
    const currentAdminIds = providerAdministrators.value.map(admin => admin.staffId);
    const missingAdminIds = newVal.filter(id => !currentAdminIds.includes(id));
    
    // 如果有新添加的管理员ID不在列表中，尝试从applyUserList中查找并添加
    if (missingAdminIds.length > 0) {
      missingAdminIds.forEach(id => {
        const admin = applyUserList.value.find(user => user.staffId === id);
        if (admin) {
          // 添加到管理员列表中
          providerAdministrators.value.push({...admin});
        }
      });
    }
  }
}, { deep: true });

const getAccountList = async (name) => {
  try {
    const res = await screenIndex.PayAccountManagePageList({
 
    });
    applyAccountList.value = res.data;
  } catch (error) {
    console.error("获取用户列表失败:", error);
  }
};

// 文件列表
const fileList = ref([]);
const imageExtraData = ref({
  // 图片上传额外参数
});
const initData = async () => {
  if (props.popupType != "add") {
  }{

   formData.value= props.rowData || {};
   if(props.rowData.providerAdministratorsName){
    const idArray=props.rowData.providerAdministratorsIds.split(',').filter(Boolean);
    const nameArray=props.rowData.providerAdministratorsName.split('，').filter(Boolean);
    //同步补充选项列表数据
    idArray.forEach((id,index)=> {
      providerAdministrators.value.push({
          staffId:id,
          staffName:nameArray[index] || '',
          orgName: ''
        });
      
    });
   }

    formData.value = JSON.parse(JSON.stringify(props.rowData))
    
    // 设置选中的管理员ID列表
    if(props.rowData.providerAdministratorsIds) {
      formData.value.providerAdministrators = props.rowData.providerAdministratorsIds.split(',').filter(Boolean);
    }
    
    if(formData.value.providerLogo){
      fileList.value = [
        { name: '', url: '', fileId:  formData.value.providerLogo  },
      ];
    }
    if(formData.value.applicableAccountsIds){
      console.log(formData.value.applicableAccountsIds)
      applicableAccountsList.value=  formData.value.applicableAccountsIds.split(',')
    }
    
  
  }

};



const saveForm = async () => {
  try {
    await formRef.value.validate();
    if (fileList.value.length > 0) {
      formData.value.providerLogo = fileList.value[0].fileId;
    } else {
      formData.value.providerLogo = "";
      
    }

    // 处理管理员IDs和名称为逗号分隔的字符串
    const adminIds = formData.value.providerAdministrators || [];
    const adminNames = [];
    
    // 确保所有选中的管理员都有对应的名称
    adminIds.forEach(id => {
      // 首先在providerAdministrators列表中查找
      const admin = providerAdministrators.value.find(item => item.staffId === id);
      
      // 如果找到了，添加名称，否则添加空字符串
      adminNames.push(admin ? admin.staffName : '');
    });

    const payload = { 
      ...formData.value,
      applicableAccountsList: applicableAccountsList.value?.map(id => ({
        accountManageId: id
      })) || [], // 处理空值情况，确保始终为数组
      providerAdministratorsIds: adminIds.join(','),
      providerAdministratorsName: adminNames.join(','),
      // 保持原有的管理员对象数组（如果后端API仍需要）
      providerAdministrators: adminIds.map((id, index) => ({
        administratorsId: id,
        administratorsName: adminNames[index] || ''
      }))
     };

    if (props.popupType === "edit") {
      const res = await screenIndex.update(payload);

      if (res.code == "1") {
        ElMessage.success("修改成功");

        emit("closeBtn");
      }
    } else if (props.popupType === "add") {
      const res = await screenIndex.insert(payload);

      if (res.code == "1") {
        ElMessage.success("新增成功");

        emit("closeBtn");
      }
    }
  } catch (error) {
    console.error("表单校验失败:", error);
  }
};


// 初始化数据

onMounted(() => {
  initData();
  getAccountList()
});

defineExpose({
  saveForm,
});
</script>



<style scoped lang="scss"></style>