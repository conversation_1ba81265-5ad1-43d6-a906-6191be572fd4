<template>
  <div class="app-container">
    <el-card style="height: 100%" shadow="never">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
        <el-form-item label="租户" prop="tenantId">
          <el-select v-model="queryParams.tenantId" placeholder="请选择租户" style="width: 180px" @change="handleQuery">
            <el-option v-for="item in tenantList" :key="item.tenantId" :label="item.tenantName" :value="item.tenantId"/>
          </el-select>
        </el-form-item>
        <el-form-item label="登录地址" prop="userIp">
          <el-input v-model="queryParams.userIp" placeholder="请输入IP地址" clearable style="width: 180px;"
                    @keyup.enter="handleQuery"/>
        </el-form-item>
        <el-form-item label="登录名称" prop="account">
          <el-input v-model="queryParams.account" placeholder="请输入登录名称" clearable style="width: 180px;"
                    @keyup.enter="handleQuery"/>
        </el-form-item>
        <el-form-item label="用户名称" prop="accountName">
          <el-input v-model="queryParams.accountName" placeholder="请输入用户名称" clearable style="width: 180px;"
                    @keyup.enter="handleQuery"/>
        </el-form-item>
        <el-form-item label="状态" prop="logStatus">
          <el-select v-model="queryParams.logStatus" placeholder="登录状态" clearable style="width: 180px"
                     @change="handleQuery">
            <el-option label="成功" value="success" />
            <el-option label="失败" value="fail" />
          </el-select>
        </el-form-item>
        <el-form-item label="登录时间">
          <el-date-picker v-model="dateRange" style="width: 320px" format="YYYY-MM-DD HH:mm:ss" type="datetimerange"
                          start-placeholder="开始日期" end-placeholder="结束日期" date-format="YYYY/MM/DD ddd" time-format="A hh:mm:ss"/>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search"  @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table v-loading="loading" :data="loginLogList" :default-sort="defaultSort" @sort-change="handleSortChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="登录名称" align="center" prop="account" :show-overflow-tooltip="true"  />
        <el-table-column label="用户名称" align="center" prop="accountName" :show-overflow-tooltip="true"  />
        <el-table-column label="登录IP" align="center" prop="userIp" width="130" :show-overflow-tooltip="true" />
        <el-table-column label="登录地点" align="center" prop="location" :show-overflow-tooltip="true" />
        <el-table-column label="浏览器" align="center" prop="browser" :show-overflow-tooltip="true" />
        <el-table-column label="操作系统" align="center" prop="os" />
        <el-table-column label="登录状态" align="center" prop="logStatus">
          <template #default="scope">
            <el-tag :type="scope.row.logStatus === 'success' ? '' : 'danger'">
              {{ scope.row.logStatus === 'success' ? '成功' : '失败' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作信息" align="center" prop="exception" />
        <el-table-column label="登录日期" align="center" prop="logTime" sortable="custom" :sort-orders="['descending', 'ascending']" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.logTime) }}</span>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize"
                  @pagination="getList"/>
    </el-card>
  </div>
</template>

<script setup name="LoginLog">
import { list } from "@/api/operation/loginLog";
import useUserStore from '@/store/modules/user';
import {getTenants} from "@/api/tenant/tenant";
import {addDateRange} from "@/utils/common";

const { proxy } = getCurrentInstance();
const defaultSort = { prop: 'log_time', order: 'descending'};
const dateRange = ref([]);
const showSearch = ref(true);

/** 初始化租户数据 */
const userStore = useUserStore();
const tenantList = ref([]);
function getTenantList() {
  getTenants().then(res => {
    tenantList.value = res.data;
  }).catch(() => {
    tenantList.value = [];
  })
}

/** 表格数据显示 */
const loginLogList = ref([]);
const loading = ref(true);
const total = ref(0);
const queryRef = ref(null)
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  userIp: undefined,
  account: undefined,
  accountName: undefined,
  logStatus: undefined,
  orderByColumn: 'log_time',
  isAsc: 'descending',
  tenantId: userStore.userInfo.tenantId,
})

function getList() {
  loading.value = true;
  const params = Object.assign(addDateRange({}, dateRange.value, 'Time').params, queryParams.value);
  list(params).then(res => {
    if (res.success) {
      loginLogList.value = res.data.records;
      total.value = res.data.total;
    }
    loading.value = false;
  }).catch(() => {
    loading.value = false;
  })
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  dateRange.value = [];
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 排序触发事件 */
const handleSortChange = (column, prop, order) => {
  queryParams.value.orderByColumn = column.prop;
  queryParams.value.isAsc = column.order;
  getList();
}

getTenantList();
getList();
</script>

