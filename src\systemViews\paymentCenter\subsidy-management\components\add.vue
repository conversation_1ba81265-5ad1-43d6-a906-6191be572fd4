<!-- 账户清零 -->

<template>
  <div
    class="dialog-box"
    :class="popupType === 'view' ? 'dialog-box-view' : 'dialog-box-edit'"
  >
    <el-form
      ref="formRef"
      :model="formData"
      label-width="95px"
      :rules="popupType !== 'view' ? rules : ''"
    >
      <el-row>
        <el-col :span="8">
          <el-form-item label="供应商" prop="providerId">
            <!-- 添加或编辑时显示选择器 -->
            <el-select
              v-model="formData.providerId"
              :disabled="popupType === 'view' || popupType === 'edit'"
              placeholder="请选择供应商"
              v-if="popupType !== 'view'"
              @change="handleProviderChange"
            >
              <el-option
                v-for="(item, index) in providerList"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>

            <!-- 查看时显示文本 -->
            <div v-else>
              {{ formData.providerName || "" }}
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="支付场景" prop="payType">
            <el-select
              v-if="popupType !== 'view'"
              @change="handleChangePayType"
              v-model="formData.payType"
              placeholder="请选择支付场景"
              :disabled="popupType === 'edit'"
              clearable
            >
              <el-option
                v-for="item in largeCategoryOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>

            <!-- 查看时显示文本 -->
            <div v-else>
              {{ formData.payTypeName || "" }}
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="支付类型" prop="paySubclass">
            <el-select
              v-if="popupType !== 'view'"
              v-model="formData.paySubclass"
              placeholder="请选择支付类型"
              :disabled="popupType === 'edit'"
              clearable
            >
              <el-option
                v-for="item in orderSubclassOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>

            <div v-else>
              {{ formData.paySubclassName || "" }}
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="popupType !== 'view'?24:8">
          <el-form-item label="价格类型" prop="priceType">
            <el-col :span="24">
              <div class="flex">
                <el-radio-group
                  v-if="popupType !== 'view'"
                  v-model="formData.priceType"
                >
                  <el-radio
                    :label="item.value"
                    v-for="(item, index) of price_type"
                    :key="index"
                    >{{ item.label }}</el-radio
                  >
                </el-radio-group>

                <NumberInput
                  style="width: 25%"
                  v-if="popupType !== 'view' && formData.priceType == 1"
                  v-model="formData.defaultAmount"
                  customPlaceholder="请输入默认价格（元）"
                  input-type="decimal"
                />
              </div>
            </el-col>

            <div v-if="popupType == 'view'">
              {{ $formatDictLabel(formData.priceType, price_type) }}
              <span v-if="formData.priceType == 1 && formData.defaultAmount">
                ({{ formData.defaultAmount }}元)
              </span>
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="日期类型" prop="datesType">
            <el-checkbox-group
              v-model="formData.datesType"
              v-if="popupType !== 'view'"
              @change="dateTypeChange"
            >
              <el-checkbox
                :label="item.value"
                v-for="(item, index) of date_kind"
                :key="index"
              >{{item.label}}</el-checkbox>
            </el-checkbox-group>

            <div v-else>
                {{ $formatDictLabels(formData.dateType, date_kind) }}
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="首次补贴" prop="firstSubsidy">
            <el-radio-group
              v-if="popupType !== 'view'"
              v-model="formData.firstSubsidy"
            >
              <el-radio
                :label="item.value"
                v-for="(item, index) of first_subsidy"
                :key="index"
                >{{ item.label }}</el-radio
              >
            </el-radio-group>

            <div v-else>
              {{ $formatDictLabel(formData.firstSubsidy, first_subsidy) }}
            </div>
          </el-form-item>
        </el-col>
   <el-col :span="8">
          <el-form-item label="是否启用" prop="status">
            <el-radio-group
              v-if="popupType != 'view'"
              v-model="formData.status"
            >
              <el-radio-button :label="item.value"    v-for="(item, index) of subsidy_status"
                :key="index" >{{item.label}}</el-radio-button>
            </el-radio-group>

            <div v-else>
              {{ $formatDictLabel(formData.status, subsidy_status) }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="消费限制" prop="consumeType">
            <el-select
              v-if="popupType !== 'view'"
              v-model="formData.consumeType"
              placeholder="请选择消费限制"
              clearable
            >
              <el-option
                v-for="item in consume_type"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>

            <div v-else>
              {{ $formatDictLabel(formData.consumeType, consume_type) }}
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="8" v-if="formData.consumeType=='1'">
          <el-form-item label="消费次数" prop="consumeNum">
            <el-select
              v-if="popupType !== 'view'"
              v-model="formData.consumeNum"
              placeholder="请选择消费次数"
              clearable
            >
              <el-option
                v-for="item in consume_num"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>

            <div v-else>
              {{ $formatDictLabel(formData.consumeNum, consume_num) }}
            </div>
          </el-form-item>
        </el-col>

      </el-row>
    </el-form>

    <!-- 表格区域 -->
    <div class="flex" style="margin-bottom: 12px"    v-if="popupType !== 'view'">
      <div class="flex-1"></div>
      <el-button type="primary" icon="Plus" @click="handleAdd"
        >新增其他价格</el-button
      >
    </div>
    <public-table
      ref="publictable"
      maxHeight="480"
      :rowKey="tabelForm.tableKey"
      :tableData="lists"
      :columns="tabelForm.columns"
      :configFlag="tabelForm.tableConfig"
      :pageValue="pageParams"
    >
      <template #yhPriceSlot="{ scope }">
        <NumberInput
          v-if="popupType !== 'view'"
          v-model="scope.row.discountAmount"
          customPlaceholder="请输入默认价格"
          input-type="decimal"
        />
        <div v-else class="text-right">{{ scope.row.discountAmount}}</div>
      </template>

      <template #accountTypeSlot="{ scope }">
        <el-select
         v-if="popupType !== 'view'"
          v-model="scope.row.accountId"
          placeholder="请选择账户类型"
          clearable
        >
          <el-option
            v-for="(item, index) of accountTypeList"
            :key="index"
            :label="item.label"
            :value="item.value"
            :disabled="isAccountTypeSelected(item.value, scope.row)"
          />
        </el-select>
         <div v-else> {{ $formatDictLabel(scope.row.accountId, accountTypeList) }}</div>
      </template>
      <template #operation="{ scope }" >
        <el-button
          type="text"
          title="删除"
          icon="Delete"
          @click="handleDelete(scope.row)"
        ></el-button>
      </template>

          <template #statusSlot="{ scope }">
             <el-radio-group
              v-if="popupType != 'view'"
              v-model="scope.row.status"
            >
              <el-radio-button :label="item.value"    v-for="(item, index) of subsidy_status"
                :key="index" >{{item.label}}</el-radio-button>
            </el-radio-group>

            <div v-else>
              {{ $formatDictLabel(scope.row.status, subsidy_status) }}
            </div>
      </template>
    </public-table>
  </div>
</template>



<script setup>
import { ref, reactive, watch, getCurrentInstance, defineProps } from "vue";

import { ElMessage } from "element-plus";

import { screenIndex } from "@/api/paymentCenter/subsidy-management/index";

import useUserStore from "@/store/modules/user";


// 定义 emits
const emit = defineEmits(["closeBtn"]);
// 定义组件 props
const props = defineProps({
  closeBtn: {
    type: Function,

    default: null,
  },

  popupType: {
    type: String,
    default: "",
  },

  rowData: {
    type: Object,
    default: () => ({}),
  },
});

// 表单引用
const formRef = ref(null);
let formData = ref({
  datesType: [],
  priceType: "1",
  firstSubsidy: "1",
  status:'1'
});
const pageParams = ref({
  pageNum: 1,
  pageSize: 10,
});
// 字典
const { proxy } = getCurrentInstance();
const {
  price_type,
  first_subsidy,
  date_kind,
  consume_type,
  consume_num,
  subsidy_status,
} = proxy.useDict(
  "price_type",
  "first_subsidy",
  "date_kind",
  "consume_type",
  "consume_num",
  "subsidy_status"
);

// 供应商列表
const providerList = ref([]);
// 账户类型列表
const accountTypeList = ref([]);
// 支付场景选项
const largeCategoryOptions = ref([]);

// 支付类型选项
const orderSubclassOptions = ref([]);

// 定义校验规则

const rules = reactive({
  providerId: [
    { required: true, message: "请选择供应商", trigger: ["blur", "change"] },
  ],
  payType: [
    { required: true, message: "请选择支付场景", trigger: ["blur", "change"] },
  ],
  paySubclass: [
    { required: true, message: "请选择支付类型", trigger: ["blur", "change"] },
  ],
  priceType: [
    { required: true, message: "请选择价格类型", trigger: ["blur", "change"] },
     {
      validator: (rule, value, callback) => {
        if (value == 1 && !formData.value.defaultAmount) {
          callback(new Error('请填写默认价格'));
        } else {
          callback();
        }
      },
      trigger: ["blur", "change"]
    }
  ],
  datesType: [
    { required: true, message: "请选择日期类型", trigger: ["blur", "change"] },
  ],
  firstSubsidy: [
    { required: true, message: "请选择首次补贴", trigger: ["blur", "change"] },
  ],
  consumeType: [
    { required: true, message: "请选择消费限制", trigger: ["blur", "change"] },
  ],
  consumeNum: [
    { required: true, message: "请选择消费次数", trigger: ["blur", "change"] },
  ],
});

// 表格配置
const lists = ref([]);

const tabelForm = reactive({
  tableKey: "subsidy",
  columns: [
    {
      label: "优惠价格(元)",
      slotname: "yhPriceSlot",
      minWidth: 80,
      fixed: "right",
      visible: true,
      slot: true,
    },
    {
      label: "账户名称",
      slotname: "accountTypeSlot",
      minWidth: 80,
      fixed: "right",
      visible: true,
      slot: true,
    },
     {
      label: "是否启用",
      slotname: "statusSlot",
      minWidth: 80,
      fixed: "right",
      visible: true,
      slot: true,
    },

    {
      label: "操作",
      slotname: "operation",
      minWidth: 80,
      fixed: "right",
      visible: props.popupType !== "view",
      slot: true,
    },
  ],
  tableConfig: {
    needPage: true,
    index: true,
    selection: false,
    indexWidth: "60",
    loading: false,
    height: null,
  },
});
// 定义方法

//新增表格数据
const handleAdd = () => {
  lists.value.push({
    discountAmount: "", // 优惠价格
    accountId: "", // 账户类型ID
    accountName: "", // 账户类型名称
    status:'0'
  });
};

// 删除表格数据
const handleDelete = (row) => {
  const index = lists.value.findIndex((item) => item === row);
  if (index !== -1) {
    lists.value.splice(index, 1);
  }
}; //日期类型选择
const dateTypeChange = (values) => {
  formData.value.dateType = values.join(",");
};

// 查询支付类型树
const payTypeTree = async (value, label) => {
  try {
    const res = await screenIndex.payTypeTree({
      id: value || "",
    });



    if (value && label) {
      orderSubclassOptions.value = res.data;
    } else {
      largeCategoryOptions.value = res.data;
    }
  } catch (error) {
    console.error("获取支付类型树失败:", error);
  }
};

const handleChangePayType = async (value) => {
  formData.value.paySubclass = "";

  const selectedOption = largeCategoryOptions.value.find(
    (item) => item.value === value
  );

  if (selectedOption) {
    try {
      await payTypeTree(selectedOption.id, selectedOption.label);
    } catch (error) {
      console.error("获取支付类型失败:", error);
      orderSubclassOptions.value = [];
    }
  }
};
// 供应商列表
const supplierPageList = async () => {
  try {
    const res = await screenIndex.providerTree({});
    providerList.value = res.data;
  } catch (error) {
    console.error("获取供应商列表失败:", error);
  }
};

// 账户类型
const payAccountTypePageList = async () => {
  const res = await screenIndex.accountTree({});

  accountTypeList.value = res.data.map((item) => ({
    // 保留原始数据（根据需要可选）
    ...item,
  }));
};
// ... existing code ...
// ... existing code ...
 const formatPrice = (value) => {
      if (!value) return 0;
      // 如果是整数，添加.00
      if (String(value).indexOf('.') === -1) {
        return Number(value).toFixed(2);
      }
      return value;
    };
const initData = async () => {
  await supplierPageList();
  await payAccountTypePageList();

  if (props.popupType === "add") {
    // 新增时初始化空数据
    formData.value = {
      datesType: [],
      priceType: "1",
      firstSubsidy: "1",
      status: '0',
      defaultAmount: '0.00'
    };
    lists.value = [];
  } else {
    // 编辑或查看时获取详情数据
    try {
      const res = await screenIndex.getInfo({ id: props.rowData.id });
      if (res.code === "1") {
        formData.value = {
          ...res.data,
          datesType: res.data.dateType ? res.data.dateType.split(',') : [],
          defaultAmount: formatPrice(res.data.defaultAmount)  // 格式化价格
        };
        lists.value = (res.data.paySubsidyDetailsVoList || []).map(item => ({
          ...item,
          discountAmount: formatPrice(item.discountAmount)  // 格式化表格行价格
        }));

        // 处理级联关系：先处理供应商，再处理支付场景，最后处理支付类型
        if (res.data.providerId) {
          // 根据选中的供应商获取支付场景列表
          const selected = providerList.value.find(item => item.value === res.data.providerId);
          const sceneName = selected ? selected.value: '';
          try {
            const sceneRes = await screenIndex.paySceneTree({ value: sceneName });
            largeCategoryOptions.value = sceneRes.data;

            // 如果有支付场景，则获取对应的支付类型
            if (res.data.payType) {
              const selectedOption = largeCategoryOptions.value.find(
                (item) => item.value === res.data.payType
              );

              if (selectedOption) {
                await payTypeTree(selectedOption.id, selectedOption.label);
                // 设置回原来的支付类型值
                if (res.data.paySubclass) {
                  formData.value.paySubclass = res.data.paySubclass;
                }
              }
            }
          } catch (error) {
            console.error('获取支付场景失败:', error);
            largeCategoryOptions.value = [];
          }
        }
      }
    } catch (error) {
      console.error('获取详情失败:', error);
    }
  }
};

const handleProviderChange = async (value) => {
  // 获取选中的供应商名称
  const selected = providerList.value.find(item => item.value === value);
  const sceneName = selected ? selected.value : '';
  try {
    const res = await screenIndex.paySceneTree({ value:sceneName });
    largeCategoryOptions.value = res.data;
  } catch (error) {
    largeCategoryOptions.value = [];
    console.error('获取支付场景失败:', error);
  }
  // 清空支付场景和小类
  formData.value.payType = '';
  formData.value.paySubclass = '';
};

// 判断账户类型是否已被选择
const isAccountTypeSelected = (accountId, currentRow) => {
  if (!accountId) return false;
  return lists.value.some(row =>
    row !== currentRow &&
    row.accountId === accountId
  );
};

const saveForm = async () => {
  try {
    await formRef.value.validate();


    const params = {
      id: formData.value.id,
      providerId: formData.value.providerId,
      payType: formData.value.payType,
      paySubclass: formData.value.paySubclass,
      priceType: formData.value.priceType,
      defaultAmount: formatPrice(formData.value.defaultAmount),
      dateType: formData.value.dateType,
      firstSubsidy: formData.value.firstSubsidy,
      consumeType: formData.value.consumeType,
      consumeNum: formData.value.consumeNum,
      status: formData.value.status,
      tenantId: useUserStore().userInfo.customParam.tenantId,
      paySubsidyDetailsVoList: lists.value.map(item => ({
        accountId: item.accountId,
        discountAmount:  formatPrice(item.discountAmount),
        status: item.status
      }))
    };
    if (props.popupType === "edit") {
      const res = await screenIndex.edit(params);
      if (res.code == "1") {
        ElMessage.success("修改成功");
        emit("closeBtn");
      }
    } else if (props.popupType === "add") {
      const res = await screenIndex.add(params);
      if (res.code == "1") {
        ElMessage.success("新增成功");
        emit("closeBtn");
      }
    }
  } catch (error) {
    console.error("表单校验失败:", error);
  }
};

// 初始化数据

onMounted(() => {
  initData();
});

defineExpose({
  saveForm,
});
</script>



<style scoped lang="scss">
:deep(.el-radio-group) {
  flex-wrap: nowrap !important;
  margin-right: 20px;
}
</style>
