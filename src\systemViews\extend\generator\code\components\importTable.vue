<template>
  <div class="import-table">
    <!--  选择数据源表  -->
    <el-dialog title="导入表" v-model="visible" width="1050px" top="5vh" append-to-body>
      <!--  搜索栏  -->
      <el-form size="small" :inline="true">
        <el-form-item label="数据源" prop="datasourceName">
          <el-select v-model="datasourceId" @change="datasourceChange(datasourceId)" placeholder="请选择" filterable>
            <el-option v-for="item in datasourceListData" :key="item.id" :label="item.databaseName" :value="item.id">
              <span style="float: left">{{ item.host }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ item.databaseName }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="表名称" prop="tableName">
          <el-input v-model="tableName" placeholder="请输入表名称" clearable @keyup.enter="handleQuery"/>
        </el-form-item>
        <el-form-item label="表描述" prop="tableComment">
          <el-input v-model="tableComment" placeholder="请输入表描述" clearable @keyup.enter="handleQuery"/>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <!-- 表格数据  -->
      <el-table v-loading="loading" :data="dbTableList" ref="tableRef" height="260px"
                @row-click="clickRow" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column prop="tableName" label="表名称" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column prop="tableComment" label="表描述" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column prop="createDate" label="创建时间"></el-table-column>
        <el-table-column prop="updateDate" label="更新时间"></el-table-column>
      </el-table>
      <!--  分页  -->
      <pagination v-show="total > 0" :total="total" v-model:page="pageParams.pageNum" v-model:limit="pageParams.pageSize"
                  @pagination="getList" />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="visible = false">取 消</el-button>
          <el-button type="primary" @click="handleImportTable">下一步</el-button>
        </div>
      </template>
    </el-dialog>

    <!--  表单配置  -->
    <el-dialog title="配置表单" v-model="formVisible" width="500px" append-to-body>
      <el-form :model="formData" ref="formRef" size="small" :rules="rules">
        <el-form-item label="包名" prop="packageName">
          <el-input v-model="formData.packageName" placeholder="请输入包名"/>
        </el-form-item>
        <el-form-item label="作者" prop="functionAuthor">
          <el-input v-model="formData.functionAuthor" placeholder="请输入作者"/>
        </el-form-item>
        <el-form-item label="页面路径" prop="pagePath">
          <el-input v-model="formData.pagePath" placeholder="请输入前端页面存放路径，例如：system/user"/>
        </el-form-item>
        <el-form-item label="lombok" prop="lombok">
          <el-switch v-model="formData.lombok"></el-switch>
        </el-form-item>
        <el-form-item label="swagger" prop="swagger">
          <el-switch v-model="formData.swagger"></el-switch>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="formVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitImportTable(formRef)">确 定</el-button>
        </div>
      </template>
    </el-dialog>

  </div>
</template>

<script setup>
import {findList, findOne} from "@/api/extend/generator/datasource";
import {findDBTablePage, importTable} from "@/api/extend/generator/genTable";
import {ElMessage} from "element-plus";

/** 分页参数 */
const total = ref(0)
const pageParams = ref({
  pageNum: 1,
  pageSize: 10
})

/** 表格数据显示 */
const loading = ref(true)
const visible = ref(false)
const tables = ref([])
const tableRef = ref(null)
const dbTableList = ref([])
const tableName = ref()
const tableComment = ref()
const databaseName = ref()

// 查询数据库表数据
function getList() {
  loading.value = true;
  const params = {
    ...pageParams.value,
    tableName: tableName.value,
    tableComment: tableComment.value,
    databaseName: databaseName.value,
    datasourceId: datasourceId.value
  }
  findDBTablePage(params).then(res => {
    if (res.data) {
      dbTableList.value = res.data.records
      total.value = res.data.total
    }
    loading.value = false;
  }).catch(() => {
    loading.value = false;
  })
}

/** 获取数据源列表 */
const datasourceName = ref('')
const datasourceId = ref()
const datasourceListData = ref([])

// 获取数据源列表
function getDatasourceList() {
  findList().then((res) => {
    datasourceListData.value = res.data;
    if (res.data.length > 0) {
      datasourceId.value = datasourceListData.value[0].id;
      databaseName.value = datasourceListData.value[0].databaseName;
      datasourceName.value = datasourceListData.value[0].host + ' -- ' + datasourceListData.value[0].databaseName;
      getList();
    } else {
      loading.value = false;
    }
  })
}

// 显示弹窗
const show = () => {
  getDatasourceList();
  visible.value = true;
}

const datasourceChange = (datasourceId) => {
  loading.value = true
  findOne(datasourceId).then(res => {
    databaseName.value = res.data.databaseName
    datasourceName.value = res.data.host + ' -- ' + res.data.databaseName
    getList()
  })
}

// 勾选
const clickRow = (row) => {
  tableRef.value.toggleRowSelection(row);
}

// 多选框中选中数据
const handleSelectionChange = (selection) => {
  tables.value = selection.map(item => item.tableName)
}

// 搜索按钮
const handleQuery = () => {
  pageParams.value.pageNum = 1;
  getList();
}

// 重置按钮
const resetQuery = () => {
  tableName.value = '';
  tableComment.value = '';
  handleQuery();
}

/** 配置表单 */
const emit = defineEmits(['ok'])
const formVisible = ref(false)
const tableNames = ref('')
const formRef = ref(null)
const formData = ref({
  packageName: '',
  functionAuthor: '',
  pagePath: '',
  tables: '',
  databaseName: '',
  datasourceName: '',
  lombok: true,
  swagger: true
})
const rules = {
  packageName: [{ required: true, message: '请输入包名', trigger: 'blur' }],
  functionAuthor: [{ required: true, message: '请输入作者', trigger: 'blur' }],
  pagePath: [{ required: true, message: '请输入前端页面存放路径', trigger: 'blur' }]
}

// 导入按钮
const handleImportTable = () => {
  tableNames.value = tables.value.join(",");
  if (tableNames.value === "") {
    ElMessage.error("请选择要导入的表");
    return
  }
  formVisible.value = true;
}

// 提交导入
const submitImportTable = (ref) => {
  ref.validate((valid, fields) => {
    if (valid) {
      formData.value.tables = tableNames.value;
      formData.value.datasourceName = datasourceName.value;
      formData.value.databaseName = databaseName.value;
      formData.value.datasourceId = datasourceId.value;
      importTable(formData.value).then(res => {
        if(res.data) {
          visible.value = false;
          formVisible.value = false;
          ElMessage.success('导入成功！');
          emit("ok",res.data);
        }
      })
    } else {
      ElMessage.warning('请完成表单填写！');
      return false;
    }
  })
}
// 设置对外公开的变量
defineExpose({
  show,
});
</script>

<style scoped lang="scss">

</style>
