<template>
 <div
    class="dialog-box"
    :class="popupType === 'view' ? 'dialog-box-view' : 'dialog-box-edit'"
  >
        <el-form ref="ruleform" :model="formData" label-width="120px" :rules="popupType !== 'view' ? rules : false">
          <el-row>
            <el-col :span="24">
              <el-form-item v-if="rootNode === 'false'" label="上级区域" prop="parentId">
                <el-select
                  v-if="popupType != 'view'"
                  filterable
                  :filter-method="dataFilter"
                  class="main-select-tree"
                  ref="selectTree"
                  v-model="formData.parentId"
                  placeholder="请选择上级区域"
                >
                  <el-option :label="formData.parentName" :value="formData.parentId" style="display: none" />
                  <el-tree
                    style="margin: 20px auto; margin-top: 10px"
                    :check-strictly="true"
                    :data="areaTreeData"
                    :filter-node-method="filterNode"
                    @node-click="handleNodeClick"
                    default-expand-all
                    node-key="id"
                    ref="areaTree"
                    highlight-current
                    :props="treeProps"
                  />
                </el-select>
                <div v-if="popupType == 'view'">{{ fatherAreaInfo.areaName || '-' }}</div>
              </el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item label="区域名称" prop="areaName">
                <el-input v-if="popupType != 'view'" v-model="formData.areaName" placeholder="请输入区域名称" clearable />
                <div v-if="popupType == 'view'">{{ formData.areaName }}</div>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="区域编码" prop="areaId" v-if="popupType == 'view'">
                <div>{{ formData.areaId }}</div>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="区域类型" prop="areaType">
                <el-select v-model="formData.areaType" placeholder="请选择区域类型" clearable v-if="popupType != 'view'">
                  <el-option v-for="dict in area_type" :key="dict.value" :label="dict.label" :value="dict.value" />
                </el-select>
                <div v-if="popupType == 'view'">{{  $formatDictLabel(formData.status,area_type)  }}</div>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="区域地点" prop="areaAddress">
                <el-input v-if="popupType != 'view'" v-model="formData.areaAddress" placeholder="请输入区域地点" clearable />
                <div v-if="popupType == 'view'">{{ formData.areaAddress }}</div>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="区域排序" prop="orderNum">
                <el-input
                  v-if="popupType != 'view'"
                  v-model="formData.orderNum"
                  placeholder="请输入区域排序"
                  oninput="value=value.replace(/[^\d]/g,'')"
                  clearable
                />
                <div v-if="popupType == 'view'">{{ formData.orderNum }}</div>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="区域状态" prop="status">
                <el-radio-group v-if="popupType != 'view'" v-model="formData.status">
                  <el-radio-button label="1">有效</el-radio-button>
                  <el-radio-button label="0">无效</el-radio-button>
                </el-radio-group>
                <div v-if="popupType == 'view'">{{ $formatDictLabel(formData.status,area_status)  }}</div>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="根区域" prop="rootNode" v-if="popupType == 'add'">
                <el-radio-group v-model="rootNode" @input="changeRootNode">
                  <el-radio-button label="true">是</el-radio-button>
                  <el-radio-button :disabled="formData.levelNum === 1" label="false">否</el-radio-button>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

       
      </div>
</template>

<script setup>
import { ref, watch, onMounted,getCurrentInstance } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getAreaInfo, saveAreaInfo ,getTreeData} from '@/api/area/area'
const { proxy } = getCurrentInstance();
const { area_type, area_status } = proxy.useDict("area_type", "area_status");

// 响应式数据
const formData = ref({
  status: '1',
  parentName: '',
})
const areaTreeData = ref([])
const areaList = ref([])
const fatherAreaInfo = ref({})
const rootNode = ref('false')
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
})
const loading = ref(false)
const total = ref(0)
const selection = ref([])
const treeProps = ref({ multiple: true, emitPath: false, value: 'id' })
const ruleform = ref(null)
const selectTree = ref(null)
const areaTreeRef = ref(null)

const rules = ref({
  parentId: [{ required: true, message: '请选择上级区域', trigger: 'blur' }],
  areaName: [{ required: true, message: '请输入区域名称', trigger: 'blur' }],
  areaType: [{ required: true, message: '请选择区域类型', trigger: 'change' }],
  status: [{ required: true, message: '请选择区域状态', trigger: 'blur' }],
})

// 监听 formData 变化
watch(
  () => formData.value,
  () => {
    if (formData.value.levelNum === 1) {
      rootNode.value = 'true'
    }
  },
  { deep: true }
)

// 生命周期
onMounted(() => {
  selectAreaTree()
  if (props.popupType === 'edit' || props.popupType === 'view') {
    formData.value = JSON.parse(JSON.stringify(props.rowData))
    getAreaInfo({ id: formData.value.parentId }).then((res) => {
      formData.value.parentName = res.data.areaName
      fatherAreaInfo.value = res.data
    })

    getAreaInfo({ id: formData.value.id }).then((res) => {
      formData.value = res.data
    })
  }
})

// 方法
const dataFilter = (val) => {
  if (val) {
    areaTreeRef.value.filter(val)
  }
}

const filterNode = (value, data) => {
  if (!value) return true
  return data.label.indexOf(value) !== -1
}

const selectAreaTree = () => {
  getTreeData().then((res) => {
    areaTreeData.value = res.data
  })
}

const handleNodeClick = (data) => {
  formData.value.parentId = data.id
  formData.value.parentName = data.label
  selectTree.value.blur()
}

const changeRootNode = (value) => {
  console.log(value)
}

const submitForm = () => {
  ruleform.value.validate((valid) => {
    if (valid) {
      if (formData.value.status === '0' && props.popupType === 'edit') {
        ElMessageBox.confirm('确认要修改区域为无效吗？\n该区域下的所有子区域都会被设为无效，请确认。')
          .then(() => {
            saveAreaInfo(formData.value).then((res) => {
              if (res.code == '1') {
                ElMessage.success('操作成功')
                emit('dialogResult', props.popupType)
                props.closeBtn('sure')
              }
            })
          })
          .catch(() => {})
      } else {
        saveAreaInfo(formData.value).then((res) => {
          if (res.code == '1') {
            ElMessage.success('保存成功')
            emit('dialogResult', props.popupType)
            props.closeBtn('sure')
          }
        })
      }
    }
  })
}

// 事件
const emit = defineEmits(['dialogResult'])
const props = defineProps({
  closeBtn: {
    type: Function,
    default: null,
  },
  popupType: {
    type: String,
    default: '',
  },
  rowData: {
    type: Object,
    default: () => ({}),
  },
})

defineExpose({
  submitForm
})
</script>

<style scoped lang="scss">
.el-common-container {
  height: auto;
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-scrollbar__wrap) {
  overflow-x: hidden;
}
</style>