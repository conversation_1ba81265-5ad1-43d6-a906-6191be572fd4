import request from '@/utils/request'


// 小程序须知获取
export function getNoticeInfo() {
  return request({
    url: '/system/notice/getNoticeInfo',
    method: 'get'
  })
}
// 小程序须知保存
export function saveNoticeInfo(query) {
  return request({
    url: '/visitor/applets/noticeInfo',
    method: 'post',
    data: query
  })
}

// 手动同步海康部门、人员
export function syncDeptUser() {
  return request({
    url: '/visitor/applets/syncDeptUser',
    method: 'post'
  })
}

// 查询推送失败数据
export function findList(params) {
  return request({
    url: '/visitorinvite/invite/selectFailList',
    method: 'post',
    params: params
  })
}

// 处理审核结果推送失败的数据
export function pushFailResult(id) {
  return request({
    url: '/visitorinvite/invite/pushFailResult/'+id,
    method: 'post'
  })
}
