<!-- 账户明细 -->
<template>
  <div class="container-table-box">
    <div class="flex"  v-if="uoloadStatus == 'failed'" style="margin-top: -12px;">
      <div class="flex-1"></div>
      <el-button type="primary" size="mini" icon="Download" @click="handleExport">下载批量导入【{{ adjustType === 'REFUND' ? '退款' : '补扣' }}】失败列表
      </el-button>
    </div>
    <div class="common-header" style="margin-bottom: 12px;" v-if="uoloadStatus != 'failed'">
      <div class="common-header-line"></div>
      <div class="common-header-text">统计结果</div>
      <div class="flex-1" />
    </div>

    <div class="stats-container" v-if="uoloadStatus != 'failed'">
      <div class="stats-card">
        <div class="stats-icon">
          <i class="User"></i> <el-icon><User /></el-icon>
        </div>
        <div class="stats-content">
          <div class="stats-label">总人数</div>
          <div class="stats-value">{{ infoData.totalNum || "0" }}</div>
        </div>
      </div>
      <div class="stats-card">
        <div class="stats-icon">
          <i class="User"></i> <el-icon><Money /></el-icon>
        </div>
        <div class="stats-content">
          <div class="stats-label">总金额</div>
          <div class="stats-value">{{ infoData.totalAmount || "0" }} <span>元</span></div>
        </div>
      </div>
    </div>

    <div class="common-header" style="margin-bottom: 12px;">
      <div class="common-header-line"></div>
      <div class="common-header-text">{{ adjustType === 'REFUND' ? '退款' : '补扣' }}列表</div>
      <div class="flex-1" />
    </div>
    <public-table ref="publictable" :rowKey="tabelForm.tableKey" :tableData="list" :columns="tabelForm.columns"
      :configFlag="tabelForm.tableConfig" :pageValue="pageParams" :total="total">
    </public-table>
  </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance, watch, onMounted } from "vue";
import { ElMessageBox, ElMessage } from "element-plus";
import { apiUrl } from "@/utils/config";
import { formatMinuteTime } from "@/utils";
import { screenIndex } from "@/api/paymentCenter/abnormal-adjustment/index";
const { proxy } = getCurrentInstance();
const { adjust_type, adjust_status } = proxy.useDict(
  "adjust_type", "adjust_status"
);
const emit = defineEmits(["closeBtn"]);

const props = defineProps({
  detailList: {
    type: Array,
    default: () => [],
  },
  uoloadStatus: {
    type: String,
    default: "",
  },
  redisLock: {
    type: String,
    default: "",
  },
  adjustType: {
    type: String,
    default: "REFUND", // 默认为退款类型
  }
});
const infoData = ref({})
const pageParams = ref({
  pageNum: 1,
  pageSize: 10,
});
const list = ref([]);
const total = ref(0);
const tabelForm = reactive({
  tableKey: "1", //表格key值
  isShowRightToolbar: true, //是否显示右侧显示和隐藏
  showSearch: true,
  // 表格表格数据
  columns: [
    {
      fieldIndex: "staffName",
      label: "员工姓名",
      show: true,
      sortable: true,
      visible: true, // 展示与隐藏
      minWidth: "120px",
    },
    {
      fieldIndex: "loginName",
      label: "登录账号",
      show: true,
      sortable: true,
      visible: true, // 展示与隐藏
      minWidth: "120px",
    },
    {
      fieldIndex: "providerName",
      label: "供应商名称",
      show: true,
      sortable: true,
      visible: true, // 展示与隐藏
      minWidth: "120px",
    },
    {
      fieldIndex: "paySceneName",
      label: "支付场景",
      show: true,
      sortable: true,
      visible: true, // 展示与隐藏
      minWidth: "120px",
    },
    {
      fieldIndex: "payTypeName",
      label: "支付类型",
      show: true,
      sortable: true,
      visible: true, // 展示与隐藏
      minWidth: "120px",
    },
    {
      fieldIndex: "accountName",
      label: "账户名称",
      show: true,
      sortable: true,
      visible: true, // 展示与隐藏
      minWidth: "120px",
    },
    {
      fieldIndex: "adjustType",
      label: "调整类型",
      show: true,
      sortable: true,
      visible: true, // 展示与隐藏
      type: 'dict',
      dictList: adjust_type,
      minWidth: "120px",
    },
    // {
    //   fieldIndex: "status",
    //   label: "调整状态",
    //   show: true,
    //   sortable: true,
    //   visible: true, // 展示与隐藏
    //   type: 'dict',
    //   dictList: adjust_status,
    //   minWidth: "120px",
    // },
    // {
    //   fieldIndex: "rechargeAmount", // 对应列内容的字段名
    //   label: "充值金额(元)", // 显示的标题
    //   resizable: true, // 对应列是否可以通过拖动改变宽度
    //   visible: true, // 展示与隐藏
    //   sortable: true, // 对应列是否可以排序
    //   fixed: "", //固定
    //   minWidth: "120", //最小宽度%
    //   width: "", //宽度
    //   type: "dollor",
    // },
  ],
  // 表格基础配置项，看个人需求添加
  tableConfig: {
    needPage: true, // 是否需要分页
    index: true, // 是否需要序号
    selection: false, // 是否需要多选
    reserveSelection: false, // 是否需要支持跨页选择
    indexFixed: false, // 序号列固定 left true right
    selectionFixed: false, //多选列固定left true right
    indexWidth: "50", //序号列宽度
    loading: false, //表格loading
    showSummary: false, //是否开启合计
    height: null, //表格固定高度 比如：300px
  },
});
/** 查询列表 */
const getList = () => {
  if (props.uoloadStatus === 'failed') {
    list.value = [];
    total.value = 0;
    return;
  }
  tabelForm.tableConfig.loading = true;
  screenIndex.getStatisticResult({
    redisLock: props.redisLock
  }).then((response) => {
    infoData.value = response.data;
  });

  screenIndex.importDataList({
    redisLock: props.redisLock
  }, pageParams.value).then((response) => {
    list.value = response.data.records;
    total.value = response.data.total;
    tabelForm.tableConfig.loading = false;
  });
};
const handleExport = () => {
  proxy.download(
    `pay${apiUrl}/pay/adjust/exportRefundData`,
    {
      redisLock: props.redisLock,
    },
    `批量导入退款失败列表_${formatMinuteTime(new Date())}.xlsx`
  );
}
// 保存
const saveBtn = async (redisLock) => {
  try {
    // 3. 调用接口（假设接口接受数组格式）
    const res = await screenIndex.batchImportAdd({ redisLock: redisLock });

    // 4. 处理结果
    if (res.success) {
      ElMessage.success("操作成功");
      emit("closeBtn");
    }
  } catch (error) {
    console.error("保存失败:", error);
  } finally {
  }
};
// 显示确认对话框
const showConfirmDialog = () => {
  ElMessageBox.confirm(
    '列表上传成功，需要点击下方【确认按钮】才可以将这些数据正式添加到系统中！',
    '信息提醒',
    {
      confirmButtonText: '确认',
      showCancelButton: false,
      type: 'success',
      closeOnClickModal: false,
      closeOnPressEscape: false,
    }
  ).then(() => {
  }).catch(() => {
    // This won't be triggered since there's no cancel button
  });
};
// 监听上传状态
watch(() => props.uoloadStatus, (newVal) => {
  if (newVal === 'successed') {
    showConfirmDialog();
  }
}, { immediate: true });



onMounted(() => {
  getList();
})
defineExpose({
  saveBtn,
});
</script>

<style scoped>
.stats-container {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  justify-content: center;
}

.stats-card {
  flex: 0 0 250px;
  display: flex;
  align-items: center;
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #eef2f5 100%);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.stats-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
}

.stats-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: rgba(64, 158, 255, 0.1);
  color: #409eff;
  font-size: 24px;
  margin-right: 15px;
}

.stats-content {
  flex: 1;
}

.stats-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 5px;
}

.stats-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  span{
    color: #c20000;
    font-size: 13px;
    padding-left: 4px;
  }
}
</style>