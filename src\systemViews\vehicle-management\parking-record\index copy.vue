<!-- 停车记录 -->
<template>
  <div class="container-table-box">
    <el-row :gutter="24">
      <el-col :span="24" :xs="24">
        <dialog-search
          @getList="getList"
          formRowNumber="4"
          :columns="tabelForm.columns"
        >
          <template #formList>
            <el-form
              :inline="true"
              :model="formData"
              size="medium"
              label-width="88px"
            >
              <!-- 表单元素保持不变 -->
              <el-form-item label="停车系统" prop="parkSystemId">
                <el-select
                  v-model="formData.parkSystemId"
                  placeholder="请选择停车系统"
                  @change="changeParkSystem"
                >
                  <el-option
                    v-for="system in parkSystemList"
                    :key="system.id"
                    :label="system.systemName"
                    :value="system.id"
                  />
                </el-select>
              </el-form-item>
              <!-- 其他表单项保持类似结构 -->
              <el-form-item label="停车场" prop="parkTableId">
              <el-select
                v-model="formData.parkTableId"
                placeholder="请选择停车场"
              >
                <el-option
                  v-for="changeReason in parkList"
                  :key="changeReason.id"
                  :label="changeReason.parkName"
                  :value="changeReason.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="出入场类型" prop="direction">
              <el-select
                v-model="formData.direction"
                placeholder="请选择类型"
                clearable
              >
                <el-option
                  v-for="currCapFlag in capFlagList"
                  :key="currCapFlag.id"
                  :label="currCapFlag.typeName"
                  :value="currCapFlag.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="车牌号" prop="dept">
              <el-input
                placeholder="请输入车牌号"
                v-model="formData.plateNo"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item label="出入场时间" prop="dept">
              <div class="formRangeTime">
                <el-date-picker
                  v-model="formData.startTime"
                  format="YYYY-MM-DD"
                  type="date"
                  value-format="YYYY-MM-DD 00:00:00"
                  placeholder="开始时间"
                ></el-date-picker>
                <span class="formRangeTime-tip">-</span>
                <el-date-picker
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD 23:59:59"
                  v-model="formData.endTime"
                  type="date"
                  placeholder="结束时间"
                >
                </el-date-picker>
              </div>
            </el-form-item>
            </el-form>

         
          </template>

          <template #searchList>
            <el-button
              class="search"
              type="primary"
              icon="Search"
              size="small"
              @click="handleQuery"
              >查询</el-button
            >
            <el-button
              class="reset"
              icon="Refresh"
              size="small"
              @click="resetQuery"
              >重置</el-button
            >
          </template>
        </dialog-search>

        <public-table
          ref="publictable"
          :rowKey="tabelForm.tableKey"
          :tableData="dataList"
          :columns="tabelForm.columns"
          :configFlag="tabelForm.tableConfig"
          :pageValue="pageParams"
          :total="total"
          :getList="getList"
        >
          <template #tableRowBtn="{ scope }">
            <el-switch
              v-model="scope.row.status"
              active-value="0"
              inactive-value="1"
              @change="handleStatusChange(scope.row)"
            />
          </template>

          <template #capSlot="{ scope }">
            <el-tag type="success" v-if="scope.row.dataType == 0">进场</el-tag>
            <el-tag type="success" v-else>出场</el-tag>
          </template>
        </public-table>
      </el-col>
    </el-row>
  </div>
</template>
  
  <script setup>
import { ref, reactive, onMounted } from "vue";
import { getParkingRecordList } from "@/api/park/record";
import { findPage } from "@/api/park/parkSystem";
import { parkingLotInfoList } from "@/api/park/management/parkingLotManagement";

// 响应式数据
const parkSystemList = ref([]);
const parkList = ref([]);
const dataList = ref([]);
const total = ref(0);
const publictable = ref(null);

// 表单数据
const formData = reactive({
  parkSystemId: "",
  parkTableId: "",
  direction: "",
  plateNo: "",
  startTime: "",
  endTime: "",
});

// 分页参数
const pageParams = reactive({
  pageNum: 1,
  pageSize: 10,
});
// 字典
const { proxy } = getCurrentInstance();
const { parking_type } = proxy.useDict("parking_type");
// 表格配置
const tabelForm = reactive({
  tableKey: "1",
  tableConfig: {
    needPage: true,
    index: true,
    selection: false,
    reserveSelection: false,
    indexFixed: false,
    selectionFixed: false,
    indexWidth: "50",
    loading: false,
    showSummary: false,
    height: null,
  },
  columns: [
    {
      fieldIndex: "plateNo",
      label: "车牌号",
      resizable: true,
      visible: true,
      sortable: true,
    },
    {
      fieldIndex: "gateName",
      label: "车辆进出场地点",
      visible: true,
    },
    {
      fieldIndex: "capFlag",
      label: "出入场类型",
      slotname: "capSlot",
      visible: true,
    },
    { fieldIndex: "passTime", label: "出入场时间", visible: true, },
    {
      fieldIndex: "parkingType",
      label: "车辆类型",
      visible: true,
      type: "dict",
      dictList: parking_type,
    },
  ],
});

// 初始化
const init = () => {
  getNowDate();
  getParkSystemList();
};

// 获取当前时间
const getNowDate = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, "0");
  const date = String(now.getDate()).padStart(2, "0");

  formData.startTime = `${year}-${month}-${date} 00:00:00`;
  formData.endTime = `${year}-${month}-${date} 23:59:59`;

};

// 获取停车系统列表
const getParkSystemList = async () => {
  try {
    const res = await findPage({ status: "0" });
    parkSystemList.value = res.rows;
    formData.parkSystemId = parkSystemList.value[0]?.id || "";
    getParkList();
  } catch (error) {
    console.error("获取停车系统失败:", error);
  }
};
// 停车系统
const changeParkSystem = () => {
  getParkList();
};
// 获取停车场列表
const getParkList = async () => {
  try {
    const res = await parkingLotInfoList({
      status: "1",
      parkSystemId: formData.parkSystemId,
    });
    parkList.value = res.rows;
    formData.parkTableId = parkList.value[0]?.id || "";
    getList();
  } catch (error) {
    console.error("获取停车场列表失败:", error);
  }
};

// 获取表格数据
const getList = async () => {
  try {
    tabelForm.tableConfig.loading = true;
    const res = await getParkingRecordList({
      ...formData,
      ...pageParams,
    });
    dataList.value = res.rows;
    total.value = res.total;
  } catch (error) {
    console.error("获取数据失败:", error);
  } finally {
    tabelForm.tableConfig.loading  = false;
  }
};

// 查询
const handleQuery = () => {
  pageParams.pageNum = 1;
  getList();
};

// 重置
const resetQuery = () => {
  Object.keys(formData).forEach((key) => {
    if (key !== "parkSystemId") formData[key] = "";
  });
  getNowDate();
  getParkSystemList();
};

// 状态变更处理
const handleStatusChange = (row) => {
  console.log("状态变更:", row);
};

// 生命周期钩子
onMounted(() => {
  init();
});
</script>
  
  <style scoped lang="scss">
.formRangeTime {
  display: flex;
  align-items: center;
  gap: 8px;

  &-tip {
    color: #999;
  }
}

:deep(.el-select) {
  width: 100%;
}
</style>