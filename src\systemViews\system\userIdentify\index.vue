<template>
  <div class="app-container">
    <Splitpanes class="default-theme">
      <Pane :size="15" :min-size="15">
        <el-card class="dep-card" style="height: 100%">
          <template #header>
            <div class="card-header">
              <span>组织</span>
              <el-button
                type="primary"
                style="float: right; padding: 3px 0"
                link
                icon="Refresh"
                @click="reloadTree"
                >刷新
              </el-button>
              <el-form
                style="margin-top: 20px; margin-bottom: -20px"
                v-if="userType === 'admin'"
              >
                <el-form-item label="租户：">
                  <el-select
                    v-model="queryParams.tenantId"
                    style="width: 120px"
                    remote
                    :remote-method="initTenantList"
                    :loading="getTenantLoading"
                    @change="handleTenantChange"
                  >
                    <el-option
                      v-for="item in tenantList"
                      :key="item.tenantId"
                      :label="item.tenantName"
                      :value="item.tenantId"
                    />
                  </el-select>
                </el-form-item>
              </el-form>
            </div>
          </template>
          <el-input
            placeholder="输入名称进行查询"
            v-model="filterText"
            clearable
            style="margin-bottom: 8px"
          >
            <template #append>
              <el-button
                icon="search"
                @click="searchFiler(filterText)"
              ></el-button>
            </template>
          </el-input>
          <treeLoad
            ref="asyncTree"
            :key="treeFatherIndex"
            :isShowSearch="false"
            :idDefaultExpandAll="false"
            :defaultProps="defaultProps"
            :treeBtnEdit="false"
            @loadFirstNodeFather="loadNodeRooter"
            @loadChildNodeFather="loadNode"
            :treeBtnDelete="false"
            tree-name="orgName"
            nodeKey="orgId"
            :treeData="treeData"
            :defaultSelectedKey="defaultSelectedKey"
            :defaultExpandedKeys="[defaultOrgId]"
            :isLazy="isLazy"
            :treeindex="treeindex"
            @checkedKeys="handleNodeClick"
            @editNodes="editNodesEdit"
          />
        </el-card>
      </Pane>
      <Pane :size="85" :min-size="65">
        <el-card class="dep-card" style="height: 100%">
          <template #header>
            <div class="card-header">
              <span>{{ selectNodeName }}</span>
              <el-button
                type="primary"
                style="float: right; padding: 3px 0"
                link
                icon="Refresh"
                @click="allUser"
              >
                全部人员
              </el-button>
            </div>
          </template>

          <!-- 搜索表单 -->

          <dialog-search
            @getList="getList"
            formRowNumber="4"
            :columns="columns"
          >
            <template v-slot:formList>
              <el-form
                :model="queryParams"
                ref="queryForm"
                :inline="true"
                v-show="showSearch"
                label-width="68px"
              >
                <el-form-item label="人员姓名" prop="staffName">
                  <el-input
                    v-model="queryParams.staffName"
                    placeholder="请输入人员姓名"
                    clearable
                    style="width: 240px"
                    @keyup.enter.native="handleQuery"
                  />
                </el-form-item>
                <el-form-item label="手机号" prop="cellphone">
                  <el-input
                    v-model="queryParams.cellphone"
                    placeholder="请输入手机号"
                    clearable
                    style="width: 240px"
                    @keyup.enter.native="handleQuery"
                  />
                </el-form-item>
                <!-- <el-form-item label="邮箱" prop="email">
              <el-input
                v-model="queryParams.email"
                placeholder="请输入邮箱"
                clearable
                style="width: 240px"
                @keyup.enter.native="handleQuery"
              />
            </el-form-item> -->
                <el-form-item label="用户类型" prop="staffType">
                  <el-select v-model="queryParams.staffType" clearable>
                    <el-option
                      v-for="item in staff_type"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="组织名称" prop="orgName">
                  <el-input
                    v-model="queryParams.orgName"
                    placeholder="请输入组织名称"
                    clearable
                    style="width: 240px"
                    @keyup.enter.native="handleQuery"
                  />
                </el-form-item>
              </el-form>
            </template>
            <template v-slot:searchList>
              <el-button type="primary" icon="Search" @click="handleQuery"
                >搜索</el-button
              >
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </template>
          </dialog-search>

          <!-- 用户信息表格 -->
          <el-table v-loading="loading" :data="userList">
            <el-table-column
              label="用户编号"
              align="left"
              key="staffId"
              prop="staffId"
              width="180"
              :show-overflow-tooltip="true"
              v-if="columns[0].visible"
            />
            <el-table-column
              label="组织"
              align="left"
              key="orgName"
              prop="orgName"
              v-if="columns[1].visible"
              :show-overflow-tooltip="true"
              width="180"
            />
            <el-table-column
              label="岗位类型"
              align="center"
              key="staffOrgType"
              prop="staffOrgType"
              v-if="columns[2].visible"
            >
              <template #default="scope">
                <dict-tag
                  :options="staff_org_type"
                  :value="scope.row.staffOrgType"
                />
              </template>
            </el-table-column>
            <el-table-column
              label="人员姓名"
              align="left"
                width="120"
              key="staffName"
              prop="staffName"
              v-if="columns[3].visible"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              label="登录名称"
              align="left"
              key="loginName"
              width="120"
              prop="loginName"
              v-if="columns[4].visible"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              label="用户类型"
              align="center"
              key="staffType"
              prop="staffType"
              v-if="columns[5].visible"
            >
              <template #default="scope">
                <dict-tag :options="staff_type" :value="scope.row.staffType" />
              </template>
            </el-table-column>
            <el-table-column
              label="用户状态"
              align="center"
              key="staffStatus"
              prop="staffStatus"
              v-if="columns[6].visible"
              width="120"
            >
              <template #default="scope">
                <dict-tag
                  :options="user_status"
                  :value="scope.row.staffStatus"
                />
              </template>
            </el-table-column>
            <el-table-column
              label="手机号码"
              align="left"
              key="cellphone"
              prop="cellphone"
              v-if="columns[7].visible"
              width="120"
            />
            <el-table-column
              label="身份证号"
              align="center"
              key="employeeCode"
              prop="employeeCode"
              v-if="columns[8].visible"
              width="150"
            />
            <!-- <el-table-column
              label="邮箱"
              align="left"
              key="email"
              prop="email"
              v-if="columns[9].visible"
              width="210"
            /> -->
            <el-table-column
              label="人脸权限"
              align="center"
              key="faceStatus"
              prop="faceStatus"
                 fixed="right"
              v-if="columns[9].visible"
              width="120"
            >
              <template #default="scope">
                <el-switch
                  active-text="启用"
                  inactive-text="禁用"
                  inline-prompt
                  v-model="scope.row.faceStatus"
                  active-color="#13ce66"
                  inactive-color="#ff4949"
                  :active-value="'0'"
                  :inactive-value="'1'"
                  :before-change="() => changeFaceStatus(scope.row)"
                />
              </template>
            </el-table-column>
            <el-table-column label="人脸图像" width="180" align="center">
              <template #default="scope">
                <el-button icon="Picture" @click="showFace(scope.row)"
                  >查看人脸</el-button
                >
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              align="center"
              fixed="right"
              width="100"
              class-name="small-padding fixed-width"
            >
              <template #default="scope">
                <el-button
                  link
                  icon="Picture"
                  type="primary"
                  :loading="getFaceLoading && scope.row.staffId === loadUserId"
                  @click="handleFace(scope.row)"
                  v-hasPermi="['sys:base:userFace:select']"
                  title="新增人脸"
                ></el-button>
                <el-button
                  link
                  icon="Delete"
                  type="primary"
                  @click="handleDelete(scope.row)"
                  v-hasPermi="['sys:base:userFace:delete']"
                  title="删除"
                ></el-button>
              </template>
            </el-table-column>
          </el-table>

          <!--分页 -->
          <pagination
            v-show="total > 0"
            :total="total"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="getList"
          />
        </el-card>
      </Pane>
    </Splitpanes>

    <!-- 新增人脸对话框 -->
    <el-dialog :title="title" v-model="open" width="1000px" append-to-body>
      <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="人员姓名" prop="staffName">
              {{ form.staffName }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="归属组织" prop="orgName">
              {{ form.orgName }}
            </el-form-item>
            <el-form-item v-show="false">
              <el-input v-model="form.orgId"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="登录名称" prop="loginName">
              {{ form.loginName }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用户类型" prop="staffType">
              {{ formatCommon(form.staffType, staff_type) }}
            </el-form-item>
          </el-col>
          <!-- <el-col :span="12">
                <el-form-item label="手机号码" prop="cellphone">
                  {{ form.cellphone }}
                </el-form-item>
              </el-col> -->
          <el-col :span="24">
            <el-form-item label="上传图像" prop="faceImage">
              <!-- 自定义图片上传组件 -->
              <ImageUpload
                v-model="fileList"
                limit="1"
                fileSize="10"
                :paramsData="imageExtraData"
                :uploadImgUrl="apiUrls + '/user/staffFace/uploadFile'"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <!-- 用户信息表格 -->
      <el-table v-loading="loading" :data="faceList">
        <el-table-column
          label="序号"
          align="left"
          width="100"
          :show-overflow-tooltip="true"
          type="index"
          :index="indexMethod"
        />
        <el-table-column
          label="人员id"
          align="left"
          key="staffId"
          prop="staffId"
          width="180"
          :show-overflow-tooltip="false"
          v-if="faceColumns[0].visible"
        />
        <el-table-column
          label="人脸编码"
          align="left"
          key="faceCode"
          prop="faceCode"
          v-if="columns[1].visible"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="人脸照片"
          align="center"
          key="faceImage"
          prop="faceImage"
          v-if="columns[2].visible"
          :show-overflow-tooltip="true"
        >
          <template #default="scope">
            <PreviewImage
              style="width: 30px; height: 30px"
              :photo-id="scope.row.faceImage"
              v-if="scope.row.faceImage"
            ></PreviewImage>
          </template>
        </el-table-column>
        <el-table-column
          label="状态"
          align="center"
          key="status"
          prop="status"
          v-if="columns[3].visible"
        >
          <template #default="scope">
            <dict-tag :options="effective_status" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column
          label="创建时间"
          align="left"
          key="createDate"
          prop="createDate"
          v-if="columns[4].visible"
        />
        <!-- <el-table-column
              label="身份证号"
              align="left"
              key="employeeCode"
              prop="employeeCode"
              v-if="columns[7].visible"
              width="180"
            /> -->
        <!-- <el-table-column
              label="邮箱"
              align="left"
              key="email"
              prop="email"
              v-if="columns[8].visible"
              width="210"
            /> -->
      </el-table>

      <!--分页 -->
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryFaceParams.pageNum"
        v-model:limit="queryFaceParams.pageSize"
        @pagination="getFaceList"
      />
      <div
        slot="footer"
        class="dialog-footer"
        style="text-align: right; margin-top: 30px"
      >
        <el-button type="primary" @click="submitForm" :loading="saveLoading"
          >确 定</el-button
        >
        <el-button @click="cancel('edit')">取 消</el-button>
      </div>
    </el-dialog>

    <!--查看人脸图片-->
    <!-- <el-dialog title="人脸图片" v-model="faceImageDialog" width="30%">
      <face
        style="width: 100%; height: 200px"
        :rowData="rowData"
        :photo-id="faceImage"
      ></face>
      <el-empty v-else description="暂无上传人脸信息"></el-empty>
    </el-dialog> -->

    <DialogBox
      :visible="faceImageDialog"
       dialogWidth="30%"
      :dialogFooterBtn="false"
      @close="close"
      dialogTop='15vh'
      dialogTitle="人脸图片"
    >
      <template #content>
        <faceInfo
        style="width: 100%; height: 200px"
        :rowData="rowData"
        :photo-id="faceImage"
      ></faceInfo>
      </template>
    </DialogBox>
  </div>
</template>

<script setup name="UserIdentify">
import treeLoad from "@/components/Tree/treeLoad";
import useUserStore from "@/store/modules/user";
import { ref } from "vue";
import { getTenants } from "@/api/tenant/tenant";
import {
  getUUId,
  listStaffFace,
  addStaffFace,
  deleteFaceImage,
  listIdentify,
  getFaceImage,
  updateStaffIdentify,
} from "@/api/system/userIdentify";
import { getDept, treeselect,getOrgSidebar } from "@/api/system/dept";
import { ElMessageBox } from "element-plus";
import { findRoleListByScope, findByOrgName } from "@/api/system/role";
import { appCode } from "@/utils/config";
import { apiUrl } from "@/utils/config";
const isLazy = ref(true);
const treeData = ref([]);
const treeindex = ref(0);
const treeFatherIndex = ref(0);
/** 树结构数据默认 */
const defaultProps = reactive({
  children: "children",
  label: "orgName",
  isLeaf: (data) => !data.isParent,
});

const { proxy } = getCurrentInstance();
const {
  staff_kind,
  staff_type,
  user_status,
  sys_user_sex,
  staff_org_type,
  effective_status,
} = proxy.useDict(
  "staff_kind",
  "staff_type",
  "user_status",
  "sys_user_sex",
  "staff_org_type",
  "effective_status"
);

const userStore = useUserStore();
const userType = userStore.userInfo.customParam.userType;
let defaultOrgId = userStore.userInfo.orgId;
const defaultSelectedKey = ref();
const getFaceLoading = ref(false);
const getTenantLoading = ref(false);
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  staffType: undefined,
  email: undefined,
  cellphone: undefined,
  orgId: "",
  staffOrgType: "F",
  tenantId: userStore.userInfo.tenantId,
});
//const isShowFile = ref(true);
const treeLoading = ref(false);
const selectNode = ref(undefined);
const loadUserId = ref(undefined);
const selectNodeName = ref("全部人员");
const tenantList = ref([]);
const userList = ref([]);
const form = ref({
  staffStatus: "valid",
  sex: "male",
});
// const positionForm = ref({
//   orgId: "",
//   orgName: "",
//   staffId: "",
//   staffName: "",
//   staffOrgType: "T",
// });
const columns = ref([
  { key: 0, label: `用户编号`, visible: false },
  { key: 1, label: `组织`, visible: true },
  { key: 2, label: `岗位类型`, visible: true },
  { key: 3, label: `人员姓名`, visible: true },
  { key: 4, label: `登录名称`, visible: true },
  { key: 5, label: `用户类型`, visible: true },
  { key: 6, label: `用户状态`, visible: true },
  { key: 7, label: `手机号码`, visible: true },
  { key: 8, label: `身份证号`, visible: true },
  { key: 9, label: `人脸权限`, visible: true },
]);
const queryFaceParams = ref({
  pageNum: 1,
  pageSize: 10,
});
const faceList = ref([]);
const faceColumns = ref([
  { key: 0, label: `序号`, visible: false },
  { key: 1, label: `人脸编码`, visible: true },
  { key: 2, label: `人脸照片`, visible: true },
  { key: 3, label: `状态`, visible: true },
  { key: 4, label: `创建时间`, visible: true },
]);
// 遮罩层
const loading = ref(true);
const total = ref(0);
// 显示搜索条件
const showSearch = ref(true);
const open = ref(false);
const title = ref("");
const rules = {
  faceImage: [{ required: true, message: "请上传人脸图像", trigger: "blur" }],
};
const indexMethod = (index) => {
  return index + 1;
};
// 文件列表
const fileList = ref([]);
// 图片预览列表
const srcList = ref([]);
const imageExtraData = ref({
  // 图片上传额外参数
  appCode: appCode,
});
const apiUrls = ref(apiUrl);
const faceImageDialog = ref(false);
const faceImage = ref("");
const rowData = ref({});
const changeFaceStatus = (row) => {
  return new Promise((resolve) => {
    const newFaceStatus = row.faceStatus === "1" ? "0" : "1"; // 切换值

    ElMessageBox.confirm(
      `确认要将人脸权限修改为"${newFaceStatus === "0" ? "启用" : "禁用"}"吗？`,
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }
    )
      .then(() => {
        // 更新 faceStatus
        const updatedRow = { id: row.id, faceStatus: newFaceStatus };
        updateStaffIdentify(updatedRow).then((response) => {
          if (!response.success) {
            proxy.$modal.msgError(response.message);
            // 返回 false 阻止切换
            resolve(false);
          } else {
            proxy.$modal.msgSuccess("修改成功");
            // 返回 true 允许切换
            resolve(true);
          }
        });
      })
      .catch(() => {
        // 返回 false 阻止切换
        resolve(false);
      });
  });
};
// 字典通用格式化
const formatCommon = (value, dict) => {
  return dict.find((item) => item.value === value)?.label || "";
};

// 刷新加载树形结构的组织
function reloadTree() {
  if (filterText.value) {
    isLazy.value = false;
    findByOrgNames();
  } else {
    isLazy.value = true;
    if (selectNode.value) {
      proxy.$refs.asyncTree.reloadTree();
    } else {
      treeindex.value++;
    }
  }
}

const filterText = ref("");
const isSearchFirst = ref(false);
const searchFiler = () => {
  isSearchFirst.value = true;

  if (filterText.value) {
    isLazy.value = false;
    findByOrgNames();
  } else {
    treeData.value = [];
    isLazy.value = true;

    if (selectNode.value) {
      proxy.$refs.asyncTree.reloadTree();
    } else {
      treeindex.value++;
    }
  }
};

//懒加载树形结构的组织
function loadNode(node, resolve) {
  treeLoading.value = true;
  if (node.level === 0) {
    getOrgSidebar({
      orgId: defaultOrgId,
      queryType: "current",
      tenantId: queryParams.value.tenantId,
      orgName: filterText.value,
    }).then((response) => {
      resolve(response.data);
      treeLoading.value = false;
    });
  } else {
    getOrgSidebar({
      orgId: node.data.orgId,
      queryType: "down",
      tenantId: queryParams.value.tenantId,
      orgName: isSearchFirst.value ? filterText.value : "",
    }).then((response) => {
      resolve(response.data);
      treeLoading.value = false;
      isSearchFirst.value = false;
    });
  }
}

//懒加载树形结构的组织
function loadNodeRooter(resolve) {
  treeLoading.value = true;
  getOrgSidebar({
    orgId: defaultOrgId,
    queryType: "current",
    tenantId: queryParams.value.tenantId,
    orgName: filterText.value,
  }).then((response) => {
    resolve(response.data);
    treeLoading.value = false;
  });
}
function findByOrgNames() {
  treeLoading.value = true;
  findByOrgName({
    tenantId: queryParams.value.tenantId,
    orgName: filterText.value,
  }).then((response) => {
    treeLoading.value = false;
    treeData.value = response.data;
  });
}

// 节点单击事件
function handleNodeClick(data) {
  queryParams.value.orgId = data.orgId;
  selectNode.value = data;
  selectNodeName.value = data.orgName;
  queryParams.value.orgName = data.orgName;
  getList();
}

// 获取租户列表
function initTenantList(tenantName) {
  getTenantLoading.value = true;
  let query = {};
  if (tenantName !== undefined && tenantName !== "") {
    query.tenantName = tenantName;
    query.tenantId = undefined;
  } else {
    query.tenantId = queryParams.value.tenantId;
  }
  getTenants(query)
    .then((response) => {
      tenantList.value = response.data;
    })
    .finally(() => {
      getTenantLoading.value = false;
    });
}

// 下拉框切换租户回调
function handleTenantChange(tenantId) {
  if (tenantId !== "") {
    const tenantObj = tenantList.value.find(
      (item) => item.tenantId === tenantId
    );
    queryParams.value.tenantName = tenantObj.tenantName;
    queryParams.value.tenantAdminId = tenantObj.tenantAdminId;
  }
  proxy.$refs.asyncTree.root.loaded = false;
  proxy.$refs.asyncTree.root.expand();
  selectNode.value = undefined;
  queryParams.value.orgId = undefined;
  handleQuery();
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList("noFirst");
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryForm");
  queryParams.value.pageNum = 1;
  selectNodeName.value = "";
  filterText.value = "";
  selectNode.value = "";
  queryParams.value.orgId = "";
  selectNodeName.value = "全部人员";
  treeData.value = [];
  isLazy.value = true;
  treeFatherIndex.value++;
  getList();
}

/** 查询用户列表 */
function getList(type) {
  loading.value = true;
  listIdentify(queryParams.value).then((response) => {
    userList.value = response.data.records;
    total.value = response.data.total;
    loading.value = false;

    if (userList.value.length > 0 && type) {
      selectNodeName.value = userList.value[0].orgName;
    }
  });
}

// 获取全部人员
function allUser() {
  resetQuery();
}

// 新增人脸按钮操作
function handleFace(row) {
  reset();
  loadUserId.value = row.staffId;
  getFaceLoading.value = true;
  let params = {
    identifyId: row.id,
  };
  form.value = {
    ...form.value,
    identifyId: row.id,
    staffName: row.staffName,
    orgName: row.orgName,
    loginName: row.loginName,
    cellphone: row.cellphone,
    staffType: row.staffType,
  };
  fileList.value = {};
  listStaffFace(params)
    .then((response) => {
      if (response.success) {
        faceList.value = response.data.records;
        total.value = response.data.total;
        loading.value = false;
        getFaceLoading.value = false;
        open.value = true;
        title.value = "新增人脸图像";
      }
    })
    .catch((e) => {
      //operateType.value = "";
      getFaceLoading.value = false;
      loadUserId.value = undefined;
      proxy.$modal.msgError("数据异常！");
    });
}

// 删除按钮操作
function handleDelete(row) {
  const id = row.id;
  proxy.$modal
    .confirm('是否确认删除用户"' + row.staffName + '"的人脸数据?')
    .then(function () {
      let params = {
        id: id,
      };
      return deleteFaceImage(params);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    });
}

// 表单重置
function reset() {
  form.value = {
    staffId: undefined,
    orgId: undefined,
    staffName: undefined,
    cellphone: undefined,
    email: undefined,
    staffStatus: "valid",
    orgName: "",
    tenantName: undefined,
    sex: "male",
    faceImage: "",
  };
  proxy.resetForm("formRef");
}

const saveLoading = ref(false);

// 提交人员信息
function submitForm() {
  // 获取文件id
  if (fileList.value.length > 0) {
    form.value.faceImage = fileList.value[0].fileId;
  } else {
    form.value.faceImage = "";
  }
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      saveLoading.value = true;
      // 设置参数
      let params = {
        identifyId: form.value.identifyId,
        faceImage: form.value.faceImage,
      };
      addStaffFace(params).then((response) => {
        saveLoading.value = false;
        if (response.success) {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        } else {
          // proxy.$modal.msgError(response.message);
        }
      });
    }
  });
}

// 新增人员取消按钮
function cancel(type) {
  if (type === "edit") {
    open.value = false;
  }
  reset();
}

// 显示人脸图片
function showFace(row) {
  let params = {
    id: row.id,
  };
  rowData.value = row;
  getFaceImage(params)
    .then((response) => {
      if (response.success) {
        faceImage.value = response.data !== null ? response.data.faceImage : "";
        faceImageDialog.value = true;
      }
    })
    .catch((e) => {
      getFaceLoading.value = false;
      loadUserId.value = undefined;
      proxy.$modal.msgError("数据异常！");
    });
}

/** 关闭弹窗 */
const close = (val) => {
  faceImageDialog.value = val;
};

initTenantList();
getList();
</script>

<style scoped>
.dep-card {
  min-height: calc(100vh - 120px);
}
.error-msg {
  color: #f56c6c;
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
}
</style>
