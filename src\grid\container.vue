<template>
    <div class="main-panel">
        <grid-layout ref="mainDom" :style="gridStyle()" v-model:layout="props.layout" :col-num="12"
            :row-height="props.size.height" :is-draggable="false" :is-resizable="false" :is-mirrored="false"
            :vertical-compact="true" :margin="[props.size.margin, props.size.margin]" :use-css-transforms="false">
            <grid-item class="grid-item" v-for="item in props.layout" :x="item.x" :y="item.y" :w="item.w" :h="item.h"
                :i="item.i" :key="item.i" static>
                <component v-if="item.comp" :is="Components[item.comp]" v-bind="item.props" />
            </grid-item>
        </grid-layout>
    </div>
</template>

<script setup>
import { ref, defineAsyncComponent } from "vue";
import { GridLayout, GridItem } from 'vue3-grid-layout'
import { CompsConfig, Modules } from "@/gridViews/config"
import { defaultSize } from "./config"

const props = defineProps({
    layout: {
        type: Object,
        default: [],
    },
    size: {
        type: Object,
        default: defaultSize,
    }
})

const mainDom = ref(null)

const Components = {}

for (const key in CompsConfig) {
    if (Object.hasOwnProperty.call(CompsConfig, key)) {
        if (Modules[key]) {
            Components[key] = defineAsyncComponent(() =>
                Modules[key]()
            )
        }
    }
}

function gridStyle() {
    if (props.size.width === '100%') {
        return {
            width: '100%'
        }
    }
    return {
        width: '100%',
        maxWidth: props.size.width
    }
}

</script>

<style scoped lang="scss">
.main-panel {
    position: relative;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    min-height: calc(100vh - 94px);
}
</style>
