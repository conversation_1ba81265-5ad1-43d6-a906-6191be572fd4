import request from "@/utils/request";

export function getServiceHealthStatus() {
  return request({
    url: "/user/commandCenter/getBasicServicesHealthStatus",
    method: "get",
  });
}
export function getBasicServicesHealthStatusList(serviceName) {
  return request({
    url: "/user/commandCenter/getBasicServicesHealthStatusList",
    method: "get",
    param: serviceName,
  });
}

export function getDatabaseStatus() {
  return request({
    url: "/user/commandCenter/getDatabaseStatus",
    method: "get"
  });
}

export function getRedisStatus() {
  return request({
    url: "/user/commandCenter/getRedisStatus",
    method: "get"
  });
}

export function getKafkaStatus() {
  return request({
    url: "/user/commandCenter/getKafkaStatus",
    method: "get"
  });
}

export function getElasticsearchStatus() {
  return request({
    url: "/user/commandCenter/getElasticsearchStatus",
    method: "get"
  });
}

export function getOSSStatus() {
  return request({
    url: "/user/commandCenter/getOSSStatus",
    method: "get"
  });
}


// 根据人员id查询详细
export function queryDetail(data) {
  return request({
    url: "/user/staffsIdentify/queryDetail",
    method: "post",
    data
  });
}

