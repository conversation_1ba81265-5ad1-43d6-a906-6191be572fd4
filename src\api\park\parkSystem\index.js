import request from '@/utils/request'



// 停车系统
export function findPage(query, params) {
  return request({
    url: '/parking/system/findPage',
    method: 'post',
    data: query,
    params: params

  })
}

// 删除停车系统
export function deleteSystem(query) {
  return request({
    url: '/parking/system/deleteSystem',
    method: 'post',
    data: query,
  })
}
// 新增或修改停车系统
export function addSystem(query) {
  return request({
    url: '/parking/system/addSystem',
    method: 'post',
    data: query,
  })
}
