<!-- 到访记录 -->

<template>
  <div class="app-container">
    <el-card>
      <dialog-search
        @getList="getList"
        formRowNumber="4"
        :columns="tabelForm.columns"
      >
        <template v-slot:formList>
          <el-form
            :model="queryParams"
            ref="queryForm"
            :inline="true"
            label-width="80px"
          >
            <el-form-item label="访客姓名" prop="visitorName">
              <el-input
                v-model="queryParams.visitorName"
                placeholder="请输入访客姓名"
                clearable
              />
            </el-form-item>

            <el-form-item label="访客电话" prop="visitorTelephone">
              <el-input
                v-model="queryParams.visitorTelephone"
                placeholder="请输入访客电话"
                clearable
              />
            </el-form-item>
            <el-form-item label="访问时间" prop="formRangeTime">
              <el-date-picker
                v-model="selectCreateTimes"
                type="datetimerange"
                value-format="YYYY-MM-DD"
                @change="selectCreateTimesChange"
                value="YYYY-MM-DD"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              >
              </el-date-picker>
             
            </el-form-item>

            <el-form-item label="访客单位" prop="unit">
              <el-input
                v-model="queryParams.unit"
                placeholder="请输入访客单位"
                clearable
              />
            </el-form-item>
            <el-form-item label="被访人" prop="inviter">
              <el-input
                v-model="queryParams.inviter"
                placeholder="请输入被访人"
                clearable
              />
            </el-form-item>
            <el-form-item label="访问天数" prop="days">
              <el-input
                v-model="queryParams.days"
                placeholder="请输入访问天数"
                oninput="value=value.replace(/[^0-9]/g,'')"
                clearable
              />
            </el-form-item>
          </el-form>
        </template>
        <template v-slot:searchList>
          <el-button type="primary" icon="Search" @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </template>

        
        <template v-slot:searchBtnList>
         <el-button
                         type="primary"
                         icon="Download"
                         @click="handleExport"
                         >导出
                       </el-button>
          </template>


      </dialog-search>

      <public-table
        ref="publictable"
        :rowKey="tabelForm.tableKey"
        :tableData="userList"
        :columns="tabelForm.columns"
        :configFlag="tabelForm.tableConfig"
        :pageValue="queryParams"
        :total="total"
        :getList="getList"
      >
      </public-table>

    </el-card>
  </div>
</template>
  
  <script setup>
import { delById, findVisitorInvitationListAll } from '@/api/visitor/visitorinvite/index'
import { getConfigInfo } from '@/api/visitor/visitorParameterConfig/index'
import { formatMinuteTime } from "@/utils/index";
import { ref, reactive } from "vue";
const userList = ref([]);
const publictable = ref(null);
const selectCreateTimes = ref([]);

const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
});
const total = ref(0);

// 字典
const { proxy } = getCurrentInstance();
const { visit_reason,visitor_status,visitor_access_status } = proxy.useDict("visit_reason","visitor_status","visitor_access_status");

const selectCreateTimesChange = (values)=>{
    queryParams.value.visitorTime = values[0]
    queryParams.value.visitorEndTime = values[1]
}

const tabelForm = reactive({
  tableKey: "1", //表格key值
  isShowRightToolbar: true, //是否显示右侧显示和隐藏
  showSearch: true,
  // 表格表格数据
  columns: [
  {
            fieldIndex: "visitorName", // 对应列内容的字段名
            label: "访客姓名", // 显示的标题
            visible: true, // 展示与隐藏
            fixed: "", //固定
            minWidth: "120px", //最小宽度%
            align: "", //表格对齐方式
          },
          {
            label: "访客电话",
            minWidth: "120px", //最小宽度%
            visible: true,
            type:'phoneHidden'
          },
          {
            label: "证件号码",
            visible: true,
            minWidth: "140px", //最小宽度%
             type:'idNumberHidden'
          },
          {
            fieldIndex: "unit", // 对应列内容的字段名
            label: "访客单位", // 显示的标题
            visible: true, // 展示与隐藏
            minWidth: "120px", //最小宽度%
            align: "left", //表格对齐方式
          },

          {
            fieldIndex: "inviter", // 对应列内容的字段名
            label: "被访人", // 显示的标题
            visible: true, // 展示与隐藏
            minWidth: "120px", //最小宽度%
            align: "left", //表格对齐方式
          },
          {
            label: "被访人电话",
            minWidth: "120px", //最小宽度%
            align: "center", //表格对齐方式
            visible: true,
            type:'phoneHidden'
          },
          {
            fieldIndex: "visitorTime", // 对应列内容的字段名
            label: "预计到访开始时间", // 显示的标题
            visible: true, // 展示与隐藏
            minWidth: "160px", //最小宽度%
            align: "center", //表格对齐方式
          },
          {
            fieldIndex: "visitorEndTime", // 对应列内容的字段名
            label: "预计结束到访时间", // 显示的标题
            visible: true, // 展示与隐藏
            minWidth: "160px", //最小宽度%
            align: "center", //表格对齐方式
          },
          {
            fieldIndex: "actualVisitorTime", // 对应列内容的字段名
            label: "实际到访开始时间", // 显示的标题
            visible: true, // 展示与隐藏
            minWidth: "160px", //最小宽度%
            align: "center", //表格对齐方式
          },
          {
            fieldIndex: "actualVisitorEndTime", // 对应列内容的字段名
            label: "实际到访结束时间", // 显示的标题
            visible: true, // 展示与隐藏
            minWidth: "160px", //最小宽度%
            align: "center", //表格对齐方式
          },
          {
            fieldIndex: "duration", // 对应列内容的字段名
            label: "访问天数", // 显示的标题
            visible: true, // 展示与隐藏
            minWidth: "120px", //最小宽度%
            align: "center", //表格对齐方式
          },
          {
            fieldIndex: "reason", // 对应列内容的字段名
            label: "来访事由", // 显示的标题
            visible: true, // 展示与隐藏
            minWidth: "120px", //最小宽度%
            width: "", //宽度
            align: "left", //表格对齐方式
            type: "dict",
            dictList: visit_reason,
          },
          {
            fieldIndex: "processStatus", // 对应列内容的字段名
            label: "状态", // 显示的标题
            visible: true, // 展示与隐藏
            minWidth: "120px", //最小宽度%
            align: "left", //表格对齐方式
            type: "dict",
            dictList: visitor_status
          },
          {
            fieldIndex: "accessStatus", // 对应列内容的字段名
            label: "门禁权限", // 显示的标题
            visible: true, // 展示与隐藏
            minWidth: "120px", //最小宽度%
            align: "left", //表格对齐方式
            type: "dict",
            dictList: visitor_access_status,
          },
  ],
  // 表格基础配置项，看个人需求添加
  tableConfig: {
    needPage: true, // 是否需要分页
    index: true, // 是否需要序号
    selection: false, // 是否需要多选
    reserveSelection: false, // 是否需要支持跨页选择
    indexFixed: false, // 序号列固定 left true right
    selectionFixed: false, //多选列固定left true right
    indexWidth: "50", //序号列宽度
    loading: false, //表格loading
    showSummary: false, //是否开启合计
    height: null, //表格固定高度 比如：300px
  },
});
// 获取配置的方法
const getConfig = async () => {
  try {
    const res = await getConfigInfo();
    const isIdC = res.rows[2].isIdcard;

    // 更新响应式变量
    isIdCard.value = isIdC === "0";

    // 等待DOM更新后执行
    await nextTick();
    // 操作响应式对象
    tabelForm.columns[2].visible = isIdCard.value;
  } catch (error) {
    console.error("获取配置失败:", error);
  }
};
/** 查询用户列表 */
const getList = () => {
  tabelForm.tableConfig.loading = true;
  findVisitorInvitationListAll(queryParams.value, queryParams.value).then((response) => {
    userList.value = response.data.records;
    total.value = response.data.total;
    tabelForm.tableConfig.loading = false;
  });
};
// 删除
const handleDelete = (row) => {
  ElMessageBox.confirm("确认删除该条访客信息？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      delById(row.id).then((res) => {
        if (res.code == "1") {
          ElMessage.success("删除成功");
          getList();
        }
      });
    })
    .catch(() => {
      // 用户取消删除
    });
};/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};
/** 重置按钮操作 */
const resetQuery = () => {
  queryParams.value = {};
  selectCreateTimes.value = []
  proxy.resetForm("queryForm");
  handleQuery();
};
/** 导出按钮操作 */
const handleExport = () => {
  proxy.download(
    `park${apiUrl}/parking/accredit/exportAllAccredit`,
    {
      ...queryParams.value,
    },
   `到访记录_${formatMinuteTime(new Date())}.xlsx`
  );
};

onMounted(() => {
  getList();
  getConfig()
});
</script>
  
  <style lang="scss" scoped>
.el-card {
  min-height: calc(100vh - 110px);
}
</style>