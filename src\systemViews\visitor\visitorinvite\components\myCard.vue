<template>
    <div class="el-common-container">
      <div class="receive-voucher">
        <div class="receive-voucher-box">
          <div class="receive-voucher-center">
            <div class="receive-center-qr">
              <img class="qrImg" :src="qrBase64" v-if="qrBase64">
            </div>
            <div class="receive-center-qr">
              <el-row>
                <el-col :span="24">
                  <el-radio-group v-model="formData.qrType" @change="qrTypeChange">
                    <el-radio-button :label="0">长期</el-radio-button>
                    <el-radio-button :label="1">短期</el-radio-button>
                  </el-radio-group>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-button icon="Refresh" @click="resetQrCode">
                    重置
                  </el-button>
                  <el-button type="primary" icon="Download"  @click="downloadQrCode">
                    保存
                  </el-button>
                </el-col>
              </el-row>
            </div>
          </div>
        </div>
      </div>
    </div>
  </template>
  
  <script setup>
  import { ref, reactive, onMounted } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { myVisitorInvite } from '@/api/visitor/visitorinvite/components/iewm.js'
  

  
  const qrBase64 = ref("")
  const formData = reactive({
    qrType: 0,
    resetType: 0
  })
  
  // 初始化获取二维码
  onMounted(() => {
    fetchQrCode()
  })
  
  // 获取二维码
  const fetchQrCode = async () => {
    try {
      const res = await myVisitorInvite(formData)
      if (!res) {
        ElMessage.error("生成名片失败")
        return
      }
      qrBase64.value = res.inviterQrCodeImg
      ElMessage.success("生成名片成功")
      formData.resetType = 0
    } catch (error) {
      console.error("二维码获取失败:", error)
      ElMessage.error("二维码生成失败")
    }
  }
  
  // 切换类型
  const qrTypeChange = () => {
    fetchQrCode()
  }
  
  // 重置二维码
  const resetQrCode = async () => {
    try {
      const message = formData.qrType === 0 
        ? "重置“长期”名片，已分享名片将会立即失效，请确认是否重置？" 
        : "重置“短期”名片，已分享名片将会立即失效，请确认是否重置？"
      
      await ElMessageBox.confirm(message, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      
      formData.resetType = 1
      await fetchQrCode()
    } catch (cancel) {
      // 用户取消操作
    }
  }
  
  // 下载二维码
  const downloadQrCode = () => {
    if (!qrBase64.value) {
      ElMessage.warning("请先生成二维码")
      return
    }
    
    const downloadLink = document.createElement('a')
    downloadLink.href = qrBase64.value
    downloadLink.download = '访客名片.png'
    document.body.appendChild(downloadLink)
    downloadLink.click()
    document.body.removeChild(downloadLink)
  }
  </script>
  
  <style scoped lang="scss">
  
  .receive-voucher {
    height: 100%;
    background-color: #F6F7F8;
    &-box {
      box-sizing: border-box;
      .receive-voucher-center {
        background: #FFFFFF;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0px 0px 14px 1px rgba(0,0,0,0.02);
      }
    }
  }
  
  .receive-center-qr {
    padding: 5px 0;
    text-align: center;
    
    .qrImg {
      display: inline-block;
      width: 350px;
      margin: 10px auto;
      border: 1px solid #ddd;
    }
  }
  
  .el-row {
    margin-bottom: 10px;
    
    :deep(.el-col) {
      display: flex;
      justify-content: center;
      gap: 10px;
    }
  }
  
  :deep(.el-radio-group) {
    margin: 10px 0;
  }
  
  :deep(.el-button) {
    padding: 8px 15px;
  }
  </style>