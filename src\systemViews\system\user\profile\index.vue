<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="6" :xs="24">
        <el-card class="boxCards">
          <template #header>
            <div class="clearfix">
              <span>个人信息</span>
            </div>
          </template>
          <div>
            <ul class="list-group list-group-striped">
              <li class="list-group-item" style="border-top: 0;margin-top: 0;">
                <el-icon><UserFilled /></el-icon> 登录名称
                <div class="pull-right">{{ userObj.loginName }}</div>
              </li>
              <li class="list-group-item">
                <el-icon><UserFilled /></el-icon> 用户名称
                <div class="pull-right">{{ userObj.staffName }}</div>
              </li>
              <li class="list-group-item">
                <el-icon><UserFilled /></el-icon> 当前组织
                <el-tooltip class="item" effect="dark" :content="userObj.orgName" placement="top">
                  <div class="pull-right" style="width:80px;
                           text-align: right;
                           white-space: nowrap;
                           overflow: hidden;
                           text-overflow: ellipsis;
                           float: right;">{{ userObj.orgName }}</div>
                </el-tooltip>
              </li>
              <li class="list-group-item">
                <el-icon><Iphone /></el-icon> 手机号码
                <div class="pull-right">{{ userObj.cellphone }}</div>
              </li>
              <li class="list-group-item">
                <el-icon><Message /></el-icon> 用户邮箱
                <div class="pull-right">{{ userObj.email }}</div>
              </li>
            </ul>
          </div>
        </el-card>
      </el-col>
      <el-col :span="18" :xs="24">
        <el-card>
          <template #header>
            <div class="clearfix">
              <span>基本资料</span>
            </div>
          </template>
          <el-tabs v-model="activeTab">
            <el-tab-pane label="基本资料" name="userinfo">
              <userInfo ref="userinfo" :user="user" />
            </el-tab-pane>
            <el-tab-pane label="修改密码" name="resetpwd">
              <resetPwd ref="resetpwd" :user="user" />
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="Profile">
import useUserStore from "@/store/modules/user";
import userInfo from "@/systemViews/system/user/profile/userInfo.vue"
import resetPwd from "@/systemViews/system/user/profile/resetPwd.vue"
import {getUser} from "../../../../api/system/user";

const userStore = useUserStore();
const user = ref({})
const activeTab = ref("userinfo")
const userObj = ref({})
const userId = userStore.userInfo.staffId;

function getUserProfile() {
  getUser(userId).then(res => {
    userObj.value =res.data;
    user.value = {
      staffId: res.data.staffId,
      staffName: res.data.staffName,
      cellphone: res.data.cellphone,
      email: res.data.email
    }
  })
}

getUserProfile();
</script>

