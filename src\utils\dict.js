import useDictStore from "@/store/modules/dict";
import { getDictInfo } from "@/api/system/dict/data";

const successValue = ["valid", "show","ok"];
const dangerValue = ["invalid", "hide","reject"];
/**
 * 获取字典数据
 */
export function useDict(...args) {
  const res = ref({});
  return (() => {
    args.forEach((dictType, index) => {
      res.value[dictType] = [];
      const dicts = useDictStore().getDict(dictType);
      if (dicts) {
        res.value[dictType] = dicts;
      } else {
        getDictInfo(dictType).then((resp) => {
          res.value[dictType] = resp.data.map((p) => ({ label: p.dictLabel, value: p.dictValue, elTagType: getTagType(p) }));
          useDictStore().setDict(dictType, res.value[dictType]);
        });
      }
    });
    return toRefs(res.value);
  })();
}

function getTagType(p) {
  if (p.dictRemark === "success"
      || p.dictRemark === "warning"
      || p.dictRemark === "danger"
      || p.dictRemark === "info") {
    return p.dictRemark
  }
  if (p.elTagType) {
    return p.elTagType;
  }
  if (successValue.includes(p.dictValue)) {
    return "success";
  }
  if (dangerValue.includes(p.dictValue)) {
    return "danger";
  }
  return "primary";
}
