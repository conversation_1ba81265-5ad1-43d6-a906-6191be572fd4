<!-- 借调人员导入详情 -->
<template>
  <div class="container-table-box">
    <!-- 查询条件 -->
    <dialog-search @getList="getList" formRowNumber="3" :columns="tabelForm.columns">
      <template v-slot:formList>

        <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="90px">
          <el-form-item label="模糊搜索">
            <el-input v-model="queryParams.params" placeholder="请输入搜索条件" clearable  />
          </el-form-item>
          <el-form-item label="数据类型">
            <el-select v-model="queryParams.dataType" placeholder="请选择数据类型" clearable>
              <el-option v-for="item in data_type" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="导入状态">
            <el-select v-model="queryParams.importStatus" placeholder="请选择导入状态" clearable>
              <el-option v-for="item in import_status" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>

        </el-form>
      </template>

      <template v-slot:searchList>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </template>

      <template v-slot:searchBtnList>
        <el-button type="primary" icon="Plus" @click="handleBatchAdd" :disabled="!hasSuccessSelection">
          批量新增
        </el-button>
        <el-button  type="primary"  icon="Delete" @click="handleBatchDelete" :disabled="multipleSelection.length === 0">
          批量删除
        </el-button>
   
      </template>
    </dialog-search>


    <public-table ref="publictable" :rowKey="tabelForm.tableKey" :tableData="list" :columns="tabelForm.columns"
      :configFlag="tabelForm.tableConfig" :pageValue="pageParams" :total="total" :getList="getList" @handleSelectionChange="handleSelectionChange">

      <template #operation="{ scope }">
              <el-button
                link
                icon="Plus"
                type="primary"
                title="新增"
                @click="handleAdd(scope.row)"
                :disabled="scope.row.importStatus !== 'success'"
              >
              </el-button>
              <el-button
                size="mini"
                type="text"
                icon="Delete"
                @click="handleDelete(scope.row)"
                title="删除"
              >
              </el-button>
            </template>
    </public-table>

    <!-- 新增弹窗 -->
    <DialogBox
      :visible="addDialog.open"
      :dialogWidth="addDialog.dialogWidth"
      :dialogFooterBtn="addDialog.dialogFooterBtn"
      @save="saveAdd"
      @cancellation="cancelAdd"
      @close="closeAdd"
      :dialogTitle="addDialog.headerTitle"
      :dialogTop="addDialog.dialogTop"
    >
      <template #content>
        <el-form ref="addFormRef" :model="addForm" :rules="addRules" label-width="100px">
          <el-row>
            <el-col :span="12">
              <el-form-item label="失效日期" prop="effectEndDate">
                <el-date-picker
                  v-model="addForm.effectEndDate"
                  type="date"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  placeholder="默认永久有效"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="申请人" prop="applicant">
                <el-select
                collapse-tags
                placeholder="请输入人员进行选择"
                clearable
                @change="changeStaff"
                filterable
                remote
                v-model="addForm.applicant"
                reserve-keyword
                :remote-method="getUserList"
              >
                <el-option
                  v-for="(item, index) in applyUserList"
                  :key="index"
                  :label="item.staffName"
                  :value="item.staffId">
                  <div>
                    {{
                      item.staffName +
                      "(" +
                      item.orgName +
                      "/" +
                      item.loginName +
                      ")"
                    }}
                  </div>
                </el-option>
              </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="申请说明" prop="description">
                <el-input
                  type="textarea"
                  placeholder="请输入申请说明"
                  v-model="addForm.description"
                  maxlength="200"
                  show-word-limit
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </template>
    </DialogBox>
    </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance, onMounted, computed } from "vue";
import { ElMessageBox, ElMessage } from "element-plus";
import { apiUrl } from "@/utils/config";
import { formatMinuteTime } from "@/utils";
import { staffCacheList, addStaffCache, delStaffCache, queryUserList } from "@/api/system/user";
const { proxy } = getCurrentInstance();
const { staff_type, data_type, import_status } = proxy.useDict(
  "staff_type",
  "data_type",
  "import_status"
);
const emit = defineEmits(["closeBtn"]);

const props = defineProps({
  detailList: {
    type: Array,
    default: () => [],
  },
  uoloadStatus: {
    type: String,
    default: "",
  },
  redisLock: {
    type: String,
    default: "",
  }
});
const pageParams = ref({
  pageNum: 1,
  pageSize: 10,
});
const queryParams = ref({
  params: '', // 模糊搜索参数
  dataType: '', // 数据类型
  importStatus: '' // 导入状态
});
const list = ref([]);
const total = ref(0);
const multipleSelection = ref([]); // 多选数据

// 计算是否有成功状态的数据被选中
const hasSuccessSelection = computed(() => {
  return multipleSelection.value.some(item => item.importStatus === 'success');
});

// 新增弹窗相关变量
const addDialog = reactive({
  open: false,
  headerTitle: "新增借调人员",
  dialogWidth: "600px",
  dialogFooterBtn: true,
  dialogTop: "12vh"
});

const addForm = ref({
  effectEndDate: "",
  applicant: "",
  description: "",
  staffIdList: []
});

const addFormRef = ref(null);
const applyUserList = ref([]); // 申请人列表

const addRules = {
  effectEndDate: [{ required: true, message: "请选择失效日期", trigger: "change" }],
  applicant: [{ required: true, message: "请选择申请人", trigger: "change" }],
  description: [{ required: true, message: "请输入申请说明", trigger: "blur" }],
};
const tabelForm = reactive({
  tableKey: "1", //表格key值
  isShowRightToolbar: true, //是否显示右侧显示和隐藏
  showSearch: true,
  // 表格表格数据
  columns: [
    {
      fieldIndex: "staffName",
      label: "员工姓名",
      show: true,
      sortable: true,
      visible: true, // 展示与隐藏
      minWidth: "120px",
    },
    {
      fieldIndex: "loginName",
      label: "登录账号",
      show: true,
      sortable: true,
      visible: true, // 展示与隐藏
      minWidth: "120px",
    },
    {
      fieldIndex: "cellphone",
      label: "联系方式",
      show: true,
      sortable: true,
      visible: true, // 展示与隐藏
      minWidth: "120px",
    },
    {
      fieldIndex: "orgName",
      label: "员工部门",
      show: true,
      sortable: true,
      visible: true, // 展示与隐藏
      align:'left',
      minWidth: "120px",
    },
    {
      fieldIndex: "staffType",
      label: "人员类型",
      show: true,
      visible: true, // 展示与隐藏
      sortable: true,
      minWidth: "120px",
      type: 'dict',
      dictList: staff_type// 只显示借调人员
    },
    {
      fieldIndex: "post",
      label: "岗位",
      show: true,
      visible: true, // 展示与隐藏
      sortable: true,
      minWidth: "120px",
    },

    {
      fieldIndex: "dataType",
      label: "数据类型",
      show: true,
      visible: true, // 展示与隐藏
      sortable: true,
      minWidth: "100px",
      type: 'dict',
      dictList: data_type
    },
    {
      fieldIndex: "importStatus",
      label: "导入状态",
      show: true,
      visible: true, // 展示与隐藏
      sortable: true,
      minWidth: "100px",
      type: 'dict',
      dictList: import_status
    },
    {
      label: "操作",
      show: true,
      visible: true,
      minWidth: "150px",
      slotname: "operation",
   
    },
  ],
  // 表格基础配置项，看个人需求添加
  tableConfig: {
    needPage: true, // 是否需要分页
    index: true, // 是否需要序号
    selection: true, // 是否需要多选
    reserveSelection: false, // 是否需要支持跨页选择
    indexFixed: false, // 序号列固定 left true right
    selectionFixed: false, //多选列固定left true right
    indexWidth: "50", //序号列宽度
    loading: false, //表格loading
    showSummary: false, //是否开启合计
    height: null, //表格固定高度 比如：300px
  },
});
/** 查询列表 */
const getList = () => {
  if (props.uoloadStatus === 'failed') {
    list.value = [];
    total.value = 0;
    return;
  }

  tabelForm.tableConfig.loading = true;
  const params = {
    // redisLock: props.redisLock,
    // staffType: "2", // 锁定为借调人员
    ...queryParams.value,
    ...pageParams.value
  };

  staffCacheList(params).then((response) => {
    list.value = response.data.records;
    total.value = response.data.total;
    tabelForm.tableConfig.loading = false;
  });
};
/** 搜索按钮操作 */
const handleQuery = () => {
  pageParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  proxy.resetForm("queryForm");
  queryParams.value = {
    params: '',
    dataType: '',
    importStatus: ''
  };
  handleQuery();
};

const handleExport = () => {
  proxy.download(
    `/user/sysUser/exportErrorData`,
    {
      redisLock: props.redisLock,
    },
    `批量导入借调人员失败列表_${formatMinuteTime(new Date())}.xlsx`
  );
};

// 多选变化处理
const handleSelectionChange = (selection) => {
  multipleSelection.value = selection;
};

// 单个新增
const handleAdd = (row) => {
  // 检查导入状态是否为成功
  if (row.importStatus !== 'success') {
    ElMessage.warning("只能新增导入状态为成功的数据");
    return;
  }

  addForm.value.staffIdList = [row.staffId];
  addDialog.headerTitle = "新增借调人员";
  addDialog.open = true;
  getUserList(); // 获取申请人列表
};

// 单个删除
const handleDelete = (row) => {
  const staffIdList = [row.staffId];
  handleDeleteStaff(staffIdList);
};

// 批量新增
const handleBatchAdd = () => {
  if (multipleSelection.value.length === 0) {
    ElMessage.warning("请选择要新增的数据");
    return;
  }

  // 过滤出导入状态为成功的数据
  const successData = multipleSelection.value.filter(item => item.importStatus === 'success');

  if (successData.length === 0) {
    ElMessage.warning("请选择导入状态为成功的数据");
    return;
  }

  if (successData.length < multipleSelection.value.length) {
    ElMessage.warning(`已过滤掉${multipleSelection.value.length - successData.length}条非成功状态的数据，将新增${successData.length}条成功数据`);
  }

  const staffIdList = successData.map(item => item.staffId);
  addForm.value.staffIdList = staffIdList;
  addDialog.headerTitle = `批量新增借调人员（${staffIdList.length}条）`;
  addDialog.open = true;
  getUserList(); // 获取申请人列表
};

// 批量删除
const handleBatchDelete = () => {
  if (multipleSelection.value.length === 0) {
    ElMessage.warning("请选择要删除的数据");
    return;
  }
  const staffIdList = multipleSelection.value.map(item => item.staffId);
  handleDeleteStaff(staffIdList);
};

// 获取申请人列表
const getUserList = async (name = '') => {
  try {
    const response = await queryUserList({
      staffName: name,
    });
    applyUserList.value = response.data || [];
  } catch (error) {
    console.error("获取用户列表失败:", error);
    applyUserList.value = [];
  }
};

// 选择申请人回调
const changeStaff = () => {
  const user = applyUserList.value.find(
    (o) => o.staffId === addForm.value.applicant
  );
  if (user) {
    addForm.value.applicant = user.staffId;
  }
};

// 弹窗保存
const saveAdd = (data) => {
  addFormRef.value.validate((valid) => {
    if (valid) {
      const params = {
        staffIdList: addForm.value.staffIdList,
        applicant: addForm.value.applicant,
        description: addForm.value.description,
        effectEndDate: addForm.value.effectEndDate
      };

      addStaffCache(params).then(() => {
        ElMessage.success("新增成功");
        getList(); // 刷新列表
        multipleSelection.value = []; // 清空选择
       
       if(data){
        emit('closeBtn')
       }else{
        closeAdd(); // 关闭弹窗
       }
      }).catch(() => {
        ElMessage.error("新增失败");
      });
    }
  });
};

// 弹窗取消
const cancelAdd = () => {
  closeAdd();
};

// 关闭弹窗
const closeAdd = () => {
  addDialog.open = false;
  addForm.value = {
    effectEndDate: "",
    applicant: "",
    description: "",
    staffIdList: []
  };
  if (addFormRef.value) {
    addFormRef.value.resetFields();
  }
};

// 执行删除操作
const handleDeleteStaff = (staffIdList) => {
  ElMessageBox.confirm(
    `确定要删除选中的 ${staffIdList.length} 条数据吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    const params = {
      staffIdList: staffIdList
    };

    delStaffCache(params).then(() => {
      ElMessage.success("删除成功");
      getList(); // 刷新列表
      multipleSelection.value = []; // 清空选择
    }).catch(() => {
      ElMessage.error("删除失败");
    });
  }).catch(() => {
    // 用户取消操作
  });
};

// 保存
const saveBtn = async (redisLock) => {
  try {
    ElMessage.success("操作成功");
    emit("closeBtn");
  } catch (error) {
    console.error("保存失败:", error);
  } finally {
  }
};

onMounted(() => {
  getList();
})

defineExpose({
  saveBtn,
  handleAdd,
  handleDelete,
  saveAdd,
  cancelAdd,
  closeAdd,
});
</script>

<style scoped></style>
