<!-- 借调人员导入详情 -->
<template>
  <div class="container-table-box">
    <!-- 查询条件 -->
    <dialog-search @getList="getList" formRowNumber="4" :columns="tabelForm.columns">
      <template v-slot:formList>

        <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="80px">
          <el-form-item label="模糊搜索">
            <el-input v-model="queryParams.params" placeholder="请输入搜索条件" clearable style="width: 200px;" />
          </el-form-item>
          <el-form-item label="数据类型">
            <el-select v-model="queryParams.dataType" placeholder="请选择数据类型" clearable>
              <el-option v-for="item in data_type" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="导入状态">
            <el-select v-model="queryParams.importStatus" placeholder="请选择导入状态" clearable>
              <el-option v-for="item in import_status" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>

        </el-form>
      </template>

      <template v-slot:searchList>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </template>

      <template v-slot:searchBtnList>

        <el-button type="primary" size="mini" icon="Download" @click="handleExport">下载批量导入失败列表
        </el-button>

      </template>
    </dialog-search>


    <public-table ref="publictable" :rowKey="tabelForm.tableKey" :tableData="list" :columns="tabelForm.columns"
      :configFlag="tabelForm.tableConfig" :pageValue="pageParams" :total="total" :getList="getList" @handleSelectionChange="handleSelectionChange">
    </public-table>
    </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance, onMounted } from "vue";
import { ElMessageBox, ElMessage } from "element-plus";
import { apiUrl } from "@/utils/config";
import { formatMinuteTime } from "@/utils";
import { staffCacheList } from "@/api/system/user";
const { proxy } = getCurrentInstance();
const { staff_type, data_type, import_status } = proxy.useDict(
  "staff_type",
  "data_type",
  "import_status"
);
const emit = defineEmits(["closeBtn"]);

const props = defineProps({
  detailList: {
    type: Array,
    default: () => [],
  },
  uoloadStatus: {
    type: String,
    default: "",
  },
  redisLock: {
    type: String,
    default: "",
  }
});
const pageParams = ref({
  pageNum: 1,
  pageSize: 10,
});
const queryParams = ref({
  params: '', // 模糊搜索参数
  dataType: '', // 数据类型
  importStatus: '' // 导入状态
});
const list = ref([]);
const total = ref(0);
const tabelForm = reactive({
  tableKey: "1", //表格key值
  isShowRightToolbar: true, //是否显示右侧显示和隐藏
  showSearch: true,
  // 表格表格数据
  columns: [
    {
      fieldIndex: "staffName",
      label: "员工姓名",
      show: true,
      sortable: true,
      visible: true, // 展示与隐藏
      minWidth: "120px",
    },
    {
      fieldIndex: "loginName",
      label: "登录账号",
      show: true,
      sortable: true,
      visible: true, // 展示与隐藏
      minWidth: "120px",
    },
    {
      fieldIndex: "cellphone",
      label: "联系方式",
      show: true,
      sortable: true,
      visible: true, // 展示与隐藏
      minWidth: "120px",
    },
    {
      fieldIndex: "orgName",
      label: "员工部门",
      show: true,
      sortable: true,
      visible: true, // 展示与隐藏
      minWidth: "120px",
    },
    {
      fieldIndex: "staffType",
      label: "人员类型",
      show: true,
      visible: true, // 展示与隐藏
      sortable: true,
      minWidth: "120px",
      type: 'dict',
      dictList: staff_type.filter(item => item.value === '2') // 只显示借调人员
    },
    {
      fieldIndex: "post",
      label: "岗位",
      show: true,
      visible: true, // 展示与隐藏
      sortable: true,
      minWidth: "120px",
    },

    {
      fieldIndex: "dataType",
      label: "数据类型",
      show: true,
      visible: true, // 展示与隐藏
      sortable: true,
      minWidth: "100px",
      type: 'dict',
      dictList: data_type
    },
    {
      fieldIndex: "importStatus",
      label: "导入状态",
      show: true,
      visible: true, // 展示与隐藏
      sortable: true,
      minWidth: "100px",
      type: 'dict',
      dictList: import_status
    },
  ],
  // 表格基础配置项，看个人需求添加
  tableConfig: {
    needPage: true, // 是否需要分页
    index: true, // 是否需要序号
    selection: true, // 是否需要多选
    reserveSelection: false, // 是否需要支持跨页选择
    indexFixed: false, // 序号列固定 left true right
    selectionFixed: false, //多选列固定left true right
    indexWidth: "50", //序号列宽度
    loading: false, //表格loading
    showSummary: false, //是否开启合计
    height: null, //表格固定高度 比如：300px
  },
});
/** 查询列表 */
const getList = () => {
  if (props.uoloadStatus === 'failed') {
    list.value = [];
    total.value = 0;
    return;
  }

  tabelForm.tableConfig.loading = true;
  const params = {
    redisLock: props.redisLock,
    staffType: "2", // 锁定为借调人员
    ...queryParams.value,
    ...pageParams.value
  };

  staffCacheList(params).then((response) => {
    list.value = response.data.records;
    total.value = response.data.total;
    tabelForm.tableConfig.loading = false;
  });
};
/** 搜索按钮操作 */
const handleQuery = () => {
  pageParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  proxy.resetForm("queryForm");
  queryParams.value = {
    params: '',
    dataType: '',
    importStatus: ''
  };
  handleQuery();
};

const handleExport = () => {
  proxy.download(
    `/user/sysUser/exportErrorData`,
    {
      redisLock: props.redisLock,
    },
    `批量导入借调人员失败列表_${formatMinuteTime(new Date())}.xlsx`
  );
};

// 多选
const handleSelectionChange
// 保存
const saveBtn = async (redisLock) => {
  try {
    ElMessage.success("操作成功");
    emit("closeBtn");
  } catch (error) {
    console.error("保存失败:", error);
  } finally {
  }
};


onMounted(() => {
  getList();
})
defineExpose({
  saveBtn,
});
</script>

<style scoped></style>
