<!-- 补贴补扣 -->

<template>
  <div class="dialog-box" :class="popupType === 'view' ? 'dialog-box-view' : 'dialog-box-edit'">
    <el-form ref="formRef" :model="formData" label-width="95px" :rules="popupType !== 'view' ? rules : ''">
      <el-row>
        <!-- 支付场景 -->
        <el-col :span="8">
          <el-form-item label="支付场景" prop="payType">
            <el-select v-if="popupType !== 'view'" @change="handleChangePayType" v-model="formData.payType"
                       placeholder="请选择支付场景" clearable>
              <el-option v-for="item in largeCategoryOptions" :key="item.id" :label="item.label" :value="item.id" />
            </el-select>
            <div v-else>
              {{ formData.payTypeName || "" }}
            </div>
          </el-form-item>
        </el-col>

        <!-- 支付类型 -->
        <el-col :span="8">
          <el-form-item label="支付类型" prop="paySubclass">
            <el-select v-if="popupType !== 'view'" v-model="formData.paySubclass" placeholder="请选择支付类型" clearable
                       @change="handlePaySubclassChange">
              <el-option v-for="item in orderSubclassOptions" :key="item.id" :label="item.label" :value="item.id" />
            </el-select>
            <div v-else>
              {{ formData.paySubclassName || "" }}
            </div>
          </el-form-item>
        </el-col>

        <!-- 供应商（不可选，只展示） -->
        <el-col :span="8">
          <el-form-item label="供应商" prop="providerId">
            <el-input v-if="popupType !== 'view'" v-model="formData.providerName" disabled placeholder="自动获取供应商" />
            <div v-else>
              {{ formData.providerName || "" }}
            </div>
          </el-form-item>
        </el-col>

        <!-- 价格类型相关输入框（动态切换） -->
        <el-col :span="8">
          <el-form-item :label="priceLabel" prop="factsAmount" :key="`price-${showPriceLoading}-${forceUpdate}`">
            <template v-if="showPriceLoading">
              <el-input disabled placeholder="请先选择支付类型" />
            </template>
            <NumberInput
                v-else
                v-model="formData.factsAmount"
                :key="`input-${formData.factsAmount}-${isFactsAmount}`"
                :customPlaceholder="priceLabel === '据实价格' ? '请输入据实价格（元）' : ''"
                input-type="decimal"
                :disabled="!isFactsAmount"
                @change="handleFactsAmountChange"
            />
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="补扣日期" prop="orderDate">
            <el-date-picker v-if="popupType !== 'view'" v-model="formData.orderDate" type="date" placeholder="请选择补扣日期"
                            format="YYYY-MM-DD" value-format="YYYY-MM-DD" @change="handleOrderDateChange" />
            <div v-else>
              {{ formData.orderDate || "" }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="人员选择" prop="unionId">
            <el-select v-if="popupType !== 'view'" v-model="formData.unionId" placeholder="人员姓名、手机号等搜索" clearable
                       @change="changeUser" filterable remote reserve-keyword :remote-method="getUserList" style="width: 100%;"
                       :disabled="!canSelectUser">
              <el-option v-for="(item, index) in administratorsIdsList" :key="index" :label="item.staffName"
                         :value="item.unionId">
                <div>
                  {{
                    item.staffName +
                    "(" +
                    item.orgName +
                    "/" +
                    item.loginName +
                    ")"
                  }}
                </div>
              </el-option>
            </el-select>
            <div v-else>
              {{ formData.staffName || "" }}
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="common-box-one">
      <div class="common-header">
        <div class="common-header-line"></div>
        <div class="common-header-text">账户列表</div>
      </div>
    </div>
    <!-- 表格区域 -->
    <public-table ref="publictable" :rowKey="tabelForm.tableKey" :tableData="lists" :columns="tabelForm.columns"
                  :configFlag="tabelForm.tableConfig" :pageValue="pageParams">
      <template #operation="{ scope }">
        <el-button type="text" title="补扣" @click="handleConfirmSubsidy(scope.row)"
                   v-if="popupType !== 'view'">
          <i class="icon icon-anniu-bukou iconfont" style="font-size: 20px!important;"></i>
        </el-button>
      </template>
    </public-table>
    <!-- 右下角关闭按钮 -->
    <div style="display: flex; justify-content: flex-end; margin-top: 24px;">
      <el-button type="primary" @click="handleClose" style="min-width: 88px;">关闭</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, getCurrentInstance, defineProps, onMounted, computed, nextTick } from "vue";
import { ElMessage,ElMessageBox } from "element-plus";
import { screenIndex } from "@/api/paymentCenter/subsidy-deduction/index";
import useUserStore from "@/store/modules/user";

const emit = defineEmits(["closeBtn"]);
const props = defineProps({
  closeBtn: {
    type: Function,
    default: null,
  },
  popupType: {
    type: String,
    default: "",
  },
  rowData: {
    type: Object,
    default: () => ({}),
  },
});

const administratorsIdsList = ref([])
const formRef = ref(null);
const formData = reactive({
  orderDate: new Date().toISOString().split('T')[0]
});
const pageParams = ref({
  pageNum: 1,
  pageSize: 10,
});
const { proxy } = getCurrentInstance();
const {
  subsidy_account_type
} = proxy.useDict(
    "subsidy_account_type"
);
const isFactsAmountData = reactive({})
// 供应商列表
const providerList = ref([]);
// 支付场景选项
const largeCategoryOptions = ref([]);
// 支付类型选项
const orderSubclassOptions = ref([]);

// 新增：价格类型相关响应式变量
// 是否为据实价格
const isFactsAmount = ref(false);
// 价格label
const priceLabel = computed(() => {
  if (showPriceLoading.value) return '支付价格';
  return isFactsAmount.value ? '据实价格' : '默认价格';
});
// 是否显示价格输入框
const showFactsAmountInput = computed(() => {
  // 只要有价格就显示输入框
  return typeof formData.factsAmount !== 'undefined' && formData.factsAmount !== '';
});

// 新增：价格类型请求loading和label切换逻辑
const showPriceLoading = ref(false);

// 强制更新视图的方法
const forceUpdate = ref(0);
const triggerUpdate = () => {
  forceUpdate.value++;
  nextTick();
};

// 是否可以选择人员（所有条件都不为空时才可选）
const canSelectUser = computed(() => {
  return (
      formData.providerId &&
      formData.payType &&
      formData.paySubclass &&
      formData.orderDate &&
      (formData.factsAmount !== '' && formData.factsAmount !== undefined && formData.factsAmount !== null)
  );
});

const rules = reactive({
  providerId: [
    { required: true, message: "请选择供应商", trigger: ["blur", "change"] },
  ],
  payType: [
    { required: true, message: "请选择支付场景", trigger: ["blur", "change"] },
  ],
  paySubclass: [
    { required: true, message: "请选择支付类型", trigger: ["blur", "change"] },
  ],
  orderDate: [
    { required: true, message: "请选择补扣日期", trigger: ["blur", "change"] },
  ],
  unionId: [
    { required: true, message: "", trigger: ["blur", "change"] }, // 不再提示请选择人员
  ],
  factsAmount: [
    { required: true, message: "请输入价格", trigger: ["blur", "change"] },
  ],
});

const lists = ref([]);
const tabelForm = reactive({
  tableKey: "subsidy",
  columns: [
    {
      fieldIndex: "accountName",
      label: "账户名称",
      minWidth: 100,
      visible: true,
    },
    {
      label: "实付金额(元)", // 原支付金额改为实付金额
      fieldIndex: "payAmount",
      minWidth: 100,
      visible: true,
      type: 'dollor'
    },
    {
      label: "订单金额(元)",
      fieldIndex: "orderAmount",
      minWidth: 100,
      visible: true,
      type: 'dollor'
    },
    {
      label: "订单日期",
      fieldIndex: "orderDate",
      minWidth: 120,
      visible: true
    },
    {
      label: "操作",
      slotname: "operation",
      minWidth: 80,
      fixed: "right",
      visible: true,
      slot: true,
    },
  ],
  tableConfig: {
    needPage: true,
    index: true,
    selection: false,
    indexWidth: "60",
    loading: false,
    height: null,
  },
});

const getUserList = async (name) => {
  if (!name || name.trim().length < 2) {
    administratorsIdsList.value = []; // 立即清空列表
    return;
  }
  try {
    const res = await screenIndex.selectUserList({
      params: name,
    });
    administratorsIdsList.value = res.data;
  } catch (error) {
    console.error("获取用户列表失败:", error);
  }
};
// 移除 watch 对 fetchAccountList 的调用，只监听 unionId
// 只在选择人员时才请求账户列表
const changeUser = (selectedIds) => {
  formData.unionId = selectedIds
  const selectedUser = administratorsIdsList.value.find(user => user.unionId === selectedIds);
  if (selectedUser) {
    formData.staffName = selectedUser.staffName;
  } else {
    formData.staffName = '';
  }
  lists.value = [];
  if (selectedIds) {
    fetchAccountList();
  }
};

// 新增：每次切换支付场景/类型/供应商时都重置价格loading
const resetPriceLoading = () => {
  showPriceLoading.value = true;
  isFactsAmount.value = false;
  formData.factsAmount = '';
  triggerUpdate(); // 强制更新视图
};

// 新增：根据支付场景id获取供应商（只会有一个）
const fetchProviderByScene = async (sceneId) => {
  try {
    const res = await screenIndex.providerTree({ id: sceneId });
    providerList.value = res.data || [];
    if (providerList.value.length > 0) {
      const provider = providerList.value[0];
      formData.providerId = provider.id;
      formData.providerName = provider.label;
      formData.providerCode = provider.value;
    } else {
      formData.providerId = '';
      formData.providerName = '';
      formData.providerCode = '';
    }
  } catch (error) {
    providerList.value = [];
    formData.providerId = '';
    formData.providerName = '';
    formData.providerCode = '';
  }
};

// 选择支付场景后，同时请求支付类型和供应商
const handleChangePayType = async (value) => {
  formData.paySubclass = "";
  lists.value = [];
  formData.unionId = '';
  resetPriceLoading();
  if (!value) {
    isFactsAmountData.largeCategoryCode = '';
    formData.payTypeName = '';
    orderSubclassOptions.value = [];
    formData.providerId = '';
    formData.providerName = '';
    formData.providerCode = '';
    providerList.value = [];
    return;
  }
  const selectedOption = largeCategoryOptions.value.find(
      (item) => item.id === value
  );
  isFactsAmountData.largeCategoryCode = selectedOption?.value || '';
  formData.payTypeName = selectedOption ? selectedOption.label : '';
  try {
    await payTypeTree(selectedOption.id);
    await fetchProviderByScene(selectedOption.id);
    // 新增：自动选中第一个支付类型并调用 handlePaySubclassChange
    if (
      orderSubclassOptions.value.length === 1 &&
      props.popupType === "add"
    ) {
      formData.paySubclass = orderSubclassOptions.value[0].id;
      await handlePaySubclassChange(formData.paySubclass);
    }
  } catch (error) {
    orderSubclassOptions.value = [];
  }
  // resetPriceLoading(); // 移除，避免覆盖 loading 状态
  lists.value = [];
  formData.unionId = '';
};

// 页面初始化：只请求支付场景
const initData = async () => {
  resetPriceLoading();
  // 只请求支付场景
  try {
    const res = await screenIndex.paySceneTree({});
    largeCategoryOptions.value = res.data || [];
    
    // 如果只有一个支付场景选项，自动选中
    if (largeCategoryOptions.value.length === 1 && props.popupType === "add") {
      formData.payType = largeCategoryOptions.value[0].id;
      await handleChangePayType(formData.payType);
    }
  } catch (error) {
    largeCategoryOptions.value = [];
    console.error('获取支付场景失败:', error);
  }
  if (props.popupType === "add") {
    formData.orderDate = new Date().toISOString().split('T')[0];
    lists.value = [];
  } else {
    // 编辑或查看时获取详情数据
    try {
      Object.assign(formData, props.rowData);
      // 处理级联关系：先处理支付场景
      if (formData.payType || formData.sceneId) {
        const sceneId = formData.payType || formData.sceneId;
        // 获取支付场景列表
        try {
          const sceneRes = await screenIndex.paySceneTree({});
          largeCategoryOptions.value = sceneRes.data;
          // 设置支付场景名称（查看模式）
          if (props.popupType === "view" && formData.sceneId) {
            const payTypeOption = sceneRes.data.find(item => item.id === formData.sceneId);
            formData.payTypeName = payTypeOption ? payTypeOption.label : '';
          }
          // 获取支付类型
          if (sceneId) {
            try {
              await payTypeTree(sceneId);
              // 设置支付类型名称（查看模式）
              if (props.popupType === "view" && (formData.paySubclass || formData.typeId)) {
                const typeId = formData.paySubclass || formData.typeId;
                const paySubclassOption = orderSubclassOptions.value.find(item => item.id === typeId);
                formData.paySubclassName = paySubclassOption ? paySubclassOption.label : '';
                if (formData.typeId && !formData.paySubclass) {
                  formData.paySubclass = formData.typeId;
                }
              }
            } catch (error) {
              console.error('获取支付类型失败:', error);
              orderSubclassOptions.value = [];
            }
          }
          // 获取供应商
          await fetchProviderByScene(sceneId);
        } catch (error) {
          console.error('获取支付场景失败:', error);
          largeCategoryOptions.value = [];
        }
      }
    } catch (error) {
      console.error('获取详情失败:', error);
    }
  }
};

// 查询支付类型树
const payTypeTree = async (value) => {
  try {
    const res = await screenIndex.payTypeTree({
      id: value || "",
    });
    if (value) {
      orderSubclassOptions.value = res.data;
      // 移除自动选中逻辑，改到 handleChangePayType 里
    } else {
      largeCategoryOptions.value = res.data;
    }
  } catch (error) {
    console.error("获取支付类型树失败:", error);
  }
};

// 处理支付类型变更，获取价格类型数据
const handlePaySubclassChange = async (value) => {
  lists.value = [];
  formData.unionId = '';
  const selectedOption = orderSubclassOptions.value.find(
      (item) => item.id === value
  );
  if (selectedOption) {
    isFactsAmountData.orderSubclassCode = selectedOption.value;
    formData.paySubclassName = selectedOption.label;
    formData.typeId = selectedOption.id;
  } else {
    isFactsAmountData.orderSubclassCode = '';
    formData.paySubclassName = '';
    formData.typeId = '';
  }
  if (formData.providerId && formData.payType && formData.paySubclass) {
    try {
      showPriceLoading.value = true;
      formData.factsAmount = '';
      isFactsAmount.value = false;
      const res = await screenIndex.isFactsAmount({
        providerId: formData.providerId,
        largeCategoryCode: isFactsAmountData.largeCategoryCode,
        orderSubclassCode: isFactsAmountData.orderSubclassCode
      });

      if (res.code === "1" && res.data) {
        isFactsAmount.value = res.data.isFactsAmount;
        formData.priceType = res.data.isFactsAmount ? '0' : '1';
        if (res.data.isFactsAmount) {
          formData.factsAmount = '';
        } else {
           showPriceLoading.value = false;
          formData.factsAmount = res.data.defaultAmount;
        }
      }

      // 确保在所有数据处理完成后才关闭loading
      showPriceLoading.value = false;
      triggerUpdate(); // 强制更新视图
      await nextTick(); // 强制刷新视图
    } catch (error) {
      showPriceLoading.value = false;
      triggerUpdate(); // 强制更新视图
      await nextTick(); // 强制刷新视图
      console.error("获取价格类型数据失败:", error);
    }
  }
};

// 日期选择和价格变化等也要清空列表和人员
// 假设有日期选择事件
const handleOrderDateChange = () => {
  lists.value = [];
  formData.unionId = '';
};
// 假设有价格输入事件
const handleFactsAmountChange = () => {
  lists.value = [];
  formData.unionId = '';
};

// 获取账户列表时，providerId、providerCode等仍然从formData中取
const fetchAccountList = async () => {
  try {
    const params = {
      providerId: formData.providerId,
      providerCode: formData.providerCode,
      largeCategoryCode: isFactsAmountData.largeCategoryCode,
      orderSubclassCode: isFactsAmountData.orderSubclassCode,
      priceType: formData.priceType, // 新增
      payAmount: formData.factsAmount !== '' ? formData.factsAmount : null,
      unionId: formData.unionId,
      orderDate: formData.orderDate
    };
    const res = await screenIndex.accountList(params);
    if (res.code === "1" && res.data) {
      lists.value = res.data
    }
  } catch (error) {
    console.error("获取账户列表失败:", error);
  }
};

// 扣款时参数不变，仍然取formData.value.providerId、providerCode等
const handleSubsidy = async (row) => {
  try {
    const params = {
      payAmount: row.payAmount || 0,
      accountListId:  row.accountListId || "",
      accountName: row.accountName || "",
      providerId: formData.providerId || "",
      providerCode: formData.providerCode || "",
      sceneId: formData.payType || "",
      sceneCode: isFactsAmountData.largeCategoryCode || "",
      typeId: formData.paySubclass || "",
      typeCode: isFactsAmountData.orderSubclassCode || "",
      priceType: formData.priceType || "", // 新增
      factsAmount: formData.factsAmount !== '' ? formData.factsAmount : null,
      orderAmount: row.orderAmount || 0,
      staffName:formData.staffName || "",
      unionId: formData.unionId || "",
      staffId: row.staffId||'',
      orderDate:  formData.orderDate || ""
    };
    const res = await screenIndex.deductAmount(params);
    if (res.code === "1") {
      ElMessage.success("补扣操作成功");
      lists.value = [];
      formData.unionId = "";
      administratorsIdsList.value = [];
    }
  } catch (error) {
    console.error("补扣操作失败:", error);
    ElMessage.error("补扣操作失败");
  }
};

// 确认补扣操作
const handleConfirmSubsidy = (row) => {
  ElMessageBox.confirm('确定要执行补扣操作吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    handleSubsidy(row);
  }).catch(() => {
    ElMessage.info('已取消补扣操作');
  });
};

// 关闭按钮事件，触发关闭弹窗并刷新页面
const handleClose = () => {
  emit('closeBtn');
};

onMounted(() => {
  initData();
});
</script>

<style scoped lang="scss">
:deep(.el-radio-group) {
  flex-wrap: nowrap !important;
  margin-right: 20px;
}

:deep(.el-select .el-input__inner) {
  min-width: 100% !important;
}
</style>
