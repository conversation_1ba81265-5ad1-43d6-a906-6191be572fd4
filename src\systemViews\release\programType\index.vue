<template>
    <div class="app-container">
      <el-card style="height: 100%" shadow="never">
        <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
          <el-form-item label="租户" prop="tenantId" v-if="userStore.userInfo.customParam.userType === 'admin'">
            <el-select v-model="queryParams.tenantId" placeholder="请选择租户" style="width: 180px" @change="handleQuery">
              <el-option v-for="item in tenantList" :key="item.tenantId" :label="item.tenantName" :value="item.tenantId"/>
            </el-select>
          </el-form-item>
          <el-form-item label="栏目类型编码" prop="programaTypeCode">
            <el-input v-model="queryParams.programaTypeCode" placeholder="请输入栏目类型编码" style="width: 180px" clearable @keyup.enter="handleQuery"/>
          </el-form-item>
          <el-form-item label="栏目类型名称" prop="programaTypeName">
            <el-input v-model="queryParams.programaTypeName" placeholder="请输入栏目类型名称" style="width: 180px" clearable @keyup.enter="handleQuery"/>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" v-hasPermi="['sys:release:programa-type:add']" @click="handleAdd">新增</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <!--  表格数据展示  -->
        <el-table v-loading="loading" :data="dataList">
          <el-table-column label="栏目类型编码" align="left" prop="programaTypeCode" width="200" :show-overflow-tooltip="true"/>
          <el-table-column label="栏目类型名称" align="left" prop="programaTypeName" width="200" :show-overflow-tooltip="true"/>
          <el-table-column label="租户名称" align="left" prop="tenantName" width="200" :show-overflow-tooltip="true"/>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template #default="scope">
              <el-button link type="primary" icon="Edit" v-hasPermi="['sys:release:programa-type:edit']" @click="handleUpdate(scope.row)"
                         :loading="reloadId === scope.row.programaTypeId && reloadType === 'update'">编辑</el-button>
              <el-button link type="primary" icon="Delete" v-hasPermi="['sys:release:programa-type:remove']" @click="handleDelete(scope.row)"
                         :loading="reloadId === scope.row.programaTypeId && reloadType === 'delete'">删除</el-button>
              <el-button link type="primary" icon="Promotion" v-hasPermi="['sys:release:programa-type:sync']" @click="handleSync(scope.row)"
                         :loading="reloadId === scope.row.programaTypeId && reloadType === 'sync'">同步至其他租户</el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize"
                    @pagination="getList"/>

        <!-- 新增/编辑对话框 -->
        <el-dialog :title="title" v-model="open" width="680px" append-to-body>
          <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
            <el-row>
              <el-col :span="24">
                <el-form-item label="栏目类型编码" prop="programaTypeCode">
                  <el-input v-model="form.programaTypeCode" placeholder="请输入栏目类型编码"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item label="栏目类型名称" prop="programaTypeName">
                  <el-input v-model="form.programaTypeName" placeholder="请输入栏目类型名称"/>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <template #footer>
            <div class="dialog-footer">
              <el-button type="primary" @click="submitForm">确 定</el-button>
              <el-button @click="cancel" >取 消</el-button>
            </div>
          </template>
        </el-dialog>
      </el-card>
    </div>
</template>

<script setup name="ProgramType">
import {getTenants} from "@/api/tenant/tenant";
import {ref} from "vue";
import useUserStore from "@/store/modules/user";
import {del, add, update, syncData, findPageList} from "@/api/release/programType";

const { proxy } = getCurrentInstance();
const showSearch = ref(true);
const reloadId = ref(undefined);
const reloadType = ref(undefined);

/** 初始化租户数据 */
const userStore = useUserStore();
const tenantList = ref([]);
function getTenantList() {
  getTenants().then(res => {
    tenantList.value = res.data;
  }).catch(() => {
    tenantList.value = [];
  })
}

/** 表格数据展示 */
const dataList = ref([]);
const loading = ref(false);
const total = ref(0);
const queryRef = ref(null);
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  tenantId: userStore.userInfo.customParam.tenantId,
  programaTypeCode: undefined,
  programaTypeName: undefined,
})
function getList() {
  loading.value = true;
  findPageList(queryParams.value).then(res => {
    if (res.data) {
      dataList.value = res.data.records;
      total.value = res.data.total;
    }
    loading.value = false;
  }).catch(() => {
    loading.value = false;
  })
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  proxy.resetForm("queryRef");
  handleQuery();
}

/**  数据行上锁 */
function setLoad(id, type) {
  reloadId.value = id;
  reloadType.value = type;
}

/**  数据行锁重置 */
function resetLoad() {
  reloadId.value = undefined;
  reloadType.value = undefined;
}

/** 删除按钮操作 */
const handleDelete = (row) => {
  setLoad(row.programaTypeId, "delete");
  proxy.$modal.confirm("是否确认删除这条数据?").then(() => {
    del({programaTypeId: row.programaTypeId}).then(res => {
      if (res.data) {
        proxy.$modal.msgSuccess("删除成功")
      } else {
        proxy.$modal.msgError("删除失败");
      }
      resetLoad();
      getList();
    })
  }).catch(() => {
    proxy.$modal.msg("取消操作");
    resetLoad();
  })
}

/** 新增按钮操作 */
const formRef = ref(null);
const open = ref(false);
const title = ref("");
const form = ref({});
const rules = {
  programaTypeCode: [
    {required: true, message: "栏目类型编码不能为空", trigger: "blur"},
    {max: 64, message: "长度需要小于 64 个字符", trigger: "blur"},
  ],
  programaTypeName: [
    {required: true, message: "栏目类型名称不能为空", trigger: "blur"},
    {max: 128, message: "长度需要小于 128 个字符", trigger: "blur"},
  ],
}

const handleAdd = () => {
  form.value.programaTypeCode = undefined;
  form.value.programaTypeName = undefined;
  form.value = {};
  open.value = true;
  title.value = "新增";
  reloadType.value = "add";
}

/** 修改按钮操作 */
const handleUpdate = (row) => {
  form.value = {
    programaTypeCode: row.programaTypeCode,
    programaTypeId: row.programaTypeId,
    programaTypeName: row.programaTypeName
  }
  open.value = true;
  title.value = "编辑";
  reloadType.value = "update";
}

/** 提交按钮操作 */
const submitForm = () => {
  proxy.$refs["formRef"].validate(valid => {
    if (valid) {
      if (reloadType.value === 'add') {
        add({tenantId: queryParams.value.tenantId, ...form.value}).then(res => {
          submitResponse(res);
        })
      } else if (reloadType.value === 'update') {
        update(form.value).then(res => {
          submitResponse(res);
        })
      }
    }
  })
}

/** 提交响应处理 */
function submitResponse(res) {
  if (!res.success) {
    proxy.$modal.error(res.message);
  } else {
    proxy.$modal.msgSuccess("保存成功");
    open.value = false;
    getList();
  }
  resetLoad();
}

/** 同步至其他租户按钮操作 */
const handleSync = (row) => {
  proxy.$modal.confirm("是否确认同步这条数据至其他所有租户?").then(() => {
    setLoad(row.programaTypeId, "sync");
    syncData({programaTypeId: row.programaTypeId}).then(res => {
      if (res.success) {
        getList();
        proxy.$modal.msgSuccess(res.message);
      } else {
        proxy.$modal.msgError(res.message);
      }
      resetLoad();
    }).catch(() => {
      resetLoad();
      proxy.$modal.msgError("同步异常,请稍后再试");
    })
  }).catch(() => {
    proxy.$modal.msg("操作已取消")
  })
}

/** 取消按钮操作 */
const cancel = () => {
  open.value = false;
  resetLoad();
}

getList();
getTenantList();
</script>
