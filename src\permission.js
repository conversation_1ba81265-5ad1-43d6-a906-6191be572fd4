import router from "./router";
import NProgress from "nprogress";
import "nprogress/nprogress.css";
import { isRelogin } from "@/utils/request";
import useUserStore from "@/store/modules/user";
import useSettingsStore from "@/store/modules/settings";
import usePermissionStore from "@/store/modules/permission";
import useAppStore from "@/store/modules/app";
//skywalking监控系统
import ClientMonitor from 'skywalking-client-js';

NProgress.configure({ showSpinner: false });

const whiteList = ["/login"];

let userStore;

let settingsStore;

let isPortalInit = false;

let isSystemInit = false;

let isDynamicInit = false;

router.beforeEach(async (to, from) => {
  //skywalking

  const skywalkingEnable = import.meta.env.VITE_SKYWALKING ? import.meta.env.VITE_SKYWALKING:"false";

  if("true"===skywalkingEnable){
    //路由上报到skywalking
    ClientMonitor.setPerformance({
      pagePath: location.href,//当前路由地址。
      useFmp: true,
    });
  }


  NProgress.start();

  if (!userStore) {
    userStore = useUserStore();
  }

  if (!settingsStore) {
    settingsStore = useSettingsStore();
  }
  if (userStore.InforAvailable && to.path === "/login") {
    to.meta.title && settingsStore.setTitle(to.meta.title);
    return { path: "/" };
  }

  if (whiteList.includes(to.path)) {
    // 在免登录白名单，直接进入
    to.meta.title && settingsStore.setTitle(to.meta.title);
    return;
  }

  if (!userStore.InforAvailable && userStore.jwtToken) {
    isRelogin.show = true;
    await userStore.getInfo();
    await useAppStore().updateHomeDesign()
    isRelogin.show = false;
  }

  if (!userStore.InforAvailable) {
   
    return { path: "/login", query: { redirect: to.fullPath } };
  }

  if (!isDynamicInit) {
    await usePermissionStore().generateDynamicRoutes();
    isDynamicInit = true;
    return { ...to };
  }

  if (!isPortalInit && to.path.startsWith("/portal")) {
    await usePermissionStore().generatePortalRoutes();
    isPortalInit = true;
    return { ...to };
  }

  if (!isSystemInit && to.path.startsWith("/system")) {
    await usePermissionStore().generateSystemRoutes();
    isSystemInit = true;
    return { ...to };
  }


});

router.afterEach(() => {
  NProgress.done();
});
