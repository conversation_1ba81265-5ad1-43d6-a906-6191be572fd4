import request from '@/utils/request'
import { apiUrl } from '@/utils/config'; 
//查询申请人列表
// export function selectApplyUserList(data) {
//   return request({
//     url: "user/staffs/selectUserList",  //########################后期更改#################
//     method: "post",
//     data: data,
//   })
// }
//获取车场主键
export function getParkingId() {
  return request({
    url: `park${apiUrl}/majorParking/parkingLot/getParkingId`,
    method: 'post'
  })
}
export function selectApplyUserList(data) {
  return request({
    url: `park${apiUrl}/majorParking/parkingLot/selectUserList`,  
    method: "post",
    data: data,
  })
}



//停车场查询
export function findparkinglot(param,data) {
  return request({
    url: `park${apiUrl}/majorParking/parkingLot/findparkinglot`,
    method: 'post',
    data: {...data,...param},
    params: param
  })
}
//根据id查询停车场信息
export function findparkinglotById(data) {
  return request({
    url: `park${apiUrl}/majorParking/parkingLot/findparkinglotById/` +data,
    method: 'post'
  })
}
//删除车场信息
export function deleteParkingById(data) {
  return request({
    url: `park${apiUrl}/majorParking/parkingLot/deleteParkingById/`+data,
    method: 'post'
  })
}
//添加停车场
export function addParking(data) {
  return request({
    url:  `park${apiUrl}/majorParking/parkingLot/addParking`,
    method: 'post',
    data:data
  })
}
//更新停车场
export function updateParkingById(data) {
  return request({
    url: `park${apiUrl}/majorParking/parkingLot/updateParkingById`,
    method: 'post',
    data:data
  })
}

//查询未绑定的停车道闸设备
export function findDeviceForParking(data) {
  return request({
    url:  `park${apiUrl}/major/parkingDevice/findDeviceForParking`,
    method: 'post',
    data:data
  })
}
//绑定停车场与设备
export function bindingParkingDevice(data) {
  return request({
    url: `park${apiUrl}/major/parkingDevice/bindingParkingDevice`,
    method: 'post',
    data:data
  })
}
//查询停车场树结构
export function findparkinglotTree(data) {
  return request({
    url: `park${apiUrl}/majorParking/parkingLot/findparkinglotTree`,
    method: 'post',
    data:data
  })
}
//根据userId查询用户信息
export function selectByUserId(data) {
  return request({
    url:  `park${apiUrl}/majorParking/parkingLot/selectByUserId/` +data,
    method: 'post',
    data:data
  })
}
//验证停车场是否可以添加车位为零时的停车规则
export function verifyRules(data) {
  return request({
    url: `park${apiUrl}/majorParking/parkingLot/verifyRules/` + data,
    method: 'post'
  })
}
//查询有效的停车场规则
export function findParkinRules(data) {
  return request({
    url: `park${apiUrl}/majorParking/parkingLotRules/findParkinRules`,
    method: 'post',
    data:data
  })
}
//根据停车场查询的余位为零时的停车场规则
export function findParkinRulesByParkingLotId(data) {
  return request({
    url: `park${apiUrl}/majorParking/parkingLotRules/findParkinRulesByParkingLotId`,
    method: 'post',
    data:data
  })
}


export function refreshParkNum(data) {
  return request({
    url: `park${apiUrl}/majorParking/parkingLot/refreshParkNum`,
    method: 'post',
    data:data
  })
}

