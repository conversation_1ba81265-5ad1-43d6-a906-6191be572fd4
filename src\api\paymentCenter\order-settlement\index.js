import request from '@/utils/request'
import { apiUrl } from '@/utils/config'; 



export class screenIndex {
    // 查询结算列表
    static settleList(data,params) {
      return request({
        url: `pay${apiUrl}/pay/settle/settleList`,
        method: 'post',
        data: {...data,...params},
      })
    }
    //查询订单结算审核列表
    static settleAuditList(data,params) {
      return request({
        url: `pay${apiUrl}/pay/settle/settleAuditList`,
        method: 'post',
       data: {...data,...params},
      })
    }

    //根据人员id查询结算管理供应商信息
    static getInfoByStaffId(data) {
      return request({
        url: `pay${apiUrl}/pay/settle/getInfoByStaffId`,
        method: 'post',
        data:data
      })
    }
    // 结算账户列表查询
    
    static listAccount(data) {
      return request({
        url: `pay${apiUrl}/pay/settle/listAccount`,
        method: 'post',
        data: data
      }) 
    }
    // 获取订单结算详细信息
    static getInfo(data) {
      return request({
        url: `pay${apiUrl}/pay/settle/getInfo`,
        method: 'post',
        data:data
      }) 
    }
    // 新增订单结算
    static add(data) {
      return request({
        url: `pay${apiUrl}/pay/settle/add`,
        method: 'post',
        data: data
      }) 
    }
    // 修改订单结算状态
    static editStatus(data) {
      return request({
        url: `pay${apiUrl}/pay/settle/editStatus`,
        method: 'post',
        data: data
      })
    }

    // 修改订单结算
    static edit(data) {
      return request({
        url: `pay${apiUrl}/pay/settle/edit`,
        method: 'post',
        data: data
      }) 
    }
    //删除订单结算
    static delete(data) {
         return request({
        url: `pay${apiUrl}/pay/settle/delete`,
        method: 'post',
        data: data
      }) 
    }
// 查询订单结算明细列表
    static settleDetailsList(data) {
      return request({
        url: `pay${apiUrl}/pay/settleDetails/list`,
        method: 'post',
        data: data
      }) 
    }

    // 查询订单结算单列表
    static settleSheetList(data) {
      return request({
        url: `pay${apiUrl}/pay/settleSheet/list`,
        method: 'post',
        data: data
      })
    }
// 查询订单结算流程列表
    static settleFlowList(data) {
      return request({
        url: `pay${apiUrl}/pay/settleFlow/list`,
        method: 'post',
        data: data
      }) 
    }

//新增订单结算流程

    static addFlow(data) {
      return request({
        url: `pay${apiUrl}/pay/settleFlow/add`,
        method: 'post',
        data: data
      }) 
    }
}