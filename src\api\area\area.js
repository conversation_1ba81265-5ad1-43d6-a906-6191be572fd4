import request from "@/utils/request";
import { apiUrl } from '@/utils/config'; 

// 获取 区域树
export function areaTreeSelect(data) {
  return request({
    url: `user${apiUrl}/area/queryLazyAreaTree`,
    method: "post",
    data: data,
  });
}

// 获取 区域路径
export function getAreaPath(params) {
  return request({
    url: `user${apiUrl}/area/findFatherPathById`,
    method: "get",
    params: params,
  });
}

// 查询后端构建区域树
export function getAllAreaTree(data) {
  return request({
    url: `user${apiUrl}/area/getAllAreaTree`,
    method: "post",
    data: data,
  });
}

// 查询区域列表
export function queryAreaList(data, params) {
  return request({
    url: `user${apiUrl}/area/queryAreaList`,
    method: "post",
    data: {...data,...params},
    params: params,
  });
}

// 查询区域信息
export function getAreaInfo(params) {
  return request({
    url: `user${apiUrl}/area/getAreaInfo`,
    method: "get",
    params: params,
  });
}

// 保存区域信息
export function saveAreaInfo(data) {
  return request({
    url: `user${apiUrl}/area/saveAreaInfo`,
    method: "post",
    data: data,
  });
}

// 删除区域信息
export function delAreaById(id) {
  return request({
    url: `user${apiUrl}/area/delById/` + id,
    method: "get",
  });
}

//上级区域树组织查询接口
export function getTreeData() {
  return request({
    url: `user${apiUrl}/area/areaTreeSelect`,
    method: "get",
  });
}
